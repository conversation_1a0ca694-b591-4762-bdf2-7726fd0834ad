import { isNil } from 'lodash/fp';
import { ApplicationKind, ApplicationModule, LegacyApplication, ModuleType, SettingId } from '../../database/documents';
import { getBankIdFromApplication } from '../../database/helpers/applications';
import { getDealershipPaymentSetting, getDealershipPaymentSettingId } from '../../utils/getDealershipPaymentSetting';
import JourneyContext from '../JourneyContext';
import JourneyStep from '../JourneyStep';
import {
    AdyenPaymentStep,
    ApplicantAgreementsStep,
    ApplicantKYCStep,
    AppointmentStep,
    DraftingStep,
    FiservPaymentStep,
    GuarantorAgreementsStep,
    GuarantorKYCStep,
    GuarantorNamirialStep,
    InsuranceNamirialStep,
    InsuranceOTPStep,
    NamirialStep,
    OTPStep,
    PayGatePaymentStep,
    PorschePaymentStep,
    SystemReceivalStep,
    TtbPaymentStep,
} from '../common';
import { hasAppointmentScenario, hasPaymentScenario } from '../common/helpers';
import { getSigningModule } from '../helper/getSigningModule';
import LegacyJourneyContext from './LegacyJourneyContext';

export const getSigningStep = async <
    TApplication extends LegacyApplication,
    TApplicationModule extends ApplicationModule,
>(
    // todo include event applications later on when bankId is available
    context: JourneyContext<TApplication, TApplicationModule>,
    isResubmit: boolean
) => {
    const { journey } = context;

    const signingModule = await getSigningModule(
        context.applicationModule,
        context.application,
        isResubmit,
        context.loaders
    );

    if (!signingModule) {
        return null;
    }

    switch (signingModule._type) {
        case ModuleType.BasicSigningModule:
            return [new OTPStep(context)];

        case ModuleType.NamirialSigningModule:
        case ModuleType.Docusign: {
            const steps: JourneyStep<JourneyContext<TApplication, TApplicationModule>>[] = [];
            if (!journey.applicantSigning?.completed) {
                steps.push(new NamirialStep(context));
            }
            if (journey.hasGuarantorCustomer && !journey.guarantorSigning?.completed) {
                steps.push(new GuarantorNamirialStep(context));
            }

            return steps;
        }

        default:
            throw Error('Invalid Module Type');
    }
};
export const getInsuranceSigningStep = async <
    TApplication extends LegacyApplication,
    TApplicationModule extends ApplicationModule,
>(
    context: JourneyContext<TApplication, TApplicationModule>
) => {
    const { application, loaders, journey } = context;

    if (journey.insuranceApplicantSigning?.completed) {
        return null;
    }

    const isApplicationResubmit = !isNil(journey.submission);
    // AN-1561: we currently don't allow insurance resubmission
    if (isApplicationResubmit) {
        return null;
    }

    // Supported insurance
    if (
        application.kind !== ApplicationKind.Standard &&
        application.kind !== ApplicationKind.Finder &&
        application.kind !== ApplicationKind.Configurator
    ) {
        return null;
    }

    if (!application.insurancing?.insurerId) {
        return null;
    }

    const insurer = await loaders.insurerById.load(application.insurancing.insurerId);

    // check whether the application has insurer
    if (!insurer) {
        return null;
    }

    const isInsuranceResubmit = !isNil(journey.insuranceSubmission);
    const { reSubmissionApprovalModuleId, submissionApprovalModuleId } = insurer;

    const moduleId = isInsuranceResubmit ? reSubmissionApprovalModuleId : submissionApprovalModuleId;

    if (!moduleId) {
        return null;
    }

    // Check also if there applicant signing using same module id
    const bankId = getBankIdFromApplication(application);

    if (bankId) {
        const bank = await loaders.systemBankById.load(bankId);
        const financeSigningModuleId = isApplicationResubmit
            ? bank?.reSubmissionApprovalModuleId
            : bank?.submissionApprovalModuleId;

        // No need to proceed insurance applicant signing if its' the same
        if (financeSigningModuleId && financeSigningModuleId.equals(moduleId)) {
            return null;
        }
    }

    const module = await loaders.moduleById.load(moduleId);

    if (!module) {
        throw Error('Module not found');
    }

    switch (module._type) {
        case ModuleType.BasicSigningModule:
            return new InsuranceOTPStep(context);

        case ModuleType.NamirialSigningModule:
        case ModuleType.Docusign:
            return new InsuranceNamirialStep(context);

        default:
            throw Error('Invalid Module Type');
    }
};

export const getDepositStep = async <
    TApplication extends LegacyApplication,
    TApplicationModule extends Exclude<
        ApplicationModule,
        { _type: ModuleType.EventApplicationModule | ModuleType.LaunchPadModule }
    >,
>(
    context: JourneyContext<TApplication, TApplicationModule>
) => {
    const { applicationModule, loaders, application } = context;

    const dealer = await loaders.dealerById.load(application.dealerId);
    const setting = await getDealershipPaymentSetting(dealer._id, applicationModule, { loaders });

    if (!setting) {
        throw new Error('Setting not found');
    }

    switch (setting.settingId) {
        case SettingId.AdyenPayment:
            return new AdyenPaymentStep(context);

        case SettingId.PorschePayment: {
            if (!dealer.integrationDetails.assortment) {
                return null;
            }

            return new PorschePaymentStep(context);
        }

        case SettingId.FiservPayment:
            return new FiservPaymentStep(context);

        case SettingId.PayGatePayment:
            return new PayGatePaymentStep(context);

        case SettingId.TtbPayment:
            return new TtbPaymentStep(context);

        default:
            throw Error('Invalid Setting Type');
    }
};

// journey order
// drafting => applicant kyc => appointment for datepicker => guarantor kyc =>
// deposit => signing => SystemReceivalStep(Done)
const makeLegacyJourney = async (context: LegacyJourneyContext) => {
    const { application, journey: journeyState, includeWholeJourney, currentStages } = context;
    const isApplyNewSubmission = !isNil(journeyState?.applyNewSubmission) && context.isApplyNewSubmission;
    const isResubmit = !isNil(journeyState.submission) && !isApplyNewSubmission;
    const hasGuarantor = journeyState?.hasGuarantorCustomer;

    let journey = new DraftingStep(context)
        .append(new ApplicantAgreementsStep(context))
        .append(new ApplicantKYCStep(context));

    const hasAppointmentStep =
        hasAppointmentScenario(context.applicationModule.scenarios) &&
        !isNil(context.applicationModule.appointmentModuleId) &&
        context.application.configuration.testDrive;

    if (hasAppointmentStep) {
        journey = journey.append(new AppointmentStep(context));
    }

    if (hasGuarantor && (!journeyState.guarantorKYC?.completed || includeWholeJourney)) {
        journey = journey.append(new GuarantorAgreementsStep(context));
        journey = journey.append(new GuarantorKYCStep(context));
    }

    const hasPaymentStep =
        hasPaymentScenario(context.applicationModule.scenarios) &&
        (!journeyState.deposit?.completed || (includeWholeJourney && !isApplyNewSubmission)) &&
        (!journeyState.deposit?.skipped || (includeWholeJourney && !isApplyNewSubmission)) &&
        !isResubmit &&
        !isNil(getDealershipPaymentSettingId(application.dealerId, context.applicationModule));

    if (hasPaymentStep) {
        const depositStep = await getDepositStep(context);

        if (depositStep) {
            journey = journey.append(depositStep);
        }
    }

    if (application.configuration.withFinancing) {
        const signingStep = await getSigningStep(context, isResubmit);

        if (!isNil(signingStep) && signingStep.length > 0) {
            signingStep.forEach(step => {
                journey = journey.append(step);
            });
        }
    }

    if (application.configuration.withInsurance) {
        const insuranceSigningStep = await getInsuranceSigningStep(context);
        if (insuranceSigningStep !== null) {
            journey = journey.append(insuranceSigningStep);
        }
    }

    journey = journey.append(new SystemReceivalStep(context));

    return journey.first;
};

export default makeLegacyJourney;
