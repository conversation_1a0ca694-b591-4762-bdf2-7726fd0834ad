import { isNil, uniq, uniqWith } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    AdvancedVersioning,
    ApplicationJourneyKYCKind,
    AuthorKind,
    CitizenshipType,
    Customer,
    CustomerKind,
    GiftVoucherStatus,
    KYCPreset,
    KycFieldPurpose,
    LocalCustomerFieldKey,
    LocalCustomerManagementModuleKycField,
    getKYCFieldsFromPresets,
    getKYCPresetsForGiftVoucher,
} from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import {
    createNewCustomerVersion,
    sortLocalCustomerFields,
    mergeLocalCustomerFields,
} from '../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import { Authoring, getAuthorFromAuthoring } from '../../../utils/versioning';
import JourneyStep from '../../JourneyStep';
import { ApplicantKYCStepPayload } from '../../common';
import GiftVoucherJourneyContext from '../../giftVoucherJourney/GiftVoucherJourneyContext';

class ApplicantKYCStep extends JourneyStep<GiftVoucherJourneyContext> {
    constructor(context: GiftVoucherJourneyContext) {
        super(context, 'applicant-kyc');
    }

    get isFinalized(): boolean {
        return this.context.journey.applicantKYC?.completed || false;
    }

    public async finalize(): Promise<void> {
        return super.finalize();
    }

    async initialize(): Promise<void> {
        if (this.context.skipCompletedSteps) {
            if (this.isFinalized) {
                return this.next.initialize();
            }

            await this.context.updateGiftVoucherStatusFromUser(GiftVoucherStatus.ApplicantDetailsReceived);
        }

        return super.initialize();
    }

    static async validateInputFields(
        kycFieldSettings: LocalCustomerManagementModuleKycField[],
        kycPresets: KYCPreset[],
        newCustomer: Customer
    ): Promise<boolean> {
        const allFields = getKYCFieldsFromPresets(kycFieldSettings, kycPresets);
        const aggregatedFields = getLocalCustomerAggregatedFields(newCustomer);

        // extract LocalCustomerFieldKey which are mandatory
        const requiredFields: LocalCustomerFieldKey[] = uniq(
            allFields
                .filter(field => {
                    const common = field.isRequired === true && field.purpose.includes(KycFieldPurpose.KYC);

                    switch (field.key) {
                        case LocalCustomerFieldKey.IdentityNumber:
                            if (
                                aggregatedFields.citizenship === CitizenshipType.SingaporeanOrPr ||
                                aggregatedFields.citizenship === CitizenshipType.Malaysian
                            ) {
                                return common;
                            }

                            return false;

                        case LocalCustomerFieldKey.Passport:
                            if (aggregatedFields.citizenship === CitizenshipType.Others) {
                                return common;
                            }

                            return false;

                        // trade in vehicle validation will be done when store it in application
                        case LocalCustomerFieldKey.CurrentVehicleSource:
                        case LocalCustomerFieldKey.CurrentVehicleOwnership:
                        case LocalCustomerFieldKey.CurrentVehicleMake:
                        case LocalCustomerFieldKey.CurrentVehicleModel:
                        case LocalCustomerFieldKey.CurrentVehicleEquipmentLine:
                        case LocalCustomerFieldKey.CurrentVehicleModelYear:
                        case LocalCustomerFieldKey.CurrentVehiclePurchaseYear:
                        case LocalCustomerFieldKey.CurrentVehicleEngineType:
                        case LocalCustomerFieldKey.CurrentVehicleRegistrationNumber:
                        case LocalCustomerFieldKey.CurrentVehicleMileage:
                        case LocalCustomerFieldKey.CurrentVehicleContractEnd:
                        case LocalCustomerFieldKey.CurrentVehiclePotentialReplacement:
                        case LocalCustomerFieldKey.CurrentVehicleVin:
                            return false;

                        default:
                            return common;
                    }
                })
                .map(field => field.key)
        );

        return !requiredFields.some(key => isNil(aggregatedFields[key]));
    }

    async updateCustomer(payload: ApplicantKYCStepPayload): Promise<void> {
        const { collections } = await getDatabaseContext();

        const { entity, loaders, user, giftVoucherModule } = this.context;

        const { fields: payloadFields } = payload;

        const customer = await loaders.customerById.load(entity.purchaserId);

        // For gift voucher, customerModule retrieved from GiftVoucher entity
        const customerModule = await loaders.customerModuleById.load(giftVoucherModule.customerModuleId);

        const kycPresets = await getKYCPresetsForGiftVoucher(
            entity,
            payload.customerKind === CustomerKind.Corporate ? 'corporate' : 'local'
        );

        kycPresets.forEach(kyc => {
            if (kyc.fields.some(field => field.key === LocalCustomerFieldKey.UAEDrivingLicense)) {
                kyc.fields.push({
                    isRequired: true,
                    key: LocalCustomerFieldKey.Birthday,
                    purpose: [KycFieldPurpose.KYC],
                });
            }

            const citizenship = kyc.fields.some(field => field.key === LocalCustomerFieldKey.Citizenship);
            const identityNumber = kyc.fields.some(field => field.key === LocalCustomerFieldKey.IdentityNumber);

            if (citizenship) {
                kyc.fields.push({
                    isRequired: false,
                    key: LocalCustomerFieldKey.Passport,
                    purpose: [KycFieldPurpose.KYC],
                });

                if (!identityNumber) {
                    kyc.fields.push({
                        isRequired: false,
                        key: LocalCustomerFieldKey.IdentityNumber,
                        purpose: [KycFieldPurpose.KYC],
                    });
                }
            }
        });

        const giftVoucherKYCIds = kycPresets.map(kyc => kyc._id);
        const mergedKycPresetIds: ObjectId[] = [...customer.kycPresetIds, ...giftVoucherKYCIds];

        const newCustomerId = new ObjectId();

        // build up versioning
        const authoring: Authoring = user
            ? { kind: AuthorKind.User, userId: user._id }
            : { kind: AuthorKind.Customer, customerId: newCustomerId };

        const versioningUpdate: AdvancedVersioning = {
            ...customer._versioning,
            updatedBy: getAuthorFromAuthoring(authoring),
            updatedAt: new Date(),
            isLatest: true,
        };

        const sortedKycFields = customerModule.kycFields.sort((a, b) => a.order - b.order);
        // create updated customer document
        const newCustomer: Customer = {
            _id: newCustomerId,
            _kind: payload.customerKind === CustomerKind.Corporate ? CustomerKind.Corporate : CustomerKind.Local,
            moduleId: customer.moduleId,
            kycPresetIds: uniqWith((a, b) => a.equals(b), mergedKycPresetIds),
            fields: sortLocalCustomerFields(
                sortedKycFields,
                kycPresets,
                mergeLocalCustomerFields(customer.fields, payloadFields)
            ),
            isDeleted: false,
            _versioning: versioningUpdate,
        };

        const isValid = await ApplicantKYCStep.validateInputFields(sortedKycFields, kycPresets, newCustomer);

        if (!isValid) {
            throw new Error('Invalid Inputs');
        }

        await createNewCustomerVersion(newCustomer);

        await this.context.updateGiftVoucherPurchaser(newCustomer._id, authoring);

        await this.context.updateJourney({
            applicantKYC: {
                moduleId: newCustomer.moduleId,
                customerId: newCustomer._id,
                completedAt: newCustomer._versioning.updatedAt,
                completed: true,
                type:
                    payload.customerKind === CustomerKind.Corporate
                        ? ApplicationJourneyKYCKind.CorporateCustomer
                        : ApplicationJourneyKYCKind.LocalCustomer,
                kycIds: giftVoucherKYCIds,
            },
            isCorporateCustomer: payload.customerKind === CustomerKind.Corporate,
        });
    }

    async execute(payload: ApplicantKYCStepPayload): Promise<void> {
        await this.updateCustomer(payload);

        return this.finalize();
    }
}

export default ApplicantKYCStep;
