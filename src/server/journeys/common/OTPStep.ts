import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationJourneySigningMode,
    ApplicationKind,
    ApplicationModule,
    ApplicationOTPCompletedAuditTrail,
    ApplicationOTPInitiatedAuditTrail,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    LegacyApplication,
    ModuleType,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getBankIdFromApplication } from '../../database/helpers/applications';
import { getApplicationLogStages } from '../../utils/application';
import { Authoring, getAuthorFromAuthoring } from '../../utils/versioning';
import JourneyContext from '../JourneyContext';
import JourneyStep from '../JourneyStep';

export type OTPStepPayload = {
    ip?: string;
};

class OTPStep<TApplication extends LegacyApplication, TApplicationModule extends ApplicationModule> extends JourneyStep<
    JourneyContext<TApplication, TApplicationModule>,
    OTPStepPayload
> {
    constructor(context: JourneyContext<TApplication, TApplicationModule>) {
        super(context, 'finance-otp-signing');
    }

    get isFinalized(): boolean {
        return this.context.journey.applicantSigning?.completed || false;
    }

    async initialize(): Promise<void> {
        if (this.context.skipCompletedSteps) {
            if (this.isFinalized) {
                return this.finalize();
            }

            await this.context.updateApplicationStatusFromUser(ApplicationStatus.SigningPending);
        }

        const { collections } = await getDatabaseContext();
        const { application, loaders, user, journey } = this.context;

        const bank = await loaders.systemBankById.load(getBankIdFromApplication(application));

        const isResubmit = !isNil(journey.submission);
        const signingModuleId = isResubmit ? bank.reSubmissionApprovalModuleId : bank.submissionApprovalModuleId;

        const authoring: Authoring = user
            ? { kind: AuthorKind.User, userId: user._id }
            : { kind: AuthorKind.Customer, customerId: application.applicantId };

        // generate the trail
        const trail: ApplicationOTPInitiatedAuditTrail = {
            _id: new ObjectId(),
            _kind: AuditTrailKind.ApplicationOTPInitiated,
            _date: new Date(),
            applicationId: application._id,
            applicationSuiteId: application._versioning.suiteId,
            stages: getApplicationLogStages(application, AuditTrailKind.ApplicationOTPInitiated),
            signingModuleId,
            author: getAuthorFromAuthoring(authoring),
            customerKind: 'applicant',
        };

        // register the trail
        await collections.auditTrails.insertOne(trail);

        return super.initialize();
    }

    async execute(payload: OTPStepPayload): Promise<void> {
        if (this.isFinalized) {
            return Promise.resolve();
        }

        if (
            (this.context.applicationModule._type === ModuleType.ConfiguratorModule &&
                this.context.applicationModule.isInventoryEnabled) ||
            this.context.applicationModule._type === ModuleType.MobilityModule
        ) {
            await this.context.getApplicationInventory();
        }

        const { collections } = await getDatabaseContext();
        const { application, loaders, user, journey } = this.context;

        const authoring: Authoring = user
            ? { kind: AuthorKind.User, userId: user._id }
            : { kind: AuthorKind.Customer, customerId: application.applicantId };

        const bank = await loaders.systemBankById.load(getBankIdFromApplication(application));

        const isResubmit = !isNil(journey.submission);
        const signingModuleId = isResubmit ? bank.reSubmissionApprovalModuleId : bank.submissionApprovalModuleId;

        // Check also if insurance application has the same signing
        const insurerId =
            (application.kind === ApplicationKind.Standard ||
                application.kind === ApplicationKind.Configurator ||
                application.kind === ApplicationKind.Finder) &&
            application.insurancing?.insurerId;

        const insurer = insurerId ? await loaders.insurerById.load(insurerId) : undefined;
        const isResubmitInsurance = !isNil(journey.insuranceSubmission);
        const insuranceSigningModuleId = isResubmitInsurance
            ? insurer?.reSubmissionApprovalModuleId
            : insurer?.submissionApprovalModuleId;

        if (!journey.applicantSigning?.completed) {
            const completedAt = new Date();

            await this.context.updateJourney({
                applicantSigning: {
                    moduleId: signingModuleId,
                    completedAt,
                    completed: true,
                    kind: ApplicationJourneySigningMode.OTP,
                },

                ...(insuranceSigningModuleId?.equals(signingModuleId) && {
                    insuranceApplicantSigning: {
                        ...journey.insuranceApplicantSigning,
                        moduleId: insuranceSigningModuleId,
                        kind: ApplicationJourneySigningMode.OTP,
                        completedAt,
                        completed: true,
                    },
                }),
            });

            await this.context.updateApplicationStatus(ApplicationStatus.OTPCompleted, authoring);

            // generate the trail
            const trail: ApplicationOTPCompletedAuditTrail = {
                _id: new ObjectId(),
                _kind: AuditTrailKind.ApplicationOTPCompleted,
                _date: completedAt,
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                stages: getApplicationLogStages(application, AuditTrailKind.ApplicationOTPCompleted),
                signingModuleId,
                author: getAuthorFromAuthoring(authoring),
                customerKind: 'applicant',
                ipAddress: payload.ip,
            };

            // register the trail
            await collections.auditTrails.insertOne(trail);
        }

        return this.finalize();
    }
}
export default OTPStep;
