import { uniqWith } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    AdvancedVersioning,
    ApplicationJourneyKYCKind,
    ApplicationModule,
    ApplicationStatus,
    ApplicationTestDriveKYCReceivedAuditTrail,
    AuditTrailKind,
    AuthorKind,
    ConsentsAndDeclarationsPurpose,
    CustomerKind,
    KycFieldPurpose,
    TestDriveApplication,
    LocalCustomerField,
    LocalCustomerFieldKey,
    Customer,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    getKYCPresetsForApplication,
    hasTestDriveProcessCondition,
    hasTestDriveProcessOrTestDriveCondition,
} from '../../database/helpers';
import {
    createNewCustomerVersion,
    sortLocalCustomerFields,
    mergeLocalCustomerFields,
} from '../../database/helpers/customers';
import { getApplicationLogStages } from '../../utils/application';
import { Authoring, getAuthorFromAuthoring } from '../../utils/versioning';
import JourneyContext from '../JourneyContext';
import JourneyStep from '../JourneyStep';
import ApplicantAgreementsStep from './ApplicantAgreementsStep';

export type TestDriveKYCStepPayload = {
    fields: LocalCustomerField[];
    customerKind: CustomerKind;
};

class TestDriveKYCStep<
    TApplication extends TestDriveApplication,
    TApplicationModule extends ApplicationModule,
> extends JourneyStep<JourneyContext<TApplication, TApplicationModule>, TestDriveKYCStepPayload> {
    constructor(context: JourneyContext<TApplication, TApplicationModule>) {
        super(context, 'test-drive-kyc');
    }

    get isFinalized(): boolean {
        return this.context.journey.testDriveKYC?.completed || false;
    }

    async initialize(): Promise<void> {
        if (this.context.skipCompletedSteps) {
            if (this.isFinalized) {
                return this.next.initialize();
            }

            await this.context.updateApplicationStatusFromUser(ApplicationStatus.TestDriveDetailsReceived);
        }

        return super.initialize();
    }

    async finalize(): Promise<void> {
        const { journey } = this.context;

        await this.context.updateLeadCustomer(journey.testDriveKYC.customerId, 'applicant');

        // synchronize applicant id on application with customer id on journey as finale
        await this.context.updateApplicationCustomer(journey.testDriveKYC.customerId, 'applicant');

        return this.next.initialize();
    }

    async finalizeDraft(): Promise<void> {
        return this.next.initialize();
    }

    async updateCustomer(payload: TestDriveKYCStepPayload): Promise<void> {
        // get initial customer
        const { collections } = await getDatabaseContext();
        const { application, loaders, journey, user } = this.context;
        const { fields: payloadFields } = payload;
        const customer = await loaders.customerById.load(application.applicantId);

        const applicantKYCPresets = await getKYCPresetsForApplication(application, 'local');
        const customerModule = await loaders.customerModuleById.load(customer.moduleId);

        // launchpad module uses both test drive and test drive process
        const hasConditionsFilter =
            application.kind === 'launchpad' ? hasTestDriveProcessOrTestDriveCondition : hasTestDriveProcessCondition;

        const testDriveKYCPresets = applicantKYCPresets.filter(kyc => hasConditionsFilter(kyc.conditions));

        const kycPresets = [...applicantKYCPresets, ...testDriveKYCPresets];
        const kycPresetsIds = kycPresets.map(kycPreset => kycPreset._id);

        kycPresets.forEach(kyc => {
            if (kyc.fields.some(field => field.key === LocalCustomerFieldKey.UAEDrivingLicense)) {
                kyc.fields.push({
                    isRequired: true,
                    key: LocalCustomerFieldKey.Birthday,
                    purpose: [KycFieldPurpose.KYC],
                });
            }

            const citizenship = kyc.fields.some(field => field.key === LocalCustomerFieldKey.Citizenship);
            const identityNumber = kyc.fields.some(field => field.key === LocalCustomerFieldKey.IdentityNumber);

            if (citizenship) {
                kyc.fields.push({
                    isRequired: false,
                    key: LocalCustomerFieldKey.Passport,
                    purpose: [KycFieldPurpose.KYC],
                });

                if (!identityNumber) {
                    kyc.fields.push({
                        isRequired: false,
                        key: LocalCustomerFieldKey.IdentityNumber,
                        purpose: [KycFieldPurpose.KYC],
                    });
                }
            }
        });

        const testDriveKycIds = kycPresets.map(kyc => kyc._id);

        const newCustomerId = new ObjectId();

        // build up versioning
        const authoring: Authoring = user
            ? { kind: AuthorKind.User, userId: user._id }
            : { kind: AuthorKind.Customer, customerId: newCustomerId };

        const versioningUpdate: AdvancedVersioning = {
            ...customer._versioning,
            updatedBy: getAuthorFromAuthoring(authoring),
            updatedAt: new Date(),
            isLatest: true,
        };

        const newCustomer: Customer = {
            ...customer,
            _id: newCustomerId,
            _kind: payload.customerKind === CustomerKind.Corporate ? CustomerKind.Corporate : CustomerKind.Local,
            kycPresetIds: uniqWith((a, b) => a.equals(b), kycPresetsIds),
            fields: sortLocalCustomerFields(
                customerModule.kycFields.sort((a, b) => a.order - b.order),
                kycPresets,
                mergeLocalCustomerFields(customer.fields, payloadFields)
            ),
            _versioning: versioningUpdate,
        };

        await createNewCustomerVersion(newCustomer);

        await this.context.updateJourney({
            testDriveKYC: {
                moduleId: newCustomer.moduleId,
                customerId: newCustomer._id,
                completedAt: new Date(),
                completed: true,
                type: ApplicationJourneyKYCKind.LocalCustomer,
                kycIds: testDriveKycIds,
            },
            hasTestDriveKYCConsentStep: false,
        });

        if (
            !ApplicantAgreementsStep.checkAgreementsCompletion(this.context.journey.testDriveAgreements, [
                ConsentsAndDeclarationsPurpose.KYC,
            ])
        ) {
            // we are still pending for agreements
            return;
        }

        if (!journey.testDriveKYC?.completed) {
            await this.context.updateApplicationStatus(ApplicationStatus.TestDriveDetailsReceived, authoring);

            // generate the trail
            const trail: ApplicationTestDriveKYCReceivedAuditTrail = {
                _id: new ObjectId(),
                _kind: AuditTrailKind.ApplicationTestDriveKYCAuditTrail,
                _date: customer._versioning.createdAt,
                customerId: newCustomer._id,
                customerSuiteID: newCustomer._versioning.suiteId,
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                stages: getApplicationLogStages(application, AuditTrailKind.ApplicationTestDriveKYCAuditTrail),
                author: getAuthorFromAuthoring(authoring),
            };

            // register the trail
            await collections.auditTrails.insertOne(trail);
        }
    }

    async execute(payload: TestDriveKYCStepPayload): Promise<void> {
        await this.updateCustomer(payload);

        return this.finalize();
    }
}

export default TestDriveKYCStep;
