import { isNil, uniq, uniqWith, xor } from 'lodash/fp';
import { ObjectId } from 'mongodb';

import {
    Application,
    ApplicationJourneyKYCKind,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    LegacyApplication,
} from '../../database/documents/Applications';
import {
    ApplicationKYCReceivedAuditTrail,
    AuditTrailKind,
    LeadKYCReceivedAuditTrail,
} from '../../database/documents/AuditTrail';
import { ConsentsAndDeclarationsPurpose } from '../../database/documents/ConsentsAndDeclarations';
import { Customer, CustomerKind, LocalCustomerField, LocalCustomerFieldKey } from '../../database/documents/Customer';
import { KycFieldPurpose, KYCPreset } from '../../database/documents/KYCPresets';
import { CapValues, LeadStatus } from '../../database/documents/Lead';
import { AdvancedVersioning, AuthorKind } from '../../database/documents/Versioning';
import {
    ApplicationModule,
    ConfiguratorModule,
    FinderApplicationModule,
    LocalCustomerManagementModuleKycField,
    ModuleType,
} from '../../database/documents/modules';
import { CitizenshipType } from '../../database/documents/shared';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getKYCFieldsFromPresets, getKYCPresetsForApplication } from '../../database/helpers';
import {
    createNewCustomerVersion,
    sortLocalCustomerFields,
    mergeLocalCustomerFields,
} from '../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../database/helpers/customers/shared';
import { getPreviousApplicationStages } from '../../database/queries/application';
import { isApplicationEndpointPrivate } from '../../integrations/cap/utils';
import { getApplicationLogStages } from '../../utils/application';
import { Authoring, getAuthorFromAuthoring } from '../../utils/versioning';
import JourneyContext from '../JourneyContext';
import JourneyStep from '../JourneyStep';
import ApplicantAgreementsStep from './ApplicantAgreementsStep';
import { hasAppointmentStage, hasLeadCaptureStage, validateApplicationStagesForStockDeduction } from './helpers';

export type ApplicantKYCStepPayload = {
    fields: LocalCustomerField[];
    saveDraft: boolean;
    customerKind: CustomerKind;
    capValues?: CapValues;
    customerCiamId?: string;
};

const getApplicationConfiguration = (application: Application) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Event:
        case ApplicationKind.Finder:
            return application.configuration;

        default:
            return undefined;
    }
};
/**
 * validating scenario is for test drive only
 * @param scenarios ApplicationScenarios
 * @returns boolean
 */
export const validateApplicationIncludeLeadAndAppointmentStage = (stages: ApplicationStage[]) => {
    if (stages.length === 1) {
        return hasLeadCaptureStage(stages) || hasAppointmentStage(stages);
    }

    if (stages.length === 2) {
        return hasLeadCaptureStage(stages) && hasAppointmentStage(stages);
    }

    return false;
};

class ApplicantKYCStep<
    TApplication extends LegacyApplication,
    TApplicationModule extends ApplicationModule,
> extends JourneyStep<JourneyContext<TApplication, TApplicationModule>, ApplicantKYCStepPayload> {
    constructor(context: JourneyContext<TApplication, TApplicationModule>) {
        super(context, 'applicant-kyc');
    }

    get isFinalized(): boolean {
        return this.context.journey.applicantKYC?.completed || false;
    }

    async initialize(): Promise<void> {
        if (this.context.skipCompletedSteps) {
            if (this.isFinalized) {
                return this.next.initialize();
            }

            await this.context.updateApplicationStatusFromUser(ApplicationStatus.ApplicantDetailsReceived);
        }

        return super.initialize();
    }

    async finalize(): Promise<void> {
        await this.context.assignApplicationIdentifier();

        const { journey } = this.context;

        await this.context.updateLeadCustomer(journey.applicantKYC.customerId, 'applicant');

        // synchronize applicant id on application with customer id on journey as finale
        await this.context.updateApplicationCustomer(journey.applicantKYC.customerId, 'applicant');

        return super.finalize();
    }

    // Finalize when draft customer data
    async finalizeDraft(): Promise<void> {
        await this.context.assignApplicationIdentifier();

        const { journey } = this.context;

        await this.context.updateLeadCustomer(journey.applicantKYC.customerId, 'applicant');

        // synchronize applicant id on application with customer id on journey as finale
        return this.context.updateApplicationCustomer(journey.applicantKYC.customerId, 'applicant');
    }

    static async validateInputFields(
        kycFieldSettings: LocalCustomerManagementModuleKycField[],
        kycPresets: KYCPreset[],
        newCustomer: Customer,
        application: Application
    ): Promise<boolean> {
        // extract fields from kycPresets
        const allFields = getKYCFieldsFromPresets(kycFieldSettings, kycPresets);

        const configuration = getApplicationConfiguration(application);
        // get aggreatedFields
        const aggreatedFields = getLocalCustomerAggregatedFields(newCustomer);
        const isTestDriveExplicitlyDisabled = configuration?.testDrive === false;

        // extract LocalCustomerFieldKey which are mandatory
        const requiredFields: LocalCustomerFieldKey[] = uniq(
            allFields
                .filter(field => {
                    const common = field.isRequired === true && field.purpose.includes(KycFieldPurpose.KYC);

                    switch (field.key) {
                        case LocalCustomerFieldKey.DrivingLicense:
                        case LocalCustomerFieldKey.DrivingLicenseTh:
                        case LocalCustomerFieldKey.DrivingLicenseMy:
                            return common && !isTestDriveExplicitlyDisabled;

                        case LocalCustomerFieldKey.IdentityNumber:
                            if (
                                aggreatedFields.citizenship === CitizenshipType.SingaporeanOrPr ||
                                aggreatedFields.citizenship === CitizenshipType.Malaysian
                            ) {
                                return common;
                            }

                            return false;

                        case LocalCustomerFieldKey.Passport:
                            if (aggreatedFields.citizenship === CitizenshipType.Others) {
                                return common;
                            }

                            return false;

                        // trade in vehicle validation will be done when store it in application
                        case LocalCustomerFieldKey.CurrentVehicleSource:
                        case LocalCustomerFieldKey.CurrentVehicleOwnership:
                        case LocalCustomerFieldKey.CurrentVehicleMake:
                        case LocalCustomerFieldKey.CurrentVehicleModel:
                        case LocalCustomerFieldKey.CurrentVehicleEquipmentLine:
                        case LocalCustomerFieldKey.CurrentVehicleModelYear:
                        case LocalCustomerFieldKey.CurrentVehiclePurchaseYear:
                        case LocalCustomerFieldKey.CurrentVehicleEngineType:
                        case LocalCustomerFieldKey.CurrentVehicleRegistrationNumber:
                        case LocalCustomerFieldKey.CurrentVehicleMileage:
                        case LocalCustomerFieldKey.CurrentVehicleContractEnd:
                        case LocalCustomerFieldKey.CurrentVehiclePotentialReplacement:
                        case LocalCustomerFieldKey.CurrentVehicleVin:
                            return false;

                        default:
                            return common;
                    }
                })
                .map(field => field.key)
        );

        return !requiredFields.some(key => isNil(aggreatedFields[key]));
    }

    async updateCustomer(payload: ApplicantKYCStepPayload): Promise<void> {
        // get initial customer
        const { collections } = await getDatabaseContext();
        const { application, lead, loaders, journey, user } = this.context;
        const { fields: payloadFields, saveDraft, customerCiamId } = payload;

        const applicationHasPorscheIDIntegrated = async () => {
            switch (application.kind) {
                case ApplicationKind.Configurator: {
                    const configuratorModule = (await loaders.moduleById.load(
                        application.moduleId
                    )) as ConfiguratorModule;

                    return (
                        configuratorModule.porscheIdModuleId && configuratorModule.isCustomerDataRetreivalByPorscheId
                    );
                }

                case ApplicationKind.Finder: {
                    const finderApplicationModule = (await loaders.moduleById.load(
                        application.moduleId
                    )) as FinderApplicationModule;

                    return (
                        finderApplicationModule._type === ModuleType.FinderApplicationPublicModule &&
                        finderApplicationModule.porscheIdModuleId &&
                        finderApplicationModule.isCustomerDataRetreivalByPorscheId
                    );
                }

                case ApplicationKind.Event: {
                    const eventDetails = await loaders.eventById.load(application.eventId);

                    return eventDetails.porscheIdModuleId && eventDetails.isCustomerDataRetreivalByPorscheId;
                }

                default:
                    return false;
            }
        };
        const isApplicationHasPorscheIDIntegrated = await applicationHasPorscheIDIntegrated();

        const resolveCustomer = async (): Promise<Customer> => {
            const applicantCustomer = await loaders.customerById.load(application.applicantId);
            if (applicantCustomer.customerCiamId) {
                return applicantCustomer;
            }
            if (isApplicationHasPorscheIDIntegrated && customerCiamId) {
                const ciamCustomer = await loaders.customerByCiamId.load({
                    customerCiamId,
                    customerModuleId: applicantCustomer.moduleId,
                });
                if (ciamCustomer) {
                    return ciamCustomer;
                }
            }

            return applicantCustomer;
        };
        const resolvedCustomer = await resolveCustomer();
        const customerWithCiamIdExist = !!resolvedCustomer.customerCiamId;
        const customerModule = await loaders.customerModuleById.load(resolvedCustomer.moduleId);
        const kycPresets = await getKYCPresetsForApplication(
            application,
            payload.customerKind === CustomerKind.Corporate ? 'corporate' : 'local'
        );

        kycPresets.forEach(kyc => {
            if (kyc.fields.some(field => field.key === LocalCustomerFieldKey.UAEDrivingLicense)) {
                kyc.fields.push({
                    isRequired: true,
                    key: LocalCustomerFieldKey.Birthday,
                    purpose: [KycFieldPurpose.KYC],
                });
            }

            const citizenship = kyc.fields.some(field => field.key === LocalCustomerFieldKey.Citizenship);
            const identityNumber = kyc.fields.some(field => field.key === LocalCustomerFieldKey.IdentityNumber);

            if (citizenship) {
                kyc.fields.push({
                    isRequired: false,
                    key: LocalCustomerFieldKey.Passport,
                    purpose: [KycFieldPurpose.KYC],
                });

                if (!identityNumber) {
                    kyc.fields.push({
                        isRequired: false,
                        key: LocalCustomerFieldKey.IdentityNumber,
                        purpose: [KycFieldPurpose.KYC],
                    });
                }
            }
        });

        const applicationKycIds = kycPresets.map(kyc => kyc._id);
        const mergedKycPresetIds: ObjectId[] = [...resolvedCustomer.kycPresetIds, ...applicationKycIds];

        const newCustomerId = new ObjectId();

        // build up versioning
        const authoring: Authoring = user
            ? { kind: AuthorKind.User, userId: user._id }
            : { kind: AuthorKind.Customer, customerId: newCustomerId };

        const versioningUpdate: AdvancedVersioning = {
            ...resolvedCustomer._versioning,
            updatedBy: getAuthorFromAuthoring(authoring),
            updatedAt: new Date(),
            isLatest: true,
        };

        const customerCiamIdValue = isApplicationHasPorscheIDIntegrated ? customerCiamId : undefined;

        // create updated customer document
        const newCustomer: Customer = {
            _id: newCustomerId,
            _kind: payload.customerKind === CustomerKind.Corporate ? CustomerKind.Corporate : CustomerKind.Local,
            moduleId: resolvedCustomer.moduleId,
            kycPresetIds: uniqWith((a, b) => a.equals(b), mergedKycPresetIds),
            fields: sortLocalCustomerFields(
                customerModule.kycFields.sort((a, b) => a.order - b.order),
                kycPresets,
                customerWithCiamIdExist
                    ? payloadFields
                    : mergeLocalCustomerFields(resolvedCustomer.fields, payloadFields)
            ),
            isDeleted: false,
            customerCiamId: payload.customerKind === CustomerKind.Local ? customerCiamIdValue : undefined,
            _versioning: versioningUpdate,
        };

        if (!saveDraft) {
            // validate mandatory fields
            const isValid = await ApplicantKYCStep.validateInputFields(
                customerModule.kycFields.sort((a, b) => a.order - b.order),
                kycPresets,
                newCustomer,
                application
            );

            if (!isValid) {
                throw new Error('Invalid Inputs');
            }
        }

        // IF lead of this application is coming from launchpad, then need to keep the existing value from launchpad
        if (
            (application.kind === ApplicationKind.Standard || application.kind === ApplicationKind.Finder) &&
            lead.kind === ApplicationKind.Launchpad
        ) {
            const applicantDataFromLead = await loaders.customerById.load(lead.customerId);
            const mergedKycPresetIdsWithLeadApplicant = uniqWith(
                (a, b) => a.equals(b),
                [...newCustomer.kycPresetIds, ...applicantDataFromLead.kycPresetIds]
            );

            newCustomer.kycPresetIds = mergedKycPresetIdsWithLeadApplicant;

            const mergedFieldsWithLeadApplicant = uniqWith(
                (a, b) => a.key === b.key,
                [...newCustomer.fields, ...applicantDataFromLead.fields]
            );

            newCustomer.fields = mergedFieldsWithLeadApplicant;
        }

        await createNewCustomerVersion(newCustomer);

        await this.context.updateJourney({
            applicantKYC: {
                moduleId: newCustomer.moduleId,
                customerId: newCustomer._id,
                completedAt: newCustomer._versioning.updatedAt,
                completed: !saveDraft,
                type:
                    payload.customerKind === CustomerKind.Corporate
                        ? ApplicationJourneyKYCKind.CorporateCustomer
                        : ApplicationJourneyKYCKind.LocalCustomer,
                kycIds: applicationKycIds,
            },
            isCorporateCustomer: payload.customerKind === CustomerKind.Corporate,
        });

        if (
            !ApplicantAgreementsStep.checkAgreementsCompletion(this.context.journey.applicantAgreements, [
                ConsentsAndDeclarationsPurpose.KYC,
            ])
        ) {
            // we are still pending for agreements
            return;
        }

        if (!journey.applicantKYC?.completed) {
            // prepare proper stage

            const currentStage = xor(
                await getPreviousApplicationStages(this.context.application._versioning.suiteId),
                this.context.application.stages
            );

            if (!saveDraft) {
                await this.context.updateApplicationStatus(
                    ApplicationStatus.ApplicantDetailsReceived,
                    authoring,
                    currentStage
                );
            }

            const kind = AuditTrailKind.ApplicationKYCReceived;
            // generate the trail
            const applicationTrail: ApplicationKYCReceivedAuditTrail = {
                _id: new ObjectId(),
                _kind: kind,
                _date: newCustomer._versioning.updatedAt,
                customerId: newCustomer._id,
                customerSuiteID: newCustomer._versioning.suiteId,
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                stages: getApplicationLogStages(application, kind, currentStage),
                author: getAuthorFromAuthoring(authoring),
            };

            const leadTrail: LeadKYCReceivedAuditTrail = {
                _id: new ObjectId(),
                _kind: AuditTrailKind.LeadKYCReceived,
                _date: newCustomer._versioning.updatedAt,
                customerId: newCustomer._id,
                customerSuiteID: newCustomer._versioning.suiteId,
                leadId: this.context.lead._id,
                leadSuiteId: this.context.lead._versioning.suiteId,
                author: getAuthorFromAuthoring(authoring),
            };

            // register the trails
            await this.context.createAuditTrails([applicationTrail, leadTrail]);
        }
    }

    async updateApplicationCapValues(payload: ApplicantKYCStepPayload): Promise<void> {
        const { application, lead } = this.context;

        if (!payload.capValues && !payload.saveDraft && !lead.isLead) {
            await this.context.updateLead({ status: LeadStatus.PendingQualify });

            return;
        }
        const { collections } = await getDatabaseContext();

        const applicationData = await collections.applications.findOne({ _id: application._id });
        if (applicationData.kind === ApplicationKind.Mobility) {
            return;
        }

        if (applicationData.kind === ApplicationKind.SalesOffer) {
            return;
        }

        const isEndpointPrivate = await isApplicationEndpointPrivate(applicationData);

        if (isEndpointPrivate) {
            const updatedCapValues = {
                ...lead.capValues,
                ...payload.capValues,
            };

            await this.context.updateLead({ capValues: updatedCapValues });
        }
    }

    async execute(payload: ApplicantKYCStepPayload): Promise<void> {
        const { currentStages } = this.context;

        if (
            [ModuleType.ConfiguratorModule, ModuleType.MobilityModule].includes(
                this.context.getApplicationContextType()
            )
        ) {
            // check the inventory before undergoes KYC step
            // Otherwise the application has KYC details but no stock to assign
            await this.context.getVariantStockInventory();
        }
        await this.updateCustomer(payload);
        await this.updateApplicationCapValues(payload);
        await this.context.updateIsDraft();

        if (!this.context.isApplyNewSubmission) {
            await this.context.assignLeadIdentifier();
        }

        if (
            !validateApplicationIncludeLeadAndAppointmentStage(currentStages) &&
            validateApplicationStagesForStockDeduction(
                this.context.applicationModule,
                this.context.application,
                !this.context.isApplyNewSubmission,
                this.context.journey,
                this.context.previousApplicationStages
            )
        ) {
            await this.context.updateInventoryStock(payload.saveDraft);
        }

        if (payload.saveDraft) {
            return this.finalizeDraft();
        }

        return this.finalize();
    }
}

export default ApplicantKYCStep;
