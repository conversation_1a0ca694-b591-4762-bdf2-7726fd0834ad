import Dayjs from 'dayjs';
import { isNil, omit } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import config from '../../core/config';
import {
    ApplicationJourneySigning,
    ApplicationJourneySigningMode,
    ApplicationModule,
    ApplicationSigningCompletedAuditTrail,
    ApplicationSigningInitiatedAuditTrail,
    ApplicationSigningStatus,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    ExternalLinkKind,
    LegacyApplication,
    NamirialSigningLink,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getBankIdFromApplication } from '../../database/helpers/applications';
import { mainQueue } from '../../queues/mainQueue';
import { getApplicationLogStages } from '../../utils/application';
import { Authoring, getAuthorFromAuthoring } from '../../utils/versioning';
import JourneyContext from '../JourneyContext';
import JourneyStep from '../JourneyStep';
import getSigningModeByModule from '../helper/getSigningModeByModule';

export type GuarantorNamirialPayload = null;

class GuarantorNamirialStep<
    TApplication extends LegacyApplication,
    TApplicationModule extends ApplicationModule,
> extends JourneyStep<JourneyContext<TApplication, TApplicationModule>, GuarantorNamirialPayload> {
    constructor(context: JourneyContext<TApplication, TApplicationModule>) {
        super(context, 'guarantor-namirial-signing');
    }

    get isFinalized(): boolean {
        return this.context.journey.guarantorSigning?.completed || false;
    }

    async initialize(): Promise<void> {
        if (this.context.skipCompletedSteps) {
            if (this.isFinalized) {
                return this.finalize();
            }

            await this.context.updateApplicationStatusFromUser(ApplicationStatus.GuarantorSigningPending);
        }

        // do not call again if it has been initiated already
        if (this.context.application.financingStage?.status !== ApplicationStatus.GuarantorSigningInitiated) {
            return this.initializeNamirialSigning();
        }

        return Promise.resolve();
    }

    private async initializeNamirialSigning(): Promise<void> {
        const { collections } = await getDatabaseContext();

        const { user, application, origin, loaders, journey } = this.context;
        const { _id, routerId, endpointId } = application;

        // build a dynamic callback
        const link: NamirialSigningLink = {
            _id: new ObjectId(),
            _kind: ExternalLinkKind.NamirialSigning,
            secret: nanoid(),
            data: { applicationId: _id, routerId, endpointId, origin },
            expiresAt: Dayjs().add(1, 'day').toDate(),
            deleteOnFetch: false,
        };

        await collections.externalLinks.insertOne(link);

        const router = await loaders.routerById.load(application.routerId);

        const clientAction = `${config.protocol}://${router.hostname}${router.pathname}/l/${link.secret}`;

        const bank = await loaders.systemBankById.load(getBankIdFromApplication(application));

        const isResubmit = !isNil(journey.submission);
        const signingModuleId = isResubmit ? bank.reSubmissionApprovalModuleId : bank.submissionApprovalModuleId;
        const signingModule = await collections.modules.findOne({ _id: signingModuleId });
        const signingKind = getSigningModeByModule(signingModule);
        await this.context.updateJourney({
            guarantorSigning: {
                ...(omit(['envelopeId', 'redirectionUrl'], journey.guarantorSigning) as ApplicationJourneySigning),
                status: ApplicationSigningStatus.Initialized,
                moduleId: signingModuleId,
                kind: signingKind,
            },
        });

        await collections.applications.findOneAndUpdate(
            { _id: application._id },
            { $set: { status: ApplicationStatus.GuarantorSigningInitiated } }
        );

        // build up authoring
        const authoring: Authoring = user
            ? { kind: AuthorKind.User, userId: user._id }
            : { kind: AuthorKind.Customer, customerId: application.applicantId };

        const kind = AuditTrailKind.ApplicationSigningInitiated;
        const trail: ApplicationSigningInitiatedAuditTrail = {
            _id: new ObjectId(),
            _date: new Date(),
            _kind: kind,
            applicationId: application._id,
            applicationSuiteId: application._versioning.suiteId,
            signingModuleId,
            customerKind: 'guarantor',
            author: getAuthorFromAuthoring(authoring),
            stages: getApplicationLogStages(
                application,
                kind,
                this.context.isApplyNewSubmission
                    ? [
                          this.context.isApplyNewSubmissionForFinancing && ApplicationStage.Financing,
                          this.context.isApplyNewSubmissionForInsurance && ApplicationStage.Insurance,
                      ].filter(Boolean)
                    : null
            ),
        };

        await collections.auditTrails.insertOne(trail);

        await mainQueue.add({
            type: 'onSigningInitialized',
            clientAction,
            applicationId: application._id,
            signerType: 'guarantor',
        });
    }

    async updateSigning(): Promise<void> {
        const { application, journey, user, loaders } = this.context;
        const { collections } = await getDatabaseContext();

        if (
            journey.guarantorSigning.kind !== ApplicationJourneySigningMode.Namirial &&
            journey.guarantorSigning.kind !== ApplicationJourneySigningMode.Docusign
        ) {
            throw new Error('Invalid Applicant Signing kind');
        }

        if (!journey.guarantorSigning?.completed) {
            // build up authoring
            const authoring: Authoring = user
                ? { kind: AuthorKind.User, userId: user._id }
                : { kind: AuthorKind.Customer, customerId: application.applicantId };

            const bank = await loaders.systemBankById.load(getBankIdFromApplication(application));

            const isResubmit = !isNil(journey.submission);
            const signingModuleId = isResubmit ? bank.reSubmissionApprovalModuleId : bank.submissionApprovalModuleId;
            const completedAt = new Date();

            await this.context.updateJourney({
                guarantorSigning: {
                    ...journey.guarantorSigning,
                    status: ApplicationSigningStatus.Completed,
                    completed: true,
                    completedAt,
                },
            });

            await this.context.updateApplicationStatus(ApplicationStatus.SigningCompleted, authoring);

            const kind = AuditTrailKind.ApplicationSigningCompleted;
            const trail: ApplicationSigningCompletedAuditTrail = {
                _id: new ObjectId(),
                _date: completedAt,
                _kind: kind,
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                signingModuleId,
                customerKind: 'guarantor',
                author: getAuthorFromAuthoring(authoring),
                stages: getApplicationLogStages(
                    application,
                    kind,
                    this.context.isApplyNewSubmission
                        ? [
                              this.context.isApplyNewSubmissionForFinancing && ApplicationStage.Financing,
                              this.context.isApplyNewSubmissionForInsurance && ApplicationStage.Insurance,
                          ].filter(Boolean)
                        : null
                ),
            };

            await collections.auditTrails.insertOne(trail);
        }
    }

    async execute(payload: GuarantorNamirialPayload): Promise<void> {
        if (this.isFinalized) {
            return Promise.resolve();
        }

        await this.updateSigning();

        return this.finalize();
    }
}
export default GuarantorNamirialStep;
