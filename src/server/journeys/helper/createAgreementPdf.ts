import { uploadFile } from '../../core/storage';
import {
    Application,
    ApplicationKind,
    ApplicationDocument,
    ApplicationDocumentKind,
    BucketType,
    UploadedFile,
    ApplicationStage,
    Lead,
} from '../../database';
import { getApplicationIdentifier } from '../../utils/application';

const getAgreementsCount = (
    application: Application,
    kind: ApplicationDocumentKind = ApplicationDocumentKind.Agreement
) => {
    switch (application.kind) {
        case ApplicationKind.Event:
        case ApplicationKind.Configurator:
        case ApplicationKind.Standard:
        case ApplicationKind.Finder:
        case ApplicationKind.Mobility:
        case ApplicationKind.Launchpad:
        case ApplicationKind.SalesOffer:
            return application.documents.filter(document => document.kind === kind).length;

        default:
            throw new Error('Application not supported for agreement count');
    }
};

export const uploadAgreementPdf = (
    pdf: Buffer,
    application: Application,
    lead: Lead,
    kind: ApplicationDocumentKind = ApplicationDocumentKind.Agreement
): Promise<UploadedFile> => {
    const count = getAgreementsCount(application, kind);
    const identifier = getApplicationIdentifier(application, lead, [
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Mobility,
        ApplicationStage.Appointment,
    ]);
    const fileName = `applicationAgreement-${identifier}-${count}.pdf`;
    const dirName = `application/agreements/`;

    // upload file to S3
    return uploadFile(BucketType.Private, dirName, fileName, pdf);
};

const createAgreementPdf = async (
    pdf: Buffer,
    application: Application,
    lead: Lead,
    kind: ApplicationDocumentKind = ApplicationDocumentKind.Agreement
): Promise<ApplicationDocument> => {
    const uploadedFile = await uploadAgreementPdf(pdf, application, lead, kind);

    return {
        ...uploadedFile,
        kind,
        applicationId: application._id,
    };
};

export default createAgreementPdf;
