import { uploadFile } from '../../core/storage';
import {
    Application,
    ApplicationDocument,
    ApplicationDocumentKind,
    ApplicationStage,
    BucketType,
    Lead,
    UploadedFile,
} from '../../database';
import { getApplicationIdentifier } from '../../utils/application';
import { getAllInsuranceAgreementPdf } from './getInsuranceAgreementPdf';

export const createInsurancePdf = (pdf: Buffer, application: Application, lead: Lead) => {
    const currentAgreements = getAllInsuranceAgreementPdf(application);

    const identifier = getApplicationIdentifier(application, lead, [ApplicationStage.Insurance]);

    const fileName = `applicationInsuranceAgreement-${identifier}-${currentAgreements.length}.pdf`;
    const dirName = `application/insuranceAgreements/`;

    // upload file to S3
    return uploadFile(BucketType.Private, dirName, fileName, pdf);
};

const uploadInsuranceAgreementPdf = async (
    pdf: <PERSON><PERSON><PERSON>,
    application: Application,
    lead: Lead
): Promise<ApplicationDocument> => {
    const uploadedFile = await createInsurancePdf(pdf, application, lead);

    return {
        ...uploadedFile,
        kind: ApplicationDocumentKind.InsuranceAgreement,
        applicationId: application._id,
    };
};

export default uploadInsuranceAgreementPdf;
