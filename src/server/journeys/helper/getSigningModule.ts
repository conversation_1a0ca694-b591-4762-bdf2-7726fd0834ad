/* eslint-disable import/prefer-default-export */
import {
    ApplicationModule,
    BasicSigningModule,
    NamirialSigningModule,
    Application,
    ModuleType,
    DocusignModule,
} from '../../database/documents';
import { getBankIdFromApplication } from '../../database/helpers/applications';
import createLoaders from '../../loaders';

export type SigningModule = BasicSigningModule | NamirialSigningModule | DocusignModule;

export const getSigningModule = async (
    applicationModule: ApplicationModule,
    application?: Application,
    isResubmit: boolean = false,
    loaders = createLoaders()
): Promise<SigningModule> => {
    let signingModule;

    switch (applicationModule._type) {
        case ModuleType.StandardApplicationModule:
        case ModuleType.ConfiguratorModule:
        case ModuleType.FinderApplicationPublicModule:
        case ModuleType.FinderApplicationPrivateModule:
        case ModuleType.EventApplicationModule:
        case ModuleType.LaunchPadModule: {
            const bankId = getBankIdFromApplication(application);

            if (!bankId) {
                return null;
            }

            const bank = await loaders.systemBankById.load(bankId);
            if (!bank) {
                return null;
            }

            const { submissionApprovalModuleId, reSubmissionApprovalModuleId } = bank;

            const signingModuleId = isResubmit ? reSubmissionApprovalModuleId : submissionApprovalModuleId;
            if (!signingModuleId) {
                return null;
            }

            signingModule = await loaders.moduleById.load(signingModuleId);
            break;
        }

        case ModuleType.MobilityModule: {
            if (!applicationModule.signing.isEnabled || !applicationModule.signing.moduleId) {
                return null;
            }

            signingModule = await loaders.moduleById.load(applicationModule.signing.moduleId);
            break;
        }

        default:
            break;
    }

    if (!signingModule) {
        throw new Error('Module not found');
    }

    if (
        signingModule._type !== ModuleType.BasicSigningModule &&
        signingModule._type !== ModuleType.NamirialSigningModule &&
        signingModule._type !== ModuleType.Docusign
    ) {
        throw new Error('Invalid Module Type');
    }

    return signingModule;
};
