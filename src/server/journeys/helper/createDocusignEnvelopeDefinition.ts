import type { Document, EnvelopeDefinition, Signer, EventNotification } from 'docusign-esign';
import type { TFunction } from 'i18next';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../core/config';
import {
    Application,
    ApplicationKind,
    Bank,
    BankIntegrationProvider,
    Company,
    Customer,
    getKYCPresetsForCustomerModule,
    LocalCustomerFieldKey,
    LocalCustomerManagementModule,
} from '../../database';
import { getCustomerFullName, getLocalCustomerFieldValueFor } from '../../database/helpers/customers';
import type { JourneyIdentifier } from '../../signing/types';
import type { CreateSigningPdf } from '../types';

const createDocusignEnvelopeDefinition = async (
    t: TFunction,
    pdfs: CreateSigningPdf[],
    documents: {
        application: Application;
        customer: Customer;
        guarantor?: Customer;
        bank?: Bank;
        company: Company;
        customerModule: LocalCustomerManagementModule;
    },
    journeyIdentifier: JourneyIdentifier
) => {
    const result: {
        definition?: EnvelopeDefinition;
        customer?: { email: string; fullName: string };
        guarantor?: { email: string; fullName: string };
        nonce: string;
    } = { nonce: nanoid() };

    const definition: EnvelopeDefinition = {};
    result.definition = definition;

    definition.emailSubject = 'Please sign this document';

    definition.documents = pdfs.map(({ name, pdf }, index) => {
        const document: Document = {
            documentBase64: Buffer.from(pdf).toString('base64'),
            name,
            fileExtension: 'pdf',
            documentId: (index + 1).toString(10), // start from 1
        };

        return document;
    });

    const { customerModule, company } = documents;
    const getRecipientDetails = (recipient: Customer) => {
        const email = getLocalCustomerFieldValueFor(recipient, LocalCustomerFieldKey.Email);
        const kycPresets = getKYCPresetsForCustomerModule(customerModule, recipient._kind);

        const fullName = getCustomerFullName(t, recipient, company, kycPresets);

        return { email, fullName };
    };

    const signers = [];

    const { application, customer, bank } = documents;
    const applicantDetails = (() => {
        if (
            (application?.kind === ApplicationKind.Standard ||
                application?.kind === ApplicationKind.Configurator ||
                application?.kind === ApplicationKind.Finder) &&
            bank?.integration?.provider === BankIntegrationProvider.ENBD &&
            application?.configuration?.withFinancing
        ) {
            return {
                email: bank.integration.financeManager.email,
                fullName: bank.integration.financeManager.name,
            };
        }

        return getRecipientDetails(customer);
    })();
    const applicant: Signer = {
        email: applicantDetails.email,
        name: applicantDetails.fullName,
        clientUserId: 'applicant',
        recipientId: 1,
        tabs: {
            signHereTabs: [
                {
                    anchorString: '/DS_APT/',
                },
            ],
        },
    } as unknown as Signer; // due to `recipientId` typed to string
    signers.push(applicant);
    result.customer = applicantDetails;

    const { guarantor: bearer } = documents;
    if (bearer) {
        const bearerDetails = getRecipientDetails(bearer);
        const guarantor: Signer = {
            email: bearerDetails.email,
            name: bearerDetails.fullName,
            clientUserId: 'guarantor',
            recipientId: 2,
            tabs: {
                signHereTabs: [
                    {
                        anchorString: '/DS_GRT/',
                    },
                ],
            },
        } as unknown as Signer; // due to `recipientId` typed to string
        signers.push(guarantor);
        result.guarantor = bearerDetails;
    }

    definition.recipients = {
        signers,
    };

    const eventUrl = urljoin(
        config.applicationEndpoint,
        '/api/webhooks/docusign/status-update',
        journeyIdentifier,
        application._id.toHexString(),
        result.nonce
    );
    definition.eventNotification = {
        url: eventUrl,
        loggingEnabled: true,
        requireAcknowledgment: false,
        envelopeEvents: [
            {
                envelopeEventStatusCode: 'Completed',
                includeDocuments: false,
            },
            {
                envelopeEventStatusCode: 'Declined',
                includeDocuments: false,
            },
        ],
        eventData: {
            version: 'restv2.1',
            format: 'json',
        },
    } as unknown as EventNotification; // due to boolean values typed to string

    definition.status = 'sent';

    return result;
};

export default createDocusignEnvelopeDefinition;
