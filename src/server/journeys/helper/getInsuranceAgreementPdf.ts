import { Application, ApplicationDocumentKind, ApplicationKind } from '../../database';

const getInsuranceAgreementPdf = (application: Application) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
            return (
                application.documents.find(
                    document =>
                        document.kind === ApplicationDocumentKind.InsuranceAgreement &&
                        document.applicationId.equals(application._id)
                ) || null
            );

        default:
            return null;
    }
};

export const getAllInsuranceAgreementPdf = (application: Application) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
        case ApplicationKind.SalesOffer:
            return application.documents.filter(
                document => document.kind === ApplicationDocumentKind.InsuranceAgreement
            );

        default:
            return [];
    }
};

export default getInsuranceAgreementPdf;
