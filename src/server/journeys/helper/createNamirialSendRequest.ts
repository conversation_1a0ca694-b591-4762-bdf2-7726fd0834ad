import { TFunction } from 'i18next';
import urljoin from 'url-join';
import config from '../../core/config';
import {
    Application,
    ApplicationKind,
    Bank,
    BankIntegrationProvider,
    Company,
    Customer,
    getKYCPresetsForCustomerModule,
    LocalCustomerManagementModule,
} from '../../database';
import { getCustomerFullName } from '../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../database/helpers/customers/shared';
import { NamirialPrepareResponse, NamirialSendRequest } from '../../signing/namirial/types';
import { JourneyIdentifier } from '../../signing/types';
import type { CreateSigningFields } from '../types';

const createNamirialSendRequest = (
    fileIds: string[],
    { Activities, ...prepareResponseRemaining }: NamirialPrepareResponse,
    documents: {
        application: Application;
        customer: Customer;
        guarantor?: Customer;
        bank?: Bank;
        company: Company;
        customerModule: LocalCustomerManagementModule;
    },
    fields: CreateSigningFields,
    journeyIdentifier: JourneyIdentifier,
    t: TFunction
): NamirialSendRequest => {
    const { customer, bank, application, company, customerModule } = documents;
    const { clientAction } = fields;

    const signingRecipient = (() => {
        if (
            (application?.kind === ApplicationKind.Standard ||
                application?.kind === ApplicationKind.Configurator ||
                application?.kind === ApplicationKind.Finder) &&
            bank?.integration?.provider === BankIntegrationProvider.ENBD &&
            application?.configuration?.withFinancing
        ) {
            return {
                email: bank.integration.financeManager.email,
                customerName: bank.integration.financeManager.name,
            };
        }

        // get aggregatedFields
        const { email } = getLocalCustomerAggregatedFields(customer);
        const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

        const customerName = getCustomerFullName(t, customer, company, kycPresets);

        return {
            email,
            customerName,
        };
    })();

    // create callbackUrl
    const callbackUrl = `${urljoin(
        config.applicationEndpoint,
        `/api/webhooks/namirial/status-update/${journeyIdentifier}`
    )}/##EnvelopeId##/##Action##`;

    return {
        // common fields
        Documents: fileIds.map(fileId => ({ FileId: fileId, DocumentNumber: 0 })),
        Name: 'Application Confirmation',
        // extra fields from prepare response
        ...prepareResponseRemaining,
        Activities: Activities.map(({ Action: { Sign, ...actionRemaining }, ...activityRemaining }) => ({
            ...activityRemaining,
            Action: {
                ...actionRemaining,
                Sign: {
                    ...Sign,
                    RecipientConfiguration: {
                        ContactInformation: {
                            Email: signingRecipient.email,
                            GivenName: signingRecipient.customerName || '.',
                            Surname: '.',
                        },
                        SendEmails: false,
                    },
                    ...(clientAction && {
                        FinishActionConfiguration: { SignAnyWhereViewer: { RedirectUri: clientAction } },
                    }),
                    GeneralPoliciesOverrides: {
                        AllowSaveDocument: false,
                        AllowSaveAuditTrail: false,
                    },
                },
            },
        })),
        CallbackConfiguration: {
            CallbackUrl: callbackUrl,
            StatusUpdateCallbackUrl: callbackUrl,
        },
    };
};

export default createNamirialSendRequest;
