import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { Company, Customer, LocalCustomerManagementModule, User, getKYCPresetsForCustomerModule } from '../../database';
import { getCustomerFullName } from '../../database/helpers/customers';
import { getTimeZoneOffset } from '../../utils/date';

type ShareHeaderInfoProps = {
    customer: Customer;
    company: Company;
    assignee?: User;
    applicationDate: Date;
    timeZone?: string;
    customerModule: LocalCustomerManagementModule;
};

type InfoFieldProps = {
    label: string;
    value?: ReactNode;
};

const Container = styled.div`
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    /* make it same with calculator */
    margin-top: 30px;
    margin-bottom: 20px;
`;

const TextContainer = styled.div<{ $width: string }>`
    display: flex;
    width: ${({ $width }) => $width};
    flex-direction: row;
    margin-bottom: 0px;
`;

export const Text = styled.div<{ $bold?: boolean; $mb?: string; $isLabel?: boolean }>`
    margin-bottom: ${({ $mb }) => $mb ?? '0px'};
    font-weight: ${({ $bold }) => ($bold ? 'bold' : 'normal')};

    ${({ $isLabel }) =>
        $isLabel &&
        `
        color: #666;
        font-size: 12px;
        `}
`;

const InfoField = ({ label, value }: InfoFieldProps) => {
    if (!value) {
        return null;
    }

    return (
        <>
            <TextContainer $width="25%">
                <Text>{label}</Text>
            </TextContainer>
            <TextContainer $width="75%">
                <Text $bold>{value}</Text>
            </TextContainer>
        </>
    );
};

const ShareHeaderInfo = ({ customer, assignee, company, applicationDate, customerModule }: ShareHeaderInfoProps) => {
    const { t } = useTranslation(['applicationPdf', 'common']);

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);
    const customerFullName = getCustomerFullName(t, customer, company, kycPresets);

    return (
        <Container>
            <InfoField label={t('applicationPdf:sections.shareHeaderInfo.customer')} value={customerFullName} />
            <InfoField
                label={t('applicationPdf:sections.shareHeaderInfo.salesConsultant')}
                value={assignee?.displayName}
            />
            <InfoField
                label={t('applicationPdf:sections.shareHeaderInfo.date', {
                    offset: getTimeZoneOffset(t, company.timeZone),
                })}
                value={applicationDate ? t('common:formats.dateFull', { date: applicationDate }) : '-'}
            />
        </Container>
    );
};

export default ShareHeaderInfo;
