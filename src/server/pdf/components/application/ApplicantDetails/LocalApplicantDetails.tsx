import { Row } from 'antd';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { Customer, CustomerKind, KYCField, KycFieldPurpose } from '../../../../database';
import { getLocalCustomerAggregatedFields } from '../../../../database/helpers/customers/shared';
import { Options } from '../../../../utils/translateOptions';
import { SubTitle, Title, ContentContainer, SubContentContainer } from '../ui';
import CustomerField from './CustomerField';
import {
    EmploymentDetailsKey,
    AddressDetailsKey,
    CorrespondenceKey,
    CustomerDetailsKey,
    OthersKey,
    CorporateInformationKey,
    IdentityDetailsKey,
    ReferenceDetailsKey,
} from './shared';

export type LocalApplicantDetailsProps = {
    applicant: Customer;
    kycFields: KYCField[];
    options: Options;
    title: string;
    subtitle: string;
    timeZone?: string;
};

const StyledDiv = styled(SubTitle)`
    font-size: 16px;
    font-weight: bold;
    margin-top: 15px;
    margin-bottom: 5px;
    break-after: avoid-page;
`;

const LocalApplicantDetails = ({
    applicant,
    kycFields,
    options,
    title,
    subtitle,
    timeZone,
}: LocalApplicantDetailsProps) => {
    const { t } = useTranslation('applicationPdf');
    const data = getLocalCustomerAggregatedFields(applicant);

    const groupByCustomerData = useMemo(() => {
        const data = {
            customerDetails: [],
            addressDetails: [],
            correspondenceAddress: [],
            corporateInformation: [],
            employmentDetails: [],
            identityDetails: [],
            referenceDetails: [],
            others: [],
        };

        (kycFields || []).forEach(kyc => {
            if (kyc.purpose.includes(KycFieldPurpose.KYC)) {
                if (CustomerDetailsKey.includes(kyc.key)) {
                    data.customerDetails.push({ key: kyc.key });
                }

                if (EmploymentDetailsKey.includes(kyc.key)) {
                    data.employmentDetails.push({ key: kyc.key });
                }

                if (CorporateInformationKey.includes(kyc.key)) {
                    data.corporateInformation.push({ key: kyc.key });
                }

                if (AddressDetailsKey.includes(kyc.key)) {
                    data.addressDetails.push({ key: kyc.key });
                }

                if (IdentityDetailsKey.includes(kyc.key)) {
                    data.identityDetails.push({ key: kyc.key });
                }

                if (ReferenceDetailsKey.includes(kyc.key)) {
                    data.referenceDetails.push({ key: kyc.key });
                }

                if (CorrespondenceKey.includes(kyc.key)) {
                    data.correspondenceAddress.push({ key: kyc.key });
                }

                if (OthersKey.includes(kyc.key)) {
                    data.others.push({ key: kyc.key });
                }
            }
        });

        return data;
    }, [kycFields]);

    const orientationBasedOnApplicant = useMemo(() => {
        switch (applicant._kind) {
            case CustomerKind.Corporate:
                return (
                    <SubContentContainer>
                        {groupByCustomerData.corporateInformation.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.corporateInformation')}
                                </StyledDiv>
                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.corporateInformation || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.addressDetails.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.residentialAddress')}
                                </StyledDiv>
                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.addressDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.customerDetails.length > 0 && (
                            <>
                                <StyledDiv>{subtitle}</StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.customerDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.employmentDetails.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.employmentDetails')}
                                </StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.employmentDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.correspondenceAddress.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.correspondenceAddress')}
                                </StyledDiv>
                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.correspondenceAddress || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.identityDetails.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.identityDetails')}
                                </StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.identityDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.referenceDetails.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.referenceDetails')}
                                </StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.referenceDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.others.length > 0 && (
                            <>
                                <StyledDiv>{t('applicationPdf:sections.applicantDetails.subTitle.others')}</StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.others || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}
                    </SubContentContainer>
                );

            default:
                return (
                    <SubContentContainer>
                        {groupByCustomerData.customerDetails.length > 0 && (
                            <>
                                <StyledDiv>{subtitle}</StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.customerDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.correspondenceAddress.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.correspondenceAddress')}
                                </StyledDiv>
                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.correspondenceAddress || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.addressDetails.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.residentialAddress')}
                                </StyledDiv>
                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.addressDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.employmentDetails.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.employmentDetails')}
                                </StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.employmentDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.corporateInformation.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.corporateInformation')}
                                </StyledDiv>
                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.corporateInformation || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.identityDetails.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.identityDetails')}
                                </StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.identityDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.referenceDetails.length > 0 && (
                            <>
                                <StyledDiv>
                                    {t('applicationPdf:sections.applicantDetails.subTitle.referenceDetails')}
                                </StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.referenceDetails || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}

                        {groupByCustomerData.others.length > 0 && (
                            <>
                                <StyledDiv>{t('applicationPdf:sections.applicantDetails.subTitle.others')}</StyledDiv>

                                <Row gutter={[12, 12]}>
                                    {(groupByCustomerData.others || []).map(field => (
                                        <CustomerField
                                            key={field.key}
                                            data={data}
                                            fieldKey={field.key}
                                            options={options}
                                            timeZone={timeZone}
                                        />
                                    ))}
                                </Row>
                            </>
                        )}
                    </SubContentContainer>
                );
        }
    }, []);

    return (
        <div>
            <Title>{title}</Title>
            <ContentContainer>{orientationBasedOnApplicant}</ContentContainer>
        </div>
    );
};

export default LocalApplicantDetails;
