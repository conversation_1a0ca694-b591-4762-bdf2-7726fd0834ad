import { Col } from 'antd';
import dayjs from 'dayjs';
import { camelCase, isNil } from 'lodash/fp';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationFieldType } from '../../../../../app/utilities/common';
import { LocalCustomerFieldKey, CitizenshipType } from '../../../../database';
import type { LocalCustomerAggregatedFields } from '../../../../database/helpers/customers/types';
import getPurchaseIntentionString from '../../../../utils/getPurchaseIntentionString';
import { Options } from '../../../../utils/translateOptions';
import OutlinedPdfInputField from '../OutlinedPdfInputField';
import { escapeNil } from '../helpers';
import DrivingLicenseDetails from './DrivingLicenseDetails';
import ReferenceDetailFieldset from './ReferenceDetailFieldset';
import SalaryTransferredBankFieldset from './SalaryTransferredBankFieldset';
import UAEIdentityFieldset from './UAEIdentityFieldset';

export type CustomerFieldProps = {
    fieldKey: LocalCustomerFieldKey;
    data: LocalCustomerAggregatedFields;
    options: Options;
    timeZone?: string;
    Component?: FC<{
        label: JSX.Element | React.ReactNode;
        value: JSX.Element | React.ReactNode;
        suffix?: JSX.Element | React.ReactNode | null;
    }>;
};

const colSpan = { xs: 12 };

const getDropdownValue = (key: LocalCustomerFieldKey) => {
    const value = 'common:options';

    switch (key) {
        case LocalCustomerFieldKey.ResidentialStatusVWFS:
            return `${value}.residentialStatus`;

        case LocalCustomerFieldKey.JobTitle:
            return `${value}.jobTitle`;

        case LocalCustomerFieldKey.JobTitleTh:
            return `${value}.jobTitleTh`;

        case LocalCustomerFieldKey.EmploymentStatus:
            return `${value}.employmentStatus`;

        case LocalCustomerFieldKey.Salutation:
            return `${value}.salutations`;

        case LocalCustomerFieldKey.SalutationBmw:
            return `${value}.salutations`;

        case LocalCustomerFieldKey.RelationshipWithApplicant:
            return `${value}.relationshipApplicant`;

        case LocalCustomerFieldKey.NonBinaryTitle:
        case LocalCustomerFieldKey.Title:
            return `${value}.titles`;

        case LocalCustomerFieldKey.Gender:
        case LocalCustomerFieldKey.NonBinaryGender:
            return `${value}.gender`;

        case LocalCustomerFieldKey.Citizenship:
            return `${value}.citizenship`;

        case LocalCustomerFieldKey.Education:
            return `${value}.education`;

        case LocalCustomerFieldKey.IncomeType:
            return `${value}.incomeType`;

        case LocalCustomerFieldKey.ResidenceType:
            return `${value}.residenceType`;

        case LocalCustomerFieldKey.AddressType:
            return `${value}.addressType`;

        case LocalCustomerFieldKey.Emirate:
            return `${value}.emirate`;

        case LocalCustomerFieldKey.PurchaseIntention:
            return `${value}.purchaseIntention`;

        default:
            return '-';
    }
};

const getGenderValue = (gender: string) => (gender === 'NOTSPECIFIED' ? 'notSpecified' : gender.toLowerCase());

const CustomerField = ({
    fieldKey: key,
    data,
    options,
    timeZone,
    Component = OutlinedPdfInputField,
}: CustomerFieldProps) => {
    const { t } = useTranslation(['applicationPdf', 'common', 'regions']);
    switch (key) {
        case LocalCustomerFieldKey.FirstName:
        case LocalCustomerFieldKey.LastName:
        case LocalCustomerFieldKey.FullName:
        case LocalCustomerFieldKey.Email:
        case LocalCustomerFieldKey.PostalCode:
        case LocalCustomerFieldKey.Address:
        case LocalCustomerFieldKey.UnitNumber:
        case LocalCustomerFieldKey.Country:
        case LocalCustomerFieldKey.Nationality:
        case LocalCustomerFieldKey.Race:
        case LocalCustomerFieldKey.MaritalStatus:
        case LocalCustomerFieldKey.ResidentialStatus:
        case LocalCustomerFieldKey.District:
        case LocalCustomerFieldKey.Road:
        case LocalCustomerFieldKey.City:
        case LocalCustomerFieldKey.CorrespondenceCity:
        case LocalCustomerFieldKey.CorrespondenceAddress:
        case LocalCustomerFieldKey.CorrespondenceDistrict:
        case LocalCustomerFieldKey.Occupation:
        case LocalCustomerFieldKey.CompanyName:
        case LocalCustomerFieldKey.CompanyPhoneticName:
        case LocalCustomerFieldKey.CompanyCity:
        case LocalCustomerFieldKey.CompanyDistrict:
        case LocalCustomerFieldKey.CompanyPhoneExtension:
        case LocalCustomerFieldKey.CompanyAddress:
        case LocalCustomerFieldKey.CorporateName:
        case LocalCustomerFieldKey.CorporateIdentityNumber:
        case LocalCustomerFieldKey.CorporateIndustryCategory:
        case LocalCustomerFieldKey.DeliveryAddress:
        case LocalCustomerFieldKey.LastNameJapan:
        case LocalCustomerFieldKey.FirstNameJapan:
        case LocalCustomerFieldKey.Comments:
        case LocalCustomerFieldKey.BusinessTitle:
            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={escapeNil(data[key])}
                    />
                </Col>
            );

        case LocalCustomerFieldKey.Gender:
        case LocalCustomerFieldKey.NonBinaryGender: {
            const translationPath = getDropdownValue(key);

            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={data[key] ? t(`${translationPath}.${getGenderValue(data[key])}`) : '-'}
                    />
                </Col>
            );
        }

        case LocalCustomerFieldKey.Region:
            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={t(`regions:${escapeNil(data[key])}`, { defaultValue: escapeNil(data[key]) })}
                    />
                </Col>
            );

        case LocalCustomerFieldKey.LastNameFront:
        case LocalCustomerFieldKey.CorporateRegisteredCapital:
        case LocalCustomerFieldKey.CorporateAnnualRevenue:
        case LocalCustomerFieldKey.CorporateNumberOfEmployee:
        case LocalCustomerFieldKey.MonthlyIncome:
        case LocalCustomerFieldKey.OtherIncome:
        case LocalCustomerFieldKey.TimeOfEmployment:
        case LocalCustomerFieldKey.TimeOfAddress: {
            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={escapeNil(data[key])}
                    />
                </Col>
            );
        }

        case LocalCustomerFieldKey.Title:
        case LocalCustomerFieldKey.NonBinaryTitle:
        case LocalCustomerFieldKey.Salutation:
        case LocalCustomerFieldKey.SalutationBmw:
        case LocalCustomerFieldKey.EmploymentStatus:
        case LocalCustomerFieldKey.AddressType:
        case LocalCustomerFieldKey.Education:
        case LocalCustomerFieldKey.Emirate:
        case LocalCustomerFieldKey.IncomeType:
        case LocalCustomerFieldKey.ResidenceType: {
            const translationPath = getDropdownValue(key);

            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={data[key] ? t(`${translationPath}.${camelCase(data[key])}`) : '-'}
                    />
                </Col>
            );
        }

        case LocalCustomerFieldKey.PurchaseIntention: {
            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={data[key] ? getPurchaseIntentionString(t, data[key]) : '-'}
                    />
                </Col>
            );
        }

        // TODO: Better handler for dropdown, keep it in-sync with `useSystemOptions`
        // Because it's pointed out to wrong translation path
        case LocalCustomerFieldKey.Citizenship: {
            const translationPath = getDropdownValue(key);

            const foundValue = data[key] ?? null;
            const value = (() => {
                if (isNil(foundValue)) {
                    return '-';
                }

                const mapKeys = {
                    [CitizenshipType.SingaporeanOrPr]: 'singaporeCitizenOrPr',
                    [CitizenshipType.Malaysian]: 'malaysian',
                    [CitizenshipType.Others]: 'others',
                };

                if (!mapKeys[foundValue]) {
                    return '-';
                }

                return t(`${translationPath}.${mapKeys[foundValue]}`);
            })();

            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={value}
                    />
                </Col>
            );
        }

        case LocalCustomerFieldKey.JobTitle:
        case LocalCustomerFieldKey.JobTitleTh:
        case LocalCustomerFieldKey.RelationshipWithApplicant:
        case LocalCustomerFieldKey.ResidentialStatusVWFS: {
            const translationPath = getDropdownValue(key);

            return (
                <>
                    <Col {...colSpan}>
                        <Component
                            {...t<string, { returnObjects: true }, TranslationFieldType>(
                                `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                                {
                                    returnObjects: true,
                                }
                            )}
                            value={data[key]?.value ? t(`${translationPath}.${camelCase(data[key].value)}`) : '-'}
                        />
                    </Col>
                    {data[key]?.description && (
                        <Col {...colSpan}>
                            <Component
                                {...t<string, { returnObjects: true }, TranslationFieldType>(
                                    `applicationPdf:sections.applicantDetails.fields.${camelCase(`Other_${key}`)}`,
                                    {
                                        returnObjects: true,
                                    }
                                )}
                                value={data[key]?.description}
                            />
                        </Col>
                    )}
                </>
            );
        }

        case LocalCustomerFieldKey.IdentityNumber:
        case LocalCustomerFieldKey.Passport: {
            if (isNil(data[key]) || (typeof data[key] === 'string' && !data[key])) {
                return null;
            }

            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={escapeNil(data[key])}
                    />
                </Col>
            );
        }

        case LocalCustomerFieldKey.CorporatePhone:
        case LocalCustomerFieldKey.Phone:
        case LocalCustomerFieldKey.CompanyPhone:
        case LocalCustomerFieldKey.Telephone:
            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={data[key]?.value ? `+${data[key].prefix} ${escapeNil(data[key].value)}` : '-'}
                    />
                </Col>
            );

        case LocalCustomerFieldKey.Birthday: {
            let dateValue = '-';

            if (data[key] && timeZone) {
                dateValue = dayjs(data[key]).tz(timeZone).format(t('common:formats.dateFullFormat'));
            }

            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={dateValue}
                    />
                </Col>
            );
        }

        case LocalCustomerFieldKey.CorporateRegistrationDate:
        case LocalCustomerFieldKey.DriverLicensePassDate:
        case LocalCustomerFieldKey.DateOfJoining:
        case LocalCustomerFieldKey.PreferredFirstPaymentDate:
            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={data[key] ? t('common:formats.date', { date: data[key] }) : '-'}
                    />
                </Col>
            );

        case LocalCustomerFieldKey.NoClaimDiscount:
            return (
                <Col {...colSpan}>
                    <Component
                        {...t<string, { returnObjects: true }, TranslationFieldType>(
                            `applicationPdf:sections.applicantDetails.fields.${camelCase(key)}`,
                            {
                                returnObjects: true,
                            }
                        )}
                        value={escapeNil(data[key] ? `${data[key]}%` : null)}
                    />
                </Col>
            );

        case LocalCustomerFieldKey.DrivingLicense:
        case LocalCustomerFieldKey.UAEDrivingLicense:
            return (
                <>
                    {(data.drivingLicense || []).map(data =>
                        data.class ? <DrivingLicenseDetails key={data.class} data={data} options={options} /> : null
                    )}
                </>
            );

        case LocalCustomerFieldKey.DrivingLicenseMy:
            return (
                <>
                    {(data.drivingLicenseMy || []).map(data =>
                        data.class ? <DrivingLicenseDetails key={data.class} data={data} options={options} /> : null
                    )}
                </>
            );

        case LocalCustomerFieldKey.DrivingLicenseTh:
            return (
                <>
                    {(data.drivingLicenseTh || []).map(data =>
                        data.type ? (
                            <DrivingLicenseDetails key={data.type} data={data} options={options} showClass={false} />
                        ) : null
                    )}
                </>
            );

        case LocalCustomerFieldKey.ReferenceDetailSet:
            return <ReferenceDetailFieldset value={data[key]} />;

        case LocalCustomerFieldKey.SalaryTransferredBankSet:
            return <SalaryTransferredBankFieldset value={data[key]} />;

        case LocalCustomerFieldKey.UAEIdentitySet:
            return <UAEIdentityFieldset value={data[key]} />;

        default:
            throw new Error(`unable to render customer field, key ${key}`);
    }
};

export default CustomerField;
