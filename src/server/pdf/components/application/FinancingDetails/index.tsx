import { Col, Row } from 'antd';
import { ObjectId } from 'bson';
import { isEmpty } from 'lodash/fp';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationFieldType } from '../../../../../app/utilities/common';
import {
    ApplicationMarket,
    Application,
    Vehicle,
    Bank,
    FinanceProduct,
    ApplicationKind,
    PromoCode,
    Company,
    ApplicationModule,
    ModuleType,
    Dealer,
    BankKind,
} from '../../../../database';
import getPromoCodeInfo from '../../../../utils/getPromoCodeInfo';
import { ApplicationPdfHelpersProps } from '../../../types';
import FinancingDisclaimerSection from '../../FinancingDisclaimerSection';
import PriceDisclaimerSection from '../../PriceDisclaimerSection';
import OutlinedPdfInputField from '../OutlinedPdfInputField';
import { escapeNil } from '../helpers';
import { Title, ContentContainer } from '../ui';
import FinancingDefaultMarket from './FinancingDefaultMarket';
import FinancingNewZealandMarket from './FinancingNewZealandMarket';
import FinancingSingaporeMarket from './FinancingSingaporeMarket';
import VehicleField from './VehicleField';

export type FinancingDetailsProps = {
    application: Application;
    company: Company;
    bank: Bank;
    financeProduct: FinanceProduct;
    vehicle: Vehicle;
    promoCode: PromoCode;
    helpers: ApplicationPdfHelpersProps;
    applicationModule: ApplicationModule;
    dealerId: ObjectId;
    dealer: Dealer;
};

const getFinancing = (application: Application) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Event:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
        case ApplicationKind.SalesOffer:
            return application.financing;

        default:
            return null;
    }
};

const FinancingDetails = ({
    vehicle,
    application,
    company,
    bank,
    financeProduct,
    promoCode,
    helpers,
    applicationModule,
    dealer,
    dealerId,
}: FinancingDetailsProps) => {
    const { t } = useTranslation(['applicationPdf', 'common']);

    const financing = getFinancing(application);
    const promoCodeInfo = getPromoCodeInfo(promoCode, helpers.formatAmountWithCurrency, t, financing?.promoCodeValue);

    const showCommentsField = bank?.kind === BankKind.System && bank.showCommentsField;

    const getMarketElement = () => {
        if (!bank) {
            return null;
        }

        switch (financing.market) {
            case ApplicationMarket.Default:
                return (
                    <FinancingDefaultMarket
                        bank={bank}
                        company={company}
                        dealerId={
                            application.kind === ApplicationKind.Standard ||
                            application.kind === ApplicationKind.Finder ||
                            application.kind === ApplicationKind.Configurator
                                ? application.dealerId
                                : null
                        }
                        financeProduct={financeProduct}
                        financing={financing}
                        module={applicationModule}
                    />
                );

            case ApplicationMarket.Singapore:
                return (
                    <FinancingSingaporeMarket
                        bank={bank}
                        company={company}
                        dealerId={
                            application.kind === ApplicationKind.Standard ||
                            application.kind === ApplicationKind.Finder ||
                            application.kind === ApplicationKind.Configurator
                                ? application.dealerId
                                : null
                        }
                        financeProduct={financeProduct}
                        financing={financing}
                        module={applicationModule}
                    />
                );

            case ApplicationMarket.NewZealand:
                return (
                    <FinancingNewZealandMarket
                        bank={bank}
                        company={company}
                        dealerId={
                            application.kind === ApplicationKind.Standard ||
                            application.kind === ApplicationKind.Finder ||
                            application.kind === ApplicationKind.Configurator
                                ? application.dealerId
                                : null
                        }
                        financeProduct={financeProduct}
                        financing={financing}
                        module={applicationModule}
                    />
                );

            default:
                return null;
        }
    };

    if (applicationModule._type === ModuleType.MobilityModule) {
        return null;
    }

    return (
        <div>
            <Title>{t('applicationPdf:sections.financingDetails.title')}</Title>
            <ContentContainer>
                <Row gutter={[12, 12]}>
                    {vehicle && (
                        <Col span={24}>
                            <VehicleField company={company} financing={financing} vehicle={vehicle} />
                        </Col>
                    )}
                    {getMarketElement()}
                    {!isEmpty(promoCodeInfo) && (
                        <Col span={24}>
                            <OutlinedPdfInputField
                                {...t<string, { returnObjects: true }, TranslationFieldType>(
                                    'applicationPdf:sections.financingDetails.fields.promoCode',
                                    {
                                        returnObjects: true,
                                    }
                                )}
                                value={promoCodeInfo}
                            />
                        </Col>
                    )}
                    {showCommentsField && (
                        <Col span={24}>
                            <OutlinedPdfInputField
                                {...t<string, { returnObjects: true }, TranslationFieldType>(
                                    'applicationPdf:sections.financingDetails.fields.commentsToBank',
                                    {
                                        returnObjects: true,
                                    }
                                )}
                                value={escapeNil(application.remarks)}
                            />
                        </Col>
                    )}
                    {financing?.affinAutoFinanceCentre && (
                        <Col span={24}>
                            <OutlinedPdfInputField
                                {...t<string, { returnObjects: true }, TranslationFieldType>(
                                    'applicationPdf:sections.financingDetails.fields.affinAutoFinanceCentre',
                                    {
                                        returnObjects: true,
                                    }
                                )}
                                value={financing.affinAutoFinanceCentre}
                            />
                        </Col>
                    )}
                </Row>

                <PriceDisclaimerSection
                    applicationModule={applicationModule}
                    bank={bank}
                    company={company}
                    dealer={dealer}
                    dealerId={dealerId}
                    isComparison={false}
                />

                <FinancingDisclaimerSection
                    applicationModule={applicationModule}
                    bank={bank}
                    company={company}
                    dealer={dealer}
                    dealerId={dealerId}
                    financing={financing}
                />
            </ContentContainer>
        </div>
    );
};

export default FinancingDetails;
