import { Col, Row } from 'antd';
import { isNil } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { TranslationFieldType } from '../../../../../app/utilities/common';
import {
    Application,
    ApplicationKind,
    ApplicationMarket,
    ApplicationModule,
    Bank,
    Company,
    Customer,
    Dealer,
    Insurer,
    LocalCustomerManagementModule,
    LocalInsuranceProduct,
    ModuleType,
    Vehicle,
    getKYCPresetsForCustomerModule,
} from '../../../../database';
import {
    getCommentsToInsurer,
    shouldDisplayDateOfBirthDisclaimer,
} from '../../../../emails/components/common/InsuranceApplicationSummary';
import { getInsuranceCustomerData } from '../../../../integrations/insurance/eazy';
import { ApplicationPdfHelpersProps } from '../../../types';
import InsuranceDisclaimerSection from '../../InsuranceDisclaimerSection';
import OutlinedPdfInputField from '../OutlinedPdfInputField';
import { escapeNil } from '../helpers';
import { ContentContainer, Title } from '../ui';
import InsurancingDefaultMarket from './InsurancingDefaultMarket';
import InsurancingNewZealandMarket from './InsurancingNewZealandMarket';
import InsurancingSingaporeMarket from './InsurancingSingaporeMarket';
import VehicleField from './VehicleField';

const FinancingContent = styled.div`
    page-break-before: always;
`;

export type InsurancingDetailsProps = {
    application: Application;
    company: Company;
    customer: Customer;
    dealer: Dealer;
    insurer: Insurer;
    vehicle: Vehicle;
    helpers: ApplicationPdfHelpersProps;
    applicationModule: ApplicationModule;
    bank?: Bank;
    customerModule: LocalCustomerManagementModule;
    insuranceProduct: LocalInsuranceProduct;
};

const getInsurancing = (application: Application) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
        case ApplicationKind.SalesOffer:
            return application.insurancing;

        default:
            return null;
    }
};

const InsurancingDetails = ({
    vehicle,
    application,
    bank,
    company,
    customer,
    dealer,
    helpers,
    insurer,
    applicationModule,
    customerModule,
    insuranceProduct,
}: InsurancingDetailsProps) => {
    const { t } = useTranslation(['applicationPdf', 'common']);
    const insurancing = getInsurancing(application);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const showCommentsToInsurer = insurer?.showCommentsField;
    const commentsToInsurer = getCommentsToInsurer(application);
    const customerData = getInsuranceCustomerData(customer, company, kycPresets, t, commentsToInsurer);
    const displayDateOfBirthDisclaimer = shouldDisplayDateOfBirthDisclaimer(
        insurancing,
        insuranceProduct,
        customerData
    );

    const getMarketElement = () => {
        if (!insurer) {
            return null;
        }

        switch (insurancing.market) {
            case ApplicationMarket.Default:
                return (
                    <InsurancingDefaultMarket
                        company={company}
                        insuranceProduct={insuranceProduct}
                        insurancing={insurancing}
                        insurer={insurer}
                    />
                );

            case ApplicationMarket.Singapore:
                return (
                    <InsurancingSingaporeMarket
                        company={company}
                        insuranceProduct={insuranceProduct}
                        insurancing={insurancing}
                        insurer={insurer}
                    />
                );

            case ApplicationMarket.NewZealand:
                return (
                    <InsurancingNewZealandMarket
                        company={company}
                        insuranceProduct={insuranceProduct}
                        insurancing={insurancing}
                        insurer={insurer}
                    />
                );

            default:
                return null;
        }
    };

    if (applicationModule._type === ModuleType.MobilityModule) {
        return null;
    }

    return (
        <FinancingContent>
            <Title>{t('applicationPdf:sections.insurancingDetails.title')}</Title>
            <ContentContainer>
                <Row gutter={[12, 12]}>
                    <Col span={24}>
                        <VehicleField company={company} insurancing={insurancing} vehicle={vehicle} />
                    </Col>
                    {getMarketElement()}

                    {showCommentsToInsurer && (
                        <Col span={24}>
                            <OutlinedPdfInputField
                                {...t<string, { returnObjects: true }, TranslationFieldType>(
                                    'applicationPdf:sections.insurancingDetails.fields.commentsToInsurer',
                                    {
                                        returnObjects: true,
                                    }
                                )}
                                value={escapeNil(commentsToInsurer)}
                            />
                        </Col>
                    )}
                    {isNil(insurancing.insurancePremium) && (
                        <Col span={24}>
                            <p>{t('applicationPdf:insurance.hint')}</p>
                        </Col>
                    )}
                    {displayDateOfBirthDisclaimer && (
                        <Col span={24}>
                            <p>
                                {t('applicationPdf:insurance.dateOfBirthDisclaimer', {
                                    oldDateOfBirth: t('common:formats.date', { date: insurancing.dateOfBirth }),
                                })}
                            </p>
                        </Col>
                    )}
                    <InsuranceDisclaimerSection
                        application={application}
                        applicationModule={applicationModule}
                        bank={bank}
                        company={company}
                        dealer={dealer}
                        insurer={insurer}
                    />
                </Row>
            </ContentContainer>
        </FinancingContent>
    );
};

export default InsurancingDetails;
