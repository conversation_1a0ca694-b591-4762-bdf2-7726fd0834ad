/* eslint-disable react/no-unescaped-entities */
import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { QuotationPdfContext } from '../../../journeys/helper/getQuotationPdfContext';
import { PreventPageBreakDiv } from './ui';

const SigningContent = styled.div`
    width: 100%;
    margin: 0 5px;
    text-align: left;
    margin-top: 35px;
`;
const SigningContainer = styled.div`
    position: relative;
    width: 300px;
    height: 170px;
    border: 1px solid;
    border-radius: 10px;
`;

const SignatureLabel = styled.div`
    margin-bottom: 10px;
    font-size: 18px;
`;

// bottom and left are tweaked via observation
// namirial preview != namirial signing page
const Signature = styled.p`
    position: absolute;
    bottom: -4px;
    left: -1px;
    margin: 0;
    font-size: xx-small;
    font-family: FreeSans, sans-serif;
    white-space: nowrap;
    color: #fff;
`;

const QuotationSignatureCell = styled.td`
    // Since the container signing is 300px + padding + border
    width: 312px;
    vertical-align: bottom;
`;

const QuotationStampBottomCell = styled.td`
    width: 160px;
    padding: 0 20px;
`;

const QuotationStampImage = styled.img`
    width: 100%;
`;

const GuarantorContainer = styled.div`
    margin-top: 10px;
`;

type SigningProps = {
    hasGuarantor: boolean;
    quotationContext?: QuotationPdfContext;
    provider: 'namirial' | 'docusign';
    label?: string;
};

// width and height of namirial signing placeholder need to match dimensions of `SigningContainer`
// despite namirial states the width and height are measured in pixel, can't use the dimension as is
const placeholders = {
    namirial: {
        applicant:
            '[[!sigField1:signer1:signature(sigType="Draw2Sign",batch=1):label("Please sign here"):size(width=224,height=127)]]',
        guarantor:
            '[[!sigField2:signer2:signature(sigType="Draw2Sign",batch=1):label("Please sign here"):size(width=224,height=127)]]',
    },
    docusign: {
        applicant: '/DS_APT/',
        guarantor: '/DS_GRT/',
    },
};

const Signing = ({ hasGuarantor, quotationContext, provider, label }: SigningProps) => {
    const { t } = useTranslation(['applicationPdf']);

    const placeholder = placeholders[provider];

    if (quotationContext?.quotation) {
        return (
            <PreventPageBreakDiv style={{ width: '100%' }}>
                <table>
                    <tr>
                        <QuotationSignatureCell>
                            <SignatureLabel>{quotationContext?.quotation?.companyName}</SignatureLabel>
                            <SigningContainer>
                                <Signature>{placeholder?.applicant}</Signature>
                            </SigningContainer>
                            <SignatureLabel>
                                {t('applicationPdf:sections.quotationDetails.fields.signature.label')}
                            </SignatureLabel>
                            <SignatureLabel>{quotationContext?.quotation?.financeManagerName}</SignatureLabel>
                        </QuotationSignatureCell>
                        <QuotationStampBottomCell>
                            <QuotationStampImage alt="quotation-stamp-bottom" src={quotationContext?.stampBottomUrl} />
                        </QuotationStampBottomCell>
                    </tr>
                </table>
            </PreventPageBreakDiv>
        );
    }

    return (
        <SigningContent>
            <PreventPageBreakDiv>
                <SignatureLabel>{label ?? t('applicationPdf:signature')}</SignatureLabel>

                <SigningContainer>
                    <Signature>{placeholder?.applicant}</Signature>
                </SigningContainer>

                {hasGuarantor && (
                    <GuarantorContainer>
                        <SignatureLabel>{t('applicationPdf:guarantorSignature')}</SignatureLabel>

                        <SigningContainer>
                            <Signature>{placeholder?.guarantor}</Signature>
                        </SigningContainer>
                    </GuarantorContainer>
                )}
            </PreventPageBreakDiv>
        </SigningContent>
    );
};

export default Signing;
