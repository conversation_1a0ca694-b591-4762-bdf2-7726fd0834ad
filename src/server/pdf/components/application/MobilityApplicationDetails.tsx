import { Col, Row } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationFieldType } from '../../../../app/utilities/common';
import { ApplicationStage, Vehicle, MobilityApplication, VehicleKind, Company, Lead } from '../../../database';
import { getMobilityLocationName } from '../../../database/helpers/applications';
import { getApplicationIdentifier } from '../../../utils/application';
import { getFormattedDate } from '../../../utils/date';
import OutlinedPdfInputField from './OutlinedPdfInputField';
import { ContentContainer, Title } from './ui';

export type MobilityApplicationDetailsProps = {
    application: MobilityApplication & {
        vin?: Date;
    };
    lead: Lead;
    vehicle: Vehicle;
    company: Company;
};

const colSpan = { xs: 12 };

const MobilityApplicationDetails = ({ application, vehicle, company, lead }: MobilityApplicationDetailsProps) => {
    const { t } = useTranslation(['applicationPdf', 'common']);

    const identifier = getApplicationIdentifier(application, lead, [ApplicationStage.Mobility]);

    return (
        <div>
            <Title>{t('applicationPdf:sections.mobilityMainDetails.title')}</Title>
            <ContentContainer>
                <Row gutter={[12, 12]}>
                    <Col {...colSpan}>
                        <OutlinedPdfInputField
                            {...t<string, { returnObjects: true }, TranslationFieldType>(
                                'applicationPdf:sections.mobilityMainDetails.fields.applicationId',
                                {
                                    returnObjects: true,
                                }
                            )}
                            value={identifier}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <OutlinedPdfInputField
                            {...t<string, { returnObjects: true }, TranslationFieldType>(
                                'applicationPdf:sections.mobilityMainDetails.fields.createIn',
                                {
                                    returnObjects: true,
                                }
                            )}
                            value={t('common:formats.date', {
                                date: application._versioning.createdAt,
                            })}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <OutlinedPdfInputField
                            {...t<string, { returnObjects: true }, TranslationFieldType>(
                                'applicationPdf:sections.mobilityMainDetails.fields.variantName',
                                {
                                    returnObjects: true,
                                }
                            )}
                            value={vehicle._kind === VehicleKind.LocalVariant ? vehicle.name.defaultValue : '-'}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <OutlinedPdfInputField
                            {...t<string, { returnObjects: true }, TranslationFieldType>(
                                'applicationPdf:sections.mobilityMainDetails.fields.rentalPeriodStart',
                                {
                                    returnObjects: true,
                                }
                            )}
                            value={getFormattedDate(
                                t,
                                application.mobilityBookingDetails.period.start,
                                company.timeZone
                            )}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <OutlinedPdfInputField
                            {...t<string, { returnObjects: true }, TranslationFieldType>(
                                'applicationPdf:sections.mobilityMainDetails.fields.rentalPeriodEnd',
                                {
                                    returnObjects: true,
                                }
                            )}
                            value={getFormattedDate(t, application.mobilityBookingDetails.period.end, company.timeZone)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <OutlinedPdfInputField
                            {...t<string, { returnObjects: true }, TranslationFieldType>(
                                'applicationPdf:sections.mobilityMainDetails.fields.location',
                                {
                                    returnObjects: true,
                                }
                            )}
                            value={getMobilityLocationName(t, application.mobilityBookingDetails.location)}
                        />
                    </Col>
                </Row>
            </ContentContainer>
        </div>
    );
};

export default MobilityApplicationDetails;
