import ApplicationPdf from './ApplicationPdf';
import ComparisonShareApplicationPdf from './ComparisonShareApplicationPdf';
import InsuranceApplicationPdf from './InsuranceApplicationPdf';
import MobilityApplicationPdf from './MobilityApplicationPdf';
import ShareApplicationPdf from './ShareApplicationPdf';
import TestDrivePdf from './TestDriveApplicationPdf';
import createRender from './createRender';
import COEBiddingAgreementPdf from './salesOffer/COEBiddingAgreementPdf';

export const renderApplicationPDF = createRender(ApplicationPdf);
export const renderMobilityApplicationPDF = createRender(MobilityApplicationPdf);
export const renderInsuranceApplicationPDF = createRender(InsuranceApplicationPdf);
export const renderShareApplicationPdf = createRender(ShareApplicationPdf);
export const renderComparisonShareApplicationPdf = createRender(ComparisonShareApplicationPdf);
export const renderTestDrivePdf = createRender(TestDrivePdf);
export const renderCOEBiddingAgreementPdf = createRender(COEBiddingAgreementPdf);
