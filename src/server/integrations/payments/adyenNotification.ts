import * as Sentry from '@sentry/node';
import { RequestHand<PERSON> } from 'express';
import { xor } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    AdyenPaymentSetting,
    Application,
    ApplicationJourney,
    ApplicationKind,
    ApplicationPaymentCompletedAuditTrail,
    ApplicationPaymentFailedAuditTrail,
    ApplicationPaymentRefundedAuditTrail,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    GiftVoucherPaymentCompletedAuditTrail,
    GiftVoucherPaymentFailedAuditTrail,
    GiftVoucherPaymentRefundedAuditTrail,
    GiftVoucherStatus,
    JourneyType,
    Lead,
    LocalCustomerManagementModule,
    LocalVariant,
    PaymentStatus,
    SettingId,
    StandardApplication,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getKYCPresetsForCustomerModule } from '../../database/helpers';
import { getCustomerEmail, getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import { getPreviousApplicationStages } from '../../database/queries/application';
import {
    AudienceMessage,
    createCompanySMTPTransports,
    sendCustomerDepositConfirmation,
    sendSalespersonDepositConfirmation,
} from '../../emails';
import { getApplicationType } from '../../export/exportApplications';
import { getAgreementDocument } from '../../journeys/helper';
import createLoaders from '../../loaders';
import { attachAgreementDocument, mainQueue, sendMobilityApplicationEmails } from '../../queues';
import { isAbleToSendSubmissionEmail } from '../../queues/implementations/sendApplicationSubmissionMail/shared';
import {
    getApplicationAssigneeId,
    getApplicationLogStages,
    getApplicationStage,
    getStatusUpdates,
} from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

enum EventCode {
    authorisation = 'AUTHORISATION',
    refund = 'REFUND',
    captureFailed = 'CAPTURE_FAILED',
}

type NotificationRequestItem = {
    amount: {
        currency: string;
        value: number;
    };
    eventCode: EventCode;
    eventDate: string;
    merchantAccountCode: string;
    merchantReference: string;
    // original reference when payment is transacted
    originalReference: string;
    paymentMethod: string;
    // transaction id
    pspReference: string;
    reason: string;
    success: 'true' | 'false';
    additionalData: {
        checkoutSessionId: 'string';
        ['metadata.journey']: JourneyType;
    };
};

type NotificationRequest = {
    live: boolean;
    // there's always only 1 item in notification item
    notificationItems: { NotificationRequestItem: NotificationRequestItem }[];
};

const withScope = (request: Record<string, unknown>) => (callback: (scope: Sentry.Scope) => void) => {
    Sentry.withScope(scope => {
        scope.clearBreadcrumbs();
        scope.setTag('integration', 'adyen');
        scope.setContext('request', request);

        const { notificationItems } = request.body as NotificationRequest;

        const [notificationItem] = notificationItems || [];
        const { NotificationRequestItem: notification } = notificationItem || {};

        if (notification) {
            const context = {
                sessionId: notification.additionalData?.checkoutSessionId,
                transactionId: notification.pspReference,
                event: notification.eventCode,
            };

            scope.setContext('context', context);
        }

        callback(scope);
    });
};

const createReturnRequest =
    (message: string, level: 'warning' | 'info'): RequestHandler =>
    (req, res) => {
        withScope({ url: req.url, params: req.params, body: req.body })(scope => {
            Sentry.captureMessage(message, level);
        });

        // return 200 so we do not block the notification service
        // when receiving
        res.status(200).json({
            notificationResponse: '[accepted]',
        });
    };

const returnInvalidRequest: RequestHandler = createReturnRequest('invalid request to adyen webhook', 'warning');
const returnInvalidAuthorization: RequestHandler = createReturnRequest(
    'invalid authorization to adyen webhook',
    'warning'
);

// No need to send if standard application
export const sendConfirmationEmail = async (
    journey: ApplicationJourney,
    application: Application,
    lead: Lead,
    currentStages: ApplicationStage[]
) => {
    switch (application.kind) {
        case ApplicationKind.Configurator:
        case ApplicationKind.Event:
        case ApplicationKind.Finder:
        case ApplicationKind.Standard: {
            const isRequestingForFinancing =
                (application.kind === ApplicationKind.Configurator || application.kind === ApplicationKind.Finder) &&
                application.configuration.requestForFinancing &&
                !application.configuration.withFinancing;

            // if the journey is yet not received, emails will be triggered in `onApplicationSubmitted` queue
            if (journey.isReceived && (await isAbleToSendSubmissionEmail(application, journey))) {
                return sendApplicationSubmissionMail(application._id, currentStages, isRequestingForFinancing);
            }

            return null;
        }

        case ApplicationKind.Mobility: {
            // generate pdf
            const agreementPdf = getAgreementDocument(application);

            if (!agreementPdf) {
                // must attach it
                await attachAgreementDocument(application, lead);
            }

            await sendMobilityApplicationEmails(application, journey);

            return null;
        }

        default:
            return null;
    }
};

/**
 * Because of configurator and event application has same
 * procedure for submission, we use one function not to repeat
 * logic
 * @param applicationId ObjectId
 */
const sendApplicationSubmissionMail = async (
    applicationId: ObjectId,
    currentStages: ApplicationStage[],
    isRequestForFinancing?: boolean
) => {
    // Send email to sales person
    await mainQueue.add({
        type: 'sendApplicationSubmissionMail',
        applicationId,
        audience: AudienceMessage.Salesperson,
        isRequestForFinancing,
        currentStages,
    });

    // Send email to customer
    await mainQueue.add({
        type: 'sendApplicationSubmissionMail',
        applicationId,
        audience: AudienceMessage.Customer,
        currentStages,
    });
};

export const sendDepositConfirmationEmail = async (journey: ApplicationJourney, application: StandardApplication) => {
    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails']);

    const { t } = i18n;

    const loaders = createLoaders();

    const applicationModule = await loaders.moduleById.load(application.moduleId);
    const lead = await loaders.leadById.load(application.leadId);

    const assigneeId = getApplicationAssigneeId(application, lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Reservation,
        ApplicationStage.Financing,
        ApplicationStage.Lead,
        ApplicationStage.Appointment,
        ApplicationStage.Insurance,
    ]);
    const [variant, customer, company, assignee] = await Promise.all([
        loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>,
        loaders.customerById.load(application.applicantId),
        loaders.companyById.load(applicationModule.companyId),
        assigneeId ? loaders.userById.load(assigneeId) : null,
    ]);

    const customerModule = (await loaders.moduleById.load(customer.moduleId)) as LocalCustomerManagementModule;
    // load mail context
    const emailContext = await getCompanyEmailContext(company);
    const transporter = await createCompanySMTPTransports(company);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);

    await sendCustomerDepositConfirmation(
        {
            i18n,
            data: {
                emailContext,
                application,
                journey,
                variant,
                customer,
                assignee,
                company,
                customerModule,
                lead,
            },
            to: { name: getCustomerEmail(t, customer), address: getCustomerEmail(t, customer) },
            subject: t('emails:customerDepositConfirmation.subject', {
                companyName: emailContext.companyName,
            }),
        },
        transporter,
        emailContext.sender
    );

    const stage = getApplicationStage(application, lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
    ]);

    await sendSalespersonDepositConfirmation(
        {
            i18n,
            data: {
                emailContext,
                application,
                journey,
                variant,
                customer,
                assignee,
                company,
                customerModule,
                lead,
            },
            to: { name: assignee.email, address: assignee.email },
            subject: t('emails:salesPersonDepositConfirmation.subject', {
                companyName: emailContext.companyName,
                applicationStage: getApplicationType(stage.stage),
                identifier: stage.value?.identifier,
                customerName,
            }),
        },
        transporter,
        emailContext.sender
    );
};

const handleApplicationNotification = async (notification: NotificationRequestItem, req, res, next) => {
    const { collections } = await getDatabaseContext();

    // find session id
    const applicationJourney = await collections.applicationJourneys.findOne({
        'deposit.session.id': notification.additionalData.checkoutSessionId,
    });

    // there's no application journey attached to this session id
    if (!applicationJourney) {
        returnInvalidRequest(req, res, next);

        return;
    }

    const application = await collections.applications.findOne({
        '_versioning.suiteId': applicationJourney.applicationSuiteId,
        '_versioning.isLatest': true,
    });

    // there's no application attached to this
    if (
        !application ||
        ![
            ApplicationKind.Standard,
            ApplicationKind.Event,
            ApplicationKind.Configurator,
            ApplicationKind.Mobility,
            ApplicationKind.Finder,
        ].includes(application.kind)
    ) {
        returnInvalidRequest(req, res, next);

        return;
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    const setting = (await collections.settings.findOne({
        _id: applicationJourney.deposit.settingId,
        settingId: SettingId.AdyenPayment,
    })) as AdyenPaymentSetting;

    // there's no setting attached to this
    if (!setting) {
        returnInvalidRequest(req, res, next);

        return;
    }

    // if (!req.headers.authorization || req.headers.authorization.indexOf('Basic ') === -1) {
    //     returnInvalidAuthorization(req, res, next);
    //
    //     return;
    // }

    // // verify auth credentials
    // const base64Credentials = req.headers.authorization.split(' ')[1];
    // const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    // const [username, password] = credentials.split(':');
    //
    // if (username !== secrets.username || password !== secrets.password) {
    //     returnInvalidAuthorization(req, res, next);
    //
    //     return;
    // }
    //
    // // validate request
    // const validator = new HMACValidator();
    // // do hmac validation
    // // @ts-ignore
    // if (!secrets.hmacKeys.some(key => validator.validateHMAC(notification, key))) {
    //     // non of the existing keys are valid
    //     returnInvalidAuthorization(req, res, next);
    //
    //     return;
    // }

    const previousStages = await getPreviousApplicationStages(application._versioning.suiteId);
    const currentStages = xor(previousStages ?? [], application.stages);

    const depositStatus = applicationJourney.deposit?.status;
    const depositTransactionId = applicationJourney.deposit?.transactionId;

    const returnDuplicatedRequest = () => {
        console.info(
            // eslint-disable-next-line max-len
            `duplicated status ${notification.eventCode} received for journey ${applicationJourney._id.toHexString()}, current status ${depositStatus}`
        );

        // return 200 so we do not block the notification service
        // when receiving
        res.status(200).json({
            notificationResponse: '[accepted]',
        });
    };

    switch (notification.eventCode) {
        case EventCode.authorisation: {
            // authorisation failed
            if (notification.success === 'false') {
                if (depositTransactionId === notification.pspReference && depositStatus === PaymentStatus.Failed) {
                    returnDuplicatedRequest();

                    return;
                }

                await collections.applicationJourneys.updateOne(
                    { _id: applicationJourney._id },
                    {
                        $set: {
                            'deposit.transactionId': notification.pspReference,
                            'deposit.status': PaymentStatus.Failed,
                        },
                    }
                );

                const trail: ApplicationPaymentFailedAuditTrail = {
                    _id: new ObjectId(),
                    _date: new Date(),
                    _kind: AuditTrailKind.ApplicationPaymentFailed,
                    applicationId: application._id,
                    applicationSuiteId: application._versioning.suiteId,
                    stages: getApplicationLogStages(application, AuditTrailKind.ApplicationPaymentFailed),
                    settingId: setting._id,
                    reason: notification.reason,
                    author: { kind: AuthorKind.System },
                };

                await collections.auditTrails.insertOne(trail);

                const statusUpdates = getStatusUpdates(application, lead, ApplicationStatus.PaymentFailed);
                await collections.applications.updateOne(
                    {
                        '_versioning.suiteId': applicationJourney.applicationSuiteId,
                        '_versioning.isLatest': true,
                    },
                    { $set: statusUpdates }
                );

                break;
            }

            if (depositTransactionId === notification.pspReference && depositStatus === PaymentStatus.Success) {
                returnDuplicatedRequest();

                return;
            }

            const journey = await collections.applicationJourneys.findOneAndUpdate(
                { _id: applicationJourney._id },
                {
                    $set: {
                        'deposit.transactionId': notification.pspReference,
                        'deposit.status': PaymentStatus.Success,
                        'deposit.paymentMethod': notification?.paymentMethod?.toUpperCase(),
                    },
                },
                { returnDocument: 'after' }
            );

            const trail: ApplicationPaymentCompletedAuditTrail = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.ApplicationPaymentCompleted,
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                stages: getApplicationLogStages(application, AuditTrailKind.ApplicationPaymentCompleted),
                settingId: setting._id,
                author: { kind: AuthorKind.System },
            };

            await collections.auditTrails.insertOne(trail);

            const statusUpdates = getStatusUpdates(application, lead, ApplicationStatus.PaymentReceived);
            await collections.applications.updateOne(
                {
                    '_versioning.suiteId': applicationJourney.applicationSuiteId,
                    '_versioning.isLatest': true,
                },
                { $set: statusUpdates }
            );

            await sendConfirmationEmail(journey, application, lead, currentStages);

            await collections.applications.findOneAndUpdate(
                { '_versioning.isLatest': true, '_versioning.suiteId': application._versioning.suiteId },
                {
                    $set: {
                        bookingEmail: {
                            bookingConfirmation: true,
                        },
                    },
                }
            );

            break;
        }

        case EventCode.refund: {
            if (depositTransactionId === notification.pspReference && depositStatus === PaymentStatus.Refunded) {
                returnDuplicatedRequest();

                return;
            }

            await collections.applicationJourneys.updateOne(
                { _id: applicationJourney._id },
                {
                    $set: {
                        'deposit.transactionId': notification.pspReference,
                        'deposit.status': PaymentStatus.Refunded,
                    },
                }
            );

            const trail: ApplicationPaymentRefundedAuditTrail = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.ApplicationPaymentRefunded,
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                stages: getApplicationLogStages(application, AuditTrailKind.ApplicationPaymentRefunded),
                settingId: setting._id,
                author: { kind: AuthorKind.System },
            };

            await collections.auditTrails.insertOne(trail);

            break;
        }

        case EventCode.captureFailed: {
            if (depositTransactionId === notification.pspReference && depositStatus === PaymentStatus.Failed) {
                returnDuplicatedRequest();

                return;
            }

            await collections.applicationJourneys.updateOne(
                { _id: applicationJourney._id },
                {
                    $set: {
                        'deposit.transactionId': notification.pspReference,
                        'deposit.status': PaymentStatus.Failed,
                    },
                }
            );

            const trail: ApplicationPaymentFailedAuditTrail = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.ApplicationPaymentFailed,
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                stages: getApplicationLogStages(application, AuditTrailKind.ApplicationPaymentFailed),
                settingId: setting._id,
                reason: notification.reason,
                author: { kind: AuthorKind.System },
            };

            await collections.auditTrails.insertOne(trail);

            const statusUpdates = getStatusUpdates(application, lead, ApplicationStatus.PaymentFailed);
            await collections.applications.findOneAndUpdate(
                {
                    '_versioning.suiteId': applicationJourney.applicationSuiteId,
                    '_versioning.isLatest': true,
                },
                { $set: statusUpdates },
                { returnDocument: 'after' }
            );

            break;
        }
    }

    res.status(200).json({
        notificationResponse: '[accepted]',
    });
};

const handleGiftVoucherNotification = async (notification: NotificationRequestItem, req, res, next) => {
    const { collections } = await getDatabaseContext();

    // find session id
    const giftVoucherJourney = await collections.giftVoucherJourney.findOne({
        'deposit.session.id': notification.additionalData.checkoutSessionId,
    });

    // there's no gift voucher journey attached to this session id
    if (!giftVoucherJourney) {
        returnInvalidRequest(req, res, next);

        return;
    }

    const giftVoucher = await collections.giftVouchers.findOne({
        '_versioning.suiteId': giftVoucherJourney.giftVoucherSuiteId,
        '_versioning.isLatest': true,
    });

    // there's no gift voucher attached to this
    if (!giftVoucher) {
        returnInvalidRequest(req, res, next);

        return;
    }

    const setting = (await collections.settings.findOne({
        _id: giftVoucherJourney.deposit.settingId,
        settingId: SettingId.AdyenPayment,
    })) as AdyenPaymentSetting;

    // there's no setting attached to this
    if (!setting) {
        returnInvalidRequest(req, res, next);

        return;
    }

    switch (notification.eventCode) {
        case EventCode.authorisation: {
            // authorisation failed
            if (notification.success === 'false') {
                await collections.giftVoucherJourney.updateOne(
                    { _id: giftVoucher._id },
                    {
                        $set: {
                            'deposit.transactionId': notification.pspReference,
                            'deposit.status': PaymentStatus.Failed,
                        },
                    }
                );

                const trail: GiftVoucherPaymentFailedAuditTrail = {
                    _id: new ObjectId(),
                    _date: new Date(),
                    _kind: AuditTrailKind.GiftVoucherPaymentFailed,
                    giftVoucherId: giftVoucher._id,
                    giftVoucherSuiteId: giftVoucher._versioning.suiteId,
                    settingId: setting._id,
                    reason: notification.reason,
                    author: { kind: AuthorKind.System },
                };

                await collections.auditTrails.insertOne(trail);

                await collections.applications.updateOne(
                    {
                        '_versioning.suiteId': giftVoucherJourney.giftVoucherSuiteId,
                        '_versioning.isLatest': true,
                    },
                    { $set: { status: GiftVoucherStatus.PaymentFailed } }
                );

                break;
            }

            await collections.giftVoucherJourney.findOneAndUpdate(
                { _id: giftVoucherJourney._id },
                {
                    $set: {
                        'deposit.transactionId': notification.pspReference,
                        'deposit.status': PaymentStatus.Success,
                        'deposit.paymentMethod': notification?.paymentMethod?.toUpperCase(),
                    },
                },
                { returnDocument: 'after' }
            );

            const trail: GiftVoucherPaymentCompletedAuditTrail = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.GiftVoucherPaymentCompleted,
                giftVoucherId: giftVoucher._id,
                giftVoucherSuiteId: giftVoucher._versioning.suiteId,
                settingId: setting._id,
                author: { kind: AuthorKind.System },
            };

            await collections.auditTrails.insertOne(trail);

            await mainQueue.add({ type: 'sendGiftVoucherEmail', giftVoucherId: giftVoucher._id });
            break;
        }

        case EventCode.refund: {
            await collections.giftVoucherJourney.updateOne(
                { _id: giftVoucherJourney._id },
                {
                    $set: {
                        'deposit.transactionId': notification.pspReference,
                        'deposit.status': PaymentStatus.Refunded,
                    },
                }
            );

            const trail: GiftVoucherPaymentRefundedAuditTrail = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.GiftVoucherPaymentRefunded,
                giftVoucherId: giftVoucher._id,
                giftVoucherSuiteId: giftVoucher._versioning.suiteId,
                settingId: setting._id,
                author: { kind: AuthorKind.System },
            };

            await collections.auditTrails.insertOne(trail);

            break;
        }

        case EventCode.captureFailed: {
            await collections.giftVoucherJourney.updateOne(
                { _id: giftVoucherJourney._id },
                {
                    $set: {
                        'deposit.transactionId': notification.pspReference,
                        'deposit.status': PaymentStatus.Failed,
                    },
                }
            );

            const trail: GiftVoucherPaymentFailedAuditTrail = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.GiftVoucherPaymentFailed,
                giftVoucherId: giftVoucher._id,
                giftVoucherSuiteId: giftVoucher._versioning.suiteId,
                settingId: setting._id,
                reason: notification.reason,
                author: { kind: AuthorKind.System },
            };

            await collections.auditTrails.insertOne(trail);

            await collections.giftVouchers.findOneAndUpdate(
                {
                    '_versioning.suiteId': giftVoucherJourney.giftVoucherSuiteId,
                    '_versioning.isLatest': true,
                },
                { $set: { status: GiftVoucherStatus.PaymentFailed } },
                { returnDocument: 'after' }
            );

            break;
        }
    }

    res.status(200).json({
        notificationResponse: '[accepted]',
    });
};

const handleNotification = async (notification: NotificationRequestItem, req, res, next) => {
    switch (notification.additionalData['metadata.journey']) {
        case JourneyType.GiftVoucher: {
            await handleGiftVoucherNotification(notification, req, res, next);

            break;
        }

        // to handle application and previous data
        case JourneyType.Application:
        default: {
            await handleApplicationNotification(notification, req, res, next);

            break;
        }
    }
};

const adyenNotification: RequestHandler = async (req, res, next) => {
    try {
        const { notificationItems } = req.body as NotificationRequest;

        const [notificationItem] = notificationItems || [];
        const { NotificationRequestItem: notification } = notificationItem || {};

        // for some reason there's no notification passed
        if (!notification) {
            returnInvalidRequest(req, res, next);

            return;
        }

        // there is no session id
        if (!notification.additionalData?.checkoutSessionId) {
            // just log in console, so we preserve sentry quota
            console.info(`Notification does not contain session id: ${JSON.stringify(notification)}`);

            res.status(200).json({
                notificationResponse: '[accepted]',
            });

            return;
        }

        await handleNotification(notification, req, res, next);
    } catch (error) {
        next(error);
    }
};

export default adyenNotification;
