import * as Sentry from '@sentry/node';
import dayjs from 'dayjs';
import { RequestHandler } from 'express';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import fetch from 'node-fetch';
import qs from 'qs';
import urlJoin from 'url-join';
import countries from '../../../../app/datasets/countries';
import config from '../../../core/config';
import {
    ApplicationJourneyPaymentGateway,
    Company,
    Customer,
    ExternalLinkKind,
    GiftVoucher,
    GiftVoucherPayGatePaymentRedirectionLink,
    PayGatePaymentSetting,
} from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getCustomerEmail } from '../../../database/helpers/customers';
import { ApplicantAgreementsStepPayload } from '../../../journeys/common';
import { consumeGiftVoucherJourneyToken } from '../../../journeys/giftVoucherJourney';
import GiftVoucherJourneyContext from '../../../journeys/giftVoucherJourney/GiftVoucherJourneyContext';
import ApplicantAgreementsStep from '../../../journeys/steps/giftVoucher/ApplicantAgreementsStep';
import createLoaders from '../../../loaders';
import createI18nInstance from '../../../utils/createI18nInstance';
import getIp from '../../../utils/getIp';
import { createChecksum, getGiftVoucherPayGatePaymentContext } from './shared';
import { handleInvalidResponse } from './submitPayment';
import { InitiateRequest, InitiateResponse, RedirectRequest } from './types';

export type SubmitGiftVoucherPaymentPayload = Pick<ApplicantAgreementsStepPayload, 'agreedConsents'> & {
    // Application Journey Token
    token: string;
};

const submitToPayGatePayment = async (
    giftVoucher: GiftVoucher,
    customer: Customer,
    company: Company,
    setting: PayGatePaymentSetting,
    amount: number
) => {
    const { collections } = await getDatabaseContext();
    const loaders = createLoaders();

    const { i18n } = await createI18nInstance(giftVoucher.languageId.toHexString());
    await i18n.loadNamespaces(['common']);
    const { t } = i18n;

    const customerEmail = getCustomerEmail(t, customer);

    const alpha3Code = countries.find(({ cca2 }) => cca2 === company.countryCode)?.cca3;

    const expiresAt = dayjs().add(1, 'd').toDate();

    // create the external link
    const link: GiftVoucherPayGatePaymentRedirectionLink = {
        _id: new ObjectId(),
        _kind: ExternalLinkKind.GiftVoucherPayGatePaymentRedirection,
        deleteOnFetch: true,
        expiresAt,
        secret: nanoid(),
        data: {
            giftVoucherId: giftVoucher._id,
            endpointId: giftVoucher.endpointId,
            routerId: giftVoucher.routerId,
        },
    };

    await collections.externalLinks.insertOne(link);

    const endpoint = urlJoin(config.applicationEndpoint, `/.callback/payGatePayment/${link.secret}`);
    const languagePack = giftVoucher?.languageId ? await loaders.languagePackById.load(giftVoucher.languageId) : null;

    const data: Omit<InitiateRequest, 'CHECKSUM'> = {
        PAYGATE_ID: setting.secrets.apiKey,
        REFERENCE: giftVoucher._id.toHexString(),
        // amount is in cents so we multiply by 100
        AMOUNT: amount * 100,
        CURRENCY: setting.currency.toUpperCase(),
        RETURN_URL: endpoint,
        TRANSACTION_DATE: dayjs(new Date()).utc().format('YYYY-MM-DD HH:mm:ss'),
        LOCALE: languagePack?.code || 'en',
        COUNTRY: alpha3Code,
        EMAIL: customerEmail,
    };

    const CHECKSUM = createChecksum([...Object.values(data), setting.secrets.encryptionKey].join(''));

    const body = qs.stringify({ ...data, CHECKSUM });

    const response = await fetch('https://secure.paygate.co.za/payweb3/initiate.trans', {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body,
        method: 'POST',
    });

    if (!response.ok) {
        await handleInvalidResponse(data, response);
    }
    const responseData = qs.parse(await response.text());

    if (responseData.ERROR) {
        const errorMessage = 'Invalid response from PayGate';
        console.error(errorMessage, responseData);

        // log with sentry
        Sentry.withScope(scope => {
            scope.setContext('payGateResponse', responseData);
            Sentry.captureMessage(errorMessage);
        });
        throw new Error(errorMessage);
    }

    return responseData as InitiateResponse;
};

const submitGiftVoucherPayment: RequestHandler<unknown, unknown, SubmitGiftVoucherPaymentPayload> = async (
    req,
    res,
    next
) => {
    try {
        const { token, agreedConsents } = req.body;

        const { giftVoucherId, origin, userId } = await consumeGiftVoucherJourneyToken(token);

        // get the application
        const { collections } = await getDatabaseContext();
        const giftVoucher = await collections.giftVouchers.findOne({
            _id: giftVoucherId,
            '_versioning.isLatest': true,
        });

        if (!giftVoucher) {
            // gift voucher not found
            throw new Error('Gift Voucher not found');
        }

        const { deposit, setting, company } = await getGiftVoucherPayGatePaymentContext(giftVoucherId);

        const customer = await collections.customers.findOne({
            _id: giftVoucher.purchaserId,
        });

        const ip = getIp(req);

        const user = userId ? await collections.users.findOne({ _id: userId }) : null;

        // update payment agreements
        const context = await GiftVoucherJourneyContext.factory(giftVoucher, user, origin);
        await ApplicantAgreementsStep.updateAgreements(
            {
                agreedConsents: agreedConsents.map(consent => ({ ...consent, id: new ObjectId(consent.id) })),
                ip,
            },
            context
        );

        // submit payment to payment url
        const response = await submitToPayGatePayment(giftVoucher, customer, company, setting, deposit.amount);

        // update application journey
        await collections.giftVoucherJourney.updateOne(
            {
                giftVoucherSuiteId: giftVoucher._versioning.suiteId,
                'deposit.gateway': ApplicationJourneyPaymentGateway.PayGate,
            },
            {
                $set: {
                    'deposit.requestId': response.PAY_REQUEST_ID,
                },
            }
        );

        const redirectRequestBody: Omit<RedirectRequest, 'CHECKSUM'> = {
            PAY_REQUEST_ID: response.PAY_REQUEST_ID,
        };

        const processChecksum = createChecksum(
            [
                setting.secrets.apiKey,
                ...Object.values(redirectRequestBody),
                giftVoucher._id.toHexString(),
                setting.secrets.encryptionKey,
            ].join('')
        );

        res.status(200).json({
            checksum: processChecksum,
            payRequestId: response.PAY_REQUEST_ID,
            url: urlJoin(setting.secrets.paymentEndpoint, 'payweb3', 'process.trans'),
        });
    } catch (error) {
        next(error);
    }
};

export default submitGiftVoucherPayment;
