import * as Sentry from '@sentry/node';
import dayjs from 'dayjs';
import { RequestHandler } from 'express';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import fetch, { Response } from 'node-fetch';
import qs from 'qs';
import urlJoin from 'url-join';
import countries from '../../../../app/datasets/countries';
import config from '../../../core/config';
import {
    ApplicationJourneyPaymentGateway,
    ApplicationStage,
    Company,
    Customer,
    ExternalLinkKind,
    Lead,
    LegacyApplication,
    PayGatePaymentRedirectionLink,
    PayGatePaymentSetting,
} from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getCustomerEmail } from '../../../database/helpers/customers';
import { consumeJourneyToken } from '../../../journeys';
import { ApplicantAgreementsStepPayload } from '../../../journeys/common';
import createLoaders from '../../../loaders';
import { getApplicationIdentifier } from '../../../utils/application';
import createI18nInstance from '../../../utils/createI18nInstance';
import getIp from '../../../utils/getIp';
import { updateAgreements } from '../../porsche/endpoints/submitPayment';
import { createChecksum, getPayGatePaymentContext } from './shared';
import { InitiateRequest, InitiateResponse, RedirectRequest } from './types';

type SubmitPaymentPayload = Pick<ApplicantAgreementsStepPayload, 'agreedConsents'> & {
    // Application Journey Token
    token: string;
};

export const handleInvalidResponse = async (
    params: Omit<InitiateRequest, 'CHECKSUM'>,
    response: Response
): Promise<void> => {
    const errorMessage = 'Invalid response from Paygate';
    const isJson = response.headers.get('content-type')?.includes('application/json');
    const data = isJson ? await response.json() : null;

    console.info('paygate request', params);
    console.error(errorMessage, data);

    Sentry.withScope(scope => {
        scope.setContext('paygateRequest', params);
        scope.setContext('paygateResponse', data);
        Sentry.captureMessage(errorMessage);
    });

    throw new Error(errorMessage);
};

const submitToPayGatePayment = async (
    application: LegacyApplication,
    lead: Lead,
    customer: Customer,
    company: Company,
    setting: PayGatePaymentSetting,
    amount: number
) => {
    const { collections } = await getDatabaseContext();
    const loaders = createLoaders();

    const { i18n } = await createI18nInstance(application.languageId?.toHexString());
    await i18n.loadNamespaces(['common']);
    const { t } = i18n;

    const customerEmail = getCustomerEmail(t, customer);

    const alpha3Code = countries.find(({ cca2 }) => cca2 === company.countryCode)?.cca3;

    const expiresAt = dayjs().add(1, 'd').toDate();

    // create the external link
    const link: PayGatePaymentRedirectionLink = {
        _id: new ObjectId(),
        _kind: ExternalLinkKind.PayGatePaymentRedirection,
        deleteOnFetch: true,
        expiresAt,
        secret: nanoid(),
        data: {
            applicationId: application._id,
            endpointId: application.endpointId,
            routerId: application.routerId,
        },
    };

    await collections.externalLinks.insertOne(link);

    const endpoint = urlJoin(config.applicationEndpoint, `/.callback/payGatePayment/${link.secret}`);
    const languagePack = application?.languageId ? await loaders.languagePackById.load(application.languageId) : null;

    const identifier = getApplicationIdentifier(application, lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Insurance,
    ]);

    const data: Omit<InitiateRequest, 'CHECKSUM'> = {
        PAYGATE_ID: setting.secrets.apiKey,
        REFERENCE: identifier,
        // amount is in cents so we multiply by 100
        AMOUNT: amount * 100,
        CURRENCY: setting.currency.toUpperCase(),
        RETURN_URL: endpoint,
        TRANSACTION_DATE: dayjs(new Date()).utc().format('YYYY-MM-DD HH:mm:ss'),
        LOCALE: languagePack?.code || 'en',
        COUNTRY: alpha3Code,
        EMAIL: customerEmail,
    };

    const CHECKSUM = createChecksum([...Object.values(data), setting.secrets.encryptionKey].join(''));

    const body = qs.stringify({ ...data, CHECKSUM });

    const response = await fetch('https://secure.paygate.co.za/payweb3/initiate.trans', {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body,
        method: 'POST',
    });

    if (!response.ok) {
        await handleInvalidResponse(data, response);
    }
    const responseData = qs.parse(await response.text());

    if (responseData.ERROR) {
        const errorMessage = 'Invalid response from PayGate';
        console.error(errorMessage, responseData);

        // log with sentry
        Sentry.withScope(scope => {
            scope.setContext('payGateResponse', responseData);
            Sentry.captureMessage(errorMessage);
        });
        throw new Error(errorMessage);
    }

    return responseData as InitiateResponse;
};

const submitPayment: RequestHandler<unknown, unknown, SubmitPaymentPayload> = async (req, res, next) => {
    try {
        const { token, agreedConsents } = req.body;

        const { applicationId, origin, userId } = await consumeJourneyToken(token);

        // get the application
        const { collections } = await getDatabaseContext();
        const application = await collections.applications.findOne({
            _id: applicationId,
            '_versioning.isLatest': true,
        });

        if (!application) {
            // application not found
            throw new Error('Application not found');
        }

        const { deposit, setting, company, lead } = await getPayGatePaymentContext(applicationId);

        const customer = await collections.customers.findOne({ _id: application.applicantId });

        const ip = getIp(req);

        const user = userId ? await collections.users.findOne({ _id: userId }) : null;

        // update payment agreements
        await updateAgreements(
            { application, origin, user },
            {
                agreedConsents: agreedConsents.map(consent => ({ ...consent, id: new ObjectId(consent.id) })),
                ip,
            }
        );

        // submit payment to payment url
        const response = await submitToPayGatePayment(application, lead, customer, company, setting, deposit.amount);

        // update application journey
        await collections.applicationJourneys.updateOne(
            {
                applicationSuiteId: application._versioning.suiteId,
                'deposit.gateway': ApplicationJourneyPaymentGateway.PayGate,
            },
            {
                $set: {
                    'deposit.requestId': response.PAY_REQUEST_ID,
                },
            }
        );

        const redirectRequestBody: Omit<RedirectRequest, 'CHECKSUM'> = {
            PAY_REQUEST_ID: response.PAY_REQUEST_ID,
        };

        const processChecksum = createChecksum(
            [
                setting.secrets.apiKey,
                ...Object.values(redirectRequestBody),
                getApplicationIdentifier(application, lead, [
                    ApplicationStage.Mobility,
                    ApplicationStage.Financing,
                    ApplicationStage.Reservation,
                    ApplicationStage.Insurance,
                ]),
                setting.secrets.encryptionKey,
            ].join('')
        );

        res.status(200).json({
            checksum: processChecksum,
            payRequestId: response.PAY_REQUEST_ID,
            url: urlJoin(setting.secrets.paymentEndpoint, 'payweb3', 'process.trans'),
        });
    } catch (error) {
        next(error);
    }
};

export default submitPayment;
