/* eslint-disable max-len */
import { Dayjs } from 'dayjs';
import { TFunction } from 'i18next';
import { isEmpty } from 'lodash/fp';
import { CapAvailableApplicationType } from '../../../database';
import { AuditTrailKind } from '../../../database/documents/AuditTrail';
import { Customer } from '../../../database/documents/Customer';
import { Dealer } from '../../../database/documents/Dealer';
import { Lead } from '../../../database/documents/Lead';
import { CapSetting } from '../../../database/documents/Setting';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import createActivity, { CreateActivityParams } from '../activity/createActivity';
import { createCapSubmissionAuditTrails, formatCapDate, withRetryApiCall } from '../utils';
import { ActivityKind, ActivityStatus } from '../utils/types';

type SubmitActivityValue = {
    t: TFunction;
    activityKind?: ActivityKind;
    activityStatus?: ActivityStatus;
    application?: CapAvailableApplicationType;
    lead: Lead;
    capSetting: CapSetting;
    auth: string;
    customerData: Customer;
    dealerData: Dealer;
    customerGuid: string;
    planEndDate: Dayjs;
    campaignGuid: string;
    salespersonGuid: string;
    leadGuid: string;
    description?: string;
};

/* 
    For some of value we passed, can check the metadata API docs & Value Set Help (inside C@P API doc (partial).zip)
    https://appvantageco.sharepoint.com/:f:/s/APVCentralRepository/EluyYWHRBpZPglaiIy4CT80BVw8rhu0yzirUFEog1MLfLQ?e=dIW4JB

    Need to confirm, regarding the activity in here, like processTypeId and categoryId, will it referring certain scenario,
    or just let it be as it is?
*/

const getLocation = ({ customerData, dealerData }: { customerData: Customer; dealerData: Dealer }) => {
    const customerFields = getLocalCustomerAggregatedFields(customerData);

    if (!isEmpty(customerFields.address)) {
        return customerFields.address;
    }

    if (!isEmpty(dealerData.contact.address?.defaultValue)) {
        return dealerData.contact.address.defaultValue;
    }

    return '';
};

const getAuditTrailKind = (activityKind: ActivityKind, activityStatus: ActivityStatus) => {
    if (activityKind === ActivityKind.TestDrive) {
        return activityStatus === ActivityStatus.Planned
            ? AuditTrailKind.CapActivityPlannedTestDriveSubmitted
            : AuditTrailKind.CapActivityEndTestDriveSubmitted;
    }

    if (activityKind === ActivityKind.ShowroomVisit) {
        return activityStatus === ActivityStatus.Planned
            ? AuditTrailKind.CapActivityPlannedShowroomVisitSubmitted
            : AuditTrailKind.CapActivityCompleteShowroomVisitSubmitted;
    }

    return AuditTrailKind.CapActivitySubmitted;
};

const submitActivity = async ({
    t,
    activityKind = ActivityKind.Normal,
    application,
    capSetting,
    auth,
    dealerData,
    customerData,
    customerGuid,
    planEndDate,
    campaignGuid,
    salespersonGuid,
    leadGuid,
    lead,
    description,
    activityStatus = ActivityStatus.Planned,
}: SubmitActivityValue) => {
    const activityData = {
        customerGuid, // Business Partner Guid
        responsibleSalesPersonGuid: salespersonGuid,
        instructorGuid: '', // TO BE CONFIRMED
        planStartDate: formatCapDate(planEndDate.subtract(1, 'hours'), true),
        planEndDate: formatCapDate(planEndDate, true),
        description: description ?? '', // TO BE CONFIRMED
        vehicleGuid: '', // TO BE CONFIRMED
        campaignGuid,
        leadGuid,
        processTypeId: activityKind === ActivityKind.TestDrive ? 'YD22' : 'YD21', // Referring to AN-2508
        categoryId: 'YD5', // TO BE CONFIRMED, CURRENTLY FOLLOWING SAMPLE REQUEST BODY
        externalId: '', // TO BE CONFIRMED
        statusId: activityStatus, // E0001 for Planned, E0005 for Completed
        reasonCodeId: '', // TO BE CONFIRMED
        statusResultId: '', // TO BE CONFIRMED
        location: getLocation({ customerData, dealerData }), // Dealer / Cust location
    };

    const auditTrailKind = getAuditTrailKind(activityKind, activityStatus);

    const createActivityValue: CreateActivityParams = {
        ...capSetting,
        t,
        application,
        lead,
        activityKind,
        auth,
        data: activityData,
        auditTrailKind,
    };

    const createdActivityResult = await withRetryApiCall(() => createActivity(createActivityValue));

    if (createdActivityResult.error) {
        // Audit trails for submission error has captured inside createActivity
        return null;
    }

    await createCapSubmissionAuditTrails({
        application,
        lead,
        capActionAuditTrail: auditTrailKind,
        success: true,
        id: createdActivityResult?.d?.activityId ? createdActivityResult.d.activityId : null,
    });

    return createdActivityResult;
};

export default submitActivity;
