/* eslint-disable max-len */
import { Dayjs } from 'dayjs';
import { TFunction } from 'i18next';
import { isEmpty, uniqBy } from 'lodash/fp';
import { ApplicationJourneyAgreements } from '../../../database/documents/Applications';
import { AuditTrailKind } from '../../../database/documents/AuditTrail';
import {
    ConsentsAndDeclarationsPurpose,
    ConsentsAndDeclarationsType,
    DataField,
} from '../../../database/documents/ConsentsAndDeclarations';
import { Customer } from '../../../database/documents/Customer';
import { Lead } from '../../../database/documents/Lead';
import { CapSetting } from '../../../database/documents/Setting';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import createLoaders from '../../../loaders';
import createConsent, { CreateConsentParams } from '../consent/createConsent';
import getConsentByPartnerId, { ConsentByPartnerData } from '../consent/getConsentByPartnerId';
import { BaseConsentDataType } from '../consent/types';
import {
    consentAgreedByAgreeingDataProcessingCountries,
    createCapSubmissionAuditTrails,
    formatCapDate,
    withRetryApiCall,
} from '../utils';
import { GenericGetResult, GenericPatchResult } from '../utils/types';

export type SubmitConsentValue = {
    t: TFunction;
    lead: Lead;
    businessPartnerId: string;
    capSetting: CapSetting;
    auth: string;
    applicationJourneyAgreements?: ApplicationJourneyAgreements;
    customerData: Customer;
    countryCode: string;
    validFrom: Dayjs;
    validTo: Dayjs;
    existingBusinessPartner: boolean;
};

/* 
    For some of value we passed, can check the metadata API docs & Value Set Help (inside C@P API doc (partial).zip)
    https://appvantageco.sharepoint.com/:f:/s/APVCentralRepository/EluyYWHRBpZPglaiIy4CT80BVw8rhu0yzirUFEog1MLfLQ?e=dIW4JB

*/

/* 
    Consent channel & permission value dictionary:

    Channel :
        TDP -> Customer & Prospect Care
        INT -> Email
        FAX -> Fax/SMS
        TEL -> Telephone
        YSM -> Social Media
        LET -> Letter

    Permission :
        001 -> Accepted
        002 -> Rejected
*/

const getConsentPermissionValue = (value: boolean) => (value ? '001' : '002');

const getConsentValue = async (
    applicationJourneyAgreements: ApplicationJourneyAgreements,
    customerData: Customer,
    newConsent: boolean,
    countryCode: string
): Promise<Pick<BaseConsentDataType, 'ConsentType' | 'Channel' | 'Permission' | 'Value'>[]> => {
    const loaders = createLoaders();
    const consentChannels = [
        { ConsentType: 'CONST_CAT', Channel: 'YDP' },
        { ConsentType: 'CONST_CAT', Channel: 'YDM' },
        { ConsentType: 'COMM_CHANN', Channel: 'INT' },
        { ConsentType: 'COMM_CHANN', Channel: 'FAX' },
        { ConsentType: 'COMM_CHANN', Channel: 'TEL' },
        { ConsentType: 'COMM_CHANN', Channel: 'LET' },
        { ConsentType: 'COMM_CHANN', Channel: 'YSM' },
    ];

    const defaultConsentValue = consentChannels.map(consent => ({
        ...consent,
        Permission: '002',
        Value: '',
    }));

    if (!applicationJourneyAgreements?.agreements?.length) {
        return newConsent ? defaultConsentValue : [];
    }

    const agreedKYCAgreements = applicationJourneyAgreements.agreements.filter(
        agreement => agreement.isAgreed && agreement.purpose.includes(ConsentsAndDeclarationsPurpose.KYC)
    );

    if (!agreedKYCAgreements.length) {
        return newConsent ? defaultConsentValue : [];
    }

    const agreedMarketingConsents = (
        await Promise.all(
            agreedKYCAgreements.map(async agreement => {
                const consentDetails = await loaders.consentById.load(agreement.consentId);
                if (!consentDetails) {
                    return null;
                }

                return consentDetails._type === ConsentsAndDeclarationsType.Marketing && agreement.platformsAgreed
                    ? agreement
                    : null;
            })
        )
    ).filter(Boolean);

    const agreedDataProcessingConsent = (
        await Promise.all(
            agreedKYCAgreements.map(async agreement => {
                const consentDetails = await loaders.consentById.load(agreement.consentId);
                if (!consentDetails) {
                    return null;
                }

                return consentDetails.dataField === DataField.DataProcessing ? agreement : null;
            })
        )
    ).filter(Boolean);

    if (![...agreedMarketingConsents, ...agreedDataProcessingConsent].length) {
        return newConsent ? defaultConsentValue : [];
    }

    const dataProcessingAgreed = agreedDataProcessingConsent.length > 0;

    const consentAgreedByAgreeingDataProcessing =
        consentAgreedByAgreeingDataProcessingCountries.includes(countryCode) && dataProcessingAgreed;

    const customerFields = getLocalCustomerAggregatedFields(customerData);
    const agreedConsents = consentChannels.map(consent => {
        switch (consent.Channel) {
            case 'YDP': {
                return newConsent || dataProcessingAgreed
                    ? {
                          ...consent,
                          Permission: getConsentPermissionValue(dataProcessingAgreed),
                          Value: '',
                      }
                    : null;
            }

            case 'INT': {
                const emailAgreed = agreedMarketingConsents.some(
                    agreedConsent => !!agreedConsent.platformsAgreed?.email
                );

                return newConsent || emailAgreed || consentAgreedByAgreeingDataProcessing
                    ? {
                          ...consent,
                          Permission: getConsentPermissionValue(emailAgreed || consentAgreedByAgreeingDataProcessing),
                          Value: customerFields.email || '',
                      }
                    : null;
            }

            case 'LET': {
                const mailAgreed = agreedMarketingConsents.some(agreedConsent => !!agreedConsent.platformsAgreed?.mail);

                return newConsent || mailAgreed || consentAgreedByAgreeingDataProcessing
                    ? {
                          ...consent,
                          Permission: getConsentPermissionValue(mailAgreed || consentAgreedByAgreeingDataProcessing),
                          Value: customerFields.address || '',
                      }
                    : null;
            }

            case 'FAX': {
                const faxOrSmsAgreed = agreedMarketingConsents.some(
                    agreedConsent => !!(agreedConsent.platformsAgreed?.fax || agreedConsent.platformsAgreed?.sms)
                );

                return newConsent || faxOrSmsAgreed || consentAgreedByAgreeingDataProcessing
                    ? {
                          ...consent,
                          Permission: getConsentPermissionValue(
                              faxOrSmsAgreed || consentAgreedByAgreeingDataProcessing
                          ),
                          Value: `+${customerFields.phone?.prefix}${customerFields.phone?.value}` || '',
                      }
                    : null;
            }

            case 'TEL': {
                const telAgreed = agreedMarketingConsents.some(agreedConsent => !!agreedConsent.platformsAgreed?.phone);

                return newConsent || telAgreed || consentAgreedByAgreeingDataProcessing
                    ? {
                          ...consent,
                          Permission: getConsentPermissionValue(telAgreed || consentAgreedByAgreeingDataProcessing),
                          Value: `+${customerFields.phone?.prefix}${customerFields.phone?.value}` || '',
                      }
                    : null;
            }

            case 'YSM': {
                return newConsent || consentAgreedByAgreeingDataProcessing
                    ? {
                          ...consent,
                          Permission: getConsentPermissionValue(consentAgreedByAgreeingDataProcessing),
                          Value: '',
                      }
                    : null;
            }

            default:
                return newConsent
                    ? {
                          ...consent,
                          Permission: '002',
                          Value: '',
                      }
                    : null;
        }
    });

    return agreedConsents.filter(Boolean);
};

const submitConsent = async ({
    t,
    lead,
    businessPartnerId,
    capSetting,
    applicationJourneyAgreements,
    auth,
    customerData,
    countryCode,
    validFrom,
    validTo,
    existingBusinessPartner,
}: SubmitConsentValue) => {
    const consentValues = await getConsentValue(
        applicationJourneyAgreements,
        customerData,
        !existingBusinessPartner,
        countryCode
    );

    if (!consentValues.length) {
        return []; // Return [] instead because the application doesn't have agreement, so not consider as error
    }

    const consentData: BaseConsentDataType[] = consentValues.map(consentValue => ({
        Origin: 'Y15', // Online
        ...consentValue,
        ValidFrom: formatCapDate(validFrom),
        ValidTo: formatCapDate(validTo),
    }));

    const consentByPartnerData = await withRetryApiCall<GenericGetResult<ConsentByPartnerData>>(() =>
        getConsentByPartnerId({ ...capSetting, t, auth, businessPartnerId, consentData })
    );

    if (consentByPartnerData.error || isEmpty(consentByPartnerData.d)) {
        await createCapSubmissionAuditTrails({
            lead,
            capActionAuditTrail: AuditTrailKind.CapConsentNotFound,
            success: false,
            id: businessPartnerId,
        });

        return null;
    }

    const createConsentValues: CreateConsentParams[] = consentByPartnerData.d.results.map(consentData => ({
        t,
        lead,
        ...capSetting,
        consentGuid: consentData.recordGuid,
        auth,
        data: consentData.consentData,
    }));

    const createdConsentResults = await Promise.all(
        createConsentValues.map(createConsentValue =>
            withRetryApiCall<GenericPatchResult>(() => createConsent(createConsentValue))
        )
    );

    if (
        createdConsentResults.length &&
        createdConsentResults.every(createdConsent => !isEmpty(createdConsent?.error))
    ) {
        const errorConsent = createdConsentResults.map(createdConsent =>
            createdConsent.error ? createdConsent : null
        );

        const uniqError = uniqBy('error', errorConsent)[0].error;
        await createCapSubmissionAuditTrails({
            lead,
            capActionAuditTrail: AuditTrailKind.CapConsentSubmitted,
            success: false,
            errorMessage: uniqError,
        });

        return null;
    }

    const succeededCreatedConsent = createdConsentResults
        .map(createdConsent => createdConsent.requestId)
        .filter(Boolean);

    await createCapSubmissionAuditTrails({
        lead,
        capActionAuditTrail: AuditTrailKind.CapConsentSubmitted,
        success: succeededCreatedConsent.length > 0,
        id: businessPartnerId,
    });

    return createdConsentResults.length > 0 ? succeededCreatedConsent : null;
};

export default submitConsent;
