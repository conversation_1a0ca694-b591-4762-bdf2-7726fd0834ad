import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { isEmpty, isNull } from 'lodash/fp';
import { CapAvailableApplicationType } from '../../../database';
import { AuditTrailKind } from '../../../database/documents/AuditTrail';
import { CapSubmissionKind, LeadStatus, CapValues, Lead } from '../../../database/documents/Lead';
import { CapSetting } from '../../../database/documents/Setting';
import { User } from '../../../database/documents/User';
import getDatabaseContext from '../../../database/getDatabaseContext';
import createLoaders from '../../../loaders';
import { getPorscheCiamIdFromCustomerId } from '../../porscheId/utils';
import ppnAuth from '../ppnAuth';
import {
    createCapSubmissionAuditTrails,
    getActivityEndDate,
    getCapModuleIdFromApplicationModule,
    getSupportingValuesFromApplicationOrLead,
    getVehicleDetailsFromLead,
} from '../utils';
import { storeCapGuidsToLead, updateCapSubmissionStatus } from '../utils/storeCapValuesToApplication';
import { ActivityKind, CapSubmissionCompletionStatus } from '../utils/types';
import submitActivity from './submitActivity';
import submitBusinessPartner from './submitBusinessPartner';
import submitConsent from './submitConsent';
import submitCustomerCompetitorVehicle from './submitCustomerCompetitorVehicle';
import submitLead from './submitLead';

// Create manual resubmit mechanism based on these values
// IF the failure on BP, then resubmit BP, consent, lead & activity
// IF the failure on consent, then resubmit consent only
// IF the failure on lead, then resubmit lead & activity
// IF the failure on activity, then resubmit activity only

type ResubmitCapValues = {
    t: TFunction;
    lead: Lead;
    application?: CapAvailableApplicationType | null;
    isTestDrive: boolean;
    user: User;
};

type ResubmissionValues = ResubmitCapValues & {
    authentication: string;
    capSetting: CapSetting;
    customerCiamId?: string;
    user: User;
};

const getSucceededSubmission = (submissionSucceeded: CapSubmissionKind[]) =>
    submissionSucceeded?.length
        ? [
              submissionSucceeded.includes(CapSubmissionKind.BusinessPartner),
              submissionSucceeded.includes(CapSubmissionKind.CustomerCompetitorVehicle),
              submissionSucceeded.includes(CapSubmissionKind.Consent),
              submissionSucceeded.includes(CapSubmissionKind.Lead),
              submissionSucceeded.includes(CapSubmissionKind.Activity),
              submissionSucceeded.includes(CapSubmissionKind.TestDrive),
          ]
        : [false, false, false, false, false];

const getSubmissionStatusForTrails = (capValues: CapValues, isTestDrive?: boolean) => {
    const { businessPartnerGuid, leadGuid, submissionSucceeded, competitorVehicleGuids } = capValues;
    const [
        bpCompletion,
        customerCompetitorVehicleCompletion,
        consentCompletion,
        leadCompletion,
        activityCompletion,
        testDriveActivityCompletion,
    ] = getSucceededSubmission(submissionSucceeded);

    if (!bpCompletion) {
        return businessPartnerGuid ? AuditTrailKind.CapBPUpdated : AuditTrailKind.CapBPCreated;
    }

    if (!customerCompetitorVehicleCompletion) {
        return competitorVehicleGuids?.length
            ? AuditTrailKind.CapCompetitorVehicleUpdated
            : AuditTrailKind.CapCompetitorVehicleCreated;
    }

    if (!consentCompletion) {
        return AuditTrailKind.CapConsentSubmitted;
    }

    if (!leadCompletion) {
        return leadGuid ? AuditTrailKind.CapLeadUpdated : AuditTrailKind.CapLeadCreated;
    }

    if (!isTestDrive && !activityCompletion) {
        return AuditTrailKind.CapActivitySubmitted;
    }

    if (isTestDrive && !testDriveActivityCompletion) {
        return AuditTrailKind.CapActivityEndTestDriveSubmitted;
    }

    // Set this as default return
    return AuditTrailKind.CapBPCreated;
};

const resubmission = async ({
    t,
    application,
    isTestDrive,
    authentication,
    capSetting,
    customerCiamId,
    lead,
}: ResubmissionValues): Promise<CapSubmissionCompletionStatus> => {
    const {
        dealerData,
        customerData,
        assigneeData,
        campaignGuid,
        dealerGuid,
        salespersonGuid,
        sourceId,
        originId,
        companyData,
    } = await getSupportingValuesFromApplicationOrLead({
        t,
        application,
        lead,
        authentication,
        capSetting,
    });

    const { collections } = await getDatabaseContext();

    const dateNow = dayjs();

    const { businessPartnerGuid, businessPartnerId, leadGuid, submissionSucceeded } = lead.capValues;

    const newlySubmittedValue = {
        businessPartnerId,
        businessPartnerGuid,
        leadGuid,
    };

    const [
        bpCompletion,
        customerCompetitorVehicleCompletion,
        consentCompletion,
        leadCompletion,
        activityCompletion,
        testDriveActivityCompletion,
    ] = getSucceededSubmission(submissionSucceeded);

    const capSubmissionStatus: CapSubmissionCompletionStatus = {
        businessPartner: bpCompletion,
        customerCompetitorVehicle: customerCompetitorVehicleCompletion,
        consent: consentCompletion,
        lead: leadCompletion,
        activity: activityCompletion,
        testDrive: testDriveActivityCompletion,
    };

    if (!bpCompletion) {
        const businessPartnerData = await submitBusinessPartner({
            t,
            application,
            capSetting,
            auth: authentication,
            customerData,
            dealerData,
            assigneeData,
            existingBusinessPartnerGuid: businessPartnerGuid,
            customerCiamId,
            lead,
        });

        if (isNull(businessPartnerData)) {
            return capSubmissionStatus;
        }

        const [submittedBusinessPartnerId, submittedBusinessPartnerGuid] = businessPartnerData;

        await storeCapGuidsToLead(lead, {
            businessPartnerGuid: submittedBusinessPartnerGuid,
            businessPartnerId: submittedBusinessPartnerId,
        });

        capSubmissionStatus.businessPartner = true;
        newlySubmittedValue.businessPartnerGuid = submittedBusinessPartnerGuid;
        newlySubmittedValue.businessPartnerId = submittedBusinessPartnerId;
    }

    if (!customerCompetitorVehicleCompletion) {
        const createdCustomerCompetitorVehicle = await submitCustomerCompetitorVehicle({
            lead,
            t,
            capSetting,
            auth: authentication,
            customerGuid: newlySubmittedValue.businessPartnerGuid,
            customerId: newlySubmittedValue.businessPartnerId,
        });

        if (!isNull(createdCustomerCompetitorVehicle)) {
            capSubmissionStatus.customerCompetitorVehicle = true;
            await storeCapGuidsToLead(lead, {
                competitorVehicleGuids: createdCustomerCompetitorVehicle,
            });
        }
    }

    if (!consentCompletion) {
        const createdConsentDetails = await submitConsent({
            t,
            lead,
            businessPartnerId: newlySubmittedValue.businessPartnerId,
            capSetting,
            countryCode: companyData.countryCode,
            auth: authentication,
            applicationJourneyAgreements: lead.customerAgreements,
            existingBusinessPartner: !!newlySubmittedValue.businessPartnerGuid,
            customerData,
            validFrom: dateNow,
            validTo: dayjs().add(6, 'month'),
        });

        if (!isNull(createdConsentDetails)) {
            capSubmissionStatus.consent = true;
            await storeCapGuidsToLead(lead, {
                consentGuids: createdConsentDetails,
            });
        }
    }

    if (!leadCompletion) {
        if (isEmpty(campaignGuid)) {
            return capSubmissionStatus;
        }
        const vehicleDetailsFromLead = await getVehicleDetailsFromLead(lead);

        const submittedLeadDetails = await submitLead({
            t,
            application,
            capSetting,
            auth: authentication,
            customerGuid: newlySubmittedValue.businessPartnerGuid,
            countryCode: companyData.countryCode,
            existingLeadGuid: leadGuid,
            interestVehicle: vehicleDetailsFromLead,
            intentionTime: dateNow,
            campaignGuid,
            dealerGuid,
            salespersonGuid,
            sourceId,
            originId,
            lead,
        });

        if (submittedLeadDetails?.leadGuid) {
            await storeCapGuidsToLead(lead, {
                leadGuid: submittedLeadDetails.leadGuid,
                leadId: submittedLeadDetails.leadId,
            });

            capSubmissionStatus.lead = true;
            newlySubmittedValue.leadGuid = submittedLeadDetails.leadGuid;
            await collections.leads.findOneAndUpdate({ _id: lead._id }, { $set: { isLead: true } });
        }
    }

    if (
        newlySubmittedValue.leadGuid &&
        ((!isTestDrive && !activityCompletion) || (isTestDrive && !testDriveActivityCompletion))
    ) {
        const createdActivityDetails = await submitActivity({
            activityKind: isTestDrive ? ActivityKind.TestDrive : undefined,
            application,
            lead,
            t,
            capSetting,
            auth: authentication,
            dealerData,
            customerData,
            customerGuid: newlySubmittedValue.businessPartnerGuid,
            planEndDate: getActivityEndDate(companyData.countryCode, application),
            campaignGuid,
            leadGuid: newlySubmittedValue.leadGuid,
            salespersonGuid,
        });

        if (createdActivityDetails) {
            await storeCapGuidsToLead(lead, {
                activityGuid: createdActivityDetails.d?.activityGuid,
            });

            if (isTestDrive) {
                capSubmissionStatus.testDrive = true;
            } else {
                capSubmissionStatus.activity = true;
            }
        }
    }

    return capSubmissionStatus;
};

const resubmitCap = async ({ application, lead, isTestDrive, user, t }: ResubmitCapValues) => {
    if (!lead.capValues) {
        return;
    }

    const loaders = createLoaders();

    const [capModuleId, customerCiamId] = await Promise.all([
        getCapModuleIdFromApplicationModule(lead),
        getPorscheCiamIdFromCustomerId(lead.customerId),
    ]);

    const capSetting = await loaders.capSettingByModuleId.load(capModuleId);

    await updateCapSubmissionStatus({
        application,
        lead,
        status: LeadStatus.SubmittingToCap,
        user,
        ignoreCapAuditTrail: true,
    });

    const authentication = await ppnAuth(capSetting);
    if (authentication.error) {
        const errorMessage = t('auditTrails:application.defaultCapError.authFailed');

        await createCapSubmissionAuditTrails({
            application,
            lead,
            capActionAuditTrail: getSubmissionStatusForTrails(lead.capValues),
            success: false,
            errorMessage,
        });

        console.error({
            message: errorMessage,
            data: {
                application: application._id,
                moduleId: capModuleId,
                capSettingId: capSetting._id,
            },
        });

        return;
    }

    const resubmissionResult = await resubmission({
        t,
        authentication: authentication.access_token,
        capSetting,
        application,
        lead,
        user,
        isTestDrive,
        customerCiamId,
    });

    const isAllSubmissionFailed = Object.keys(resubmissionResult)
        .filter(key => (isTestDrive ? key !== 'activity' : key !== 'testDrive'))
        .map(key => resubmissionResult[key])
        .every(status => status === false);

    if (isAllSubmissionFailed) {
        await updateCapSubmissionStatus({
            application,
            lead,
            status: LeadStatus.SubmissionFailed,
            user,
        });

        return;
    }

    const hasFailedSubmission = Object.keys(resubmissionResult)
        .filter(key => (isTestDrive ? key !== 'activity' : key !== 'testDrive'))
        .map(key => resubmissionResult[key])
        .some(status => status === false);

    if (hasFailedSubmission) {
        if (!resubmissionResult.lead) {
            await updateCapSubmissionStatus({
                application,
                lead,
                status: LeadStatus.SubmissionFailed,
                user,
            });
        } else {
            await updateCapSubmissionStatus({
                application,
                lead,
                status: LeadStatus.SubmittedWithError,
                user,
                capSubmissionCompletionStatus: resubmissionResult,
            });
        }
    } else {
        await updateCapSubmissionStatus({
            application,
            lead,
            status: LeadStatus.SubmittedToCap,
            user,
        });
    }
};

export default resubmitCap;
