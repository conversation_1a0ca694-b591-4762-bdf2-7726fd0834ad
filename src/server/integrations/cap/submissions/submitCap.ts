/* eslint-disable max-len */
import * as Sentry from '@sentry/node';
import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { isEmpty, isNull } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { CapAvailableApplicationType } from '../../../database';
import { AuditTrailKind } from '../../../database/documents/AuditTrail';
import { Lead, LeadStatus } from '../../../database/documents/Lead';
import { User } from '../../../database/documents/User';
import getDatabaseContext from '../../../database/getDatabaseContext';
import createLoaders from '../../../loaders';
import { mainQueue } from '../../../queues/mainQueue';
import ppnAuth from '../ppnAuth';
import {
    createCapSubmissionAuditTrails,
    getActivityEndDate,
    getCapPrequalifyStatus,
    getSupportingValuesFromApplicationOrLead,
    getVehicleDetailsFromApplication,
    getVehicleDetailsFromLead,
    isAbleToSubmitCapCheck,
    isApplicationEndpointApplicable,
    storeCapGuidsToLead,
    updateCapSubmissionStatus,
} from '../utils';
import { ActivityKind, ActivityStatus, CapSubmissionCompletionStatus, ExistingCapValues } from '../utils/types';
import submitActivity from './submitActivity';
import submitBusinessPartner from './submitBusinessPartner';
import submitConsent from './submitConsent';
import submitCustomerCompetitorVehicle from './submitCustomerCompetitorVehicle';
import submitCustomerHobbies from './submitCustomerHobbies';
import submitLead from './submitLead';

// Applying for every services (Business Partner, Lead, Activity & Consent)
// For details submission flows, check -> https://miro.com/app/board/uXjVMu9aAyQ=/

type SubmitCapType = {
    t: TFunction;
    application?: CapAvailableApplicationType | null;
    lead: Lead;
    capModuleId: ObjectId;
    activityKind?: ActivityKind;
    activityStatus?: ActivityStatus;
    existingCapValues?: ExistingCapValues;
    isQualified?: boolean;
    user?: User;
    customerCiamId?: string;
    isLeadQualification?: boolean;
};

const submitCap = async (capSubmissionValues: SubmitCapType) => {
    const {
        application,
        t,
        capModuleId,
        existingCapValues,
        isQualified,
        user,
        customerCiamId,
        lead,
        isLeadQualification,
        activityKind,
        activityStatus,
    } = capSubmissionValues;
    try {
        const loaders = createLoaders();
        const { collections } = await getDatabaseContext();
        const [capSetting, isEndpointApplicable] = await Promise.all([
            loaders.capSettingByModuleId.load(capModuleId),
            application ? isApplicationEndpointApplicable(application) : true,
        ]);
        const capSubmissionStatus: CapSubmissionCompletionStatus = {
            businessPartner: false,
            customerCompetitorVehicle: false,
            lead: false,
            activity: false,
            consent: false,
            testDrive: false,
        };

        const isApplicationAbleToSubmitCap = await isAbleToSubmitCapCheck({
            loaders,
            lead,
            application,
            isEndpointApplicable,
            isLeadQualified: isQualified,
        });

        if (isApplicationAbleToSubmitCap) {
            updateCapSubmissionStatus({
                application,
                lead,
                status: LeadStatus.SubmittingToCap,
                user,
                ignoreCapAuditTrail: true,
            });

            const authentication = await ppnAuth(capSetting);
            if (authentication.error) {
                const errorMessage = t('auditTrails:application.defaultCapError.authFailed');
                await createCapSubmissionAuditTrails({
                    application,
                    lead,
                    capActionAuditTrail: existingCapValues?.businessPartnerGuid
                        ? AuditTrailKind.CapBPUpdated
                        : AuditTrailKind.CapBPCreated,
                    success: false,
                    errorMessage,
                });

                console.error({
                    message: errorMessage,
                    data: {
                        leadId: lead._id,
                        application: application?._id,
                        moduleId: capModuleId,
                        capSettingId: capSetting._id,
                    },
                });

                updateCapSubmissionStatus({
                    application,
                    lead,
                    status: LeadStatus.SubmissionFailed,
                    user,
                });
            } else {
                const {
                    dealerData,
                    customerData,
                    assigneeData,
                    companyData,
                    campaignGuid,
                    dealerGuid,
                    salespersonGuid,
                    sourceId,
                    originId,
                } = await getSupportingValuesFromApplicationOrLead({
                    t,
                    application,
                    lead,
                    authentication: authentication.access_token,
                    capSetting,
                });

                await storeCapGuidsToLead(lead, {
                    campaignGuid,
                    dealerGuid,
                    salespersonGuid,
                    salesPersonId: assigneeData?.alias,
                });

                const businessPartnerData = await submitBusinessPartner({
                    t,
                    application,
                    lead,
                    capSetting,
                    auth: authentication.access_token,
                    customerData,
                    dealerData,
                    assigneeData,
                    existingBusinessPartnerGuid: existingCapValues?.businessPartnerGuid,
                    customerCiamId,
                });

                if (isNull(businessPartnerData)) {
                    updateCapSubmissionStatus({
                        application,
                        lead,
                        status: LeadStatus.SubmissionFailed,
                        user,
                    });

                    return;
                }

                if (businessPartnerData) {
                    /* 
                        dateNow & dateTo is being use for the timerange value when submitting:
                         - Consent (validFrom & validTo) -> TBC
                         - Lead (intentionTime) -> TBC
                         - Activity (planStartDate & planEndDate) -> Following appointment date
                        
                         For the dateTo value, require further discussion with PM/PO about the how the value will be or from what it'll referenced
                         By default put it 6 months
                    */
                    capSubmissionStatus.businessPartner = true;

                    const dateNow = dayjs();

                    const [businessPartnerId, businessPartnerGuid] = businessPartnerData;

                    await storeCapGuidsToLead(lead, {
                        businessPartnerGuid,
                        businessPartnerId,
                    });

                    await submitCustomerHobbies({
                        t,
                        application,
                        lead,
                        auth: authentication.access_token,
                        capSetting,
                        customerGuid: businessPartnerGuid,
                        customerData,
                        companyData,
                    });

                    const createdCustomerCompetitorVehicle = await submitCustomerCompetitorVehicle({
                        lead,
                        t,
                        capSetting,
                        auth: authentication.access_token,
                        customerGuid: businessPartnerGuid,
                        customerId: businessPartnerId,
                    });

                    if (!isNull(createdCustomerCompetitorVehicle)) {
                        capSubmissionStatus.customerCompetitorVehicle = true;
                        await storeCapGuidsToLead(lead, {
                            competitorVehicleGuids: createdCustomerCompetitorVehicle,
                        });
                    }

                    const createdConsentDetails = await submitConsent({
                        lead,
                        t,
                        businessPartnerId,
                        capSetting,
                        auth: authentication.access_token,
                        applicationJourneyAgreements: lead?.customerAgreements,
                        existingBusinessPartner: !!existingCapValues?.businessPartnerGuid,
                        customerData,
                        countryCode: companyData.countryCode,
                        validFrom: dateNow,
                        validTo: dayjs().add(6, 'month'),
                    });

                    if (!isNull(createdConsentDetails)) {
                        capSubmissionStatus.consent = true;
                        await storeCapGuidsToLead(lead, {
                            consentGuids: createdConsentDetails,
                        });
                    }

                    if (!isEmpty(campaignGuid)) {
                        // Need to submit first before submit the activity
                        // Because if no lead created, it'll have "There's no lead to create follow up" error
                        const vehicleDetailsFromApplication = application
                            ? await getVehicleDetailsFromApplication(application)
                            : await getVehicleDetailsFromLead(lead);

                        const submittedLeadDetails = await submitLead({
                            application,
                            lead,
                            t,
                            capSetting,
                            auth: authentication.access_token,
                            customerGuid: businessPartnerGuid,
                            countryCode: companyData.countryCode,
                            existingLeadGuid: existingCapValues?.leadGuid,
                            interestVehicle: vehicleDetailsFromApplication,
                            intentionTime: dateNow,
                            campaignGuid,
                            dealerGuid,
                            salespersonGuid,
                            sourceId,
                            originId,
                        });

                        if (submittedLeadDetails?.leadGuid) {
                            await storeCapGuidsToLead(lead, {
                                leadGuid: submittedLeadDetails.leadGuid,
                                leadId: submittedLeadDetails.leadId,
                            });
                            capSubmissionStatus.lead = true;

                            await collections.leads.findOneAndUpdate({ _id: lead._id }, { $set: { isLead: true } });

                            const createdActivityDetails = await submitActivity({
                                activityKind,
                                activityStatus,
                                application,
                                lead,
                                t,
                                capSetting,
                                auth: authentication.access_token,
                                dealerData,
                                customerData,
                                customerGuid: businessPartnerGuid,
                                planEndDate: getActivityEndDate(companyData.countryCode, application),
                                campaignGuid,
                                leadGuid: submittedLeadDetails.leadGuid,
                                salespersonGuid,
                            });

                            if (createdActivityDetails) {
                                if (activityKind === ActivityKind.TestDrive) {
                                    await storeCapGuidsToLead(lead, {
                                        testDriveActivityGuid: createdActivityDetails.d?.activityGuid,
                                        testDriveId: createdActivityDetails.d?.activityId,
                                    });
                                    capSubmissionStatus.testDrive = true;
                                } else if (activityKind === ActivityKind.ShowroomVisit) {
                                    await storeCapGuidsToLead(lead, {
                                        showroomVisitActivityGuid: createdActivityDetails.d?.activityGuid,
                                        showroomVisitId: createdActivityDetails.d?.activityId,
                                    });
                                    capSubmissionStatus.activity = true;
                                } else {
                                    await storeCapGuidsToLead(lead, {
                                        activityGuid: createdActivityDetails.d.activityGuid,
                                    });
                                    capSubmissionStatus.activity = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        // If application not able to submit to C@P now because it's require to be qualified
        const shouldPrequalify = await getCapPrequalifyStatus(lead);
        if (shouldPrequalify && !isQualified) {
            return;
        }

        const isAllSubmissionFailed = Object.keys(capSubmissionStatus)
            .filter(key => (activityKind === ActivityKind.TestDrive ? key !== 'activity' : key !== 'testDrive'))
            .map(key => capSubmissionStatus[key])
            .every(status => status === false);

        if (isAllSubmissionFailed) {
            await updateCapSubmissionStatus({
                application,
                lead,
                status: LeadStatus.SubmissionFailed,
                user,
            });

            return;
        }

        const hasFailedSubmission = Object.keys(capSubmissionStatus)
            .filter(key => (activityKind === ActivityKind.TestDrive ? key !== 'activity' : key !== 'testDrive'))
            .map(key => capSubmissionStatus[key])
            .some(status => status === false);

        if (hasFailedSubmission) {
            if (!capSubmissionStatus.lead) {
                await updateCapSubmissionStatus({
                    application,
                    lead,
                    status: LeadStatus.SubmissionFailed,
                    user,
                });
            } else {
                await updateCapSubmissionStatus({
                    application,
                    lead,
                    status: LeadStatus.SubmittedWithError,
                    user,
                    capSubmissionCompletionStatus: capSubmissionStatus,
                });
            }
        } else {
            await updateCapSubmissionStatus({
                application,
                lead,
                status: LeadStatus.SubmittedToCap,
                user,
                ignoreCapAuditTrail: isLeadQualification,
            });
        }
    } catch (error) {
        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('api', 'c@p');
            scope.setContext('Error when submitting', { application, capModuleId });
            Sentry.captureException(error);
        });

        // throw error
        throw new Error('Error when submitting to C@P');
    } finally {
        mainQueue.add({
            type: 'onCapSubmitted',
            leadId: lead._id,
            applicationId: application?._id,
        });
    }
};

export default submitCap;
