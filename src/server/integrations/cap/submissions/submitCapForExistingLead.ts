import * as Sentry from '@sentry/node';
import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { isNull } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { CapAvailableApplicationType } from '../../../database/documents/Applications';
import { AuditTrailKind } from '../../../database/documents/AuditTrail';
import { Lead } from '../../../database/documents/Lead';
import { User } from '../../../database/documents/User';
import createLoaders from '../../../loaders';
import ppnAuth from '../ppnAuth';
import {
    createCapSubmissionAuditTrails,
    getSupportingValuesFromApplicationOrLead,
    getVehicleDetailsFromApplication,
    getVehicleDetailsFromLead,
} from '../utils';
import { ExistingCapValues } from '../utils/types';
import submitBusinessPartner from './submitBusinessPartner';
import submitCustomerHobbies from './submitCustomerHobbies';
import submitLead from './submitLead';

type SubmitCapType = {
    t: TFunction;
    application?: CapAvailableApplicationType | null;
    lead: Lead;
    capModuleId: ObjectId;
    existingCapValues?: ExistingCapValues;
    user?: User;
    customerCiamId?: string;
};

/**
 * Updates only customer KYC and vehicle interest in CAP
 * Doesn't create a lead or additional data (activity, consent, etc.)
 */
const submitCapForExistingLead = async (capSubmissionValues: SubmitCapType) => {
    const { application, t, capModuleId, existingCapValues, customerCiamId, lead } = capSubmissionValues;

    try {
        const loaders = createLoaders();
        const capSetting = await loaders.capSettingByModuleId.load(capModuleId);

        const authentication = await ppnAuth(capSetting);
        if (authentication.error) {
            const errorMessage = t('auditTrails:application.defaultCapError.authFailed');
            await createCapSubmissionAuditTrails({
                application,
                lead,
                capActionAuditTrail: existingCapValues?.businessPartnerGuid
                    ? AuditTrailKind.CapBPUpdated
                    : AuditTrailKind.CapBPCreated,
                success: false,
                errorMessage,
            });

            console.error({
                message: errorMessage,
                data: {
                    leadId: lead._id,
                    application: application?._id,
                    moduleId: capModuleId,
                    capSettingId: capSetting._id,
                },
            });

            return;
        }

        const {
            dealerData,
            customerData,
            assigneeData,
            companyData,
            campaignGuid,
            dealerGuid,
            salespersonGuid,
            sourceId,
            originId,
        } = await getSupportingValuesFromApplicationOrLead({
            t,
            application,
            lead,
            authentication: authentication.access_token,
            capSetting,
        });

        const businessPartnerData = await submitBusinessPartner({
            t,
            application,
            lead,
            capSetting,
            auth: authentication.access_token,
            customerData,
            dealerData,
            assigneeData,
            existingBusinessPartnerGuid: existingCapValues?.businessPartnerGuid,
            customerCiamId,
        });

        if (isNull(businessPartnerData)) {
            return;
        }

        const [, businessPartnerGuid] = businessPartnerData;

        await submitCustomerHobbies({
            t,
            application,
            lead,
            auth: authentication.access_token,
            capSetting,
            customerGuid: businessPartnerGuid,
            customerData,
            companyData,
        });

        if (existingCapValues?.leadGuid) {
            const vehicleDetailsFromApplication = application
                ? await getVehicleDetailsFromApplication(application)
                : await getVehicleDetailsFromLead(lead);

            await submitLead({
                application,
                lead,
                t,
                capSetting,
                auth: authentication.access_token,
                customerGuid: businessPartnerGuid,
                countryCode: companyData.countryCode,
                existingLeadGuid: existingCapValues.leadGuid,
                interestVehicle: vehicleDetailsFromApplication,
                intentionTime: dayjs(),
                campaignGuid: lead.capValues?.campaignGuid || campaignGuid,
                dealerGuid,
                salespersonGuid,
                sourceId,
                originId,
            });
        }
    } catch (error) {
        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('api', 'c@p');
            scope.setContext('Error when submitting', { application, capModuleId });
            Sentry.captureException(error);
        });

        throw new Error('Error when submitting to C@P');
    }
};

export default submitCapForExistingLead;
