import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { isEmpty, omit } from 'lodash/fp';
import { CapAvailableApplicationType } from '../../../database';
import { AuditTrailKind } from '../../../database/documents/AuditTrail';
import { Customer, CustomerKind } from '../../../database/documents/Customer';
import { Dealer } from '../../../database/documents/Dealer';
import { Lead } from '../../../database/documents/Lead';
import { CapSetting } from '../../../database/documents/Setting';
import { User } from '../../../database/documents/User';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import createLoaders from '../../../loaders';
import {
    updateBusinessPartnerDemographics,
    createBusinessPartner,
    searchBusinessPartner,
    updateBusinessPartner,
    updateBusinessPartnerCommMail,
    getBusinessPartnerAddress,
    updateBusinessPartnerAddress,
    updateBusinessPartnerComm,
} from '../businessPartner';
import getBusinessPartnerFromCiamId from '../businessPartner/getBusinessPartnerFromCiamId';
import { BPDemographics, BusinessPartnerResData, CreateBusinessPartnerData } from '../businessPartner/types';
import {
    getCapTitleCode,
    getMaritalStatusCode,
    getCountryCodeWithFallback,
    formatCapDate,
    getCustomerLastName,
    getGenderCode,
    getRegionCode,
    getBpComm,
    getCompanyName2,
    createCapSubmissionAuditTrails,
    withRetryApiCall,
    updateLeadAssigneeId,
    getJobTitleThCapCode,
} from '../utils';
import { GenericGetResult } from '../utils/types';

type SubmitBusinessPartnerValue = {
    t: TFunction;
    lead: Lead;
    application: CapAvailableApplicationType;
    capSetting: CapSetting;
    auth: string;
    customerData: Customer;
    dealerData: Dealer;
    assigneeData: User;
    existingBusinessPartnerGuid?: string;
    customerCiamId?: string;
};

const extractBusinessPartnerResData = (
    businessPartnerResData: Omit<GenericGetResult<BusinessPartnerResData>, 'error'>
) => {
    const {
        d: { results: businessPartnerDetails },
    } = businessPartnerResData;

    const [currentBusinessPartnerData] = businessPartnerDetails;

    return currentBusinessPartnerData;
};

const submitBusinessPartner = async ({
    t,
    application,
    capSetting,
    auth,
    customerData,
    dealerData,
    assigneeData,
    existingBusinessPartnerGuid,
    customerCiamId,
    lead,
}: SubmitBusinessPartnerValue): Promise<string[] | null> => {
    const loaders = createLoaders();
    const customerFields = getLocalCustomerAggregatedFields(customerData);
    const companyData = await loaders.companyById.load(dealerData.companyId);
    const credsParams = {
        ...capSetting,
        auth,
    };

    const businessPartnerFromCiamId =
        customerCiamId && dealerData?.integrationDetails?.dealerCode
            ? await getBusinessPartnerFromCiamId({
                  capSetting,
                  auth,
                  t,
                  customerCiamId,
                  email: customerFields.email,
                  dealer: dealerData?.integrationDetails?.dealerCode,
              })
            : null;

    const customerCountryCode = getCountryCodeWithFallback(customerFields.country, companyData.countryCode);
    const customerRegion = customerFields.region ? getRegionCode(customerCountryCode, customerFields.region) : '';
    const customerTitle = !isEmpty(customerFields.nonBinaryTitle)
        ? customerFields.nonBinaryTitle
        : customerFields.title;

    const businessPartnerSubmissionValue: CreateBusinessPartnerData = {
        IsCompany: customerData._kind === CustomerKind.Corporate,
        Title: getCapTitleCode(customerTitle),
        CustomerStatus: 'X',
        NameFirst: customerFields.firstName ?? '',
        NameLast: getCustomerLastName(customerFields),
        NameMiddle: customerFields.firstNameJapan ?? '',
        NameLastOther: customerFields.lastNameJapan ?? '',
        PreferredCommunication: '',
        PreferredCommunicationMethodTxt: '',
        CorrespondenceLang: capSetting.capLanguage,
        AuthGroup: capSetting.capAuthGroup,
        Language: capSetting.capLanguage,
        Source: capSetting.capSource,
        PagImporterId: capSetting.capPagImporterId,
        Occupation: getJobTitleThCapCode(customerFields.jobTitleTh, customerCountryCode),
        ResponsibleSalesDealerId: dealerData?.integrationDetails?.dealerCode,
        ResponsibleServiceDealerId: dealerData?.integrationDetails?.dealerCode,
        ResponsibleSalesPersonId: assigneeData?.alias ?? '',
        ResponsibleServicePersonId: assigneeData?.alias ?? '',
        CompanyName1: customerFields.companyName ?? '',
        BusinessTitle: customerFields.businessTitle ?? '',
        ...(customerFields.birthday ? { DateOfBirth: formatCapDate(dayjs(customerFields.birthday)) } : {}),
        ...(customerCiamId ? { CiamId: customerCiamId } : {}),
        BpData_To_BpAddress: [
            {
                UndeliverableReason: '',
                AddressId: '',
                FormattedAddr: '',
                District: customerFields.district ?? '',
                NameCo: '',
                CompanyName1: customerFields.companyName ?? '',
                CompanyName2: getCompanyName2(customerFields),
                Street: customerFields.address ?? '',
                StrSuppl1: '',
                StrSuppl2: '',
                StrSuppl3: '',
                HouseNum1: customerFields.unitNumber ?? '',
                HouseNum2: '',
                PostCode1: customerFields.postalCode,
                PostCode2: '',
                City1: customerFields.city ?? '',
                Country: customerCountryCode,
                Region: customerRegion,
                Building: '',
                Roomnumber: '',
                Floor: '',
                Location: '',
                HomeCity: '',
                PoBox: '',
                PoBoxLoc: '',
                AdrKind: 'XXDEFAULT',
                AddressType: 'H',
            },
        ],
        BpData_To_BpComm: getBpComm(customerFields, companyData),
        BpData_To_BpCommMail: [
            {
                SmtpAddr: customerFields.email ?? '',
                AddressType: 'H',
            },
        ],
    };

    const customerGender = !isEmpty(customerFields.nonBinaryGender)
        ? customerFields.nonBinaryGender
        : customerFields.gender;

    const bpDataToBpDemographics: BPDemographics = {
        MaritalStatus: customerFields.maritalStatus ? getMaritalStatusCode(customerFields.maritalStatus) : '',
        Sex: customerFields.gender ? getGenderCode(customerGender) : '',
    };

    if (!existingBusinessPartnerGuid && !businessPartnerFromCiamId) {
        const createBusinessPartnerValue = {
            t,
            application,
            lead,
            ...credsParams,
            data: businessPartnerSubmissionValue,
        };

        const createdBusinessPartnerRes = await withRetryApiCall(() =>
            createBusinessPartner(createBusinessPartnerValue)
        );

        if (createdBusinessPartnerRes.error) {
            // Audit trails for submission error has captured inside createBusinessPartner
            return null;
        }

        const businessPartnerId = createdBusinessPartnerRes.d.BusinessPartnerId;

        await updateBusinessPartnerDemographics({
            ...credsParams,
            businessPartnerId,
            data: bpDataToBpDemographics,
        });

        await createCapSubmissionAuditTrails({
            application,
            lead,
            capActionAuditTrail: AuditTrailKind.CapBPCreated,
            success: true,
            id: createdBusinessPartnerRes?.d?.BusinessPartnerId,
        });

        return [createdBusinessPartnerRes.d.BusinessPartnerId, createdBusinessPartnerRes.d.BusinessPartnerGuid];
    }

    const businessPartnerDetailRes = existingBusinessPartnerGuid
        ? await withRetryApiCall(() =>
              searchBusinessPartner({
                  ...credsParams,
                  t,
                  businessPartnerGuid: existingBusinessPartnerGuid,
              })
          )
        : null;

    if (
        (businessPartnerDetailRes?.error || !businessPartnerDetailRes?.d?.results?.length) &&
        !businessPartnerFromCiamId
    ) {
        await createCapSubmissionAuditTrails({
            application,
            lead,
            capActionAuditTrail: AuditTrailKind.CapBPSearchFailed,
            id: lead.capValues.businessPartnerId,
            success: false,
            errorMessage:
                businessPartnerDetailRes?.error ||
                t('auditTrails:application.defaultCapError.businessPartnerDetailsNotFound'),
        });

        return null;
    }

    const existingBusinessPartner = businessPartnerDetailRes?.d?.results?.length
        ? extractBusinessPartnerResData(businessPartnerDetailRes)
        : businessPartnerFromCiamId;

    const { BusinessPartnerId, BusinessPartnerGuid, ResponsibleSalesPersonId, ResponsibleServicePersonId } =
        existingBusinessPartner;

    const basicBPParams = {
        ...credsParams,
        t,
        application,
        businessPartnerId: BusinessPartnerId,
    };

    const updateBusinessPartnerValue = {
        ...basicBPParams,
        value: {
            ...omit(
                [
                    'ResponsibleSalesPersonId',
                    'ResponsibleServicePersonId',
                    'BpData_To_BpAddress',
                    'BpData_To_BpComm',
                    'BpData_To_BpCommMail',
                    'BpData_To_BpDemographics',
                ],
                businessPartnerSubmissionValue
            ),

            // If current BP already have responsible salesperson, then use existing
            // Else use the new one from lead's assignee
            ResponsibleSalesPersonId: !isEmpty(ResponsibleSalesPersonId)
                ? ResponsibleSalesPersonId
                : businessPartnerSubmissionValue.ResponsibleSalesPersonId,
            ResponsibleServicePersonId: !isEmpty(ResponsibleServicePersonId)
                ? ResponsibleSalesPersonId
                : businessPartnerSubmissionValue.ResponsibleServicePersonId,
        },
    };

    const updateBusinessPartnerRes = await withRetryApiCall(() => updateBusinessPartner(updateBusinessPartnerValue));

    if (updateBusinessPartnerRes.error) {
        // Audit trails for submission error has captured inside updateBusinessPartner
        return null;
    }

    // Update application assigneeId if we have existing responsible salesperson from BP
    if (!isEmpty(ResponsibleSalesPersonId)) {
        await updateLeadAssigneeId(lead, ResponsibleSalesPersonId);
    }

    await updateBusinessPartnerCommMail({
        ...basicBPParams,
        data: businessPartnerSubmissionValue.BpData_To_BpCommMail[0],
    });

    await updateBusinessPartnerDemographics({
        ...basicBPParams,
        data: bpDataToBpDemographics,
    });

    const businessPartnerAddress = await getBusinessPartnerAddress(basicBPParams);

    if (businessPartnerAddress?.d?.results?.length) {
        const {
            d: { results: businessPartnerAddressDetails },
        } = businessPartnerAddress;

        const [businessPartnerAddressData] = businessPartnerAddressDetails;
        const { AddressId } = businessPartnerAddressData;

        await updateBusinessPartnerAddress({
            ...basicBPParams,
            addressId: AddressId,
            data: businessPartnerSubmissionValue.BpData_To_BpAddress[0],
        });

        await updateBusinessPartnerComm({
            ...basicBPParams,
            addressId: AddressId,
            data: businessPartnerSubmissionValue.BpData_To_BpComm[0],
        });
    }

    await createCapSubmissionAuditTrails({
        application,
        lead,
        capActionAuditTrail: AuditTrailKind.CapBPUpdated,
        success: true,
        id: BusinessPartnerId,
    });

    return [BusinessPartnerId, BusinessPartnerGuid];
};

export default submitBusinessPartner;
