import dayjs from 'dayjs';
import type { TFunction } from 'i18next';
import { map, uniq, flow, differenceBy } from 'lodash/fp';
import {
    CAP_HOBBY_ATTRIBUTE_CODE,
    CAP_HOBBY_SUB_ATTRIBUTE_CODE,
    CAP_PASSION_ATTRIBUTE_CODE,
} from '../../../../shared/capHobbies';
import { CapAvailableApplicationType } from '../../../database/documents/Applications';
import { AuditTrailKind } from '../../../database/documents/AuditTrail';
import type { Company } from '../../../database/documents/Company';
import type { Customer } from '../../../database/documents/Customer';
import type { Lead } from '../../../database/documents/Lead';
import type { CapSetting } from '../../../database/documents/Setting';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import {
    type CreateCustomerAttributeData,
    createCustomerAttribute,
    updateCustomerAttribute,
    deleteCustomerAttribute,
} from '../customerAttribute';
import { getCapHobby, createCapSubmissionAuditTrails, withRetryApiCall, formatCapDate } from '../utils';
import { getCustomerExistingHobbies } from '../utils/getExistingHobbies';

const ENABLED_VALID_TO = formatCapDate(dayjs('9999-12-31T00:00:00.000Z'));

type SubmitCustomerAttributeValue = {
    t: TFunction;
    application: CapAvailableApplicationType;
    lead: Lead;
    capSetting: CapSetting;
    auth: string;
    customerGuid: string;
    customerData: Customer;
    companyData: Company;
};

const submitCustomerHobbies = async ({
    t,
    application,
    lead,
    capSetting,
    auth,
    customerGuid,
    customerData,
    companyData,
}: SubmitCustomerAttributeValue) => {
    const customerFields = getLocalCustomerAggregatedFields(customerData);
    const credsParams = {
        ...capSetting,
        auth,
    };

    const validHobbies = (customerFields.hobby ?? [])
        .map(hobby => {
            const capValues = getCapHobby(hobby, companyData.countryCode);
            if (capValues.attributeCategoryCode && capValues.attributeSubCategoryCode) {
                return {
                    hobbyKey: hobby,
                    attributeCategoryCode: capValues.attributeCategoryCode,
                    attributeSubCategoryCode: capValues.attributeSubCategoryCode,
                    mappingKey: `${capValues.attributeCategoryCode}-${capValues.attributeSubCategoryCode}`,
                };
            }

            return null;
        })
        .filter(Boolean);

    const validPassionPoints: string[] = flow([
        map<{ attributeCategoryCode: string }, string>(({ attributeCategoryCode }) => attributeCategoryCode),
        uniq<string>,
    ])(validHobbies);

    const { existingHobbiesMap, existingPassionPointsMap } = await getCustomerExistingHobbies({
        t,
        application,
        lead,
        customerGuid,
        companyCode: companyData.countryCode,
        ...credsParams,
    });

    const hobbiesToAdd = validHobbies.filter(hobby => !existingHobbiesMap.has(hobby.mappingKey));
    const hobbiesToEnable = validHobbies
        .filter(hobby => existingHobbiesMap.get(hobby.mappingKey)?.isValid === false)
        .map(hobby => ({
            ...hobby,
            recordGuid: existingHobbiesMap.get(hobby.mappingKey)?.recordGuid,
        }));
    const hobbiesToDelete = differenceBy('hobbyKey', [...existingHobbiesMap.values()], validHobbies);

    const passionPointsToAdd = validPassionPoints.filter(passionPoint => !existingPassionPointsMap.has(passionPoint));
    const passionPointsToEnable = validPassionPoints
        .filter(passionPoint => existingPassionPointsMap.get(passionPoint)?.isValid === false)
        .map(passionPoint => ({
            attributeCategoryCode: passionPoint,
            recordGuid: existingPassionPointsMap.get(passionPoint)?.recordGuid,
        }));
    const passionPointsToDelete = [...existingPassionPointsMap.values()].filter(
        ({ attributeCategoryCode, isValid }) =>
            !validHobbies.some(hobby => hobby.attributeCategoryCode === attributeCategoryCode) && isValid
    );

    const baseApiParams = {
        t,
        application,
        lead,
        ...credsParams,
    };

    const itemsToEnable = [
        ...hobbiesToEnable.map(({ recordGuid }) => recordGuid),
        ...passionPointsToEnable.map(({ recordGuid }) => recordGuid),
    ];

    const reenabledGuids = await Promise.all(
        itemsToEnable.map(async recordGuid => {
            if (!recordGuid) {
                return null;
            }

            const updateParams = {
                ...baseApiParams,
                data: { validToDateTime: ENABLED_VALID_TO },
                customerAttributeId: recordGuid,
            };

            const updateResult = await withRetryApiCall(() => updateCustomerAttribute(updateParams));

            if (updateResult.error) {
                return null;
            }

            await createCapSubmissionAuditTrails({
                application,
                lead,
                capActionAuditTrail: AuditTrailKind.CapCustomerAttributeUpdated,
                success: true,
                id: recordGuid,
            });

            return recordGuid;
        })
    );

    const itemsToDelete = [
        ...hobbiesToDelete.map(({ recordGuid }) => recordGuid),
        ...passionPointsToDelete.map(({ recordGuid }) => recordGuid),
    ];

    await Promise.all(
        itemsToDelete.map(async recordGuid => {
            if (!recordGuid) {
                return null;
            }
            const deleteParams = {
                ...baseApiParams,
                customerAttributeId: recordGuid,
            };

            const deleteResult = await withRetryApiCall(() => deleteCustomerAttribute(deleteParams));

            if (deleteResult.error) {
                return null;
            }

            await createCapSubmissionAuditTrails({
                application,
                lead,
                capActionAuditTrail: AuditTrailKind.CapCustomerAttributeDeleted,
                success: true,
                id: recordGuid,
            });

            return recordGuid;
        })
    );

    const createdGuids = await Promise.all(
        // hobbies
        [
            ...hobbiesToAdd.map(async ({ attributeCategoryCode, attributeSubCategoryCode }) => {
                const payload: CreateCustomerAttributeData = {
                    customerGuid,
                    attributeTypeCode: CAP_HOBBY_ATTRIBUTE_CODE,
                    attributeSubTypeCode: CAP_HOBBY_SUB_ATTRIBUTE_CODE,
                    attributeCategoryCode,
                    attributeSubCategoryCode,
                    importerNumber: capSetting.capPagImporterId,
                };

                const createCustomerAttributeParams = {
                    ...baseApiParams,
                    data: payload,
                };

                const createdCustomerAttributeRes = await withRetryApiCall(() =>
                    createCustomerAttribute(createCustomerAttributeParams)
                );

                if (createdCustomerAttributeRes.error) {
                    return null;
                }

                const recordGuid = createdCustomerAttributeRes?.d.recordGuid;

                await createCapSubmissionAuditTrails({
                    application,
                    lead,
                    capActionAuditTrail: AuditTrailKind.CapCustomerAttributeCreated,
                    success: true,
                    id: recordGuid,
                });

                return recordGuid;
            }),
            // passion points
            ...passionPointsToAdd.map(async attributeCategoryCode => {
                const payload: CreateCustomerAttributeData = {
                    customerGuid,
                    attributeTypeCode: CAP_PASSION_ATTRIBUTE_CODE,
                    attributeCategoryCode,
                    importerNumber: capSetting.capPagImporterId,
                };

                const createCustomerAttributeParams = {
                    ...baseApiParams,
                    data: payload,
                };

                const createdCustomerAttributeRes = await withRetryApiCall(() =>
                    createCustomerAttribute(createCustomerAttributeParams)
                );

                if (createdCustomerAttributeRes.error) {
                    return null;
                }

                const recordGuid = createdCustomerAttributeRes?.d.recordGuid;

                await createCapSubmissionAuditTrails({
                    application,
                    lead,
                    capActionAuditTrail: AuditTrailKind.CapCustomerAttributeCreated,
                    success: true,
                    id: recordGuid,
                });

                return recordGuid;
            }),
        ]
    );

    const allHobbyGuids = [...reenabledGuids, ...createdGuids].filter(Boolean);

    return allHobbyGuids.length ? allHobbyGuids : null;
};

export default submitCustomerHobbies;
