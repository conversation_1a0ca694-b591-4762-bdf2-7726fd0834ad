import { ObjectId } from 'mongodb';
import type {
    CapActivitySubmitted,
    CapBPCreated,
    CapBPIsExist,
    CapBPUpdated,
    CapConsentSubmitted,
    CapLeadCreated,
    CapLeadIsExist,
    CapLeadUpdated,
    CapActivityEndTestDriveSubmitted,
    CapLeadCampaignNotFound,
    CapConsentNotFound,
    CapBPSearchFailed,
    CapLeadSearchFailed,
    CapCompetitorVehicleCreated,
    CapCompetitorVehicleUpdated,
    LeadQualifiedAuditTrail,
    LeadUnqualifiedAuditTrail,
    LeadLostAuditTrail,
    LeadCompletedAuditTrail,
    LeadSubmissionFailedAuditTrail,
    LeadSubmittedWithErrorAuditTrail,
    CapCustomerAttributeCreated,
    CapCustomerAttributeSearchFailed,
    CapActivityPlannedTestDriveSubmitted,
    CapActivityPlannedShowroomVisitSubmitted,
    CapActivityCompleteShowroomVisitSubmitted,
    CapCustomerAttributeDeleted,
    CapCustomerAttributeUpdated,
} from '../../../database/documents/AuditTrail';
import { AuditTrailKind } from '../../../database/documents/AuditTrail';
import { LeadStatus } from '../../../database/documents/Lead';
import { AuthorKind } from '../../../database/documents/Versioning';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { CapStatusChangeActivityLogParams, CreateActivityLogParams } from './types';

const getAuditTrailKind = (submissionStatus: LeadStatus) => {
    switch (submissionStatus) {
        case LeadStatus.SubmittedToCap:
            return AuditTrailKind.LeadQualified;

        case LeadStatus.Unqualified:
            return AuditTrailKind.LeadUnqualified;

        case LeadStatus.SubmissionFailed:
            return AuditTrailKind.LeadSubmissionFailed;

        case LeadStatus.SubmittedWithError:
            return AuditTrailKind.LeadSubmittedWithError;

        default:
            return null;
    }
};

export const storeCapStatusChangeAuditTrails = async (activityLogParams: CapStatusChangeActivityLogParams) => {
    const { submissionStatus, application, user, lead } = activityLogParams;

    const { collections } = await getDatabaseContext();

    const auditTrailKind = getAuditTrailKind(submissionStatus);

    if (auditTrailKind) {
        await collections.auditTrails.insertOne({
            _date: new Date(),
            _id: new ObjectId(),
            _kind: auditTrailKind,
            applicationId: application?._id,
            applicationSuiteId: application?._versioning.suiteId,
            author: { kind: user ? AuthorKind.User : AuthorKind.System, id: user?._id },
            leadId: lead._id,
            leadSuiteId: lead._versioning.suiteId,
        });
    }
};

export const createCapSubmissionAuditTrails = async (activityLogParams: CreateActivityLogParams) => {
    const { application, capActionAuditTrail, success, id, errorMessage, lead } = activityLogParams;

    const { collections } = await getDatabaseContext();

    const trails:
        | CapBPIsExist
        | CapBPCreated
        | CapBPUpdated
        | CapCompetitorVehicleCreated
        | CapCompetitorVehicleUpdated
        | CapLeadIsExist
        | CapLeadCampaignNotFound
        | CapLeadCreated
        | CapLeadUpdated
        | CapActivitySubmitted
        | CapActivityEndTestDriveSubmitted
        | CapActivityPlannedTestDriveSubmitted
        | CapActivityPlannedShowroomVisitSubmitted
        | CapActivityCompleteShowroomVisitSubmitted
        | CapConsentNotFound
        | CapConsentSubmitted
        | CapBPSearchFailed
        | CapLeadSearchFailed
        | CapCustomerAttributeCreated
        | CapCustomerAttributeUpdated
        | CapCustomerAttributeDeleted
        | CapCustomerAttributeSearchFailed
        | LeadQualifiedAuditTrail
        | LeadUnqualifiedAuditTrail
        | LeadLostAuditTrail
        | LeadCompletedAuditTrail
        | LeadSubmissionFailedAuditTrail
        | LeadSubmittedWithErrorAuditTrail = {
        _date: new Date(),
        _id: new ObjectId(),
        _kind: capActionAuditTrail,
        applicationId: application?._id,
        applicationSuiteId: application?._versioning?.suiteId,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        stages: application?.stages,
        success,
        id,
        errorMessage,
    };

    await collections.auditTrails.insertOne(trails);
};
