import { TFunction } from 'i18next';
import { CapAvailableApplicationType } from '../../../database';
import { Lead } from '../../../database/documents/Lead';
import { CapSetting } from '../../../database/documents/Setting';
import createLoaders from '../../../loaders';
import getCampaignDetails from '../getCampaignDetails';
import getDealerDetails from '../getDealerDetails';
import getSalespersonDetails from '../getSalespersonDetails';
import getAssigneeBasedOnApplicationStage from './getAssigneeBasedOnApplicationStage';
import { getLeadDetailFromApplication } from './getLeadDetailFromApplication';
import retryApiCall from './retryApiCall';

const getSupportingValuesFromApplication = async (
    t: TFunction,
    lead: Lead,
    authentication: string,
    capSetting: CapSetting,
    application?: CapAvailableApplicationType
) => {
    const loaders = createLoaders();
    const assigneeUserId = getAssigneeBasedOnApplicationStage(application, lead);
    const module = await loaders.moduleById.load(application.moduleId);

    const [customerData, dealerData, assigneeData, companyData] = await Promise.all([
        loaders.customerById.load(application.applicantId),
        loaders.dealerById.load(application.dealerId),
        assigneeUserId?.assigneeId ? loaders.userById.load(assigneeUserId.assigneeId) : null,
        loaders.companyById.load(module.companyId),
    ]);

    const leadDetailsFromApplication = await getLeadDetailFromApplication(application);

    const [campaignGuid, dealerGuid, salespersonGuid] = await Promise.all([
        leadDetailsFromApplication?.campaignId
            ? retryApiCall(() =>
                  getCampaignDetails({
                      ...capSetting,
                      application,
                      lead,
                      t,
                      auth: authentication,
                      campaignID: leadDetailsFromApplication.campaignId,
                  })
              ).then(campaignDetails =>
                  campaignDetails?.d?.results?.length ? campaignDetails.d.results[0].campaignGuid : ''
              )
            : '',
        dealerData?.integrationDetails?.dealerCode
            ? retryApiCall(() =>
                  getDealerDetails({
                      ...capSetting,
                      auth: authentication,
                      dealerCode: dealerData.integrationDetails.dealerCode,
                  })
              ).then(dealerDetails => (dealerDetails?.d?.results?.length ? dealerDetails.d.results[0].dealerGuid : ''))
            : '',
        assigneeData?.alias
            ? retryApiCall(() =>
                  getSalespersonDetails({
                      ...capSetting,
                      auth: authentication,
                      employeeId: assigneeData.alias,
                  })
              ).then(salespersonDetails =>
                  salespersonDetails?.d?.results?.length ? salespersonDetails.d.results[0].employeeGuid : ''
              )
            : '',
    ]);

    return {
        customerData,
        dealerData,
        assigneeData,
        companyData,
        campaignGuid,
        dealerGuid,
        salespersonGuid,
        sourceId: leadDetailsFromApplication.sourceId,
        originId: leadDetailsFromApplication.mediumId,
    };
};

export default getSupportingValuesFromApplication;
