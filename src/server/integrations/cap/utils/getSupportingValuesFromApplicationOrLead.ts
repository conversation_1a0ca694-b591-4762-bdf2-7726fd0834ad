import { TFunction } from 'i18next';
import { CapAvailableApplicationType } from '../../../database';
import { Lead } from '../../../database/documents/Lead';
import { CapSetting } from '../../../database/documents/Setting';
import getSupportingValuesFromApplication from './getSupportingValuesFromApplication';
import getSupportingValuesFromLead from './getSupportingValuesFromLead';

type Params = {
    t: TFunction;
    lead: Lead;
    authentication: string;
    capSetting: CapSetting;
    application?: CapAvailableApplicationType;
};

const getSupportingValuesFromApplicationOrLead = async ({
    t,
    lead,
    authentication,
    capSetting,
    application,
}: Params) => {
    if (!application) {
        return getSupportingValuesFromLead(t, lead, authentication, capSetting);
    }

    return getSupportingValuesFromApplication(t, lead, authentication, capSetting, application);
};

export default getSupportingValuesFromApplicationOrLead;
