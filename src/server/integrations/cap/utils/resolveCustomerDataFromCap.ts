import { TFunction } from 'i18next';
import type { CapSetting, Lead } from '../../../database';
import type { Loaders } from '../../../loaders';
import { CreateBusinessPartnerData } from '../businessPartner/types';
import getCampaignDetails from '../getCampaignDetails';

export const getResponsibleSalesPersonAndDealerFromCap = async (result: unknown, loaders: Loaders) => {
    const { ResponsibleSalesDealerId, ResponsibleSalesPersonId } = result as CreateBusinessPartnerData;

    const data = {
        responsibleSalesPerson: null,
        responsibleSalesPersonId: ResponsibleSalesPersonId,
        dealer: null,
        dealerId: ResponsibleSalesDealerId,
    };

    const [users, dealers] = await Promise.all([
        ResponsibleSalesPersonId ? loaders.userByAlias.load(ResponsibleSalesPersonId) : null,
        ResponsibleSalesDealerId ? loaders.dealerByCode.load(ResponsibleSalesDealerId) : null,
    ]);

    if (users && users.length > 0) {
        data.responsibleSalesPerson = users[0].displayName;
    }

    if (dealers && dealers.length > 0) {
        data.dealer = dealers[0].displayName;
    }

    return data;
};

export const getCampaignIdFromCap = (
    t: TFunction,
    lead: Lead,
    capSetting: CapSetting,
    auth: string,
    campaignGuid: string
) =>
    getCampaignDetails({ t, lead, ...capSetting, auth, campaignGuid }).then(campaignDetails =>
        campaignDetails?.d?.results?.length ? campaignDetails.d.results[0].campaignId : ''
    );
