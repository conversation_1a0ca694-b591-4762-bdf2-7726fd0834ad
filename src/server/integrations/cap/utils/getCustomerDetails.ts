import dayjs from 'dayjs';
import { CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';
import { isEmpty, isNil } from 'lodash/fp';
import { Company } from '../../../database/documents/Company';
import type { LocalCustomerAggregatedFields } from '../../../database/helpers/customers/types';
import { getCountryCode, getCountryCodeByPhonePrefix } from './getCountryCode';

export const capTitleCode = {
    MR: '0001',
    MS: '0005',
    MX: '0009',
};

export const getCapTitleCode = (title: string) => capTitleCode[title] ?? '';
export const getTitleByCapCode = (capCode: string) =>
    Object.keys(capTitleCode).find(key => capTitleCode[key] === capCode) ?? '';

const capMaritalStatusCode = {
    SINGLE: 'S',
    MARRIED: 'M',
    WIDOWED: 'W',
    DIVORCED: 'D',
};

export const getMaritalStatusCode = (maritalStatus: string) => capMaritalStatusCode[maritalStatus] ?? '';
export const getMaritalByCapCode = (capCode: string) =>
    Object.keys(capMaritalStatusCode).find(key => capMaritalStatusCode[key] === capCode) ?? '';

export const getCustomerLastName = (customerFields: LocalCustomerAggregatedFields) => {
    if (!isEmpty(customerFields.lastName)) {
        return customerFields.lastName;
    }

    if (!isEmpty(customerFields.lastNameFront)) {
        return customerFields.lastNameFront;
    }

    return '';
};

const capGenderCode = {
    FEMALE: '1',
    MALE: '2',
    NOTSPECIFIED: '',
};

export const getGenderCode = (gender: string) => capGenderCode[gender] ?? '';
export const getGenderByCapCode = (capCode: string) =>
    Object.keys(capGenderCode).find(key => capGenderCode[key] === capCode) ?? '';

export const getBpComm = (customerFields: LocalCustomerAggregatedFields, companyData: Company) => {
    const { phone, telephone } = customerFields;

    const bpComm = [];

    const resolveCountry = (prefix: number | undefined, fallbackCountry?: string): string =>
        (prefix ? getCountryCodeByPhonePrefix(prefix) : getCountryCode(customerFields.country)) ??
        fallbackCountry?.toUpperCase() ??
        '';

    const formatNumberForCountry = (value: string, country: string, companyData: Company) => {
        if (companyData.countryCode === 'KR') {
            const parsed = parsePhoneNumberFromString(value, country as CountryCode);

            return parsed?.formatNational() ?? value;
        }

        return value;
    };

    if (!isEmpty(phone?.value)) {
        const country = resolveCountry(phone.prefix, companyData.countryCode);
        bpComm.push({
            Type: 'ZMO',
            Country: country,
            Number: formatNumberForCountry(phone.value, country, companyData),
            AddressType: 'H',
        });
    }

    if (!isEmpty(telephone?.value)) {
        const country = resolveCountry(telephone.prefix, companyData.countryCode);
        bpComm.push({
            Type: 'TEL',
            Country: country,
            Number: formatNumberForCountry(telephone.value, country, companyData),
            AddressType: 'H',
        });
    }

    return bpComm;
};

export const getCompanyName2 = (customerFields: LocalCustomerAggregatedFields) =>
    (customerFields.occupation || '') +
    (customerFields.companyPhoneticName
        ? `${customerFields.occupation ? ', ' : ''}${customerFields.companyPhoneticName}`
        : '');

// If the companyName2 doesn't have the comma separation, we not sure whether it's occupation or phoeticName,
// hence let's return empty for now
export const getOccupation = (companyName2: string) =>
    companyName2?.includes(',') ? companyName2.split(', ')[0] : undefined;

export const getCompanyPhoeticName = (companyName2: string) =>
    companyName2?.includes(',') ? companyName2.split(', ')[1] : undefined;

export const getCapCustomerBirthday = (birthday?: Date, timeZone?: string) => {
    if (!isNil(birthday)) {
        if (timeZone) {
            return dayjs(birthday).tz(timeZone).format('DD.MM.YYYY');
        }

        return dayjs(birthday).format('DD.MM.YYYY');
    }

    return '';
};
