import { TFunction } from 'i18next';
import { Lead } from '../../../database/documents/Lead';
import { CapSetting } from '../../../database/documents/Setting';
import createLoaders from '../../../loaders';
import getCampaignDetails from '../getCampaignDetails';
import getDealerDetails from '../getDealerDetails';
import getSalespersonDetails from '../getSalespersonDetails';
import { getLeadDetailsFromLead } from './getLeadDetailFromApplication';
import retryApiCall from './retryApiCall';

const getSupportingValuesFromLead = async (
    t: TFunction,
    lead: Lead,
    authentication: string,
    capSetting: CapSetting
) => {
    const loaders = createLoaders();
    const assigneeUserId = lead.assigneeId;
    const module = await loaders.moduleById.load(lead.moduleId);

    const [customerData, dealerData, assigneeData, companyData] = await Promise.all([
        loaders.customerById.load(lead.customerId),
        loaders.dealerById.load(lead.dealerId),
        assigneeUserId ? loaders.userById.load(assigneeUserId) : null,
        loaders.companyById.load(module.companyId),
    ]);

    const leadDetailsFromApplication = await getLeadDetailsFromLead(lead);

    const [campaignGuid, dealerGuid, salespersonGuid] = await Promise.all([
        leadDetailsFromApplication?.campaignId
            ? retryApiCall(() =>
                  getCampaignDetails({
                      ...capSetting,
                      t,
                      lead,
                      auth: authentication,
                      campaignID: leadDetailsFromApplication.campaignId,
                  })
              ).then(campaignDetails =>
                  campaignDetails?.d?.results?.length ? campaignDetails.d.results[0].campaignGuid : ''
              )
            : '',
        dealerData?.integrationDetails?.dealerCode
            ? retryApiCall(() =>
                  getDealerDetails({
                      ...capSetting,
                      auth: authentication,
                      dealerCode: dealerData.integrationDetails.dealerCode,
                  })
              ).then(dealerDetails => (dealerDetails?.d?.results?.length ? dealerDetails.d.results[0].dealerGuid : ''))
            : '',
        assigneeData?.alias
            ? retryApiCall(() =>
                  getSalespersonDetails({
                      ...capSetting,
                      auth: authentication,
                      employeeId: assigneeData.alias,
                  })
              ).then(salespersonDetails =>
                  salespersonDetails?.d?.results?.length ? salespersonDetails.d.results[0].employeeGuid : ''
              )
            : '',
    ]);

    return {
        companyData,
        customerData,
        dealerData,
        assigneeData,
        campaignGuid,
        dealerGuid,
        salespersonGuid,
        sourceId: leadDetailsFromApplication.sourceId,
        originId: leadDetailsFromApplication.mediumId,
    };
};

export default getSupportingValuesFromLead;
