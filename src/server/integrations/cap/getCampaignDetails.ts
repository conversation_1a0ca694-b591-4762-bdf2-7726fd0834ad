import { URLSearchParams } from 'url';
import { omitBy } from 'lodash/fp';
import fetch from 'node-fetch';
import urljoin from 'url-join';
import { AuditTrailKind } from '../../database/documents/AuditTrail';
import captureCapError from './utils/captureCapError';
import type { CapRequestParamsWithTrails, GenericCapRequestParams, GenericGetResult } from './utils/types';

export type CampaignData = {
    campaignGuid: string;
    campaignId: string;
    description: string;
    statusId: string;
    isFavourite: boolean;
    responsibleDealerGuid: string;
};

type GetCampaignParams = CapRequestParamsWithTrails &
    GenericCapRequestParams & {
        campaignID?: string;
        campaignGuid?: string;
    };

const getCampaignDetails = async (campaignParams: GetCampaignParams): Promise<GenericGetResult<CampaignData>> => {
    const {
        t,
        application,
        lead,
        secrets,
        capRegion,
        auth,
        capBaseUrl,
        capGroup,
        capEndpointEnv,
        campaignID,
        campaignGuid,
    } = campaignParams;

    try {
        if (secrets?.clientId && secrets?.clientSecret && capRegion && auth && (campaignID || campaignGuid)) {
            const endpoint = urljoin(`https://${capBaseUrl}`, capGroup, capEndpointEnv, 'crm/v2/lead/LeadCampaignSet');

            const filterField = [];

            if (campaignID) {
                filterField.push(`campaignId eq '${campaignID}'`);
            }

            if (campaignGuid) {
                filterField.push(`campaignGuid eq '${campaignGuid}'`);
            }

            const paramsDetails = {
                $format: 'json',
                $filter: filterField.join(' and '),
            };

            const params = new URLSearchParams();
            const paramsKeys = Object.keys(paramsDetails);
            paramsKeys.forEach(key => {
                params.append(key, paramsDetails[key]);
            });

            const headers = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'application/json',
                Authorization: `Bearer ${auth}`,
                'X-Porsche-Client-Id': secrets?.clientId,
                'X-Porsche-Client-Secret': secrets?.clientSecret,
                region: capRegion,
            };

            const response = await fetch(`${endpoint}?${params.toString()}`, {
                headers,
                method: 'GET',
            });

            const responseData = await response?.json();

            if (response?.status === 200) {
                if (responseData?.d?.results?.length) {
                    return responseData;
                }

                const campaignNotFoundMessage = t('auditTrails:application.capLeadCampaignNotFound', {
                    id: campaignID,
                });

                await captureCapError({
                    application,
                    lead,
                    id: campaignID,
                    captureTrails: true,
                    auditTrailKind: AuditTrailKind.CapLeadCampaignNotFound,
                    auditTrailErrorMessage: campaignNotFoundMessage,
                    apiName: 'GET CampaignDetails',
                    requestData: omitBy(['application', 't'], campaignParams),
                    capturedException: campaignNotFoundMessage,
                });

                return { error: campaignNotFoundMessage };
            }

            const responseErrorMessage = responseData?.error?.message?.value || responseData?.moreInformation;
            const errorMessage = responseErrorMessage || t('auditTrails:application.defaultCapError.searchCampaign');

            await captureCapError({
                application,
                lead,
                id: campaignID,
                captureTrails: true,
                auditTrailKind: AuditTrailKind.CapLeadCampaignNotFound,
                auditTrailErrorMessage: errorMessage,
                apiName: 'GET CampaignDetails',
                requestData: omitBy(['application', 't'], campaignParams),
                capturedException: errorMessage,
            });

            return { error: errorMessage };
        }

        const errorMessage = t('auditTrails:application.missingCapValue.searchCampaign');
        await captureCapError({
            application,
            lead,
            id: campaignID,
            captureTrails: true,
            auditTrailKind: AuditTrailKind.CapLeadCampaignNotFound,
            auditTrailErrorMessage: errorMessage,
            apiName: 'GET CampaignDetails',
            requestData: omitBy(['application', 't'], campaignParams),
            capturedException: errorMessage,
        });

        return { error: errorMessage };
    } catch (error) {
        const errorMessage = `Error found in getCampaignDetails: ${error}`;

        await captureCapError({
            application,
            lead,
            id: campaignID,
            captureTrails: true,
            auditTrailKind: AuditTrailKind.CapLeadCampaignNotFound,
            auditTrailErrorMessage: errorMessage,
            apiName: 'GET CampaignDetails',
            requestData: omitBy(['application', 't'], campaignParams),
            capturedException: errorMessage,
        });

        console.error(errorMessage);

        return { error: errorMessage };
    }
};

export default getCampaignDetails;
