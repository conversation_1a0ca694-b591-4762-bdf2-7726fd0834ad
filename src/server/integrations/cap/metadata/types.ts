import { CapSetting } from '../../../database/documents';

export type CapMetadataProps = Omit<
    CapSetting,
    'capModuleId' | '_id' | 'settingId' | 'date' | 'capLeadProcessTypeId'
> & {
    auth: string;
};

export type MetadataValueHelp = {
    Key: string;
    Value: string;
};

export type MetadataTechnicalFieldSet = {
    ValueHelpSet: { results: MetadataValueHelp[] };
};

export type MetadataEntitySet = {
    TechnicalFieldSet: { results: MetadataTechnicalFieldSet[] };
};

export type MetadataResData = {
    d?: { EntitySet: { results: MetadataEntitySet[] } };
    error?: string;
};
