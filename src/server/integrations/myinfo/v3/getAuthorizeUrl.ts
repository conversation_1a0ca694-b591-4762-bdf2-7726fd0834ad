import { ObjectId } from 'mongodb';
import qs from 'qs';
import urlJoin from 'url-join';
import { MyInfoSetting, Router, MyInfoSettingVersion } from '../../../database/documents';
import { ConditionContext } from '../../../database/helpers';
import { getCallbackUrl, getFields } from '../shared';
import { GetAuthorizedUrlFn } from '../types';

/* Build HTTP query arguments to request authorization */
const getAuthorizeParams = async (
    router: Router,
    isPublicJourney: boolean,
    customerKind: ConditionContext['customerKind'],
    applicationId: ObjectId,
    myInfoSetting: MyInfoSetting,
    state: string,
    withTradeIn: boolean,
    withTestDrive: boolean
) => ({
    client_id: myInfoSetting.secrets.clientId,
    attributes: (await getFields(applicationId, customerKind, withTradeIn, withTestDrive, true))
        .map(field => (Array.isArray(field.attribute) ? field.attribute.join(',') : field.attribute))
        .filter(Boolean)
        .join(','),
    purpose:
        myInfoSetting.secrets.version === MyInfoSettingVersion.MyInfoV3 ? myInfoSetting.secrets.requestPurpose : null,
    redirect_uri: getCallbackUrl(router.hostname, myInfoSetting.secrets.isTestEnvironment),
    state,
    login_type: isPublicJourney ? 'SINGPASS' : 'QR',
});

const getAuthorizeUrl: GetAuthorizedUrlFn = async (
    myInfoSetting,
    router,
    isPublicJourney,
    customerKind,
    applicationId,
    link,
    withTradeIn,
    withTestDrive
): Promise<string> => {
    const authorizeEndpoint = urlJoin(myInfoSetting.secrets.baseUrl, 'authorise');

    const parameters = await getAuthorizeParams(
        router,
        isPublicJourney,
        customerKind,
        applicationId,
        myInfoSetting,
        link.secret,
        withTradeIn,
        withTestDrive
    );

    return `${authorizeEndpoint}?${qs.stringify(parameters)}`;
};

export default getAuthorizeUrl;
