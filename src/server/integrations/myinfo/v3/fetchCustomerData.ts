import crypto from 'crypto';
import * as jose from 'jose';
import qs from 'qs';
import urlJoin from 'url-join';
import {
    LocalCustomerFieldKey,
    MyInfoSettingSecretsV3,
    MyInfoSettingVersion,
    Router,
} from '../../../database/documents';
import { sortObjectProperties } from '../../../utils/common';
import { getCallbackUrl, getFields, handleInvalidResponse } from '../shared';
import { FetchCustomerDataFn, MyInfoData } from '../types';

/* Build HTTP query arguments to fetch the access token */
const getTokenParams = (secrets: MyInfoSettingSecretsV3, router: Router, authorizationCode: string): any => ({
    grant_type: 'authorization_code',
    code: authorizationCode,
    redirect_uri: getCallbackUrl(router.hostname, secrets.isTestEnvironment),
    client_id: secrets.clientId,
    client_secret: secrets.clientSecret,
});

/* Generic Apex header for PKI digital signature */
const generateApexHeader = (secrets: MyInfoSettingSecretsV3, method, url, params) => {
    const nonceValue = crypto.randomBytes(16).toString('base64');
    const timestamp = new Date().getTime();

    const baseParams = sortObjectProperties({
        ...params,
        app_id: secrets.clientId,
        nonce: nonceValue,
        signature_method: 'RS256',
        timestamp,
    });

    // generate base string (content to encrypt/sign)
    const baseString = `${method.toUpperCase()}&${url}&${qs.stringify(baseParams, { encode: false })}`;

    // then sign using crypto module from node
    const signature = crypto.createSign('RSA-SHA256').update(baseString).sign(secrets.privateKey, 'base64');

    const parts = [
        `timestamp="${timestamp}"`,
        `nonce="${nonceValue}"`,
        `app_id="${secrets.clientId}"`,
        'signature_method="RS256"',
        `signature="${signature}"`,
    ];

    return `PKI_SIGN ${parts.join(',')}`;
};

/* Fetch access token for MyInfo */
const fetchAccessToken = async (
    secrets: MyInfoSettingSecretsV3,
    router: Router,
    authorizationCode: string
): Promise<string> => {
    const tokenEndpoint = urlJoin(secrets.baseUrl, 'token');

    const headers: any = {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
    };

    const params = getTokenParams(secrets, router, authorizationCode);

    if (secrets.pkiSignature) {
        headers.Authorization = generateApexHeader(secrets, 'POST', tokenEndpoint, params);
    }

    const response = await fetch(tokenEndpoint, { headers, method: 'POST', body: qs.stringify(params) });

    if (!response.ok) {
        await handleInvalidResponse(response);
    }

    const { access_token: token } = await response.json();

    return token;
};

/* Verify & decode JWT/JWS with the public certificate from MyInfo */
const verifyJWS = async (secrets: MyInfoSettingSecretsV3, jws: string): Promise<jose.JWTVerifyResult> => {
    const key = await jose.importX509(secrets.publicCertificate, 'sha256WithRSAEncryption');

    return jose.jwtVerify(jws, key, { algorithms: ['RS256'], clockTolerance: 600 });
};

/* Fetch customer data */
const fetchCustomData: FetchCustomerDataFn = async (
    { secrets },
    router,
    authorizationCode,
    customerKind,
    applicationId,
    link,
    withTradeIn,
    withTestDrive
): Promise<{ myInfoData: MyInfoData; fieldKeys: LocalCustomerFieldKey[] }> => {
    if (secrets.version !== MyInfoSettingVersion.MyInfoV3) {
        return null;
    }

    const personEndpoint = urlJoin(secrets.baseUrl, 'person');
    try {
        const accessToken = await fetchAccessToken(secrets, router, authorizationCode);
        const { payload } = await verifyJWS(secrets, accessToken);

        const fields = await getFields(applicationId, customerKind, withTradeIn, withTestDrive, true);

        const params = {
            client_id: secrets.clientId,
            attributes: fields
                .map(field => (Array.isArray(field.attribute) ? field.attribute.join(',') : field.attribute))
                .filter(Boolean)
                .join(','),
        };

        const endpoint = urlJoin(personEndpoint, payload.sub);

        const headers: any = {
            'Cache-Control': 'no-cache',
            Authorization: secrets.pkiSignature
                ? `${generateApexHeader(secrets, 'GET', endpoint, params)},Bearer ${accessToken}`
                : `Bearer ${accessToken}`,
        };

        const url = `${endpoint}?${qs.stringify(params)}`;

        const response = await fetch(url, { headers });

        if (!response.ok) {
            await handleInvalidResponse(response);
        }

        if (!secrets.pkiSignature) {
            return response.json();
        }

        const responseText = await response.text();
        const key = await jose.importPKCS8(secrets.privateKey, 'RS256');
        const decryptedData = await jose.compactDecrypt(responseText, key);
        const verifiedResults = await verifyJWS(secrets, JSON.parse(decryptedData.plaintext.toString()));

        return {
            fieldKeys: fields.map(field => field.fieldKey).filter(Boolean),
            myInfoData: verifiedResults.payload as MyInfoData,
        };
    } catch (error) {
        console.error(error);

        return null;
    }
};

export default fetchCustomData;
