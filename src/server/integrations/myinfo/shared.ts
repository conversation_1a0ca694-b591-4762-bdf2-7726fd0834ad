import * as Sentry from '@sentry/node';
import { ObjectId } from 'mongodb';
import urlJoin from 'url-join';
import config from '../../core/config';
import {
    KYCField,
    KycFieldPurpose,
    LocalCustomerFieldKey,
    Module,
    ModuleType,
    MyInfoModule,
    MyInfoSetting,
    Setting,
    SettingId,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    ConditionContext,
    getCustomerModuleFromApplication,
    getKYCFieldsFromPresets,
    getKYCPresetsForApplication,
} from '../../database/helpers';
import { Page } from '../../utils/pagination';

export const getCallbackUrl = (hostname: string, isTestEnvironment: boolean) =>
    isTestEnvironment ? 'http://localhost:3001/callback' : urlJoin(`${config.protocol}://${hostname}`, `/.callback/mi`);

const getAttributeBasedOnKycPreset = (key: LocalCustomerFieldKey, isV3: boolean) => {
    switch (key) {
        case LocalCustomerFieldKey.IdentityNumber:
            // skip `uinfin` scope explicitly for V3 integrations, as existing apps never requested this scope
            return isV3 ? null : 'uinfin';

        case LocalCustomerFieldKey.FullName:
            return 'name';

        case LocalCustomerFieldKey.Birthday:
            return 'dob';

        case LocalCustomerFieldKey.Nationality:
            return 'nationality';

        case LocalCustomerFieldKey.Email:
            return 'email';

        case LocalCustomerFieldKey.Phone:
            return 'mobileno';

        case LocalCustomerFieldKey.DrivingLicense:
            return ['drivinglicence.qdl.validity', 'drivinglicence.qdl.expirydate', 'drivinglicence.qdl.classes'];

        case LocalCustomerFieldKey.Race:
            return 'race';

        case LocalCustomerFieldKey.Title:
        case LocalCustomerFieldKey.NonBinaryTitle:
        case LocalCustomerFieldKey.Gender:
        case LocalCustomerFieldKey.NonBinaryGender:
            return 'sex';

        case LocalCustomerFieldKey.MaritalStatus:
            return 'marital';

        case LocalCustomerFieldKey.ResidentialStatus:
            return 'residentialstatus';

        case LocalCustomerFieldKey.CurrentVehicleMake:
            return 'vehicles.make';

        case LocalCustomerFieldKey.CurrentVehicleModel:
            return 'vehicles.model';

        case LocalCustomerFieldKey.CurrentVehicleModelYear:
            return 'vehicles.yearofmanufacture';

        case LocalCustomerFieldKey.CurrentVehicleRegistrationNumber:
            return 'vehicles.vehicleno';

        default:
            return null;
    }
};

const vehicleAttributes = [
    'vehicles.vehicleno',
    'vehicles.make',
    'vehicles.model',
    'vehicles.yearofmanufacture',
    'vehicles.firstregistrationdate',
    'vehicles.roadtaxexpirydate',
    'vehicles.enginecapacity',
    'vehicles.propellant',
    'vehicles.primarycolour',
    'vehicles.secondarycolour',
    'vehicles.status',
    'vehicles.scheme',
    'vehicles.coecategory',
    'vehicles.coeexpirydate',
    'vehicles.quotapremium',
    'vehicles.openmarketvalue',
    'vehicles.nooftransfers',
];

const isAddressField = (field: KYCField) =>
    [
        LocalCustomerFieldKey.Address,
        LocalCustomerFieldKey.Country,
        LocalCustomerFieldKey.PostalCode,
        LocalCustomerFieldKey.UnitNumber,
    ].includes(field.key);

export const getFields = async (
    applicationId: ObjectId,
    customerKind: ConditionContext['customerKind'],
    withTradeIn: boolean,
    withTestDrive: boolean,
    isV3 = false
) => {
    const { collections } = await getDatabaseContext();

    const application = await collections.applications.findOne({ _id: applicationId });

    const customerModule = await getCustomerModuleFromApplication(application);
    const presets = await getKYCPresetsForApplication(application, customerKind);
    const kycFields = getKYCFieldsFromPresets(
        customerModule.kycFields.sort((a, b) => a.order - b.order),
        presets
    ).filter(field => field.purpose.includes(KycFieldPurpose.KYC));

    const fields = kycFields
        .map(field => {
            if (!withTestDrive && field.key === LocalCustomerFieldKey.DrivingLicense) {
                return null;
            }
            const attribute = getAttributeBasedOnKycPreset(field.key, isV3);

            return (
                attribute && {
                    fieldKey: field.key,
                    attribute,
                }
            );
        })
        .filter(Boolean);

    if (withTradeIn) {
        fields.push({ fieldKey: null, attribute: vehicleAttributes });
    }

    if (kycFields.some(isAddressField)) {
        fields.push({ fieldKey: null, attribute: 'regadd' });
        const addressFields = kycFields.filter(isAddressField).map(field => ({ fieldKey: field.key, attribute: null }));

        return fields.concat(addressFields);
    }

    return fields;
};

export const ensureMyInfoModule =
    <T extends ModuleType>(moduleType: T) =>
    (myInfo: MyInfoModule): myInfo is Extract<Module, { _t: T }> =>
        myInfo._type === moduleType;

export const ensureMyInfoModulePage =
    <T extends ModuleType>(moduleType: T) =>
    (page: Page<MyInfoModule>): Page<Extract<MyInfoModule, { _t: T }>> => ({
        ...page,
        items: page.items.filter(ensureMyInfoModule(moduleType)),
    });

export const ensureMyInfoSetting =
    <T extends SettingId>(settingId: T) =>
    (myInfo: MyInfoSetting): myInfo is Extract<Setting, { _t: T }> =>
        myInfo.settingId === settingId;

export const ensureMyInfoSettingPage =
    <T extends SettingId>(settingId: T) =>
    (page: Page<MyInfoSetting>): Page<Extract<MyInfoSetting, { _t: T }>> => ({
        ...page,
        items: page.items.filter(ensureMyInfoSetting(settingId)),
    });

/* handle invalid response from MyInfo */
export const handleInvalidResponse = async (response: Response): Promise<void> => {
    const errorMessage = 'Invalid response from Myinfo';
    const responseBody = await response.json();

    console.error(errorMessage, responseBody);

    Sentry.withScope(scope => {
        scope.setContext('myInfoResponse', responseBody);
        Sentry.captureMessage(errorMessage);
    });

    throw new Error(errorMessage);
};
