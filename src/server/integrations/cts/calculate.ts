import * as Sentry from '@sentry/node';
import { RequestHandler } from 'express';
import { isArray, isEmpty, isNil, sum } from 'lodash/fp';
import { getTermRestrictions } from '../../../app/calculator/computing/utils';
import {
    BalloonPayment,
    Calculator,
    DownPayment,
    InterestRate,
    Mileage,
    MonthlyInstalment,
    Query,
    ResidualValue,
} from '../../calculator';
import {
    getComplementaryCompoundValue,
    toCompoundValue,
    toUnitValue,
    toValidCompoundValue,
} from '../../calculator/nodes/utils';
import {
    AmountUnit,
    ApplicationMarket,
    BalloonBasedOn,
    CtsModule,
    CtsSetting,
    FinanceProduct,
    FinanceProductType,
    LocalFinanceProduct,
    SettingId,
    Vehicle,
    VehicleKind,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { DefaultMarketTypeValue } from '../../utils/getDealerSpecificMarketValues';
import getStepFromCalculationRounding from '../../utils/getStepFromCalculationRounding';
import getWebCalcParametersFromCalculator from '../../utils/getWebCalcParametersFromCalculator';
import isVehicleAssignedToFinanceProduct from '../../utils/isVehicleAssignedToFinanceProduct';
import { calculate as calculateWithWebCalc } from '../remote/webcalc';
import { ErrorCode, getError } from '../utils';
import { VALID_CONDITIONS } from './getFinanceProductsByVehicleIdentifier';
import { CompanyRounding, getCompanyRounding } from './rounding';
import {
    CtsErrorCode,
    filterFinanceProduct,
    getCompanyFormats,
    getCtsConfigurationFromRequest,
    getDisclaimerTemplateFunction,
    getPriceDisclaimer,
    getProductionFinanceProductsFilter,
    getProductionVehicleNewFilter,
    mapCalculationError,
    mapValidationErrorCode,
    MarketFees,
    retrieveMarketTypeValue,
    SUPPORTED_FP_TYPES,
    VALID_ENGINE_TYPE,
    ValueAndPercentage,
    VehicleParameterRequest,
} from './shared';
import type { SimpleMarketFees } from './types';
import {
    baseValidator,
    calculatorQueryValidator,
    financeProductValidator,
    calculationRequestValidator,
} from './validator';

type Body = {
    condition: 'new' | 'preowned';
    term: number;
    downPayment?: number;
    downPaymentUnit: '%' | string;
    loan?: number;
    loanUnit?: '%' | string;
    interestRate: number;
    carPrice: number;
    financeProductId: string;
    vehicleReference: VehicleParameterRequest;
    carOptions: Array<{
        value: number;
        name?: string;
    }>;
    balloon: number;
    balloonUnit: '%' | string;
    mileage: number;
    residualValue: number;
    residualValueUnit: '%' | string;
    marketFees: SimpleMarketFees;
};

type SuccessResponse = Partial<{
    monthlyInstalments: Array<{ start: number; end: number; value: number }>;
    carPrice: number;
    totalPrice: number;
    downPayment: ValueAndPercentage;
    loan: ValueAndPercentage;
    balloon: ValueAndPercentage;
    interestRate: number;
    mileage: number;
    marketFees: MarketFees;
    residualValue: ValueAndPercentage;
    disclaimer: string;
    optionFactor: number;
    estimatedSurplusAfterARV: number;

    // For NZ market
    totalAmountPayable: number;

    // For setting from header
    settingId: string;
}>;

type ErrorResponse = {
    error: string;
    code: ErrorCode | CtsErrorCode;
};

type Response = SuccessResponse | ErrorResponse;

type CommonDocuments = {
    financeProduct: FinanceProduct;
    module: CtsModule;
    setting: CtsSetting;
    vehicle: Vehicle | null;
    rounding: CompanyRounding;
    marketTypeValue: DefaultMarketTypeValue;
};

const getDownpaymentAndLoan = (payload: Body, totalPrice: number) => {
    if (isNil(payload.downPayment)) {
        const loan = toCompoundValue(
            payload.loan,
            payload.loanUnit === '%' ? AmountUnit.Percentage : AmountUnit.Currency,
            totalPrice
        );

        const downPayment = getComplementaryCompoundValue(loan, totalPrice);

        return {
            loan,
            downPayment,
        };
    }

    if (isNil(payload.loan)) {
        const downPayment = toCompoundValue(
            payload.downPayment,
            payload.downPaymentUnit === '%' ? AmountUnit.Percentage : AmountUnit.Currency,
            totalPrice
        );

        const loan = getComplementaryCompoundValue(downPayment, totalPrice);

        return {
            loan,
            downPayment,
        };
    }

    const downPayment = toCompoundValue(
        payload.downPayment,
        payload.downPaymentUnit === '%' ? AmountUnit.Percentage : AmountUnit.Currency,
        totalPrice
    );

    const loan = toCompoundValue(
        payload.loan,
        payload.loanUnit === '%' ? AmountUnit.Percentage : AmountUnit.Currency,
        totalPrice
    );

    return {
        downPayment,
        loan,
    };
};

const prepareValues = (document: CommonDocuments, payload: Body) => {
    const { setting, marketTypeValue } = document;
    const prices = [payload.carPrice, ...(payload.carOptions ?? []).map(({ value }) => value)];

    const marketFees: MarketFees = {};
    const { market } = setting;
    switch (market.type) {
        case ApplicationMarket.Default: {
            break;
        }

        case ApplicationMarket.Singapore: {
            marketFees.coe = marketTypeValue.coe;
            marketFees.updatedCoe = payload.marketFees?.coe;
            prices.push(marketFees.updatedCoe ?? marketFees.coe);
            break;
        }

        case ApplicationMarket.NewZealand: {
            marketFees.establishmentFee = marketTypeValue.estFee;
            marketFees.updatedEstablishmentFee = payload.marketFees?.establishmentFee;
            marketFees.ppsr = marketTypeValue.ppsr;
            marketFees.updatedPPSR = payload.marketFees?.ppsr;
            prices.push(
                marketFees.updatedEstablishmentFee ?? marketFees.establishmentFee,
                marketFees.updatedPPSR ?? marketFees.ppsr
            );
            break;
        }
    }

    return {
        marketFees,
        totalPrice: sum(prices),
    };
};
const getCalculatorQuery = (
    documents: CommonDocuments,
    payload: Body,
    prepared: ReturnType<typeof prepareValues>
): Query | null => {
    const { financeProduct, vehicle } = documents;
    const { totalPrice } = prepared;
    const { carPrice } = payload;
    const common: Pick<
        Query,
        'totalPrice' | 'variantSuiteId' | 'term' | 'carPrice' | 'vehicleKind' | 'finderRestrictedValue'
    > = {
        totalPrice,
        variantSuiteId: vehicle?._versioning.suiteId,
        term: payload.term,
        carPrice,
    };

    if (vehicle && vehicle._kind === 'finderVehicle' && vehicle.lta?.coeExpiryDate) {
        const updatedTerm = getTermRestrictions(vehicle.lta.coeExpiryDate);

        if (updatedTerm) {
            common.finderRestrictedValue = {
                maxTerm: updatedTerm.max,
            };
        }
    }

    switch (financeProduct.type) {
        case FinanceProductType.HirePurchase:
        case FinanceProductType.DeferredPrincipal: {
            return {
                ...common,
                interestRate: payload.interestRate,
                ...getDownpaymentAndLoan(payload, totalPrice),
            };
        }

        case FinanceProductType.UCCLLeasing: {
            // Displacement is based on vehicle
            // Inside calculator it's just an input, so map this
            const displacement = (() => {
                switch (vehicle?._kind) {
                    case VehicleKind.FinderVehicle: {
                        return vehicle?.listing.vehicle.displacement?.cubicCentimeter?.value ?? 0;
                    }

                    case VehicleKind.LocalVariant:
                        return vehicle?.engineDisplacement ?? 0;

                    default:
                        return 0;
                }
            })();

            return {
                ...common,
                interestRate: payload.interestRate,
                displacement,
                ...getDownpaymentAndLoan(payload, totalPrice),
            };
        }

        case FinanceProductType.HirePurchaseWithBalloon: {
            const { downPayment, loan } = getDownpaymentAndLoan(payload, totalPrice);

            const basedOnValue = (() => {
                switch (financeProduct.balloon.basedOn) {
                    case BalloonBasedOn.CarPrice:
                        return carPrice;

                    case BalloonBasedOn.LoanAmount:
                        return loan.amount;

                    case BalloonBasedOn.TotalPrice:
                        return totalPrice;

                    default:
                        return null;
                }
            })();

            let balloonPayment: { percentage: number; amount: number };
            if (payload.balloon) {
                balloonPayment = toValidCompoundValue(
                    payload.balloon,
                    payload.balloonUnit === '%' ? AmountUnit.Percentage : AmountUnit.Currency,
                    basedOnValue
                );
            }

            return {
                ...common,
                interestRate: payload.interestRate,
                downPayment,
                carPrice: payload.carPrice,
                loan,
                balloonPayment,
            };
        }

        case FinanceProductType.LeasePurchase: {
            let residualValue = null;

            if (!isNil(payload.residualValue)) {
                residualValue =
                    payload.residualValueUnit === '%'
                        ? payload.residualValue
                        : toUnitValue(
                              payload.residualValue,
                              payload.carPrice,
                              AmountUnit.Currency,
                              AmountUnit.Percentage
                          );
            }

            return {
                ...common,
                interestRate: payload.interestRate,
                residualValue,
                carPrice: payload.carPrice,
                mileage: payload.mileage,
                ...getDownpaymentAndLoan(payload, totalPrice),
            };
        }

        default:
            return null;
    }
};

const isFinite = (value: unknown): value is number => Number.isFinite(value);

const execute = async (
    marketType: ApplicationMarket,
    calculator: Calculator,
    query: Query,
    rounding: CompanyRounding,
    response: SuccessResponse,
    financeProduct: LocalFinanceProduct
) => {
    const { collections } = await getDatabaseContext();
    // perform the calculation
    calculator.calculate(query);

    // update response accordingly
    switch (calculator.product.type) {
        case FinanceProductType.HirePurchase:
        case FinanceProductType.DeferredPrincipal:
        case FinanceProductType.LeasePurchase:
        case FinanceProductType.HirePurchaseWithBalloon:
        case FinanceProductType.UCCLLeasing: {
            const downPayment = (calculator.root.seek(DownPayment) as DownPayment).value;
            response.downPayment = {
                value: rounding.amount.up(downPayment.amount),
                percentage: rounding.percentage.up(downPayment.percentage),
            };

            response.loan = {
                value: query.totalPrice - response.downPayment.value,
                percentage: 100 - response.downPayment.percentage,
            };

            const webCalcSetting = financeProduct.webCalcSettingId
                ? await collections.settings.findOne({ _id: financeProduct.webCalcSettingId })
                : null;

            const webCalcMonthlyInstallment =
                webCalcSetting?.settingId === SettingId.WebCalc
                    ? await calculateWithWebCalc(webCalcSetting, financeProduct, {
                          ...getWebCalcParametersFromCalculator(calculator),
                          totalPrice: response.totalPrice,
                      })
                    : null;

            const calculated = !isNil(webCalcMonthlyInstallment)
                ? webCalcMonthlyInstallment
                : (calculator.root.seek(MonthlyInstalment) as MonthlyInstalment).value;

            response.interestRate = (calculator.root.seek(InterestRate) as InterestRate).value;

            // For Hire Purchase and UCCL Leasing
            if (calculator.product.type === FinanceProductType.HirePurchase) {
                if (!isFinite(calculated)) {
                    throw new Error('Invalid result for Hire Purchase');
                }

                response.monthlyInstalments = [
                    {
                        start: 1,
                        end: query.term,
                        value: rounding.amount.up(calculated),
                    },
                ];

                response.optionFactor = calculated / calculator.query.totalPrice;

                if (marketType === ApplicationMarket.NewZealand) {
                    response.totalAmountPayable = rounding.amount.round(calculated * calculator.query.term);
                }
            }

            // For Hire Purchase balloon
            if (calculator.product.type === FinanceProductType.HirePurchaseWithBalloon) {
                if (!isFinite(calculated)) {
                    throw new Error('Invalid result for Hire Purchase with Balloon');
                }

                response.monthlyInstalments = [
                    {
                        start: 1,
                        end: query.term,
                        value: rounding.amount.up(calculated),
                    },
                ];

                response.optionFactor = calculated / calculator.query.totalPrice;

                // For Hire Purchase with Balloon, the balloon is returned
                const balloonPayment = calculator.root.seek(BalloonPayment) as BalloonPayment;
                response.balloon = {
                    value: rounding.amount.down(balloonPayment.value.amount),
                    percentage: rounding.percentage.down(balloonPayment.value.percentage),
                };

                if (marketType === ApplicationMarket.NewZealand) {
                    response.totalAmountPayable = rounding.amount.round(
                        calculated * calculator.query.term + (balloonPayment?.value?.amount ?? 0)
                    );
                }
            }

            // For Deferred Principal
            if (calculator.product.type === FinanceProductType.DeferredPrincipal) {
                if (!isArray(calculated) || calculated.length !== 2) {
                    throw new Error('Invalid result for Deferred Principal');
                }

                const { interestOnly } = calculator.product.term;
                const [first, remaining] = calculated;
                response.monthlyInstalments = [
                    {
                        start: 1,
                        end: interestOnly,
                        value: rounding.amount.up(first),
                    },
                    {
                        start: interestOnly + 1,
                        end: query.term,
                        value: rounding.amount.up(remaining),
                    },
                ];

                // total amount paid for the loan
                const paid = interestOnly * first + remaining * (query.term - interestOnly);
                // average amount paid per month
                const average = paid / query.term;
                response.optionFactor = average / calculator.query.totalPrice;

                if (marketType === ApplicationMarket.NewZealand) {
                    response.totalAmountPayable = rounding.amount.round(first * calculator.query.term);
                }
            }

            // For Lease Purchase
            if (calculator.product.type === FinanceProductType.LeasePurchase) {
                if (!isFinite(calculated)) {
                    throw new Error('Invalid result for Lease Purchase');
                }
                response.monthlyInstalments = [
                    {
                        start: 1,
                        end: query.term,
                        value: rounding.amount.up(calculated),
                    },
                ];
                const mileage = (calculator.root.seek(Mileage) as Mileage).value;
                response.mileage = mileage;

                const residualValue = (calculator.root.seek(ResidualValue) as ResidualValue).value;

                response.residualValue = {
                    value: rounding.amount.up(residualValue.amount),
                    percentage: rounding.percentage.up(residualValue.percentage),
                };

                response.optionFactor = calculated / calculator.query.totalPrice;
            }

            if (calculator.product.type === FinanceProductType.UCCLLeasing) {
                if (!isFinite(calculated)) {
                    throw new Error('Invalid result for UCCL Leasing');
                }

                response.monthlyInstalments = [
                    {
                        start: 1,
                        end: query.term,
                        value: rounding.amount.up(calculated),
                    },
                ];

                response.optionFactor = calculated / calculator.query.totalPrice;

                if (marketType === ApplicationMarket.NewZealand) {
                    response.totalAmountPayable = rounding.amount.round(calculated * calculator.query.term);
                }
            }

            break;
        }
    }
};

const handler: RequestHandler<unknown, Response, Body> = async (req, res) => {
    const { module, setting, headerSettingId } = await getCtsConfigurationFromRequest(req);
    if (!module) {
        res.status(400).send(getError(ErrorCode.AuthorizationError, 'Unauthorized request'));

        return;
    }

    if (!setting) {
        res.status(400).json(getError(ErrorCode.AuthorizationError, 'Invalid setting ID'));

        return;
    }

    let payload = req.body;

    const errors = baseValidator.validate(payload);
    if (errors) {
        res.status(400).json(getError(mapValidationErrorCode(errors), JSON.stringify(errors)));

        return;
    }

    // local variant validation
    if (payload.vehicleReference.type === 'vehicle') {
        if (isNil(payload.vehicleReference.engineType) && isNil(payload.vehicleReference.orderTypeCode)) {
            res.status(400).json(
                getError(ErrorCode.ValidationError, 'request must include order type code or other vehicle parameters')
            );

            return;
        }

        if (
            payload.vehicleReference.engineType &&
            !VALID_ENGINE_TYPE.includes(payload.vehicleReference.engineType.toLowerCase())
        ) {
            res.status(400).json(getError(ErrorCode.ValidationError, 'Invalid engine type'));

            return;
        }
    }

    // finder vehicle validation
    if (payload.vehicleReference.type === 'finderVehicle') {
        if (
            isNil(payload.vehicleReference.engineType) &&
            isNil(payload.vehicleReference.modelYear) &&
            isNil(payload.vehicleReference.mileage) &&
            isNil(payload.vehicleReference.orderTypeCode) &&
            isNil(payload.vehicleReference.condition) &&
            isNil(payload.vehicleReference.vehicleId)
        ) {
            res.status(400).json(
                getError(
                    ErrorCode.ValidationError,
                    // eslint-disable-next-line max-len
                    'request must include vehicle id or other vehicle parameters '
                )
            );

            return;
        }

        if (payload.vehicleReference.condition && !VALID_CONDITIONS.includes(payload.vehicleReference.condition)) {
            res.status(400).json(getError(ErrorCode.ValidationError, 'Invalid condition'));

            return;
        }

        if (
            payload.vehicleReference.engineType &&
            !VALID_ENGINE_TYPE.includes(payload.vehicleReference.engineType.toLowerCase())
        ) {
            res.status(400).json(getError(ErrorCode.ValidationError, 'Invalid engine type'));

            return;
        }
    }

    const { collections } = await getDatabaseContext();

    const { financeProductId, vehicleReference } = payload;
    // first check if can find FP by financeProductId
    const financeProduct = await collections.financeProducts.findOne({
        ...getProductionFinanceProductsFilter(setting.financeProductSuiteIds),
        identifier: financeProductId,
    });

    // Check supported FP
    if (!financeProduct || !SUPPORTED_FP_TYPES.includes(financeProduct.type)) {
        res.status(400).json(getError(CtsErrorCode.FinanceProductNotFound, 'Invalid finance product ID'));

        return;
    }

    const hasVehicleId =
        (vehicleReference.type === 'finderVehicle' && vehicleReference.vehicleId) ||
        (vehicleReference.type === 'vehicle' && vehicleReference.orderTypeCode);

    const vehicle = hasVehicleId
        ? await collections.vehicles.findOne(
              getProductionVehicleNewFilter(
                  setting.vehicleModuleIds,
                  vehicleReference.type === 'finderVehicle' ? vehicleReference.vehicleId : undefined,
                  vehicleReference.type === 'vehicle' ? vehicleReference.orderTypeCode : undefined
              )
          )
        : null;

    const hasParameter =
        (vehicleReference.type === 'finderVehicle' &&
            (vehicleReference.engineType ||
                vehicleReference.modelYear ||
                vehicleReference.mileage ||
                vehicleReference.condition ||
                vehicleReference.orderTypeCode)) ||
        (vehicleReference.type === 'vehicle' && vehicleReference.engineType);

    // only no parameter but has vehicleId(or orderTypeCode) and not found vehicle
    // will throw VehicleNotFound error
    if (!hasParameter && hasVehicleId && !vehicle) {
        res.status(400).json(getError(CtsErrorCode.VehicleNotFound, 'Invalid vehicle id or order type code'));

        return;
    }

    // filter FP
    const getFilteredFinanceProducts = async () => {
        // get FP by vehicle
        const financeProductsByVehicle = vehicle
            ? [financeProduct].filter(financeProduct => isVehicleAssignedToFinanceProduct(financeProduct, vehicle))
            : [];
        // get FP by params
        const financeProductsByParameters = hasParameter
            ? filterFinanceProduct([financeProduct], vehicleReference)
            : [];

        if (financeProductsByVehicle.length) {
            return financeProductsByVehicle;
        }
        if (financeProductsByParameters.length) {
            return financeProductsByParameters;
        }

        return [];
    };

    const filteredFinanceProducts = await getFilteredFinanceProducts();

    if (filteredFinanceProducts.length <= 0) {
        res.status(400).json(
            getError(CtsErrorCode.FinanceProductNotFound, 'Invalid vehicle id or order type code or parameter')
        );

        return;
    }

    const [finalFinanceProduct] = filteredFinanceProducts;

    // validate downpayment and loan
    const downpaymentErrors = calculationRequestValidator(finalFinanceProduct).validate(payload);
    if (downpaymentErrors) {
        res.status(400).json(getError(mapValidationErrorCode(downpaymentErrors), JSON.stringify(downpaymentErrors)));

        return;
    }

    if (isNil(req.body?.downPayment) && isNil(req.body?.loan)) {
        payload = {
            ...req.body,
            downPayment: 0,
            downPaymentUnit: '%',
        };
    }

    // validation for FP
    const fpValidationErrors = financeProductValidator(finalFinanceProduct.type).validate(payload);
    if (fpValidationErrors) {
        res.status(400).json(getError(mapValidationErrorCode(fpValidationErrors), JSON.stringify(fpValidationErrors)));

        return;
    }

    // Get company for getting precision
    const company = await collections.companies.findOne({ _id: module.companyId });

    // prepare market type value
    const dealers = await collections.dealers.find({ companyId: module.companyId }).toArray();
    const marketTypeValue = retrieveMarketTypeValue(
        vehicle?._kind === VehicleKind.FinderVehicle ? 'finderVehicle' : 'vehicle',
        payload.condition,
        dealers,
        company
    );

    const formats = getCompanyFormats(company);
    const rounding = getCompanyRounding(company);

    const calculator = new Calculator(finalFinanceProduct, getStepFromCalculationRounding(company.calculationRounding));
    const commonDocuments = {
        module,
        vehicle,
        financeProduct: finalFinanceProduct,
        rounding,
        setting,
        marketTypeValue,
    };

    const prepared = prepareValues(commonDocuments, payload);

    const response: SuccessResponse = {
        ...prepared,
        carPrice: payload.carPrice,
        settingId: headerSettingId,
    };

    const condition =
        vehicle?._kind === VehicleKind.FinderVehicle ? vehicle?.listing?.vehicle?.condition?.value : undefined;

    const retrieveTemplate = await getDisclaimerTemplateFunction(company._id, vehicle);

    response.disclaimer = getPriceDisclaimer(
        setting,
        formats,
        marketTypeValue,
        condition,
        payload.marketFees,
        retrieveTemplate
    );

    const vehicleKind = (() => {
        if (vehicle) {
            return vehicle?._kind;
        }

        if (vehicleReference.type === 'vehicle') {
            return VehicleKind.LocalVariant;
        }
        if (vehicleReference.type === 'finderVehicle') {
            return VehicleKind.FinderVehicle;
        }

        return null;
    })();

    let updatedQuery;

    try {
        const query = getCalculatorQuery(commonDocuments, payload, prepared);
        if (isEmpty(query)) {
            res.status(400).json(getError(ErrorCode.CalculatorError, 'Unsupported calculation query'));

            return;
        }

        const queryValidationErrors = calculatorQueryValidator(
            finalFinanceProduct,
            query.finderRestrictedValue
        ).validate(query);
        if (queryValidationErrors) {
            res.status(400).json(
                getError(mapValidationErrorCode(queryValidationErrors), JSON.stringify(queryValidationErrors))
            );

            return;
        }

        updatedQuery = {
            ...query,
            vehicleKind,
        };

        await execute(setting.market.type, calculator, updatedQuery, rounding, response, finalFinanceProduct);
    } catch (error) {
        console.error(error);

        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setLevel('fatal');
            scope.setContext('calculation', updatedQuery);
            Sentry.captureException(error);
        });

        res.status(400).json(getError(mapCalculationError(error), 'Internal error happened when calculating'));

        return;
    }

    res.status(200).json(response);
};

export default handler;
