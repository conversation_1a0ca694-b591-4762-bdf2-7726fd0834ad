import { isNil } from 'lodash/fp';
import { CompoundValue, FinderRestrictedValuePayload } from '../../calculator';
import { isOnMatchingUnitStep, isWithinRange, isWithinRangeAndOnStep } from '../../calculator/nodes/utils';
import {
    AmountUnit,
    BalloonBasedOn,
    FinanceProductBasedOn,
    FinanceProductType,
    LocalFinanceProduct,
} from '../../database/documents';
import validators from '../../utils/validators';
import { CtsErrorCode } from './shared';

const baseValidator = validators.compose(
    validators.requiredString('financeProductId'),
    validators.requiredString('vehicleReference.type')
);

const calculationRequestValidator = (financeProduct: LocalFinanceProduct) =>
    validators.compose(
        validators.requiredNumber('term', CtsErrorCode.CalculateTermMissing),
        validators.requiredNumber('interestRate', CtsErrorCode.CalculateInterestRateMissing),
        validators.requiredNumber('carPrice'),

        validators.only(
            values =>
                (financeProduct.type === FinanceProductType.HirePurchase ||
                    financeProduct.type === FinanceProductType.HirePurchaseWithBalloon ||
                    financeProduct.type === FinanceProductType.HirePurchaseWithBalloonGFV ||
                    financeProduct.type === FinanceProductType.DeferredPrincipal ||
                    financeProduct.type === FinanceProductType.UCCLLeasing) &&
                financeProduct.downPayment.type !== 'table',
            validators.compose(
                validators.only(
                    values => isNil(values.downPayment) && isNil(values.loan),
                    validators.compose(
                        validators.requiredNumber('downPayment', CtsErrorCode.CalculateDownPaymentMissing),
                        validators.requiredNumber('loan', CtsErrorCode.CalculateLoanMissing)
                    )
                ),

                validators.only(
                    values => !isNil(values.downPayment),
                    validators.compose(
                        validators.requiredNumber('downPayment', CtsErrorCode.CalculateDownPaymentMissing),
                        validators.requiredString('downPaymentUnit')
                    )
                ),
                validators.only(
                    values => !isNil(values.loan),
                    validators.compose(validators.requiredNumber('loan'), validators.requiredString('loanUnit'))
                )
            )
        )
    );

const hirePurchaseWithBalloonValidator = () =>
    validators.compose(
        validators.requiredNumber('balloon', CtsErrorCode.CalculateBalloonMissing),
        validators.requiredString('balloonUnit')
    );

const leasePurchaseValidator = () =>
    validators.compose(validators.requiredNumber('mileage', CtsErrorCode.CalculateMileageMissing));

const financeProductValidator = (financeProductType: FinanceProductType) =>
    validators.compose(
        validators.only(
            values => financeProductType === FinanceProductType.HirePurchaseWithBalloon,
            hirePurchaseWithBalloonValidator()
        ),
        validators.only(values => financeProductType === FinanceProductType.LeasePurchase, leasePurchaseValidator())
    );

const calculatorQueryValidator = (
    financeProduct: LocalFinanceProduct,
    finderRestrictedValue?: FinderRestrictedValuePayload
) =>
    validators.compose(
        validators.custom('downPayment', (value, values) => {
            const { amount, percentage } = value as CompoundValue;

            if (
                !('downPayment' in financeProduct) ||
                financeProduct.basedOn === FinanceProductBasedOn.Loan ||
                financeProduct.downPayment.type === 'table'
            ) {
                return null;
            }

            const { downPayment } = financeProduct;
            const { min, max, unit } = downPayment;

            const valueToCheck: number = unit === AmountUnit.Currency ? amount : percentage;
            if (
                min !== max &&
                (!value ||
                    !isWithinRange(valueToCheck, downPayment) ||
                    !isOnMatchingUnitStep(values.totalPrice, value, downPayment))
            ) {
                return CtsErrorCode.CalculateDownPaymentInvalid;
            }

            return null;
        }),
        validators.custom('loan', (value, values) => {
            const { amount, percentage } = value as CompoundValue;

            if (
                !('loan' in financeProduct) ||
                financeProduct.basedOn === FinanceProductBasedOn.DownPayment ||
                !('min' in financeProduct.loan)
            ) {
                return null;
            }

            const { loan } = financeProduct;
            const { min, max, unit } = loan;

            const valueToCheck: number = unit === AmountUnit.Currency ? amount : percentage;

            if (
                min !== max &&
                (!value || !isWithinRange(valueToCheck, loan) || !isOnMatchingUnitStep(values.totalPrice, value, loan))
            ) {
                return CtsErrorCode.CalculateLoanInvalid;
            }

            return null;
        }),
        validators.custom('balloonPayment', (value, values) => {
            if (
                !('balloon' in financeProduct) ||
                financeProduct.type === FinanceProductType.HirePurchaseWithBalloonGFV ||
                financeProduct.balloon.type === 'table'
            ) {
                return null;
            }

            const { amount, percentage } = value as CompoundValue;
            const { balloon } = financeProduct;
            const { unit, min, max, basedOn } = balloon;

            const basedOnValue = ((): number | null => {
                switch (basedOn) {
                    case BalloonBasedOn.CarPrice:
                        return values.carPrice;

                    case BalloonBasedOn.LoanAmount:
                        return values.loan?.amount;

                    case BalloonBasedOn.TotalPrice:
                        return values.totalPrice;

                    default:
                        return null;
                }
            })();

            const valueToCheck: number = unit === AmountUnit.Currency ? amount : percentage;

            if (
                min !== max &&
                (!value || !isWithinRange(valueToCheck, balloon) || !isOnMatchingUnitStep(basedOnValue, value, balloon))
            ) {
                return CtsErrorCode.CalculateBalloonInvalid;
            }

            return null;
        }),
        validators.custom('mileage', (value, values) => {
            if (!('residualValue' in financeProduct)) {
                return null;
            }

            const { residualValue } = financeProduct;
            const { averageMileage } = residualValue;
            const { min, max } = averageMileage;

            if (min !== max && (!value || !isWithinRangeAndOnStep(value, averageMileage))) {
                return CtsErrorCode.CalculateMileageInvalid;
            }

            return null;
        }),
        validators.custom('interestRate', (value, values) => {
            if (!('interestRate' in financeProduct) || financeProduct.interestRate.type === 'table') {
                return null;
            }

            if (financeProduct.interestRate.type === 'fixed') {
                const { interestRate } = financeProduct;

                return value === interestRate.default ? null : CtsErrorCode.CalculateInterestRateInvalid;
            }

            if (financeProduct.interestRate.type === 'range') {
                const { interestRate } = financeProduct;
                const { min, max } = interestRate;

                if (min !== max && (!value || !isWithinRangeAndOnStep(value, interestRate))) {
                    return CtsErrorCode.CalculateInterestRateInvalid;
                }
            }

            return null;
        }),
        validators.custom('term', (value, values) => {
            if (!('term' in financeProduct)) {
                return null;
            }

            const { term } = financeProduct;
            const updatedTerm = finderRestrictedValue ? { ...term, max: finderRestrictedValue.maxTerm } : term;
            const { min, max } = updatedTerm;

            // only for preowned finder vehicle = finderRestrictedValue
            if (min !== max && (!value || !isWithinRangeAndOnStep(value, updatedTerm, finderRestrictedValue))) {
                return CtsErrorCode.CalculateTermInvalid;
            }

            return null;
        })
    );

export { baseValidator, financeProductValidator, calculatorQueryValidator, calculationRequestValidator };
