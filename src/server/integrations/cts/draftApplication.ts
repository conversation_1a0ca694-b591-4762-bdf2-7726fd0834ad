import * as Sentry from '@sentry/node';
import dayjs from 'dayjs';
import { RequestHandler } from 'express';
import { isNil, omit } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../core/config';
import {
    ApplicationDraftedAuditTrail,
    ApplicationFinancing,
    ApplicationJourney,
    ApplicationKind,
    ApplicationMarket,
    ApplicationStatus,
    ApplicationValueSetting,
    AuditTrailKind,
    AuthorKind,
    CTSFinderRedirectionLink,
    CtsSetting,
    CustomerKind,
    DefaultApplicationFinancing,
    EndpointType,
    ExternalLinkKind,
    FinancingPreferenceValue,
    FinderApplication,
    FinderApplicationPublicModule,
    FinderVehicleCondition,
    FinderVehicleStatus,
    Lead,
    LeadStatus,
    LocalCustomer,
    LocalFinanceProduct,
    ModuleType,
    MonthlyInstalment,
    NewZealandApplicationFinancing,
    SingaporeApplicationFinancing,
    VehicleKind,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    getApplicationInitialStage,
    retrieveFeatureProps,
} from '../../database/helpers/applications';
import { executeFinderJourney } from '../../journeys/finderJourney';
import { validateEndpoint } from '../../schema/resolvers/mutations/applications/helpers';
import { getApplicationLogStages } from '../../utils/application';
import { getDealershipPaymentSetting } from '../../utils/getDealershipPaymentSetting';
import {
    CustomerAuthoring,
    getAdvancedVersioningBySystemForCreation,
    getAdvancedVersioningForCreation,
    getAuthorFromAuthoring,
} from '../../utils/versioning';
import { ErrorCode, getError } from '../utils';
import { getCtsConfigurationFromRequest, ValueAndPercentage } from './shared';

type MonthlyInstallment = {
    value: number;
    end: number;
    start: number;
};

type StandardVehicleParameter = {
    type: 'vehicle';
    vehicleId: string;
};

type FinderVehicleParameter = {
    type: 'finderVehicle';
    vehicleId: string;
};

type PayloadFinancing = {
    carPrice: number;
    totalPrice: number;
    financeProductId: string;
    carOptions?: {
        value: number;
        name?: string;
    };
    marketFees?: {
        coe?: number;
        ppsr?: number;
        establishmentFee?: number;
    };
    monthlyInstalments: MonthlyInstallment[];
    interestRate: number;
    term: number;
    downPayment: ValueAndPercentage;
    loan: ValueAndPercentage;
    balloon?: ValueAndPercentage;
    mileage?: number;
    residualValue?: ValueAndPercentage;
};

type Body = {
    vehicleReference: StandardVehicleParameter | FinderVehicleParameter;
    financing: PayloadFinancing;
};

const retrieveFinderVehicleCondition = (value: string): FinderVehicleCondition => {
    switch (value) {
        case 'new':
            return FinderVehicleCondition.New;

        case 'preowned':
            return FinderVehicleCondition.Preowned;

        case 'porscheApproved':
            return FinderVehicleCondition.PorscheApproved;

        default:
            return null;
    }
};

enum ApplicationFinancingCalculationType {
    DownPayment = 'downPayment',
    Loan = 'loan',
    Balloon = 'balloon',
    ResidualValue = 'residualValue',
}

const convertApplicationValueSetting = (
    type: ApplicationFinancingCalculationType,
    value: ValueAndPercentage
): ApplicationValueSetting => {
    switch (type) {
        case ApplicationFinancingCalculationType.DownPayment:
        case ApplicationFinancingCalculationType.Loan:
        case ApplicationFinancingCalculationType.Balloon:
        case ApplicationFinancingCalculationType.ResidualValue:
            return {
                amount: value.value,
                percentage: value.percentage,
            };

        default:
            throw new Error('Application Financing Calculation Type not found');
    }
};

const defaultApplicationFinancing = (
    financing: PayloadFinancing,
    financeProduct: LocalFinanceProduct,
    setting: CtsSetting
) => {
    const monthlyInstalment: MonthlyInstalment[] = financing.monthlyInstalments.map(installment => ({
        ...installment,
        amount: installment.value,
    }));

    return {
        ...omit(['marketFees', 'carOptions', 'monthlyInstalments', 'loan', 'balloon', 'residualValue'], financing),
        financeProductId: financeProduct._id,
        market: setting.market.type,
        financedAmount: financing.totalPrice,
        term: financing.term,
        monthlyInstalment,
        loan: convertApplicationValueSetting(ApplicationFinancingCalculationType.Loan, financing.loan),
        balloonPayment: financing?.balloon
            ? convertApplicationValueSetting(ApplicationFinancingCalculationType.Balloon, financing?.balloon)
            : null,
        residualValue: financing.residualValue
            ? convertApplicationValueSetting(ApplicationFinancingCalculationType.Loan, financing.residualValue)
            : null,
        downPayment: convertApplicationValueSetting(
            ApplicationFinancingCalculationType.DownPayment,
            financing.downPayment
        ),
    };
};

const retrieveFinancingApplication = (
    financing: PayloadFinancing,
    financeProduct: LocalFinanceProduct,
    setting: CtsSetting
): ApplicationFinancing => {
    const defaultApplicationFinancingValue = defaultApplicationFinancing(financing, financeProduct, setting);

    if (financing?.marketFees?.coe) {
        if (defaultApplicationFinancingValue.market !== ApplicationMarket.Singapore) {
            return null;
        }

        return {
            ...defaultApplicationFinancingValue,
            coe: financing.marketFees.coe,
            financedAmount: financing.totalPrice + financing.marketFees.coe,
        } as SingaporeApplicationFinancing;
    }

    if (financing?.marketFees?.ppsr || financing?.marketFees?.establishmentFee) {
        if (defaultApplicationFinancingValue.market !== ApplicationMarket.NewZealand) {
            return null;
        }

        return {
            ...defaultApplicationFinancingValue,
            estFee: financing.marketFees.establishmentFee,
            ppsr: financing.marketFees.ppsr,
            financedAmount: financing.totalPrice + financing.marketFees.ppsr + financing.marketFees.establishmentFee,
        } as NewZealandApplicationFinancing;
    }

    if (defaultApplicationFinancingValue.market !== ApplicationMarket.Default) {
        return null;
    }

    return defaultApplicationFinancingValue as DefaultApplicationFinancing;
};

type Params = {
    Authorization: string;
    'Setting-Id': string;
};

const getVehicleTypeFilter = (
    vehicleReference: StandardVehicleParameter | FinderVehicleParameter,
    moduleIds: ObjectId[]
) => {
    switch (vehicleReference.type) {
        case 'finderVehicle':
            return {
                _kind: VehicleKind.FinderVehicle,
                'listing.id': vehicleReference.vehicleId,
                isDeleted: false,
                '_versioning.isLatest': true,
                moduleId: { $in: moduleIds },
                status: FinderVehicleStatus.Available,
            };

        // TOConfirm :: ICC is using `identifier`.
        // for AN-1828 :: type `vehicle` should throw not support
        // case 'vehicle':
        //     return {
        //         _kind: VehicleKind.LocalVariant,
        //         identifier: vehicleId,
        //     };

        default:
            return null;
    }
};
const handler: RequestHandler<Params, unknown, Body> = async (req, res) => {
    try {
        const { module, setting } = await getCtsConfigurationFromRequest(req);
        if (!module) {
            res.status(400).send(getError(ErrorCode.AuthorizationError, 'Unauthorized request'));

            return;
        }

        const { collections } = await getDatabaseContext();
        const { vehicleReference, financing: financingPayload } = req.body;
        const { vehicleId, type } = vehicleReference;
        const { financeProductId } = financingPayload;

        const filter = getVehicleTypeFilter(vehicleReference, setting.vehicleModuleIds);

        if (isNil(filter)) {
            res.status(400).send(
                getError(ErrorCode.GeneralError, `Vehicle type(${vehicleReference.type}) not supported`)
            );
        }

        const vehicle = await collections.vehicles.findOne(filter);
        const finderModules = await collections.modules.find({ _id: { $in: setting.finderModuleIds } }).toArray();

        const financeProduct = await collections.financeProducts.findOne({ identifier: financeProductId });

        const finderModule = finderModules.find(module => {
            if (
                module._type !== ModuleType.FinderApplicationPublicModule ||
                vehicle._kind !== VehicleKind.FinderVehicle
            ) {
                return null;
            }

            return module.finderVehicleConditions.includes(
                retrieveFinderVehicleCondition(vehicle.listing.vehicle.condition.value)
            );
        }) as FinderApplicationPublicModule;

        if (!finderModule) {
            res.status(400).json(getError(ErrorCode.ValidationError, 'Finder Module or Vehicle not found'));

            return;
        }

        const customerModule = await collections.modules.findOne({ _id: finderModule.customerModuleId });

        if (!customerModule) {
            res.status(400).json(getError(ErrorCode.ValidationError, 'Customer Module not found'));

            return;
        }

        const applicant: LocalCustomer = {
            _id: new ObjectId(),
            _kind: CustomerKind.Local,
            moduleId: customerModule._id,
            kycPresetIds: [],
            fields: [],
            isDeleted: false,
            _versioning: getAdvancedVersioningBySystemForCreation(),
        };

        // create the customer field first
        await collections.customers.insertOne(applicant);

        const assigneeId =
            (finderModule._type === ModuleType.FinderApplicationPublicModule
                ? finderModule.assignee.defaultId
                : null) ?? null;

        const financing = retrieveFinancingApplication(req.body.financing, financeProduct, setting);

        const author: CustomerAuthoring = {
            kind: AuthorKind.Customer,
            customerId: applicant._id,
            date: applicant._versioning.createdAt,
        };

        const dealer =
            vehicle._kind === VehicleKind.FinderVehicle
                ? await collections.dealers.findOne({
                      'integrationDetails.partnerNumber': vehicle.listing.seller.porschePartnerNumber,
                  })
                : null;

        if (!dealer) {
            res.status(400).json(getError(ErrorCode.ValidationError, 'Dealer not found'));

            return;
        }
        const router = await collections.routers.findOne({ companyId: finderModule.companyId });

        if (!router) {
            res.status(400).json(getError(ErrorCode.ValidationError, 'Router not found'));

            return;
        }
        const endpoint = router.endpoints.find(
            endpoint => endpoint._type === EndpointType.FinderApplicationPublicAccessEntrypoint
        );

        if (!endpoint) {
            res.status(400).json(getError(ErrorCode.ValidationError, 'Endpoint not found'));

            return;
        }

        const paymentSetting = await getDealershipPaymentSetting(dealer._id, finderModule, { collections });

        const stage = getApplicationInitialStage({
            dealer,
            paymentSetting,
            scenarios: finderModule.scenarios,
            withFinancing: true, // configuration.withFinancing
            isFinancingOptional: finderModule.financingPreference !== FinancingPreferenceValue.Mandatory,
            withInsurance: false, // configuration.withInsurance
            isInsuranceOptional: false, // finderModule.isInsuranceOptional
            testDrive: false, // configuration.testDrive
        });

        const origins = await validateEndpoint(endpoint._id, finderModule);

        const lead: Lead = {
            _id: new ObjectId(),
            kind: ApplicationKind.Finder,
            status: LeadStatus.Drafted,
            isLead: false,
            moduleId: finderModule._id,
            vehicleId: vehicle._id,
            customerId: applicant._id,
            dealerId: dealer._id,
            isDraft: true,
            identifier: '',
            tradeInVehicle: [],
            documents: [],
            _versioning: getAdvancedVersioningForCreation(author),
        };

        const application: FinderApplication = {
            _id: new ObjectId(),
            kind: ApplicationKind.Finder,
            moduleId: finderModule._id,
            scenarios: finderModule._type === ModuleType.FinderApplicationPublicModule ? finderModule.scenarios : [],
            stages: [stage],
            ...getApplicationInitialStageDetails(
                stage,
                ApplicationStatus.Drafted,
                assigneeId,
                await retrieveFeatureProps(finderModule._id)
            ),
            isDraft: true,
            applicantId: applicant._id,
            vehicleId: vehicle._id,
            bankId: financeProduct.bankId,

            dealerId: dealer?._id ?? null,
            promoCodeId: null,
            remarks: '',
            documents: [],
            guarantorId: [],
            _versioning: getAdvancedVersioningForCreation(author),
            configuration: {
                withFinancing: true,
                withInsurance: false,
                testDrive: false,
                tradeIn: false,
                requestForFinancing: true,
            },
            tradeInVehicle: [],
            financing,
            insurancing: null,
            referenceApplicationSuiteIds: [],
            languageId: null,
            ...origins,

            withCustomerDevice: false,
            useMyinfo: {
                customer: false,
                guarantor: false,
            },
            ctsSettingId: setting._id,

            leadId: lead._id,
        };

        await collections.leads.insertOne(lead);
        await collections.applications.insertOne(application);

        // generate the trail
        const trail: ApplicationDraftedAuditTrail = {
            _id: new ObjectId(),
            _kind: AuditTrailKind.ApplicationDrafted,
            _date: applicant._versioning.createdAt,
            applicationId: application._id,
            applicationSuiteId: application._versioning.suiteId,
            stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
            author: getAuthorFromAuthoring(author),
        };

        // register the trail
        await collections.auditTrails.insertOne(trail);

        const company = await collections.companies.findOne({ _id: finderModule.companyId });

        // create an external link for the user to navigate to offer page
        const externalLink: CTSFinderRedirectionLink = {
            _id: new ObjectId(),
            _kind: ExternalLinkKind.CTSFinderRedirection,
            data: {
                applicationId: application._id,
                endpointId: endpoint._id,
                routerId: router._id,
                type,
                vehicleId,
                settingId: setting._id,
            },
            deleteOnFetch: false,
            expiresAt: dayjs().add(company.sessionTimeout, 'minute').toDate(),
            secret: nanoid(),
        };

        await collections.externalLinks.insertOne(externalLink);

        // build up application journey
        const applicationJourney: ApplicationJourney = {
            _id: new ObjectId(),
            applicationSuiteId: application._versioning.suiteId,
            updatedAt: applicant._versioning.createdAt,
            isReceived: false,
            isImmutable: false,
            isCorporateCustomer: false,
        };

        await collections.applicationJourneys.insertOne(applicationJourney);

        // finally execute the journey
        const { token } = await executeFinderJourney({
            application,
            identifier: 'drafting',
            origin: 'draft',
            user: null,
            payload: null,
        });

        res.status(200).json({
            id: token,
            url: urljoin(config.applicationEndpoint, router.pathname, `l/${externalLink.secret}`),
        });
    } catch (error) {
        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('api', 'cts endpoint');
            scope.setContext('payload', req.body);
            Sentry.captureException(error);
        });

        console.warn(error);
    }
};

export default handler;
