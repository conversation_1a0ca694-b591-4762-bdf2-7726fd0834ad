import { TFunction } from 'i18next';
import { Response } from 'node-fetch';
import {
    Application,
    ApplicationKind,
    Company,
    Customer,
    CustomerKind,
    EazyInsurerIntegrationSetting,
    KYCPreset,
    LocalCustomerFieldKey,
    Vehicle,
    VehicleKind,
} from '../../../database/documents';
import { getCustomerFullName } from '../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import authenticate from './api/authenticate';
import { logError } from './setup';

export const prepareEazyHeaders = async (setting: EazyInsurerIntegrationSetting, refetch: boolean = false) => {
    const token = await authenticate(setting, refetch);

    return {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };
};

export type GetEazyVehicleDetailsParams = {
    vehicle: Vehicle;
};

export const getEazyVehicleDetails = ({ vehicle }: GetEazyVehicleDetailsParams) => {
    switch (vehicle._kind) {
        case VehicleKind.FinderVehicle: {
            const isNew = vehicle.listing.vehicle?.condition?.value?.toLowerCase() === 'new';

            const identifier = vehicle.listing.vehicle?.orderTypeCode;

            return {
                // For porsche finder, no matter it is new condition or not
                // car make model is orderTypeCode
                carMakeModel: identifier,
                originalRegistrationDate: isNew ? new Date() : vehicle.listing?.vehicle?.firstRegistrationDate?.value,
                displacementValue: vehicle.listing?.vehicle?.displacement?.cubicCentimeter?.value,
            };
        }

        case VehicleKind.LocalVariant: {
            return {
                carMakeModel: vehicle.identifier,

                // Currently it's using new Date, as there is no preowned version yet
                originalRegistrationDate: new Date(),

                displacementValue: vehicle.engineDisplacement,
            };
        }

        default: {
            throw new Error(`Eazy: Unsupported vehicle kind: ${vehicle._kind}`);
        }
    }
};

export type InsuranceVehicleData = ReturnType<typeof getEazyVehicleDetails>;

/**
 * Since the value is based on src/app/datasets/maritalStatus.json
 * Treat it other than "MARRIED" as "SINGLE"
 */
export const getEazyMaritalStatus = (maritalStatus: string) => {
    switch (maritalStatus) {
        case 'MARRIED':
            return 'MARRIED';

        default:
            return 'SINGLE';
    }
};

export const getInsuranceCustomerData = (
    customer: Customer,
    company: Company,
    kycPresets: KYCPreset[],
    t: TFunction,
    commentsToInsurer?: string
) => {
    if (!customer) {
        throw new Error('Customer is required');
    }

    switch (customer._kind) {
        case CustomerKind.Local: {
            const fields = getLocalCustomerAggregatedFields(customer);
            const fullName = getCustomerFullName(t, customer, company, kycPresets);

            // TODO VF-875: In regards of gender, do check the availability value from NonBnaryGender
            return {
                fullName,
                identityNumber: fields[LocalCustomerFieldKey.IdentityNumber],
                dateOfBirth: fields[LocalCustomerFieldKey.Birthday],
                gender: fields[LocalCustomerFieldKey.Gender],
                maritalStatus: getEazyMaritalStatus(fields[LocalCustomerFieldKey.MaritalStatus]),
                mobile: fields[LocalCustomerFieldKey.Phone]?.value,
                email: fields[LocalCustomerFieldKey.Email],
                additionalComment: commentsToInsurer,
                driverLicensePassDate: fields[LocalCustomerFieldKey.DriverLicensePassDate],
            };
        }

        default:
            throw new Error(`Unsupported customer kind: ${customer._kind}`);
    }
};

export type InsuranceCustomerData = ReturnType<typeof getInsuranceCustomerData>;

export const getInsuranceCalculatorData = (application: Application) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
        case ApplicationKind.SalesOffer:
            return application.insurancing;

        default:
            throw new Error(`Eazy: Unsupported application kind for insurance calculator: ${application.kind}`);
    }
};

export type InsuranceCalculatorData = ReturnType<typeof getInsuranceCalculatorData>;

export const callEazyApi = async (
    fetchApi: (headers?: Record<string, string>) => Promise<Response>,
    setting: EazyInsurerIntegrationSetting
) => {
    const response = await fetchApi();

    const handleError = async apiResponse => {
        const message = await apiResponse.text();

        logError(message);

        throw new Error(message);
    };

    if (!response.ok) {
        if (response.status === 401) {
            const headers = await prepareEazyHeaders(setting, true);
            const retriedResponse = await fetchApi(headers);

            // if still not ok, we record the error
            if (!retriedResponse.ok) {
                return handleError(retriedResponse);
            }

            return retriedResponse.json();
        }

        return handleError(response);
    }

    return response.json();
};
