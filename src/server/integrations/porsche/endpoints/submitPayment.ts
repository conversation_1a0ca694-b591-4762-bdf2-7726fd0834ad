import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { ObjectId } from 'mongodb';
import urlJoin from 'url-join';
import config from '../../../core/config';
import { Application, ApplicationKind, ApplicationStage, ExternalLinkKind, User } from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getCustomerEmail } from '../../../database/helpers/customers';
import { ApplicantAgreementsStepPayload } from '../../../journeys/common';
import ApplicantAgreementsStep from '../../../journeys/common/ApplicantAgreementsStep';
import { ConfiguratorJourneyContext } from '../../../journeys/configuratorJourney';
import { EventJourneyContext } from '../../../journeys/eventJourney';
import FinderJourneyContext from '../../../journeys/finderJourney/FinderJourneyContext';
import { consumeJourneyToken, JourneyTokenPayload } from '../../../journeys/generateJourneyToken';
import { LegacyJourneyContext } from '../../../journeys/legacyJourney';
import MobilityJourneyContext from '../../../journeys/mobilityJourney/MobilityJourneyContext';
import { getPorschePaymentContext } from '../../../schema/resolvers/queries/payment/shared';
import { getApplicationIdentifier } from '../../../utils/application';
import createI18nInstance from '../../../utils/createI18nInstance';
import getIp from '../../../utils/getIp';
import toNumberWithMinor from '../../../utils/numberHelper';
import authenticate from '../authenticate';
import createTransaction from '../createTransaction';
import { TransactionStatus } from '../enums';
import getTransactionStatus from '../getTransactionStatus';
import manageTransaction from '../manageTransaction';
import { handleTransactionStatusChange, retrieveCompanyCode } from '../shared';
import { PaymentMethod, StrongCustomerAuthenticationData } from '../types';

type SubmitPaymentPayload = Pick<ApplicantAgreementsStepPayload, 'agreedConsents'> & {
    // Application Journey Token
    token: string;
    // Encrypted Payment Method
    paymentMethod: PaymentMethod;
    // Customer Authentication Data
    strongCustomerAuthenticationData: StrongCustomerAuthenticationData;
    // Promo code id
    promoCodeId?: ObjectId;
};

export const updateAgreements = async (
    {
        application,
        user,
        origin,
        ...args
    }: {
        application: Application;
        user: User | null;
        origin: JourneyTokenPayload['origin'];
        skipCompletedSteps?: boolean;
    },
    payload: ApplicantAgreementsStepPayload
) => {
    switch (application.kind) {
        case ApplicationKind.Standard: {
            const context = await LegacyJourneyContext.factory(
                application,
                user,
                origin,
                undefined,
                args.skipCompletedSteps
            );
            await ApplicantAgreementsStep.updateAgreements(payload, context);

            break;
        }

        case ApplicationKind.Event: {
            const context = await EventJourneyContext.factory(application, user, origin);
            await ApplicantAgreementsStep.updateAgreements(payload, context);

            break;
        }

        case ApplicationKind.Configurator: {
            const context = await ConfiguratorJourneyContext.factory(application, user, origin);
            await ApplicantAgreementsStep.updateAgreements(payload, context);

            break;
        }

        case ApplicationKind.Mobility: {
            const context = await MobilityJourneyContext.factory(application, user, origin);
            await ApplicantAgreementsStep.updateAgreements(payload, context);

            break;
        }

        case ApplicationKind.Finder: {
            const context = await FinderJourneyContext.factory(application, user, origin);
            await ApplicantAgreementsStep.updateAgreements(payload, context);

            break;
        }

        default:
            throw new Error('Application kind not supported');
    }
};

const submitPayment: RequestHandler<unknown, unknown, SubmitPaymentPayload> = async (req, res, next) => {
    try {
        const { token, strongCustomerAuthenticationData, paymentMethod, agreedConsents } = req.body;

        const { applicationId, origin, userId } = await consumeJourneyToken(token);

        // get the application
        const { collections } = await getDatabaseContext();
        const application = await collections.applications.findOne({
            _id: applicationId,
            '_versioning.isLatest': true,
        });

        const { i18n } = await createI18nInstance(application.languageId?.toHexString());
        await i18n.loadNamespaces(['common']);
        const { t } = i18n;

        if (!application || application.kind === ApplicationKind.Launchpad) {
            // application not found
            throw new Error('Application not found');
        }

        const ip = getIp(req);

        const customer = await collections.customers.findOne({ _id: application.applicantId });

        const { company, setting, deposit, lead } = await getPorschePaymentContext(applicationId);
        const user = userId ? await collections.users.findOne({ _id: userId }) : null;

        // update payment agreements
        await updateAgreements(
            { application, origin, user },
            {
                agreedConsents: agreedConsents.map(consent => ({ ...consent, id: new ObjectId(consent.id) })),
                ip,
            }
        );

        const credentials = {
            id: setting.secrets.apiKey,
            secret: setting.secrets.secretKey,
        };

        // get token
        const porscheToken = await authenticate(
            credentials,
            setting.secrets.loginEndpoint,
            setting.secrets.apiEndpoint
        );

        const link = await collections.externalLinks.findOne({
            'data.applicationId': application._id,
            _kind: ExternalLinkKind.PorschePaymentRedirection,
        });

        // create transaction
        const { transactionId, redirectUrl, isRedirect } = await createTransaction(
            credentials,
            setting.secrets.apiEndpoint,
            setting.secrets.endpointApiVersion,
            {
                currency: setting.currency,
                countryCode: retrieveCompanyCode(company),
                amount: toNumberWithMinor(deposit.amount, setting.currency),
                assortment: deposit.assortment,

                strongCustomerAuthenticationData: {
                    ...strongCustomerAuthenticationData,
                    shopperIP: ip,
                    deliveryEmail: getCustomerEmail(t, customer),
                },
                paymentMethod,
                confirmationPageUrl: urlJoin(config.applicationEndpoint, `/.callback/porschePayment/${link.secret}`),
                referenceId: getApplicationIdentifier(application, lead, [
                    ApplicationStage.Mobility,
                    ApplicationStage.Financing,
                    ApplicationStage.Reservation,
                ]),
            },
            porscheToken
        );

        // update journey for transaction id and payment method
        await collections.applicationJourneys.updateOne(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    'deposit.transactionId': transactionId,
                    'deposit.paymentMethod': paymentMethod.typeDisplayName,
                },
            }
        );

        const transaction = await (async () => {
            // In porsche payment v3, creating transaction can have redirection url (for 3DS)
            // When there is none, we set status to CAPTURED
            if (!isRedirect) {
                const managedTransaction = await manageTransaction(
                    credentials,
                    setting.secrets.apiEndpoint,
                    setting.secrets.endpointApiVersion,
                    {
                        transactionId,
                        countryCode: retrieveCompanyCode(company),
                        status: TransactionStatus.CAPTURED,
                    },
                    porscheToken
                );

                await handleTransactionStatusChange(application, lead, setting, transactionId, managedTransaction);

                return managedTransaction;
            }

            // When there is a redirection url
            // we should return the status and set isRedirect to true with the redirection URL
            const transactionStatus = await getTransactionStatus(
                credentials,
                setting.secrets.apiEndpoint,
                setting.secrets.endpointApiVersion,
                { transactionId, countryCode: retrieveCompanyCode(company) },
                porscheToken
            );

            return {
                status: transactionStatus,
                isRedirect: true,
                url: redirectUrl,
            };
        })();

        res.status(200).json({
            id: transactionId,
            status: transaction.status,
            redirectUrl: transaction.isRedirect ? transaction.url : null,
        });
    } catch (error) {
        next(error);
    }
};

export default submitPayment;
