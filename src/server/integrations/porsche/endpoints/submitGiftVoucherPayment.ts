import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { ObjectId } from 'mongodb';
import urlJoin from 'url-join';
import config from '../../../core/config';
import { ExternalLinkKind } from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getCustomerEmail } from '../../../database/helpers/customers';
import { ApplicantAgreementsStepPayload } from '../../../journeys/common';
import { consumeGiftVoucherJourneyToken } from '../../../journeys/giftVoucherJourney';
import GiftVoucherJourneyContext from '../../../journeys/giftVoucherJourney/GiftVoucherJourneyContext';
import ApplicantAgreementsStep from '../../../journeys/steps/giftVoucher/ApplicantAgreementsStep';
import { getGiftVoucherPorschePaymentContext } from '../../../schema/resolvers/queries/payment/shared';
import createI18nInstance from '../../../utils/createI18nInstance';
import getIp from '../../../utils/getIp';
import toNumberWithMinor from '../../../utils/numberHelper';
import authenticate from '../authenticate';
import createTransaction from '../createTransaction';
import { TransactionStatus } from '../enums';
import getTransactionStatus from '../getTransactionStatus';
import manageTransaction from '../manageTransaction';
import { handleGiftVoucherTransactionStatusChange, retrieveCompanyCode } from '../shared';
import { PaymentMethod, StrongCustomerAuthenticationData } from '../types';

type SubmitPaymentPayload = Pick<ApplicantAgreementsStepPayload, 'agreedConsents'> & {
    // Application Journey Token
    token: string;
    // Encrypted Payment Method
    paymentMethod: PaymentMethod;
    // Customer Authentication Data
    strongCustomerAuthenticationData: StrongCustomerAuthenticationData;
    // Promo code id
    promoCodeId?: ObjectId;
};

const submitGiftVoucherPayment: RequestHandler<unknown, unknown, SubmitPaymentPayload> = async (req, res, next) => {
    try {
        const { token, strongCustomerAuthenticationData, paymentMethod, agreedConsents } = req.body;

        const { giftVoucherId, origin, userId } = await consumeGiftVoucherJourneyToken(token);

        // get the application
        const { collections } = await getDatabaseContext();
        const giftVoucher = await collections.giftVouchers.findOne({
            _id: giftVoucherId,
            '_versioning.isLatest': true,
        });

        if (!giftVoucher) {
            // gift voucher not found
            throw new Error('Gift Voucher not found');
        }

        const ip = getIp(req);

        const customer = await collections.customers.findOne({
            _id: giftVoucher.purchaserId,
        });

        const { i18n } = await createI18nInstance(giftVoucher.languageId.toHexString());
        await i18n.loadNamespaces(['common']);
        const { t } = i18n;

        const { company, setting, deposit } = await getGiftVoucherPorschePaymentContext(giftVoucherId);
        const user = userId ? await collections.users.findOne({ _id: userId }) : null;

        const context = await GiftVoucherJourneyContext.factory(giftVoucher, user, origin);

        await ApplicantAgreementsStep.updateAgreements(
            {
                agreedConsents: agreedConsents.map(consent => ({ ...consent, id: new ObjectId(consent.id) })),
                ip,
            },
            context
        );

        const credentials = {
            id: setting.secrets.apiKey,
            secret: setting.secrets.secretKey,
        };

        // get token
        const porscheToken = await authenticate(
            credentials,
            setting.secrets.loginEndpoint,
            setting.secrets.apiEndpoint
        );

        const link = await collections.externalLinks.findOne({
            'data.giftVoucherId': giftVoucher._id,
            _kind: ExternalLinkKind.GiftVoucherPorschePaymentRedirection,
        });

        // create transaction
        const { transactionId, redirectUrl, isRedirect } = await createTransaction(
            credentials,
            setting.secrets.apiEndpoint,
            setting.secrets.endpointApiVersion,
            {
                currency: setting.currency,
                countryCode: retrieveCompanyCode(company),
                amount: toNumberWithMinor(deposit.amount, setting.currency),
                assortment: deposit.assortment,

                strongCustomerAuthenticationData: {
                    ...strongCustomerAuthenticationData,
                    shopperIP: ip,
                    deliveryEmail: getCustomerEmail(t, customer),
                },
                paymentMethod,
                confirmationPageUrl: urlJoin(config.applicationEndpoint, `/.callback/porschePayment/${link.secret}`),
                referenceId: giftVoucherId.toHexString(),
            },
            porscheToken
        );

        // update journey for transaction id and payment method
        await collections.giftVoucherJourney.updateOne(
            { giftVoucherSuiteId: giftVoucher._versioning.suiteId },
            {
                $set: {
                    'deposit.transactionId': transactionId,
                    'deposit.paymentMethod': paymentMethod.typeDisplayName,
                },
            }
        );

        const transaction = await (async () => {
            // In porsche payment v3, creating transaction can have redirection url (for 3DS)
            // When there is none, we set status to CAPTURED
            if (!isRedirect) {
                const managedTransaction = await manageTransaction(
                    credentials,
                    setting.secrets.apiEndpoint,
                    setting.secrets.endpointApiVersion,
                    {
                        transactionId,
                        countryCode: retrieveCompanyCode(company),
                        status: TransactionStatus.CAPTURED,
                    },
                    porscheToken
                );

                await handleGiftVoucherTransactionStatusChange(giftVoucher, setting, transactionId, managedTransaction);

                return managedTransaction;
            }

            // When there is a redirection url
            // we should return the status and set isRedirect to true with the redirection URL
            const transactionStatus = await getTransactionStatus(
                credentials,
                setting.secrets.apiEndpoint,
                setting.secrets.endpointApiVersion,
                { transactionId, countryCode: retrieveCompanyCode(company) },
                porscheToken
            );

            return {
                status: transactionStatus,
                isRedirect: true,
                url: redirectUrl,
            };
        })();

        res.status(200).json({
            id: transactionId,
            status: transaction.status,
            redirectUrl: transaction.isRedirect ? transaction.url : null,
        });
    } catch (error) {
        next(error);
    }
};

export default submitGiftVoucherPayment;
