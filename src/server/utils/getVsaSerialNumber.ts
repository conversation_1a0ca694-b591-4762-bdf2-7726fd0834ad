import { Company, CounterSettings } from '../database/documents';
import { increaseCompanyCounter, parseCounterPrefix } from '../database/helpers';
// generate the serial number for VSA from VSA counter
const getVsaSerialNumber = async (company: Company, counter: CounterSettings) => {
    // get the index on the counter
    // the index is based on the raw prefix to ensure global appliance
    const index = await increaseCompanyCounter(company._id, counter.prefix, counter.method, company.timeZone);

    return [
        // first part is the computed prefix
        parseCounterPrefix(counter.prefix, company.timeZone),
        // second part is the allocated index with leading zeros
        index.toString().padStart(counter.padding + 1, '0'),
    ].join('');
};

export default getVsaSerialNumber;
