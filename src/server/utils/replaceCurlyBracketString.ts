import { camelCase, isNil, kebabCase, lowerCase, snakeCase, startCase } from 'lodash/fp';

type BracketDictionaryInput = {
    [key: string]: string | number;
};

type BracketDictionaryOutput = {
    [key: string]: string;
};

const regexp = (key: string): string => `{{\\s?${key}\\s?}}|{{{\\s?${key}\\s?}}}`;

const transformCases = (input: string): string[] => {
    const exact = input;
    const camel = camelCase(input);
    const pascal = startCase(camelCase(input)).replace(/ /g, '');
    const snake = snakeCase(input);
    const kebab = kebabCase(input);

    return Array.from(new Set([exact, camel, pascal, snake, kebab])).map(regexp);
};

// Map input parameters to all possible case keys, that using same value
export const populateParamsToAllCases = (params: BracketDictionaryInput): BracketDictionaryOutput =>
    Object.keys(params).reduce(
        (previous, key) =>
            transformCases(key).reduce(
                (prev, k) => ({
                    ...prev,
                    [k]: params[key].toString(),
                }),
                previous
            ),
        {}
    );

// This function to replace dynamic value that using curly bracket (can 2 or 3 curly bracket)
// with the defined parameters
const replaceCurlyBracketString = (text: string, params: BracketDictionaryInput): string =>
    Object.keys(params).reduce(
        (previous, key) =>
            !isNil(params[key])
                ? previous.replaceAll(new RegExp(transformCases(key).join('|'), 'g'), params[key].toString())
                : previous,
        text
    );

const matchRegexp = /{{{\s?[A-Za-z0-9_-]+\s?}}}|{{\s?[A-Za-z0-9_-]+\s?}}/g;
// This function to replace dynamic value (case-insensitive) that using curly bracket (can 2 or 3 curly bracket)
// with the defined parameters
const replaceCurlyBracketStringCaseInsensitive = (text: string, params: BracketDictionaryInput): string =>
    text.match(matchRegexp)?.reduce((previous, reg) => {
        const key = lowerCase(reg).replace(/ /g, '');

        return Object.keys(params).includes(key) && !isNil(params[key])
            ? previous.replaceAll(new RegExp(reg, 'g'), params[key].toString())
            : previous;
    }, text) || text;

export default (text: string, params: BracketDictionaryInput): string =>
    replaceCurlyBracketString(replaceCurlyBracketStringCaseInsensitive(text, params), params);
