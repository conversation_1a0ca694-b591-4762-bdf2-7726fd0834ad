import sharp from 'sharp';
import config from '../core/config';
import { getFileStream, getUrlForUpload } from '../core/storage';
import { Company, SettingId } from '../database';
import { EmailContext, EmailLogo, defaultLogoUrl, getDefaultEmailContext } from '../database/helpers/settings';

import defaultLogoMetaData from '../datasets/defaultLogoMetaData';
import createLoaders from '../loaders';
import streamToBuffer from './streamToBuffer';

const getLogo = async (company?: Company | null): Promise<EmailLogo> => {
    if (!company?.logo) {
        const { width, height } = defaultLogoMetaData;

        return {
            url: defaultLogoUrl,
            width,
            height,
        };
    }

    const companyLogoUrl = await getUrlForUpload(company.logo);
    const buffer = await streamToBuffer(await getFileStream(company.logo));
    const { width, height } = await sharp(buffer).metadata();

    return {
        url: companyLogoUrl,
        width,
        height,
    };
};

const getCompanyEmailContext = async (company?: Company | null): Promise<EmailContext> => {
    const defaultContext = await getDefaultEmailContext();
    const logo = await getLogo(company);

    if (!company) {
        return {
            ...defaultContext,
            logo,
        };
    }

    const loaders = createLoaders();

    const settings =
        company.emailSettings.provider === 'smtp'
            ? await loaders.settingById.load(company.emailSettings.settingId)
            : null;

    const sender =
        settings && settings.settingId === SettingId.CompanySMTP ? settings.secrets.from : config.smtp.sender;

    return {
        company,
        companyName: company.displayName,
        fontUrl: company.font ? await getUrlForUpload(company.font) : defaultContext.fontUrl,
        fontBoldUrl: company.fontBold ? await getUrlForUpload(company.fontBold) : defaultContext.fontBoldUrl,
        logo,
        sender,
    };
};

export default getCompanyEmailContext;
