import { ObjectId } from 'mongodb';
import { type ExportLead, type LeadModule, LeadStageOption } from '../../../database';
import type { Loaders } from '../../../loaders';
import createI18nInstance from '../../createI18nInstance';
import { ExcelExportFormat } from '../enums';
import getApplicationFormatCapRows, { prepareCapDataFromLeads } from './cap/format';
import { type CapSupportedModule, FormatCapPurpose } from './cap/types';
import getLeadModulesReportingRows from './reporting/getLeadModulesReportingRows';
import { getLeadModulesSystemRowsStreaming } from './system/getLeadModulesSystemRows';
import { prepareSystemLeadData } from './system/utils';

type FormatSettingSystem = {
    format: ExcelExportFormat.system;
    stage: LeadStageOption;

    // This currency code will be used as header, retrieved from company
    // For inside excel row usage, better use support.company which comes from preparationData mapping
    currencyCode?: string;

    // This timezone will be used as header, retrieved from company
    // For inside excel row usage, better use support.company which comes from preparationData mapping
    timeZone?: string;

    routerFirstLanguage?: string;
};

type FormatSettingReporting = {
    format: ExcelExportFormat.reporting;
    stage: LeadStageOption;

    // This currency code will be used as header, retrieved from company
    // For inside excel row usage, better use support.company which comes from preparationData mapping
    currencyCode?: string;

    // This timezone will be used as header, retrieved from company
    // For inside excel row usage, better use support.company which comes from preparationData mapping
    timeZone?: string;

    routerFirstLanguage?: string;
};

type FormatSettingCap = {
    format: ExcelExportFormat.cap;
    capPurpose: FormatCapPurpose;
    tenant: string;
    stage?: LeadStageOption;
    routerFirstLanguage?: string;

    // This timezone will be used to correct DOB value, retrieved from company
    timeZone?: string;
};

export type FormatSetting = FormatSettingSystem | FormatSettingReporting | FormatSettingCap;

// Configuration for automatic streaming detection
const DEFAULT_STREAM_BATCH_SIZE = 1000;

const getExcelLeadRows = async (
    leads: ExportLead[],
    modules: LeadModule[],
    formatSetting: FormatSetting,
    loaders: Loaders
) => {
    switch (formatSetting.format) {
        case ExcelExportFormat.cap: {
            const items = await prepareCapDataFromLeads(
                formatSetting.capPurpose,
                leads,
                {
                    modules: modules as CapSupportedModule[],
                    stage: formatSetting.stage,
                },
                loaders
            );

            const rows = getApplicationFormatCapRows(
                formatSetting.capPurpose,
                items,
                formatSetting.tenant,
                formatSetting.timeZone
            );

            return rows;
        }

        case ExcelExportFormat.reporting: {
            const language = await loaders.languagePackById.load(new ObjectId(formatSetting.routerFirstLanguage));
            const { i18n } = await createI18nInstance(language?._id?.toHexString() || 'en');
            await i18n.loadNamespaces([
                'common',
                'applicationList',
                'auditTrails',
                'leadListPage',
                'applicationExcel',
                'customerDetails',
            ]);
            const { t } = i18n;

            const { preparedData } = await prepareSystemLeadData(leads, modules, formatSetting.stage, t, loaders);

            const rows = await getLeadModulesReportingRows(modules, preparedData, {
                t,
                currencyCode: formatSetting.currencyCode,
                timeZone: formatSetting.timeZone,
                stage: formatSetting.stage,
                language: language?._id.toHexString(),
            });

            return rows;
        }

        case ExcelExportFormat.system:
        default: {
            const language = await loaders.languagePackById.load(new ObjectId(formatSetting.routerFirstLanguage));
            const { i18n } = await createI18nInstance(language?._id?.toHexString() || 'en');
            await i18n.loadNamespaces([
                'common',
                'applicationList',
                'auditTrails',
                'leadListPage',
                'applicationExcel',
                'customerDetails',
            ]);
            const { t } = i18n;

            const { preparedData } = await prepareSystemLeadData(leads, modules, formatSetting.stage, t, loaders);

            const allRows: any[][] = [];
            const batchSize = DEFAULT_STREAM_BATCH_SIZE;

            const streamingGenerator = getLeadModulesSystemRowsStreaming(
                modules,
                preparedData,
                {
                    t,
                    currencyCode: formatSetting.currencyCode,
                    timeZone: formatSetting.timeZone,
                    stage: formatSetting.stage,
                    language: language?._id.toHexString(),
                },
                batchSize
            );

            for await (const rowBatch of streamingGenerator) {
                if (Array.isArray(rowBatch)) {
                    allRows.push(...rowBatch);
                }
            }

            return allRows;
        }
    }
};

export default getExcelLeadRows;
