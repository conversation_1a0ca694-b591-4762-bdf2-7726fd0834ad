import { isNil } from 'lodash/fp';
import { getCountryEventExportConfig } from '../../../../core/config';
import { Dealer, ExportLead, Lead, LeadStageOption, ModuleType, TradeInVehicle } from '../../../../database';
import { getLocalCustomerAggregatedFields } from '../../../../database/helpers/customers/shared';
import { RowValue } from '../../../../export/type';
import {
    getCapCustomerBirthday,
    getCapTitleCode,
    getMaritalStatusCode,
} from '../../../../integrations/cap/utils/getCustomerDetails';
import {
    getVehicleEngineTypeCode,
    getVehicleEquipmentLineCode,
} from '../../../../integrations/cap/utils/getVehicleDetails';
import createLoaders, { Loaders } from '../../../../loaders';
import { BpLeadUploadRow, BpUploadRow, CAP_HEADER_BP, CAP_HEADER_BP_LEAD } from '../../applications/cap/header';
import {
    getCapPrimaryInterest,
    getCapPurchaseMonthAndYear,
    getCapSexCode,
    getCompanyName2,
    getCountryCode,
    getRegionCode,
    getVehicleVariantID,
    getVehicleCondition,
    getCountryCodeWithFallback,
} from '../../applications/cap/utils';
import { modelMakeByVariantIdFromLeads } from '../system/utils';
import { AdditionalCapData, CapPreparedData, CapSupportedModule, FormatCapPurpose } from './types';
import { getCapConsentStatus, getCapSupportingData } from './utils';

type CapSupportingData = {
    modules: CapSupportedModule[];
    stage?: LeadStageOption;
};

const companyByIdFromDealer = async (dealer: Dealer, loaders: Loaders) => loaders.companyById.load(dealer.companyId);

const getSpecificCapDataFromApplication = (
    item: Lead,
    module: CapSupportedModule
): Partial<Omit<AdditionalCapData, 'appId'>> & { appId: string } => {
    switch (module._type) {
        // TO DO: Need to be adjusted later when the lead document has included the medium & lead source
        case ModuleType.EventApplicationModule: {
            return {
                appId: item.identifier,
                campaignId: item.campaignValues?.capCampaignId,
                medium: '',
                leadSource: '',
            };
        }
        case ModuleType.FinderApplicationPublicModule:
        case ModuleType.FinderApplicationPrivateModule:
        case ModuleType.ConfiguratorModule:
        case ModuleType.StandardApplicationModule: {
            return {
                appId: item.identifier,
                campaignId: item.campaignValues?.capCampaignId,
            };
        }

        case ModuleType.LaunchPadModule:
            return { appId: item.identifier, campaignId: item.campaignValues?.capCampaignId };

        default:
            throw new Error('Unsupported module kind for c@p export');
    }
};

export const prepareCapDataFromLeads = async (
    purpose: FormatCapPurpose,
    leads: ExportLead[],
    { modules, stage }: CapSupportingData,
    loaders = createLoaders()
): Promise<CapPreparedData[]> => {
    const { customerById, assigneeById, dealerById, vehicleById, consentList } = await getCapSupportingData(
        leads,
        modules,
        loaders
    );

    const getPreparedDataFromPurpose = await (async () => {
        switch (purpose) {
            case FormatCapPurpose.BP_LEAD: {
                const vehicleInfoByVariantId = await modelMakeByVariantIdFromLeads(leads, loaders);

                return (item: Lead): { primaryInterest?: string } => ({
                    primaryInterest: getCapPrimaryInterest(
                        item.vehicleId?.toHexString()
                            ? (vehicleInfoByVariantId[item.vehicleId.toHexString()]?.model ?? null)
                            : null,
                        item.vehicleId?.toHexString() ? (vehicleById[item.vehicleId.toHexString()] ?? null) : null
                    ),
                });
            }

            default:
                return () => ({});
        }
    })();

    return Promise.all(
        leads.map(async item => {
            const { assigneeId } = item;
            const module = modules.find(module => module._id.equals(item.moduleId));

            const companyByDealer = item.dealerId
                ? await companyByIdFromDealer(dealerById[item.dealerId.toHexString()], loaders)
                : null;

            return {
                ...item,
                cap: {
                    ...getSpecificCapDataFromApplication(item, module),
                    ...getPreparedDataFromPurpose?.(item),
                    customer: customerById[item.customerId.toHexString()]
                        ? getLocalCustomerAggregatedFields(customerById[item.customerId.toHexString()])
                        : null,
                    assignee: assigneeId ? assigneeById[assigneeId.toHexString()] : null,
                    preferredSellingDealer: item.dealerId ? dealerById[item.dealerId.toHexString()] : null,
                    preferredServiceDealer: item.dealerId ? dealerById[item.dealerId.toHexString()] : null,
                    companyCountryCode: companyByDealer ? companyByDealer.countryCode : null,
                    consentList,
                    vehicle: vehicleById[item.vehicleId?.toHexString()] ?? null,
                },
            };
        })
    );
};

const getCustomerCurrentVehicle = (tradeInVehicle: TradeInVehicle[]) =>
    !tradeInVehicle.length
        ? {
              make: null,
              model: null,
              modelYear: null,
              ownership: null,
              equipmentLine: null,
              purchaseYear: null,
              engineType: null,
          }
        : tradeInVehicle[0];

const getCapRowsForBp = (preparedData: CapPreparedData[], tenant: string, timeZone?: string): RowValue[][] => {
    const rows: RowValue[][] = [[...CAP_HEADER_BP]];

    for (const item of preparedData) {
        const { customerAgreements } = item;

        const {
            appId,
            customer,
            assignee,
            consentList,
            preferredSellingDealer,
            preferredServiceDealer,
            companyCountryCode,
        } = item.cap;

        const {
            EVENT_EXPORT_IDTYPE,
            EVENT_EXPORT_LAST_NAME,
            EVENT_EXPORT_LANGUAGE_CORR,
            EVENT_EXPORT_ADDRESS_TYPE,
            EVENT_EXPORT_MOBILE_TYPE,
            EVENT_EXPORT_MAIL_TYPE,
            EVENT_EXPORT_CUSTOMER_STATUS_P,
        } = getCountryEventExportConfig(tenant, companyCountryCode);

        const [DATA_PROCESSING_CONSENT, MAIL_CONSENT, PHONE_CONSENT, EMAIL_CONSENT, FAX_CONSENT] = getCapConsentStatus(
            consentList,
            customerAgreements,
            FormatCapPurpose.BP,
            companyCountryCode
        );

        const columnByHeader: BpUploadRow = {
            ID_NUMBER: appId,
            TK_IDTYPE: EVENT_EXPORT_IDTYPE,
            TK_TITLE: customer && getCapTitleCode(customer.title),
            FIRST_NAME: customer?.firstName ?? '',
            LAST_NAME: customer?.lastNameFront ?? customer?.lastName ?? EVENT_EXPORT_LAST_NAME,
            TK_LANGUAGE_CORR: EVENT_EXPORT_LANGUAGE_CORR,
            MIDDLE_NAME: customer?.firstNameJapan ?? '',
            LAST_NAME_2: customer?.lastNameJapan ?? '',
            TK_ADDRESS_TYPE: EVENT_EXPORT_ADDRESS_TYPE,
            STREET: customer?.address ?? '',
            HOUSE_NUMBER: customer?.unitNumber ?? '',
            STREET_SUPPL_1: customer?.district ?? '',
            CITY: customer?.city ?? '',
            POSTAL_CODE: customer?.postalCode ?? '',
            TK_REGION: customer && getRegionCode(customer.region),
            TK_COUNTRY: customer && getCountryCodeWithFallback(customer.country, companyCountryCode),
            MOBILE_PHONE: customer?.phone?.value ?? '',
            TK_MOBILE_TYPE: EVENT_EXPORT_MOBILE_TYPE,
            EMAIL: customer?.email ?? '',
            TK_MAIL_TYPE: EVENT_EXPORT_MAIL_TYPE,
            RESP_SALES_DEALER: preferredSellingDealer?.integrationDetails?.dealerCode,
            RESP_SERVICE_DEALER: preferredServiceDealer?.integrationDetails?.dealerCode,
            RESP_SALES_PERSON: assignee?.alias,
            RESP_SERVICE_PERSON: assignee?.alias,
            BIRTHDATE: getCapCustomerBirthday(customer?.birthday, timeZone),
            TK_SEX: customer && getCapSexCode(customer.title ?? customer.gender),
            TK_MARITIAL_STATUS:
                customer && !isNil(customer.maritalStatus) ? getMaritalStatusCode(customer.maritalStatus) : '',
            DATA_PROCESSING_CONSENT,
            MAIL_CONSENT,
            PHONE_CONSENT,
            EMAIL_CONSENT,
            FAX_CONSENT,
            CUSTOMER_STATUS_P: EVENT_EXPORT_CUSTOMER_STATUS_P,
            LEAD_CAPTURE_FORM_LABEL: item.eventDisplayName,
        };

        rows.push(CAP_HEADER_BP.map(header => columnByHeader[header] || null));
    }

    return rows;
};

const getCapRowsForBpLead = (preparedData: CapPreparedData[], tenant: string, timeZone?: string) => {
    const rows: RowValue[][] = [[...CAP_HEADER_BP_LEAD]];

    for (const item of preparedData) {
        const { customerAgreements } = item;

        const {
            appId,
            customer,
            assignee,
            consentList,
            preferredSellingDealer,
            preferredServiceDealer,
            primaryInterest,
            medium,
            campaignId,
            leadSource,
            vehicle,
            companyCountryCode,
        } = item.cap;

        const [month, year] = getCapPurchaseMonthAndYear(item._versioning.createdAt, companyCountryCode);

        const { make, model, modelYear, ownership, equipmentLine, engineType } = getCustomerCurrentVehicle(
            item.tradeInVehicle
        );

        const { EVENT_EXPORT_IMP, EVENT_EXPORT_SOURCE, EVENT_EXPORT_TYPE, EVENT_EXPORT_LAST_NAME } =
            getCountryEventExportConfig(tenant, companyCountryCode);

        const [YKIS_DATAPROC, YBLOCK_MAIL, YBLOCK_PHONE, YBLOCK_EMAIL, YBLOCK_FAX] = getCapConsentStatus(
            consentList,
            customerAgreements,
            FormatCapPurpose.BP_LEAD,
            companyCountryCode
        );

        const columnByHeader: BpLeadUploadRow = {
            Source: EVENT_EXPORT_SOURCE,
            ID: appId,
            TITLE: customer && getCapTitleCode(customer.title),
            BUSINESS_TITLE: customer?.businessTitle ?? '',
            FIRST_NAME: customer?.firstName ?? '',
            LAST_NAME: customer?.lastNameFront ?? customer?.lastName ?? EVENT_EXPORT_LAST_NAME,
            NAME_LST2: customer?.lastNameJapan ?? '',
            NAMEMIDDLE: customer?.firstNameJapan ?? '',
            COMPANY_1: customer?.companyName ?? '',
            COMPANY_2: customer && getCompanyName2(customer),
            STREET: customer?.address ?? '',
            STR_SUPPL1: customer?.district ?? '',
            HOUSE_NUM1: customer?.unitNumber ?? '',
            POST_CODE1: customer?.postalCode ?? '',
            CITY1: customer?.city ?? '',
            REGION: customer?.region ?? '',
            COUNTRY: customer && (getCountryCode(customer.country) ?? ''),
            SEX: customer && getCapSexCode(customer.title ?? customer.gender),
            BIRTHDATE: getCapCustomerBirthday(customer?.birthday, timeZone),
            TELEPHONE_M: customer?.phone?.value ?? '',
            SMTP_ADDR1: customer?.email,
            YPREF_SAL_DEALER: preferredSellingDealer?.integrationDetails?.dealerCode,
            YPREF_SRV_DEALER: preferredServiceDealer?.integrationDetails?.dealerCode,
            MEDIUM: medium ?? 'N/A',
            MAKE1: make ?? '',
            MODEL1: model ?? '',
            MODEL_YEAR1: modelYear ?? '',
            yveh_own_vehicle: ownership ? 'X' : '',
            yveh_equipment_line: equipmentLine ? getVehicleEquipmentLineCode(equipmentLine) : '',
            yveh_engine_type: engineType ? getVehicleEngineTypeCode(engineType) : '',
            CAR_PUR_MM: month,
            CAR_PUR_JJJJ: year,
            MODEL_INTEREST1: getVehicleVariantID(vehicle),
            CAMPAIGN_ID: campaignId ?? '',
            LEAD_SOURCE: leadSource ?? 'N/A',
            IMP: EVENT_EXPORT_IMP,
            YKIS_DATAPROC,
            YBLOCK_MAIL,
            YBLOCK_PHONE,
            YBLOCK_EMAIL,
            YBLOCK_FAX,
            TYPE: EVENT_EXPORT_TYPE,
            IDNUMBER: appId,
            YRSALESP: assignee?.alias,
            YRSERVP: assignee?.alias,
            PRIMARY_INTEREST: primaryInterest ?? '',
            ...getVehicleCondition(vehicle),
            LEAD_CAPTURE_FORM_LABEL: item.eventDisplayName,
        };

        rows.push(CAP_HEADER_BP_LEAD.map(header => columnByHeader[header] || null));
    }

    return rows;
};

export const getApplicationFormatCapRows = (
    purpose: FormatCapPurpose,
    preparedData: CapPreparedData[],
    tenant: string,
    // This timezone will be used to correct DOB value, retrieved from company
    timeZone?: string
) => {
    switch (purpose) {
        case FormatCapPurpose.BP:
            return getCapRowsForBp(preparedData, tenant, timeZone);

        case FormatCapPurpose.BP_LEAD:
            return getCapRowsForBpLead(preparedData, tenant, timeZone);

        default:
            throw new Error('Unknown c@p export purpose: ', purpose);
    }
};

export default getApplicationFormatCapRows;
