import { WithId } from 'mongodb';
import {
    ConfiguratorModule,
    EventApplicationModule,
    FinderApplicationPublicModule,
    FinderApplicationPrivateModule,
    User,
    Dealer,
    ConsentsAndDeclarations,
    Vehicle,
    DealershipSetting,
    StandardApplicationModule,
    MobilityModule,
    LaunchPadModule,
    Lead,
    ExportLead,
} from '../../../../database';
import type { LocalCustomerAggregatedFields } from '../../../../database/helpers/customers/types';

export type CapSupportedModule =
    | EventApplicationModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | ConfiguratorModule
    | StandardApplicationModule
    | LaunchPadModule;

export type AgreementSupportedModule = CapSupportedModule | StandardApplicationModule | MobilityModule;

// Make the same value with previous one in EvenExcelPurpose
export enum FormatCapPurpose {
    BP = 'BP_UPLOAD',
    BP_LEAD = 'BP_LEAD_UPLOAD',
}

export type AdditionalCapData = {
    appId: string;
    customer: LocalCustomerAggregatedFields;
    assignee: WithId<User> | null;
    preferredSellingDealer: WithId<Dealer> | null;
    preferredServiceDealer: WithId<Dealer> | null;
    consentList: ConsentsAndDeclarations[];
    vehicle: WithId<Vehicle> | null;

    // For event
    defaultSalesPerson?: DealershipSetting;

    // For bp lead upload
    primaryInterest?: string;
    medium?: string;
    campaignId?: string;
    leadSource?: string;

    // As reference for selecting excel default values config by country
    companyCountryCode: string | null;
};

export type CapPreparedData = ExportLead & {
    cap: AdditionalCapData;
};
