import type { TFunction } from 'i18next';
import { type Dictionary } from 'lodash';
import { groupBy, keyBy } from 'lodash/fp';
import { WithId } from 'mongodb';
import {
    type Author,
    AuthorKind,
    type Company,
    type ConsentsAndDeclarations,
    ConsentsAndDeclarationsType,
    type Customer,
    DataField,
    type Dealer,
    type ExportLead,
    type Lead,
    type LeadModule,
    LeadStageOption,
    type LocalCustomerManagementModule,
    type LocalMake,
    type LocalModel,
    type LocalVariant,
    ModuleType,
    type User,
    type Vehicle,
    VehicleKind,
    getKYCPresetsForCustomerModule,
} from '../../../../database';
import { getCustomerFullName } from '../../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../../database/helpers/customers/shared';
import createLoaders, { Loaders } from '../../../../loaders';
import ensureManyFromLoaders from '../../../ensureManyFromLoaders';
import { uniqueObjectIds } from '../../../fp';
import { type PreparedSystemLeadData, CheckConsent } from '../shared/types';

export const customerByIdFromLeads = async (leads: Lead[], loaders: Loaders) => {
    const customerIds = uniqueObjectIds(
        leads
            .flatMap(item => {
                if (!item) {
                    return [];
                }

                const creatorCustomer =
                    item._versioning.createdBy?.kind === AuthorKind.Customer ? [item._versioning.createdBy.id] : [];
                const editorCustomer =
                    item._versioning.updatedBy?.kind === AuthorKind.Customer ? [item._versioning.updatedBy.id] : [];

                return [...creatorCustomer, ...editorCustomer, item.customerId];
            })
            .filter(Boolean)
    );

    if (!customerIds.length) {
        return {};
    }

    // Retrieve customer by ids
    const customers = (await loaders.customerById.loadMany(customerIds).then(ensureManyFromLoaders)).filter(c => c);
    const customerModuleIds = uniqueObjectIds(customers.map(customer => customer.moduleId));

    const customerModules = (await loaders.moduleById
        .loadMany(customerModuleIds)
        .then(ensureManyFromLoaders)) as LocalCustomerManagementModule[];

    // Suite ids is required as well, for author
    // Because as creator of application, there is no data yet for the customer
    const customerSuiteIds = uniqueObjectIds(customers.map(item => item._versioning.suiteId));
    const customersLatest = await loaders.customerByLatestSuiteId
        .loadMany(customerSuiteIds)
        .then(ensureManyFromLoaders);

    return {
        // Key by customer id
        customerById: keyBy(item => item._id.toHexString(), customers),

        // Key by customer Module ID
        customerModuleByModuleId: keyBy(item => item._id.toHexString(), customerModules),

        // Key by suite id, the latest customer data
        customerByLatestSuiteId: keyBy(item => item._versioning.suiteId.toHexString(), customersLatest),
    };
};

export const userByIdFromLeads = async (leads: Lead[], loaders: Loaders) => {
    const allUsersInLeads = uniqueObjectIds(
        leads
            .flatMap(item => [
                item.assigneeId,
                item._versioning.createdBy?.kind === AuthorKind.User ? item._versioning.createdBy.id : undefined,
                item._versioning.updatedBy?.kind === AuthorKind.User ? item._versioning.updatedBy.id : undefined,
            ])
            .filter(Boolean)
    );

    if (!allUsersInLeads.length) {
        return {};
    }

    const users = await loaders.userById.loadMany(allUsersInLeads).then(ensureManyFromLoaders<User>);

    return keyBy(item => item._id.toHexString(), users);
};

export const dealerByIdFromLeads = async (leads: Lead[], loaders: Loaders) => {
    const dealerIds = uniqueObjectIds(leads.map(item => item.dealerId).filter(Boolean));

    const dealers = await loaders.dealerById.loadMany(dealerIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), dealers);
};

export const vehicleByIdFromLeads = async (leads: Lead[], loaders: Loaders) => {
    const vehicleIds = uniqueObjectIds(leads.map(item => item.vehicleId).filter(Boolean));

    const vehicles = await loaders.vehicleById.loadMany(vehicleIds).then(ensureManyFromLoaders);

    return keyBy(item => item._id.toHexString(), vehicles);
};

export const applicationsByLeadId = async (leads: Lead[], loaders: Loaders) => {
    const leadsWithApplication = (
        await Promise.all(
            leads.map(async lead => {
                const module = await loaders.moduleById.load(lead.moduleId);

                return module._type !== ModuleType.LaunchPadModule ? lead : null;
            })
        )
    ).filter(Boolean);

    const leadIds = uniqueObjectIds(leadsWithApplication.map(lead => lead._id).filter(Boolean));

    if (!leadIds.length) {
        return [];
    }

    const applications = (await loaders.applicationByLeadId.loadMany(leadIds).then(ensureManyFromLoaders)).flatMap(
        application => application
    );

    return groupBy(item => item.leadId.toHexString(), applications);
};

export const modelMakeByVariantIdFromLeads = async (leads: Lead[], loaders: Loaders) => {
    const vehicleIds = uniqueObjectIds(leads.map(item => item.vehicleId).filter(Boolean));
    if (!vehicleIds.length) {
        return {};
    }

    const loadedVariants = await loaders.vehicleById.loadMany(vehicleIds).then(ensureManyFromLoaders<LocalVariant>);
    const variants = loadedVariants.filter(item => item._kind === VehicleKind.LocalVariant);

    if (!variants.length) {
        return {};
    }

    const vehicleModelIds = uniqueObjectIds(variants.flatMap(item => [item.modelId, item.submodelId]).filter(Boolean));
    const models = await loaders.vehicleById.loadMany(vehicleModelIds).then(ensureManyFromLoaders<LocalModel>);

    const modelMakeIds = uniqueObjectIds(models.map(item => item.makeId));
    const makes = await loaders.vehicleById.loadMany(modelMakeIds).then(ensureManyFromLoaders<LocalMake>);

    const modelById = keyBy(item => item._id.toHexString(), models);
    const makeById = keyBy(item => item._id.toHexString(), makes);

    return variants.reduce(
        (acc, variant) => {
            const model = modelById[variant.modelId.toHexString()] ?? null;

            return {
                ...acc,
                [variant._id.toHexString()]: {
                    model,
                    subModel: variant.submodelId ? modelById[variant.submodelId.toHexString()] : null,
                    make: model?.makeId ? makeById[model.makeId.toHexString()] : null,
                },
            };
        },
        {} as {
            [vehicleId: string]: {
                model: LocalModel | null;
                subModel: LocalModel | null;
                make: LocalMake | null;
            };
        }
    );
};

const getConsentData = (lead: Lead, consentById: { [key: string]: ConsentsAndDeclarations }) => {
    const consentData = {
        personal: CheckConsent.NotExist,
        email: CheckConsent.NotExist,
        fax: CheckConsent.NotExist,
        mail: CheckConsent.NotExist,
        phone: CheckConsent.NotExist,
        sms: CheckConsent.NotExist,
    };

    if (lead.customerAgreements?.agreements?.length === 0) {
        return consentData;
    }
    const consentsLoaders = lead.customerAgreements.agreements.map(
        agreement => consentById[agreement.consentId.toHexString()]
    );

    // get the first checkbox and Data processing checkbox
    const firstCheckBox = consentsLoaders.find(
        consent =>
            consent._type === ConsentsAndDeclarationsType.Checkbox && consent.dataField === DataField.DataProcessing
    );
    if (firstCheckBox) {
        const leadFirstCheckBox = lead.customerAgreements.agreements.find(agreement =>
            agreement.consentId.equals(firstCheckBox._id)
        );
        if (leadFirstCheckBox) {
            consentData.personal = leadFirstCheckBox.isAgreed ? CheckConsent.Yes : CheckConsent.No;
        }
    }

    // get the first marketing consent in the lead
    const firstMarketingCheckbox = consentsLoaders.find(
        consent => consent._type === ConsentsAndDeclarationsType.Marketing
    );
    if (firstMarketingCheckbox) {
        const leadFirstMarketing = lead.customerAgreements.agreements.find(agreement =>
            agreement.consentId.equals(firstMarketingCheckbox._id)
        );

        if (leadFirstMarketing?.platformsAgreed) {
            consentData.email = leadFirstMarketing.platformsAgreed.email ? CheckConsent.Yes : CheckConsent.No;
            consentData.fax = leadFirstMarketing.platformsAgreed.fax ? CheckConsent.Yes : CheckConsent.No;
            consentData.mail = leadFirstMarketing.platformsAgreed.mail ? CheckConsent.Yes : CheckConsent.No;
            consentData.phone = leadFirstMarketing.platformsAgreed.phone ? CheckConsent.Yes : CheckConsent.No;
            consentData.sms = leadFirstMarketing.platformsAgreed.sms ? CheckConsent.Yes : CheckConsent.No;
        }
    }

    return consentData;
};

export type GetConsentDataResult = ReturnType<typeof getConsentData>;

const consentDataByLeads = async (leads: Lead[], loaders: Loaders) => {
    const allConsentIds = leads
        .flatMap(item => item.customerAgreements?.agreements.map(agreement => agreement?.consentId) ?? [])
        .filter(Boolean);

    if (!allConsentIds.length) {
        return {};
    }

    const allConsents = await loaders.consentById.loadMany(allConsentIds).then(ensureManyFromLoaders);

    const consentById = keyBy(item => item._id.toHexString(), allConsents);

    return leads
        .filter(item => item.customerAgreements?.agreements?.length)
        .reduce(
            (acc, item) => ({
                ...acc,
                [item._versioning.suiteId.toHexString()]: getConsentData(item, consentById),
            }),
            {} as { [key: string]: GetConsentDataResult }
        );
};

const getAuthorName = (
    author: Author,
    t: TFunction,
    {
        userById,
        customerById,
        customerByLatestSuiteId,
        company,
        customerModuleByModuleId,
    }: {
        userById?: Dictionary<User>;
        customerById: Dictionary<Customer>;
        customerByLatestSuiteId?: Dictionary<Customer>;
        customerModuleByModuleId: Dictionary<LocalCustomerManagementModule>;
        company: Company;
    }
) => {
    switch (author.kind) {
        case AuthorKind.Customer: {
            const customer = customerById?.[author.id.toHexString()];

            const customerModule = customerModuleByModuleId?.[customer.moduleId.toHexString()];

            if (!customer) {
                return '';
            }

            const latestCustomer =
                !customer._versioning.isLatest && customerByLatestSuiteId?.[customer._versioning.suiteId.toHexString()]
                    ? customerByLatestSuiteId[customer._versioning.suiteId.toHexString()]
                    : customer;

            const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

            return latestCustomer ? getCustomerFullName(t, latestCustomer, company, kycPresets) : '';
        }

        case AuthorKind.User:
            return userById?.[author.id.toHexString()]?.displayName ?? '';

        case AuthorKind.System:
            return 'System';

        case AuthorKind.PorscheRetain:
            return 'Porsche Retain';

        default:
            throw new Error('Author name is not implemented');
    }
};

const companyByModuleIdFromModules = async (modules: LeadModule[], loaders: Loaders) => {
    if (!modules?.length) {
        return {};
    }

    const companyIds = uniqueObjectIds(modules.map(module => module.companyId));
    if (!companyIds.length) {
        return {};
    }

    const companies = await loaders.companyById.loadMany(companyIds).then(ensureManyFromLoaders<Company>);

    const companyById = keyBy(item => item._id.toHexString(), companies);

    return modules.reduce(
        (acc, module) => ({
            ...acc,
            [module._id.toHexString()]: companyById[module.companyId.toHexString()],
        }),
        {} as Dictionary<Company>
    );
};

const getCampaignValues = (lead: Lead, module: LeadModule) => {
    switch (module._type) {
        case ModuleType.LaunchPadModule: {
            return {
                capCampaignId: lead.campaignValues?.capCampaignId,
                capLeadSource: module.capLeadOrigin,
                capLeadOrigin: module.capLeadMedium,
            };
        }

        case ModuleType.StandardApplicationModule:
        case ModuleType.ConfiguratorModule:
        case ModuleType.FinderApplicationPrivateModule:
        case ModuleType.FinderApplicationPublicModule: {
            return {
                capCampaignId: lead.campaignValues?.capCampaignId || module.leadCampaignId,
                capLeadSource: module.leadOrigin,
                capLeadOrigin: module.leadMedium,
            };
        }

        // TO DO VF-765: Figure out later how to getting the lead source & origin value from event
        case ModuleType.EventApplicationModule:
            return {
                utmSource: lead.campaignValues?.utmSource,
                utmMedium: lead.campaignValues?.utmMedium,
                utmCampaign: lead.campaignValues?.utmCampaign,
                capCampaignId: lead.campaignValues?.capCampaignId,
                capLeadSource: lead.campaignValues?.capLeadSource,
                capLeadOrigin: lead.campaignValues?.capLeadOrigin,
            };

        default: {
            return null;
        }
    }
};

const getSystemSupportingData = async (leads: Lead[], modules: LeadModule[], loaders: Loaders) => {
    const [userById, companyByModuleId, applicationsByLead] = await Promise.all([
        userByIdFromLeads(leads, loaders),
        companyByModuleIdFromModules(modules, loaders),
        applicationsByLeadId(leads, loaders),
    ]);

    const consentData = await consentDataByLeads(leads, loaders);

    const [dealerById, vehicleById, modelMakeByVariantId] = await (async (): Promise<
        [
            Dictionary<WithId<Dealer>> | null,
            Dictionary<WithId<Vehicle>> | null,
            Dictionary<{
                model: LocalModel | null;
                subModel: LocalModel | null;
                make: LocalMake | null;
            }> | null,
        ]
    > =>
        Promise.all([
            dealerByIdFromLeads(leads, loaders),
            vehicleByIdFromLeads(leads, loaders),
            modelMakeByVariantIdFromLeads(leads, loaders),
        ]))();

    const { customerById, customerByLatestSuiteId, customerModuleByModuleId } = await customerByIdFromLeads(
        leads,
        loaders
    );

    return {
        userById,
        consentData,
        vehicleById,
        modelMakeByVariantId,
        dealerById,
        customerById,
        customerByLatestSuiteId,
        companyByModuleId,
        customerModuleByModuleId,
        applicationsByLead,
    };
};

export const prepareSystemLeadData = async (
    leads: ExportLead[],
    modules: LeadModule[],
    stage: LeadStageOption,
    t: TFunction,
    loaders = createLoaders()
): Promise<{
    preparedData: PreparedSystemLeadData[];
}> => {
    const {
        userById,
        consentData,
        vehicleById,
        modelMakeByVariantId,
        dealerById,
        customerById,
        customerByLatestSuiteId,
        companyByModuleId,
        customerModuleByModuleId,
        applicationsByLead,
    } = await getSystemSupportingData(leads, modules, loaders);

    return {
        preparedData: leads.map((item): PreparedSystemLeadData => {
            const {
                _id,
                capValues,
                assigneeId,
                customerId,
                _versioning: { suiteId, updatedBy },
                vehicleId,
                dealerId,
                moduleId,
            } = item;

            // Get customer
            const customer = (() => {
                const foundCustomer = customerById?.[customerId.toHexString()] ?? null;

                return foundCustomer ? getLocalCustomerAggregatedFields(foundCustomer) : null;
            })();

            const [vehicle, vehicleInfo, dealer] = (() => [
                vehicleId?.toHexString() ? vehicleById?.[vehicleId.toHexString()] : null,
                vehicleId?.toHexString() ? modelMakeByVariantId?.[vehicleId.toHexString()] : null,
                dealerId?.toHexString() ? dealerById?.[dealerId.toHexString()] : null,
            ])();

            const company = companyByModuleId[moduleId.toHexString()];
            const module = modules.find(module => module._id.equals(moduleId));
            const campaignValues = getCampaignValues(item, module);

            return {
                ...item,
                support: {
                    customer,
                    company,
                    assignee: assigneeId ? userById?.[assigneeId.toHexString()] : null,
                    creatorName: item._versioning.createdBy
                        ? getAuthorName(item._versioning.createdBy, t, {
                              userById,
                              customerById,
                              customerByLatestSuiteId,
                              company,
                              customerModuleByModuleId,
                          })
                        : null,
                    editorName: updatedBy
                        ? getAuthorName(updatedBy, t, {
                              userById,
                              customerById,
                              customerByLatestSuiteId,
                              company,
                              customerModuleByModuleId,
                          })
                        : null,
                    consentData: consentData?.[suiteId.toHexString()],
                    module: modules.find(module => module._id.equals(item.moduleId)),
                    relatedApplications: applicationsByLead?.[_id.toHexString()] || [],
                    leadStage: stage,
                    vehicle,
                    dealer,
                    model: vehicleInfo?.model,
                    subModel: vehicleInfo?.subModel,
                    make: vehicleInfo?.make,
                    capValues,
                    campaignValues,
                },
            };
        }),
    };
};
