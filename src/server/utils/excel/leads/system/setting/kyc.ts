/* eslint-disable max-len */
import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { Dictionary } from 'lodash';
import { isEmpty, keyBy, uniqBy } from 'lodash/fp';
import {
    KYCPreset,
    KycFieldPurpose,
    LocalCustomerFieldKey,
    LocalCustomerManagementModule,
    ModuleType,
    UAEDrivingLicense,
    getKYCPresetsForCustomerModuleId,
    getKYCPresetsForEvent,
} from '../../../../../database';
import createLoaders from '../../../../../loaders';
import { getFormattedDateOnly } from '../../../../date';
import getPurchaseIntentionString from '../../../../getPurchaseIntentionString';
import type { SystemKycSupportedApplicationModule, SystemLeadColumnSetting } from '../../shared/types';

export type SystemKYCColumnSetting = SystemLeadColumnSetting & {
    key: string;

    // For set, this one is required
    fieldKey?: LocalCustomerFieldKey;
};

const updateKYCHeader = (t: TFunction) => {
    const kycDrivingLicenseSetting: SystemKYCColumnSetting[] = [
        {
            key: `${LocalCustomerFieldKey.DrivingLicense}Type`,
            header: t('customerDetails:fields.drivingLicense.type.excel'),
            getCellValue: ({ support: { customer } }) => customer?.drivingLicense?.[0]?.type,
            fieldKey: LocalCustomerFieldKey.DrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicense}Validity`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.validity.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.drivingLicense?.[0]?.validity,
            fieldKey: LocalCustomerFieldKey.DrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicense}Class`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.class.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.drivingLicense?.[0]?.class,
            fieldKey: LocalCustomerFieldKey.DrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicense}IssueDate`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.issueDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.drivingLicense?.[0]?.issueDate
                    ? getFormattedDateOnly(t, customer?.drivingLicense?.[0]?.issueDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.DrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicense}ExpiryDate`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.expireDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.drivingLicense?.[0]?.expiryDate
                    ? getFormattedDateOnly(t, customer?.drivingLicense?.[0]?.expiryDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.DrivingLicense,
        },
    ];

    const kycDrivingLicenseMySetting: SystemKYCColumnSetting[] = [
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseMy}Type`,
            header: t('customerDetails:fields.drivingLicense.type.excel'),
            getCellValue: ({ support: { customer } }) => customer?.drivingLicenseMy?.[0]?.type,
            fieldKey: LocalCustomerFieldKey.DrivingLicenseMy,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseMy}Validity`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.validity.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.drivingLicenseMy?.[0]?.validity,
            fieldKey: LocalCustomerFieldKey.DrivingLicenseMy,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseMy}Class`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.class.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.drivingLicenseMy?.[0]?.class,
            fieldKey: LocalCustomerFieldKey.DrivingLicenseMy,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseMy}IssueDate`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.issueDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.drivingLicenseMy?.[0]?.issueDate
                    ? getFormattedDateOnly(t, customer?.drivingLicenseMy?.[0]?.issueDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.DrivingLicenseMy,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseMy}ExpiryDate`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.expireDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.drivingLicenseMy?.[0]?.expiryDate
                    ? getFormattedDateOnly(t, customer?.drivingLicenseMy?.[0]?.expiryDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.DrivingLicenseMy,
        },
    ];

    const kycDrivingLicenseThSetting: SystemKYCColumnSetting[] = [
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseTh}Type`,
            header: t('customerDetails:fields.drivingLicense.type.excel'),
            getCellValue: ({ support: { customer } }) => customer?.drivingLicenseTh?.[0]?.type,
            fieldKey: LocalCustomerFieldKey.DrivingLicenseTh,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseTh}Validity`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.validity.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.drivingLicenseTh?.[0]?.validity,
            fieldKey: LocalCustomerFieldKey.DrivingLicenseTh,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseTh}IssueDate`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.issueDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.drivingLicenseTh?.[0]?.issueDate
                    ? getFormattedDateOnly(t, customer?.drivingLicenseTh?.[0]?.issueDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.DrivingLicenseTh,
        },
        {
            key: `${LocalCustomerFieldKey.DrivingLicenseTh}ExpiryDate`,
            header: `${t('customerDetails:fields.drivingLicense.type.label')} ${t('customerDetails:fields.drivingLicense.expireDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.drivingLicenseTh?.[0]?.expiryDate
                    ? getFormattedDateOnly(t, customer?.drivingLicenseTh?.[0]?.expiryDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.DrivingLicenseTh,
        },
    ];

    const kycUaeDrivingLicenseSetting: SystemKYCColumnSetting[] = [
        {
            key: LocalCustomerFieldKey.Birthday,
            header: t('customerDetails:fields.Birthday.label'),
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.birthday ? getFormattedDateOnly(t, customer?.birthday, company?.timeZone) : '',
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.UAEDrivingLicense}Resident`,
            header: t('customerDetails:fields.drivingLicense.uaeResident.isUAEResident.excel'),
            getCellValue: ({ support: { customer } }) => {
                const uaeDrivingLicense = customer?.UAEDrivingLicense?.[0] as UAEDrivingLicense;

                return uaeDrivingLicense?.isUAEResident ? 'Yes' : 'No';
            },
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.UAEDrivingLicense}EmiratesIdNumber`,
            header: t('customerDetails:fields.drivingLicense.uaeResident.emiratesId.label'),
            getCellValue: ({ support: { customer } }) => {
                const uaeDrivingLicense = customer?.UAEDrivingLicense?.[0] as UAEDrivingLicense;

                return uaeDrivingLicense?.isUAEResident ? uaeDrivingLicense?.emiratesId : '';
            },
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.UAEDrivingLicense}IssueDate`,
            header: t('customerDetails:fields.drivingLicense.uaeResident.uaeDriverLicenseIssueDate.label'),
            getCellValue: ({ support: { customer, company } }, { t }) => {
                const uaeDrivingLicense = customer?.UAEDrivingLicense?.[0] as UAEDrivingLicense;

                return uaeDrivingLicense?.isUAEResident && uaeDrivingLicense?.issueDate
                    ? dayjs(uaeDrivingLicense?.issueDate).tz(company?.timeZone).format(t('common:formats.datePicker'))
                    : '';
            },
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.UAEDrivingLicense}ExpiryDate`,
            header: t('customerDetails:fields.drivingLicense.uaeResident.uaeDriverLicenseExpiryDate.label'),
            getCellValue: ({ support: { customer, company } }, { t }) => {
                const uaeDrivingLicense = customer?.UAEDrivingLicense?.[0] as UAEDrivingLicense;

                return uaeDrivingLicense?.isUAEResident && uaeDrivingLicense?.expiryDate
                    ? dayjs(uaeDrivingLicense?.expiryDate).tz(company?.timeZone).format(t('common:formats.datePicker'))
                    : '';
            },
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.UAEDrivingLicense}PassportNumber`,
            header: t('customerDetails:fields.drivingLicense.uaeResident.passport.label'),
            getCellValue: ({ support: { customer } }) => {
                const uaeDrivingLicense = customer?.UAEDrivingLicense?.[0] as UAEDrivingLicense;

                return !uaeDrivingLicense?.isUAEResident ? uaeDrivingLicense?.passport : '';
            },
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.UAEDrivingLicense}PassportIssueCountry`,
            header: t('customerDetails:fields.drivingLicense.uaeResident.issuedCountry.label'),
            getCellValue: ({ support: { customer } }) => {
                const uaeDrivingLicense = customer?.UAEDrivingLicense?.[0] as UAEDrivingLicense;

                return !uaeDrivingLicense?.isUAEResident ? uaeDrivingLicense?.issuedCountry : '';
            },
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.UAEDrivingLicense}ForeignerIssueDate`,
            header: t('customerDetails:fields.drivingLicense.uaeResident.foreignerDrivingLicenseIssueDate.label'),
            getCellValue: ({ support: { customer, company } }, { t }) => {
                const uaeDrivingLicense = customer?.UAEDrivingLicense?.[0] as UAEDrivingLicense;

                return !uaeDrivingLicense?.isUAEResident && uaeDrivingLicense?.issueDate
                    ? dayjs(uaeDrivingLicense?.issueDate).tz(company?.timeZone).format(t('common:formats.datePicker'))
                    : '';
            },
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
        {
            key: `${LocalCustomerFieldKey.UAEDrivingLicense}ForeignerExpiryDate`,
            header: t('customerDetails:fields.drivingLicense.uaeResident.foreignerDrivingLicenseExpiryDate.label'),
            getCellValue: ({ support: { customer, company } }, { t }) => {
                const uaeDrivingLicense = customer?.UAEDrivingLicense?.[0] as UAEDrivingLicense;

                return !uaeDrivingLicense?.isUAEResident && uaeDrivingLicense?.expiryDate
                    ? dayjs(uaeDrivingLicense?.expiryDate).tz(company?.timeZone).format(t('common:formats.datePicker'))
                    : '';
            },
            fieldKey: LocalCustomerFieldKey.UAEDrivingLicense,
        },
    ];

    const kycSalaryTransferredBankSetting: SystemKYCColumnSetting[] = [
        {
            key: `${LocalCustomerFieldKey.SalaryTransferredBankSet}Bank`,
            header: t('customerDetails:fields.salaryTransferredBank.enabled.label'),
            getCellValue: ({ support: { customer } }) => {
                if (typeof customer?.salaryTransferredBankSet === 'undefined') {
                    return '';
                }

                return customer?.salaryTransferredBankSet?.enabled ? 'Yes' : 'No';
            },
            fieldKey: LocalCustomerFieldKey.SalaryTransferredBankSet,
        },
        {
            key: `${LocalCustomerFieldKey.SalaryTransferredBankSet}BankName`,
            header: t('customerDetails:fields.salaryTransferredBank.bankName.label'),
            getCellValue: ({ support: { customer } }) => customer?.salaryTransferredBankSet?.bankName ?? '',
            fieldKey: LocalCustomerFieldKey.SalaryTransferredBankSet,
        },
        {
            key: `${LocalCustomerFieldKey.SalaryTransferredBankSet}BankAccountNumber`,
            header: t('customerDetails:fields.salaryTransferredBank.bankAccountNumber.label'),
            getCellValue: ({ support: { customer } }) => customer?.salaryTransferredBankSet?.bankAccountNumber ?? '',
            fieldKey: LocalCustomerFieldKey.SalaryTransferredBankSet,
        },
    ];

    const kycReferenceDetailSetting: SystemKYCColumnSetting[] = [
        {
            key: `${LocalCustomerFieldKey.ReferenceDetailSet}Name`,
            header: t('customerDetails:fields.referenceDetail.name.label'),
            getCellValue: ({ support: { customer } }) => customer?.referenceDetailSet?.name ?? '',
            fieldKey: LocalCustomerFieldKey.SalaryTransferredBankSet,
        },
        {
            key: `${LocalCustomerFieldKey.ReferenceDetailSet}Relationship`,
            header: t('customerDetails:fields.referenceDetail.relationship.label'),
            getCellValue: ({ support: { customer } }) => customer?.referenceDetailSet?.relationship ?? '',
            fieldKey: LocalCustomerFieldKey.SalaryTransferredBankSet,
        },
        {
            key: `${LocalCustomerFieldKey.ReferenceDetailSet}ContactNumber`,
            header: t('customerDetails:fields.referenceDetail.contactNumber.label'),
            getCellValue: ({ support: { customer } }) =>
                customer?.referenceDetailSet?.contactNumber?.value
                    ? `+${customer?.referenceDetailSet?.contactNumber?.prefix}${customer?.referenceDetailSet?.contactNumber?.value}`
                    : '',
            fieldKey: LocalCustomerFieldKey.SalaryTransferredBankSet,
        },
    ];

    const kycUAEIdentitySetSetting: SystemKYCColumnSetting[] = [
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}PassportNumber`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.passportNumber.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.uaeIdentitySet?.passportNumber ?? '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}PassportExpiryDate`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.passportExpiryDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.uaeIdentitySet?.passportExpiryDate
                    ? getFormattedDateOnly(t, customer?.uaeIdentitySet?.passportExpiryDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}EmiratesIdNumber`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.emiratesIdNumber.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.uaeIdentitySet?.emiratesIdNumber ?? '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}EmiratesIdExpiryDate`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.emiratesIdExpiryDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.uaeIdentitySet?.emiratesIdExpiryDate
                    ? getFormattedDateOnly(t, customer?.uaeIdentitySet?.emiratesIdExpiryDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}DrivingLicenseNumber`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.drivingLicenseNumber.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.uaeIdentitySet?.drivingLicenseNumber ?? '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}DrivingLicenseExpiryDate`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.drivingLicenseNumber.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.uaeIdentitySet?.drivingLicenseExpiryDate
                    ? getFormattedDateOnly(t, customer?.uaeIdentitySet?.drivingLicenseExpiryDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}ResidenceSince`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.uaeResidenceSince.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.uaeIdentitySet?.uaeResidenceSince
                    ? getFormattedDateOnly(t, customer?.uaeIdentitySet?.uaeResidenceSince, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}VisaNumber`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.uaeVisaNumber.label')}`,
            getCellValue: ({ support: { customer } }) => customer?.uaeIdentitySet?.uaeVisaNumber ?? '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}VisaIssueDate`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.uaeVisaIssueDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.uaeIdentitySet?.uaeVisaIssueDate
                    ? getFormattedDateOnly(t, customer?.uaeIdentitySet?.uaeVisaIssueDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
        {
            key: `${LocalCustomerFieldKey.UAEIdentitySet}VisaExpiryDate`,
            header: `${t('customerDetails:fields.uaeIdSet.excel.label')}${t('customerDetails:fields.uaeIdSet.uaeVisaExpiryDate.label')}`,
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.uaeIdentitySet?.uaeVisaExpiryDate
                    ? getFormattedDateOnly(t, customer?.uaeIdentitySet?.uaeVisaExpiryDate, company?.timeZone)
                    : '',
            fieldKey: LocalCustomerFieldKey.UAEIdentitySet,
        },
    ];

    const kycMainSetting: (SystemKYCColumnSetting & {
        key: LocalCustomerFieldKey;
    })[] = [
        {
            key: LocalCustomerFieldKey.Title,
            header: t('customerDetails:fields.Title.label'),
            getCellValue: ({ support: { customer } }) => customer?.title ?? '',
        },
        {
            key: LocalCustomerFieldKey.Salutation,
            header: t('customerDetails:fields.Salutation.label'),
            getCellValue: ({ support: { customer } }) => customer?.salutation ?? '',
        },
        {
            key: LocalCustomerFieldKey.SalutationBmw,
            header: t('customerDetails:fields.SalutationBmw.label'),
            getCellValue: ({ support: { customer } }) => customer?.salutationBmw ?? '',
        },
        {
            key: LocalCustomerFieldKey.LastNameFront,
            header: t('customerDetails:fields.LastNameFront.label'),
            getCellValue: ({ support: { customer } }) => customer?.lastNameFront ?? '',
        },
        {
            key: LocalCustomerFieldKey.FirstName,
            header: t('customerDetails:fields.FirstName.label'),
            getCellValue: ({ support: { customer } }) => customer?.firstName ?? '',
        },
        {
            key: LocalCustomerFieldKey.LastName,
            header: t('customerDetails:fields.LastName.label'),
            getCellValue: ({ support: { customer } }) => customer?.lastName ?? '',
        },
        {
            key: LocalCustomerFieldKey.LastNameJapan,
            header: t('customerDetails:fields.LastNameJapan.label'),
            getCellValue: ({ support: { customer } }) => customer?.lastNameJapan ?? '',
        },
        {
            key: LocalCustomerFieldKey.FirstNameJapan,
            header: t('customerDetails:fields.FirstNameJapan.label'),
            getCellValue: ({ support: { customer } }) => customer?.firstNameJapan ?? '',
        },
        {
            key: LocalCustomerFieldKey.FullName,
            header: t('customerDetails:fields.FullName.label'),
            getCellValue: ({ support: { customer } }) => customer?.fullName ?? '',
        },
        {
            key: LocalCustomerFieldKey.IdentityNumber,
            header: t('customerDetails:fields.IdentityNumber.label'),
            getCellValue: ({ support: { customer } }) => customer?.identityNumber ?? '',
        },
        {
            key: LocalCustomerFieldKey.Passport,
            header: t('customerDetails:fields.Passport.label'),
            getCellValue: ({ support: { customer } }) => customer?.passport ?? '',
        },
        {
            key: LocalCustomerFieldKey.Birthday,
            header: t('customerDetails:fields.Birthday.label'),
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.birthday ? getFormattedDateOnly(t, customer?.birthday, company?.timeZone) : '',
        },
        {
            key: LocalCustomerFieldKey.Gender,
            header: t('customerDetails:fields.Gender.label'),
            getCellValue: ({ support: { customer } }, { t }) => customer?.gender ?? '',
        },
        {
            key: LocalCustomerFieldKey.Email,
            header: t('customerDetails:fields.Email.label'),
            getCellValue: ({ support: { customer } }) => customer?.email ?? '',
        },
        {
            key: LocalCustomerFieldKey.Phone,
            header: t('customerDetails:fields.Phone.label'),
            getCellValue: ({ support: { customer } }) =>
                customer?.phone?.value ? `+${customer?.phone.prefix}${customer?.phone.value}` : '',
        },
        {
            key: LocalCustomerFieldKey.Country,
            header: t('customerDetails:fields.Country.label'),
            getCellValue: ({ support: { customer } }) => customer?.country ?? '',
        },
        {
            key: LocalCustomerFieldKey.Nationality,
            header: t('customerDetails:fields.Nationality.label'),
            getCellValue: ({ support: { customer } }) => customer?.nationality ?? '',
        },
        {
            key: LocalCustomerFieldKey.PostalCode,
            header: t('customerDetails:fields.PostalCode.label'),
            getCellValue: ({ support: { customer } }) => customer?.postalCode ?? '',
        },
        {
            key: LocalCustomerFieldKey.Address,
            header: t('customerDetails:fields.Address.label'),
            getCellValue: ({ support: { customer } }) => customer?.address ?? '',
        },
        {
            key: LocalCustomerFieldKey.AddressType,
            header: t('customerDetails:fields.AddressType.label'),
            getCellValue: ({ support: { customer } }) => customer?.addressType ?? '',
        },
        {
            key: LocalCustomerFieldKey.UnitNumber,
            header: t('customerDetails:fields.UnitNumber.label'),
            getCellValue: ({ support: { customer } }) => customer?.unitNumber ?? '',
        },
        {
            key: LocalCustomerFieldKey.District,
            header: t('customerDetails:fields.District.label'),
            getCellValue: ({ support: { customer } }) => customer?.district ?? '',
        },
        {
            key: LocalCustomerFieldKey.Emirate,
            header: t('customerDetails:fields.Emirate.label'),
            getCellValue: ({ support: { customer } }) => customer?.emirate ?? '',
        },
        {
            key: LocalCustomerFieldKey.Road,
            header: t('customerDetails:fields.Road.label'),
            getCellValue: ({ support: { customer } }) => customer?.road ?? '',
        },
        {
            key: LocalCustomerFieldKey.Telephone,
            header: t('customerDetails:fields.Telephone.label'),
            getCellValue: ({ support: { customer } }) =>
                customer?.telephone ? `+${customer?.telephone.prefix}${customer?.telephone.value}` : '',
        },
        {
            key: LocalCustomerFieldKey.Region,
            header: t('customerDetails:fields.Region.label'),
            getCellValue: ({ support: { customer } }) => customer?.region ?? '',
        },
        {
            key: LocalCustomerFieldKey.City,
            header: t('customerDetails:fields.City.label'),
            getCellValue: ({ support: { customer } }) => customer?.city ?? '',
        },
        {
            key: LocalCustomerFieldKey.Citizenship,
            header: t('customerDetails:fields.Citizenship.label'),
            getCellValue: ({ support: { customer } }) => customer?.citizenship ?? '',
        },
        {
            key: LocalCustomerFieldKey.Education,
            header: t('customerDetails:fields.Education.label'),
            getCellValue: ({ support: { customer } }) => customer?.education ?? '',
        },
        {
            key: LocalCustomerFieldKey.ResidentialStatus,
            header: t('customerDetails:fields.ResidentialStatus.label'),
            getCellValue: ({ support: { customer } }) => customer?.residentialStatus ?? '',
        },
        {
            key: LocalCustomerFieldKey.ResidentialStatusVWFS,
            header: t('customerDetails:fields.ResidentialStatusVWFS.label'),
            getCellValue: ({ support: { customer } }) => customer?.residentialStatusVWFS?.value ?? '',
        },
        {
            key: LocalCustomerFieldKey.ResidenceType,
            header: t('customerDetails:fields.ResidenceType.label'),
            getCellValue: ({ support: { customer } }) => customer?.residenceType ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorrespondenceCity,
            header: t('customerDetails:fields.CorrespondenceCity.label'),
            getCellValue: ({ support: { customer } }) => customer?.correspondenceCity ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorrespondenceDistrict,
            header: t('customerDetails:fields.CorrespondenceDistrict.label'),
            getCellValue: ({ support: { customer } }) => customer?.correspondenceDistrict ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorrespondenceAddress,
            header: t('customerDetails:fields.CorrespondenceAddress.label'),
            getCellValue: ({ support: { customer } }) => customer?.correspondenceAddress ?? '',
        },
        {
            key: LocalCustomerFieldKey.IncomeType,
            header: t('customerDetails:fields.IncomeType.label'),
            getCellValue: ({ support: { customer } }) => customer?.incomeType ?? '',
        },
        {
            key: LocalCustomerFieldKey.MonthlyIncome,
            header: t('customerDetails:fields.MonthlyIncome.label'),
            getCellValue: ({ support: { customer } }) => customer?.monthlyIncome ?? '',
        },
        {
            key: LocalCustomerFieldKey.OtherIncome,
            header: t('customerDetails:fields.OtherIncome.label'),
            getCellValue: ({ support: { customer } }) => customer?.otherIncome ?? '',
        },
        {
            key: LocalCustomerFieldKey.DateOfJoining,
            header: t('customerDetails:fields.DateOfJoining.label'),
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.dateOfJoining ? getFormattedDateOnly(t, customer?.dateOfJoining, company?.timeZone) : '',
        },
        {
            key: LocalCustomerFieldKey.PreferredFirstPaymentDate,
            header: t('customerDetails:fields.PreferredFirstPaymentDate.label'),
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.preferredFirstPaymentDate
                    ? getFormattedDateOnly(t, customer?.preferredFirstPaymentDate, company?.timeZone)
                    : '',
        },
        {
            key: LocalCustomerFieldKey.JobTitle,
            header: t('customerDetails:fields.JobTitle.label'),
            getCellValue: ({ support: { customer } }) => customer?.jobTitle?.value ?? '',
        },
        {
            key: LocalCustomerFieldKey.JobTitleTh,
            header: t('customerDetails:fields.JobTitleTh.label'),
            getCellValue: ({ support: { customer } }) => customer?.jobTitleTh?.value ?? '',
        },
        {
            key: LocalCustomerFieldKey.Occupation,
            header: t('customerDetails:fields.Occupation.label'),
            getCellValue: ({ support: { customer } }) => customer?.occupation ?? '',
        },
        {
            key: LocalCustomerFieldKey.EmploymentStatus,
            header: t('customerDetails:fields.EmploymentStatus.label'),
            getCellValue: ({ support: { customer } }) => customer?.employmentStatus ?? '',
        },
        {
            key: LocalCustomerFieldKey.TimeOfEmployment,
            header: t('customerDetails:fields.TimeOfEmployment.label'),
            getCellValue: ({ support: { customer } }) => customer?.timeOfEmployment ?? '',
        },
        {
            key: LocalCustomerFieldKey.TimeOfAddress,
            header: t('customerDetails:fields.TimeOfAddress.label'),
            getCellValue: ({ support: { customer } }) => customer?.timeOfAddress ?? '',
        },
        {
            key: LocalCustomerFieldKey.CompanyAddress,
            header: t('customerDetails:fields.CompanyAddress.label'),
            getCellValue: ({ support: { customer } }) => customer?.companyName ?? '',
        },
        {
            key: LocalCustomerFieldKey.CompanyPhoneticName,
            header: t('customerDetails:fields.CompanyPhoneticName.label'),
            getCellValue: ({ support: { customer } }) => customer?.companyPhoneticName ?? '',
        },
        {
            key: LocalCustomerFieldKey.CompanyCity,
            header: t('customerDetails:fields.CompanyCity.label'),
            getCellValue: ({ support: { customer } }) => customer?.companyCity ?? '',
        },
        {
            key: LocalCustomerFieldKey.CompanyDistrict,
            header: t('customerDetails:fields.CompanyDistrict.label'),
            getCellValue: ({ support: { customer } }) => customer?.companyDistrict ?? '',
        },
        {
            key: LocalCustomerFieldKey.CompanyAddress,
            header: t('customerDetails:fields.CompanyAddress.label'),
            getCellValue: ({ support: { customer } }) => customer?.companyAddress ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorporateName,
            header: t('customerDetails:fields.CorporateName.label'),
            getCellValue: ({ support: { customer } }) => customer?.corporateName ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorporateIdentityNumber,
            header: t('customerDetails:fields.CorporateIdentityNumber.label'),
            getCellValue: ({ support: { customer } }) => customer?.corporateIdentityNumber ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorporateRegistrationDate,
            header: t('customerDetails:fields.CorporateRegistrationDate.label'),
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.corporateRegistrationDate
                    ? getFormattedDateOnly(t, customer?.corporateRegistrationDate, company?.timeZone)
                    : '',
        },
        {
            key: LocalCustomerFieldKey.CorporateIndustryCategory,
            header: t('customerDetails:fields.CorporateIndustryCategory.label'),
            getCellValue: ({ support: { customer } }) => customer?.corporateIndustryCategory ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorporateAnnualRevenue,
            header: t('customerDetails:fields.CorporateAnnualRevenue.label'),
            getCellValue: ({ support: { customer } }) => customer?.corporateAnnualRevenue ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorporateNumberOfEmployee,
            header: t('customerDetails:fields.CorporateNumberOfEmployee.label'),
            getCellValue: ({ support: { customer } }) => customer?.corporateNumberOfEmployee ?? '',
        },
        {
            key: LocalCustomerFieldKey.CorporatePhone,
            header: t('customerDetails:fields.CorporatePhone.label'),
            getCellValue: ({ support: { customer } }) =>
                customer?.corporatePhone ? `+${customer?.corporatePhone.prefix}${customer?.corporatePhone?.value}` : '',
        },
        {
            key: LocalCustomerFieldKey.DriverLicensePassDate,
            header: t('customerDetails:fields.DriverLicensePassDate.label'),
            getCellValue: ({ support: { customer, company } }, { t }) =>
                customer?.driverLicensePassDate
                    ? getFormattedDateOnly(t, customer?.driverLicensePassDate, company?.timeZone)
                    : '',
        },
        {
            key: LocalCustomerFieldKey.PurchaseIntention,
            header: t('customerDetails:fields.PurchaseIntention.label'),
            getCellValue: ({ support: { customer } }, { t }) =>
                getPurchaseIntentionString(t, customer?.purchaseIntention),
        },
        {
            key: LocalCustomerFieldKey.Comments,
            header: t('customerDetails:fields.Comments.label'),
            getCellValue: ({ support: { customer } }) => customer?.comments ?? '',
        },
        {
            key: LocalCustomerFieldKey.BusinessTitle,
            header: t('customerDetails:fields.BusinessTitle.label'),
            getCellValue: ({ support: { customer } }) => customer?.businessTitle ?? '',
        },
    ];

    return [
        kycDrivingLicenseSetting,
        kycDrivingLicenseMySetting,
        kycDrivingLicenseThSetting,
        kycUaeDrivingLicenseSetting,
        kycSalaryTransferredBankSetting,
        kycReferenceDetailSetting,
        kycUAEIdentitySetSetting,
        kycMainSetting,
    ];
};

export type SystemKYCSetting = [
    Dictionary<SystemKYCColumnSetting>,
    SystemKYCColumnSetting[],
    { [key: string]: number },
];

const getSystemKycSetting = async (
    module: SystemKycSupportedApplicationModule | LocalCustomerManagementModule,
    t: TFunction,
    loaders = createLoaders()
): Promise<SystemKYCSetting> => {
    const customerModuleId = module._type === ModuleType.LocalCustomerManagement ? module._id : module.customerModuleId;
    if (!customerModuleId) {
        return null;
    }

    const customerModule = await loaders.customerModuleById.load(customerModuleId);
    if (!customerModule || customerModule._type !== ModuleType.LocalCustomerManagement) {
        return null;
    }

    const sortedKycFields = customerModule.kycFields.sort((a, b) => a.order - b.order);
    const customerFieldOrder = sortedKycFields.reduce(
        (acc, setting) => {
            acc[setting.field] = setting.order;

            return acc;
        },
        {} as { [key in LocalCustomerFieldKey]: number }
    );

    const [
        kycDrivingLicenseSetting,
        kycDrivingLicenseMySetting,
        kycDrivingLicenseThSetting,
        kycUaeDrivingLicenseSetting,
        kycSalaryTransferredBankSetting,
        kycReferenceDetailSetting,
        kycUAEIdentitySetSetting,
        kycMainSetting,
    ] = updateKYCHeader(t);

    const mappedKycMain = keyBy('key', kycMainSetting);

    const getHeaderFromKycPreset = (kycPresets: KYCPreset[]) => {
        const normalFields: SystemKYCColumnSetting[] = [];
        const fieldSets: SystemKYCColumnSetting[] = [];

        kycPresets.forEach(preset => {
            preset.fields.forEach(field => {
                if (field.purpose?.includes(KycFieldPurpose.KYC) || field.purpose?.includes(KycFieldPurpose.Share)) {
                    if (field.key === LocalCustomerFieldKey.DrivingLicense) {
                        fieldSets.push(...kycDrivingLicenseSetting);
                    }

                    if (field.key === LocalCustomerFieldKey.DrivingLicenseMy) {
                        fieldSets.push(...kycDrivingLicenseMySetting);
                    }

                    if (field.key === LocalCustomerFieldKey.DrivingLicenseTh) {
                        fieldSets.push(...kycDrivingLicenseThSetting);
                    }

                    if (field.key === LocalCustomerFieldKey.UAEDrivingLicense) {
                        fieldSets.push(...kycUaeDrivingLicenseSetting);
                    }

                    if (field.key === LocalCustomerFieldKey.SalaryTransferredBankSet) {
                        fieldSets.push(...kycSalaryTransferredBankSetting);
                    }

                    if (field.key === LocalCustomerFieldKey.ReferenceDetailSet) {
                        fieldSets.push(...kycReferenceDetailSetting);
                    }

                    if (field.key === LocalCustomerFieldKey.UAEIdentitySet) {
                        fieldSets.push(...kycUAEIdentitySetSetting);
                    }

                    if (mappedKycMain[field.key]) {
                        normalFields.push(mappedKycMain[field.key]);
                    }
                }
            });
        });

        return [...normalFields, ...fieldSets];
    };

    const allSettings = await (async () => {
        if (module._type === ModuleType.EventApplicationModule) {
            const events = await loaders.eventsByModuleId.load(module._id);

            const eventKycPresets = await Promise.all(events.map(event => getKYCPresetsForEvent(event, 'local')));
            const allHeaders = eventKycPresets.map(getHeaderFromKycPreset);

            return uniqBy('header', allHeaders.flat());
        }

        const applicantKyc = await getKYCPresetsForCustomerModuleId(customerModuleId, 'local', loaders);
        const applicantHeader = getHeaderFromKycPreset(applicantKyc);

        const corporateKyc = await getKYCPresetsForCustomerModuleId(customerModuleId, 'corporate', loaders);
        const corporateHeader = getHeaderFromKycPreset(corporateKyc);

        // Using header, because for fieldset, it was determined by same key
        return uniqBy('header', [...applicantHeader, ...corporateHeader]);
    })();
    if (isEmpty(allSettings)) {
        return null;
    }

    const sortedSettings = [...allSettings].sort((a, b) => {
        const aComparedKey = a.fieldKey ?? a.key;
        const bComparedKey = b.fieldKey ?? b.key;

        return (customerFieldOrder[aComparedKey] ?? 0) - (customerFieldOrder[bComparedKey] ?? 0);
    });

    return [keyBy('header', sortedSettings), sortedSettings, customerFieldOrder] as const;
};

export default getSystemKycSetting;
