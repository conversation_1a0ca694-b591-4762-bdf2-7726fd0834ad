import type { TFunction } from 'i18next';
import { keyBy } from 'lodash/fp';
import { type ConsentsAndDeclarations, ConsentsAndDeclarationsType } from '../../../../../database';
import { CheckConsent, type SystemLeadColumnSetting } from '../../shared/types';

export enum SystemConsentSettingKey {
    PersonalData = 'personalData',
    Mail = 'mail',
    Phone = 'phone',
    Email = 'email',
    Sms = 'sms',
    Fax = 'fax',
}

export type SystemConsentColumnSetting = SystemLeadColumnSetting & {
    key: SystemConsentSettingKey;
};

type SystemConsentKey = { [key in SystemConsentSettingKey]: SystemConsentColumnSetting };

// created this file to pass consent instead of module ids
// this is to observe and see `consent.ts` is to be deprecated
const getSystemConsentSetting = (t: TFunction, consents: ConsentsAndDeclarations[]) => {
    if (!consents || consents.length === 0) {
        return null;
    }

    const hasMarketingConsentsInModule = consents.some(
        consent => consent._type === ConsentsAndDeclarationsType.Marketing
    );

    const allSettings: SystemConsentColumnSetting[] = hasMarketingConsentsInModule
        ? [
              {
                  key: SystemConsentSettingKey.PersonalData,
                  header: t('applicationExcel:consent.personalData'),
                  getCellValue: ({ support: { consentData } }) => consentData?.personal ?? CheckConsent.NotExist,
              },
              {
                  key: SystemConsentSettingKey.Mail,
                  header: t('applicationExcel:consent.mail'),
                  getCellValue: ({ support: { consentData } }) => consentData?.mail ?? CheckConsent.NotExist,
              },
              {
                  key: SystemConsentSettingKey.Phone,
                  header: t('applicationExcel:consent.phone'),
                  getCellValue: ({ support: { consentData } }) => consentData?.phone ?? CheckConsent.NotExist,
              },
              {
                  key: SystemConsentSettingKey.Email,
                  header: t('applicationExcel:consent.email'),
                  getCellValue: ({ support: { consentData } }) => consentData?.email ?? CheckConsent.NotExist,
              },
              {
                  key: SystemConsentSettingKey.Sms,
                  header: t('applicationExcel:consent.sms'),
                  getCellValue: ({ support: { consentData } }) => consentData?.sms ?? CheckConsent.NotExist,
              },
              {
                  key: SystemConsentSettingKey.Fax,
                  header: t('applicationExcel:consent.fax'),
                  getCellValue: ({ support: { consentData } }) => consentData?.fax ?? CheckConsent.NotExist,
              },
          ]
        : [];

    return [keyBy('key', allSettings) as SystemConsentKey, allSettings] as const;
};

export default getSystemConsentSetting;
