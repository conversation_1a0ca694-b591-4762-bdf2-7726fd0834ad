import type { TFunction } from 'i18next';
import { keyBy } from 'lodash/fp';
import { VehicleKind } from '../../../../../database';
import type { SystemLeadColumnSetting } from '../../shared/types';

export enum SystemVehicleSettingKey {
    Make = 'make',
    Model = 'model',
    SubModel = 'subModel',
    VariantName = 'variantName',
    VariantId = 'variantId',
    Price = 'price',
    TestDrive = 'testDrive',
    ChassisNo = 'chassisNo',
    EngineNo = 'engineNo',
}

export type SystemVehicleColumnSetting = SystemLeadColumnSetting & {
    key: SystemVehicleSettingKey;
};

type SystemVehicleKey = { [key in SystemVehicleSettingKey]: SystemVehicleColumnSetting };

const getSystemVehicleSetting = (t: TFunction) => {
    const allSettings: SystemVehicleColumnSetting[] = [
        {
            key: SystemVehicleSettingKey.Make,
            header: t('applicationExcel:vehicle.make'),
            getCellValue: preparedData => {
                const { vehicle, make } = preparedData.support;

                if (!vehicle) {
                    return '';
                }

                if (vehicle._kind === VehicleKind.FinderVehicle) {
                    return 'Porsche';
                }

                return make?.name?.defaultValue ?? '';
            },
        },
        {
            key: SystemVehicleSettingKey.Model,
            header: t('applicationExcel:vehicle.model'),
            getCellValue: preparedData => {
                const { model, vehicle } = preparedData.support;
                if (!vehicle) {
                    return '';
                }

                if (vehicle._kind === VehicleKind.FinderVehicle) {
                    return vehicle.listing?.vehicle?.modelCategory?.localize ?? '';
                }

                return model?.name?.defaultValue ?? '';
            },
        },
        {
            key: SystemVehicleSettingKey.SubModel,
            header: t('applicationExcel:vehicle.subModel'),
            getCellValue: preparedData => {
                const { subModel, vehicle } = preparedData.support;
                if (!vehicle) {
                    return '';
                }

                if (vehicle._kind === VehicleKind.FinderVehicle) {
                    return '';
                }

                return subModel?.name?.defaultValue ?? '';
            },
        },
        {
            key: SystemVehicleSettingKey.VariantName,
            header: t('applicationExcel:vehicle.vehicleName'),
            getCellValue: preparedData => preparedData.support.vehicle?.name?.defaultValue ?? '',
        },
        {
            key: SystemVehicleSettingKey.VariantId,
            header: t('applicationExcel:vehicle.vehicleId'),
            getCellValue: preparedData => {
                const { vehicle } = preparedData.support;

                if (!vehicle) {
                    return '';
                }

                if (vehicle._kind === VehicleKind.FinderVehicle) {
                    return vehicle.listing?.vehicle?.orderTypeCode ?? '';
                }

                return vehicle.identifier ?? '';
            },
        },
        {
            key: SystemVehicleSettingKey.Price,
            header: t('applicationExcel:vehicle.price'),
            getCellValue: preparedData => {
                const { vehicle } = preparedData.support;

                if (vehicle?._kind === VehicleKind.FinderVehicle) {
                    return vehicle.listing?.price?.value ?? '';
                }

                if (vehicle?._kind === VehicleKind.LocalVariant) {
                    return vehicle.vehiclePrice ?? '';
                }

                return '';
            },
        },
        {
            key: SystemVehicleSettingKey.ChassisNo,
            header: t('applicationExcel:vehicle.chassisNo'),
            getCellValue: preparedData => {
                const { vehicle } = preparedData.support;

                if (vehicle?._kind === VehicleKind.FinderVehicle) {
                    return vehicle.listing?.vehicle.vin ?? '';
                }

                return '';
            },
        },
        {
            key: SystemVehicleSettingKey.EngineNo,
            header: t('applicationExcel:vehicle.engineNo'),
            getCellValue: preparedData => {
                const { vehicle } = preparedData.support;

                if (vehicle?._kind === VehicleKind.FinderVehicle) {
                    return vehicle.lta?.engineNumber ?? '';
                }

                return '';
            },
        },
    ];

    return [keyBy('key', allSettings) as SystemVehicleKey, allSettings] as const;
};

export default getSystemVehicleSetting;
