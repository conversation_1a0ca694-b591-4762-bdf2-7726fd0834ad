import { flow, isNil, map, uniq, uniqBy } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationEventCustomizedField,
    ApplicationKind,
    type ConsentsAndDeclarations,
    type EventLead,
    type LeadModule,
    type Module,
    ModuleType,
} from '../../../../database';
import createLoaders from '../../../../loaders';
import { getTimeZoneOffset } from '../../../date';
import ensureManyFromLoaders from '../../../ensureManyFromLoaders';
import { CapColumnSetting, getCapColumns } from '../shared/setting/cap';
import {
    LEAD_EXCEL_MODULE_TYPES,
    type GetLeadRowsSupportingData,
    type PreparedSystemLeadData,
    type SystemKycSupportedApplicationModule,
} from '../shared/types';
import { CampaignValuesColumnSetting, getCampaignValuesColumns } from './setting/campaign';
import getSystemConsentSetting, { SystemConsentColumnSetting } from './setting/consentUpdated';
import getSystemKycSetting, { SystemKYCColumnSetting, SystemKYCSetting } from './setting/kyc';
import type { SystemMainColumnSetting } from './setting/main';
import getSystemMainSetting from './setting/main';
import getSystemMainSettingId from './setting/mainId';
import getSystemVehicleSetting, { SystemVehicleColumnSetting } from './setting/vehicle';

type ProcessingCache = {
    moduleTypes: ModuleType[];
    hasCapBPId: boolean;
    hasCapLeadId: boolean;
    eventLeads: EventLead[];
    customizedFields: ApplicationEventCustomizedField[];
    agreementsModuleIds: ObjectId[];
    consents: ConsentsAndDeclarations[];
    systemKYCSetting: SystemKYCSetting[];
};

// Precompute expensive operations once
const createProcessingCache = async (
    modules: LeadModule[],
    items: PreparedSystemLeadData[],
    support: GetLeadRowsSupportingData
): Promise<ProcessingCache> => {
    const { t } = support;
    const moduleTypes = flow([map((module: Module) => module._type), uniq])(modules);

    // Compute CAP flags once
    const hasCapBPId = items.some(i => i.support.capValues?.businessPartnerId);
    const hasCapLeadId = items.some(i => i.support.capValues?.leadId);

    // Extract event-related data once
    const eventLeads = items.filter(({ kind }) => kind === ApplicationKind.Event) as EventLead[];
    const customizedFields = eventLeads.flatMap(({ customizedFields }) => customizedFields);

    // Extract agreements module IDs once
    const agreementsModuleIds = modules.map(module => module.agreementsModuleId).filter(Boolean);

    const { consentsAndDeclarationsByModuleId } = createLoaders();

    const consents = Array.isArray(agreementsModuleIds)
        ? (await consentsAndDeclarationsByModuleId.loadMany(agreementsModuleIds).then(ensureManyFromLoaders)).flatMap(
              consents => consents
          )
        : await consentsAndDeclarationsByModuleId.load(agreementsModuleIds);

    const kycSupportedModules = !modules.some(module => LEAD_EXCEL_MODULE_TYPES.includes(module._type))
        ? []
        : (modules as SystemKycSupportedApplicationModule[]);
    const settingsPromises = kycSupportedModules.map(module => getSystemKycSetting(module, t));
    const systemKYCSetting = (await Promise.all(settingsPromises)).filter(Boolean);

    return {
        moduleTypes,
        hasCapBPId,
        hasCapLeadId,
        eventLeads,
        customizedFields,
        agreementsModuleIds,
        consents,
        systemKYCSetting,
    };
};

// Optimized column generators with caching
const createColumnGenerators = (cache: ProcessingCache, support: GetLeadRowsSupportingData) => {
    const { t, showCreatedDateWithTime, timeZone } = support;
    const { moduleTypes, hasCapBPId, hasCapLeadId, customizedFields, consents, systemKYCSetting } = cache;

    return {
        // Main ID columns - lightweight, compute synchronously
        getMainIdColumns: () => {
            if (!isNil(moduleTypes) && moduleTypes.some(moduleType => LEAD_EXCEL_MODULE_TYPES.includes(moduleType))) {
                const [, allMainColumnIds] = getSystemMainSettingId({ id: `LEAD ID` });

                return uniqBy('key', allMainColumnIds);
            }

            return [];
        },

        // Main columns - lightweight, compute synchronously
        getMainColumns: () => {
            if (moduleTypes.some(moduleType => LEAD_EXCEL_MODULE_TYPES.includes(moduleType))) {
                const [, allMainColumns] = getSystemMainSetting({
                    createdAt:
                        timeZone && showCreatedDateWithTime
                            ? `Date Created ${getTimeZoneOffset(t, timeZone)}`
                            : 'Date Created',
                    lastActivity: timeZone ? `Last Activity ${getTimeZoneOffset(t, timeZone)}` : 'Last Activity',
                    status: 'Status',
                    timeZone,
                    t,
                    showCreatedDateWithTime,
                });

                return uniqBy('key', allMainColumns);
            }

            return [];
        },

        // CAP columns - lightweight, compute synchronously
        getCapColumns: () => getCapColumns(t, hasCapBPId, hasCapLeadId),

        // Vehicle columns - lightweight, compute synchronously
        getVehicleColumns: () => {
            if (moduleTypes.some(moduleType => LEAD_EXCEL_MODULE_TYPES.includes(moduleType))) {
                const [, allVehicleColumns] = getSystemVehicleSetting(t);

                return uniqBy('key', allVehicleColumns);
            }

            return [];
        },

        // Campaign columns - lightweight, compute synchronously
        getCampaignColumns: (items: PreparedSystemLeadData[]) => getCampaignValuesColumns(t, items),

        // Customized fields - lightweight, compute synchronously
        getCustomizedFieldColumns: () =>
            uniqBy(({ displayName }) => displayName.defaultValue, customizedFields).map(value => ({
                key: value.displayName.defaultValue,
                header: value.displayName.defaultValue,
                getCellValue: (preparedData: PreparedSystemLeadData) => {
                    if (preparedData.kind === ApplicationKind.Event) {
                        const customizedField = preparedData.customizedFields.find(
                            ({ displayName }) => displayName.defaultValue === value.displayName.defaultValue
                        );

                        return customizedField ? customizedField.value : '';
                    }

                    return '';
                },
            })),

        // Consent columns - async, needs to be awaited
        getConsentColumns: async () => {
            if (consents.length === 0) {
                return [];
            }

            const [, allConsentColumns] = getSystemConsentSetting(t, consents);

            return allConsentColumns || [];
        },

        // KYC columns - async, needs to be awaited
        getKycColumns: async (modules: LeadModule[]) => {
            if (systemKYCSetting.length === 0) {
                return [];
            }

            // Handle multiple modules - only include columns common to all modules
            const columns = systemKYCSetting.flatMap(([, allKycColumns]) =>
                modules.length > 1
                    ? allKycColumns.filter(column =>
                          systemKYCSetting.every(
                              setting => setting && setting[1].find(col => col.header === column.header)
                          )
                      )
                    : allKycColumns
            );

            return uniqBy('key', columns);
        },
    };
};

type ColumnSetting =
    | SystemMainColumnSetting
    | SystemConsentColumnSetting
    | SystemVehicleColumnSetting
    | SystemKYCColumnSetting
    | CampaignValuesColumnSetting
    | CapColumnSetting;

// Optimized row settings generation with parallel processing
const getRowSettingsForLeads = async (
    modules: LeadModule[],
    items: PreparedSystemLeadData[],
    support: GetLeadRowsSupportingData,
    cache: ProcessingCache
): Promise<ColumnSetting[]> => {
    const generators = createColumnGenerators(cache, support);

    // Execute lightweight operations synchronously
    const [mainIdColumns, mainColumns, capColumns, vehicleColumns, campaignColumns, customizedFieldColumns] = [
        generators.getMainIdColumns(),
        generators.getMainColumns(),
        generators.getCapColumns(),
        generators.getVehicleColumns(),
        generators.getCampaignColumns(items),
        generators.getCustomizedFieldColumns(),
    ];

    // Execute expensive async operations in parallel
    const [consentColumns, kycColumns] = await Promise.all([
        generators.getConsentColumns(),
        generators.getKycColumns(modules),
    ]);

    // Combine all columns in the correct order
    return [
        // Application Ids
        ...mainIdColumns,
        // Application Details
        ...mainColumns,
        // C@P related values
        ...capColumns,
        // Customer Details
        ...kycColumns,
        // Vehicle Details
        ...vehicleColumns,
        // Consents
        ...consentColumns,
        // Campaign Values
        ...campaignColumns,
        // Get customized fields
        ...customizedFieldColumns,
    ].filter(Boolean);
};

// Optimized row generation with better memory management
const generateRows = (
    items: PreparedSystemLeadData[],
    settings: ColumnSetting[],
    support: GetLeadRowsSupportingData
): any[][] => {
    // Pre-filter valid settings once
    const validSettings = settings.filter(Boolean);

    // Generate header row
    const header = validSettings.map(setting => setting.header);

    // Generate data rows with optimized cell value extraction
    const dataRows = items.map(item =>
        validSettings.map(setting => {
            try {
                return setting.getCellValue(item, support) ?? '';
            } catch (error) {
                console.warn(`Error extracting cell value for ${setting.header}:`, error);

                return '';
            }
        })
    );

    // return is a mix of string and row value
    return [header, ...dataRows];
};

// Main refactored function with improved performance and error handling
const getLeadModulesSystemRows = async (
    modules: LeadModule[],
    items: PreparedSystemLeadData[],
    support: GetLeadRowsSupportingData
): Promise<any[][]> => {
    // Early validation
    if (!modules?.length || !items?.length) {
        return [[]]; // Return empty header row
    }

    try {
        const cache = await createProcessingCache(modules, items, support);
        const settings = await getRowSettingsForLeads(modules, items, support, cache);

        return generateRows(items, settings, support);
    } catch (error) {
        console.error('Error in getLeadModulesSystemRows:', error);

        // Return minimal fallback data
        return [
            ['Error'], // Header
            ...items.map(() => ['Export Error']), // Error rows
        ];
    }
};

// Alternative streaming version for very large datasets
export const getLeadModulesSystemRowsStreaming = async function* (
    modules: LeadModule[],
    items: PreparedSystemLeadData[],
    support: GetLeadRowsSupportingData,
    batchSize: number = 1000
) {
    if (!modules?.length || !items?.length) {
        yield [[]];

        return;
    }

    try {
        // Precompute cache and settings once
        const cache = await createProcessingCache(modules, items, support);
        const settings = await getRowSettingsForLeads(modules, items, support, cache);
        const validSettings = settings.filter(Boolean);

        // Yield header first
        const header = validSettings.map(setting => setting.header);
        yield [header];

        // Process items in batches
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            const batchRows = batch.map(item =>
                validSettings.map(setting => {
                    try {
                        return setting.getCellValue(item, support) ?? '';
                    } catch (error) {
                        console.warn(`Error extracting cell value for ${setting.header}:`, error);

                        return '';
                    }
                })
            );

            yield batchRows;

            // Yield control periodically
            if (i % (batchSize * 10) === 0) {
                // eslint-disable-next-line no-await-in-loop
                await new Promise(resolve => {
                    setImmediate(resolve);
                });
            }
        }
    } catch (error) {
        console.error('Error in streaming getLeadModulesSystemRows:', error);
        yield [['Export Error']];
    }
};

export default getLeadModulesSystemRows;
