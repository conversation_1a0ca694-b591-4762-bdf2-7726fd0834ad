import type { TFunction } from 'i18next';
import {
    type Application,
    type ApplicationCampaignValues,
    type ApplicationResubmittedToBankAuditTrail,
    type ApplicationResubmittedToSystemAuditTrail,
    type ApplicationSubmittedToBankAuditTrail,
    type ApplicationSubmittedToSystemAuditTrail,
    type Bank,
    type BookingAmendedAuditTrail,
    type BookingSubmittedAuditTrail,
    type CapValues,
    type Company,
    type ConfiguratorApplication,
    type ConfiguratorModule,
    type Dealer,
    type EventApplication,
    type EventApplicationModule,
    type ExportLead,
    type FinderApplication,
    type FinderApplicationPrivateModule,
    type FinderApplicationPublicModule,
    type GiftVoucher,
    type GiftVoucherModule,
    type Insurer,
    type LeadModule,
    type LeadStageOption,
    type LocalMake,
    type LocalModel,
    type MobilityApplication,
    type MobilityModule,
    type PromoCode,
    type StandardApplication,
    type StandardApplicationModule,
    type StockInventory,
    type User,
    type Vehicle,
    ModuleType,
} from '../../../../database/documents';
import type { LocalCustomerAggregatedFields } from '../../../../database/helpers/customers/types';
import type { RowValue } from '../../../../export/type';

export type SystemKycSupportedApplicationModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | MobilityModule
    | GiftVoucherModule;

export type SystemSupportedApplicationModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | MobilityModule;

export type KycSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type VehicleSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type DealerSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type BankSupportedApplication =
    | StandardApplication
    | FinderApplication
    | EventApplication
    | ConfiguratorApplication;

export enum CheckConsent {
    Yes = 'Yes',
    No = 'No',
    NotExist = 'NotExist',
}

export type PreparedSystemLeadData = ExportLead & {
    support: {
        company: Company;
        customer: LocalCustomerAggregatedFields;
        module: LeadModule;
        submittedAuditTrails?: Array<
            | ApplicationSubmittedToSystemAuditTrail
            | ApplicationResubmittedToSystemAuditTrail
            | ApplicationSubmittedToBankAuditTrail
            | ApplicationResubmittedToBankAuditTrail
            | BookingSubmittedAuditTrail
            | BookingAmendedAuditTrail
        >;
        relatedApplications?: Application[];
        bank?: Bank;
        assignee?: User;
        creatorName?: string;
        editorName?: string;
        leadStage: LeadStageOption;
        consentData: {
            personal: CheckConsent;
            email: CheckConsent;
            phone: CheckConsent;
            mail: CheckConsent;
            sms: CheckConsent;
            fax: CheckConsent;
        };
        vehicle?: Vehicle;
        model?: LocalModel;
        subModel?: LocalModel;
        make?: LocalMake;
        stock?: StockInventory;
        insurer?: Insurer;
        dealer?: Dealer;
        promoCode?: PromoCode;
        giftVoucher?: GiftVoucher;
        capValues?: CapValues;
        campaignValues?: ApplicationCampaignValues;
    };
};

export type GetLeadRowsSupportingData = {
    t: TFunction;
    currencyCode?: string;
    timeZone?: string;
    stage?: LeadStageOption;
    language?: string;
    showCreatedDateWithTime?: boolean;
};

export const LEAD_EXCEL_MODULE_TYPES: ReadonlyArray<ModuleType> = [
    ModuleType.StandardApplicationModule,
    ModuleType.FinderApplicationPublicModule,
    ModuleType.FinderApplicationPrivateModule,
    ModuleType.ConfiguratorModule,
    ModuleType.EventApplicationModule,
    ModuleType.GiftVoucherModule,
];

export type SystemLeadColumnSetting = {
    header: string;
    getCellValue: (
        data: PreparedSystemLeadData,
        support: {
            t: TFunction;
            timeZone?: string;
            currencyCode?: string;
            routerFirstLanguage?: string;
        }
    ) => RowValue;
};
