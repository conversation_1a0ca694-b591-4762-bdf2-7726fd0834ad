import { TFunction } from 'i18next';
import { keyBy, uniqBy } from 'lodash/fp';
import { SystemLeadColumnSetting } from '../types';

export enum CapSettingKey {
    CapBPId = 'capBPId',
    CapLeadId = 'capLeadId',
}

export type CapColumnSetting = SystemLeadColumnSetting & { key: CapSettingKey };
type CapKey = { [key in CapSettingKey]: CapColumnSetting };

const getCapSetting = (t: TFunction, isHasCapBPId: boolean = true, isHasCapLeadId: boolean = true) => {
    const allSettings: CapColumnSetting[] = [
        isHasCapBPId && {
            key: CapSettingKey.CapBPId,
            header: t('applicationExcel:cap.businessPartnerId'),
            getCellValue: preparedData => preparedData.support.capValues?.businessPartnerId,
        },
        isHasCapLeadId && {
            key: CapSettingKey.CapLeadId,
            header: t('applicationExcel:cap.leadId'),
            getCellValue: preparedData => preparedData.support.capValues?.leadId,
        },
    ];

    return [keyBy('key', allSettings) as CapKey, allSettings] as const;
};

export const getCapColumns = (t: TFunction, isHasCapBPId: boolean = true, isHasCapLeadId: boolean = true) => {
    const [, capColumns] = getCapSetting(t, isHasCapBPId, isHasCapLeadId);

    return uniqBy('key', capColumns);
};
