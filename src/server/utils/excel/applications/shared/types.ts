import { TFunction } from 'i18next';
import {
    type Application,
    type ApplicationApprovedAuditTrail,
    type ApplicationCampaignValues,
    type ApplicationDeclinedAuditTrail,
    type ApplicationJourney,
    type ApplicationResubmittedToBankAuditTrail,
    type ApplicationResubmittedToSystemAuditTrail,
    type ApplicationSubmittedToBankAuditTrail,
    type ApplicationSubmittedToSystemAuditTrail,
    type Bank,
    type BookingAmendedAuditTrail,
    type BookingSubmittedAuditTrail,
    type CapValues,
    type Company,
    type ConfiguratorApplication,
    type ConfiguratorModule,
    type Dealer,
    type EventApplication,
    type EventApplicationModule,
    type FinanceProduct,
    type FinderApplication,
    type FinderApplicationPrivateModule,
    type FinderApplicationPublicModule,
    type GiftVoucher,
    type GiftVoucherModule,
    type Insurer,
    type LaunchPadModule,
    type LocalInsuranceProduct,
    type LocalMake,
    type LocalModel,
    type MobilityApplication,
    type MobilityModule,
    type PromoCode,
    type StandardApplication,
    type StandardApplicationModule,
    type StockInventory,
    type User,
    type Vehicle,
    ApplicationStage,
    ModuleType,
    SalesOfferModule,
} from '../../../../database/documents';
import type { LocalCustomerAggregatedFields } from '../../../../database/helpers/customers/types';
import type { RowValue } from '../../../../export/type';
import type { GetApplicationStageReturnType } from '../../../application';

export type SystemKycSupportedApplicationModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | MobilityModule
    | GiftVoucherModule
    | LaunchPadModule;

export type SystemSupportedApplicationModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | MobilityModule
    | LaunchPadModule
    | SalesOfferModule;

export type KycSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type VehicleSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type DealerSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type BankSupportedApplication =
    | StandardApplication
    | FinderApplication
    | EventApplication
    | ConfiguratorApplication;

export enum CheckConsent {
    Yes = 'Yes',
    No = 'No',
    NotExist = 'NotExist',
}

export type PreparedSystemApplicationData = Application & {
    support: {
        company: Company;
        customer: LocalCustomerAggregatedFields;
        module: SystemSupportedApplicationModule;
        journey?: ApplicationJourney;
        submittedAuditTrails?: Array<
            | ApplicationSubmittedToSystemAuditTrail
            | ApplicationResubmittedToSystemAuditTrail
            | ApplicationSubmittedToBankAuditTrail
            | ApplicationResubmittedToBankAuditTrail
            | BookingSubmittedAuditTrail
            | BookingAmendedAuditTrail
        >;
        applicationApproval?: (ApplicationApprovedAuditTrail | ApplicationDeclinedAuditTrail)[];
        referenceApplications: Application[];
        bank?: Bank;
        financeProduct?: FinanceProduct;
        assignee?: User;
        creatorName?: string;
        editorName?: string;
        appStage?: GetApplicationStageReturnType;
        consentData: {
            personal: CheckConsent;
            email: CheckConsent;
            phone: CheckConsent;
            mail: CheckConsent;
            sms: CheckConsent;
            fax: CheckConsent;
        };
        vehicle?: Vehicle;
        model?: LocalModel;
        subModel?: LocalModel;
        make?: LocalMake;
        stock?: StockInventory;
        insurer?: Insurer;
        dealer?: Dealer;
        promoCode?: PromoCode;
        giftVoucher?: GiftVoucher;
        insuranceProduct: LocalInsuranceProduct;
        capValues?: CapValues;
        campaignValues?: ApplicationCampaignValues;
    };
};

export type SystemApplicationColumnSetting = {
    header: string;
    getCellValue: (
        data: PreparedSystemApplicationData,
        support: {
            t: TFunction;
            timeZone?: string;
            currencyCode?: string;
            routerFirstLanguage?: string;
        }
    ) => RowValue;
};

export type GetApplicationRowsSupportingData = {
    t: TFunction;
    currencyCode?: string;
    timeZone?: string;
    stage?: ApplicationStage;
    hasAppointment: boolean;
    hasAffinBankAutoFinanceCentre?: boolean;
    language?: string;
};

export const APPLICATION_EXCEL_MODULE_TYPES: ReadonlyArray<ModuleType> = [
    ModuleType.StandardApplicationModule,
    ModuleType.FinderApplicationPublicModule,
    ModuleType.FinderApplicationPrivateModule,
    ModuleType.ConfiguratorModule,
    ModuleType.EventApplicationModule,
    ModuleType.GiftVoucherModule,
];
