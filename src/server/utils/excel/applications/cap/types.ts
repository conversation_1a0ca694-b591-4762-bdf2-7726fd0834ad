import { WithId } from 'mongodb';
import {
    ConfiguratorModule,
    ConfiguratorApplication,
    EventApplication,
    EventApplicationModule,
    FinderApplication,
    FinderApplicationPublicModule,
    FinderApplicationPrivateModule,
    User,
    Dealer,
    ConsentsAndDeclarations,
    ApplicationJourney,
    Vehicle,
    DealershipSetting,
    StandardApplicationModule,
    MobilityModule,
    StandardApplication,
} from '../../../../database';

import type { LocalCustomerAggregatedFields } from '../../../../database/helpers/customers/types';

export type CapSupportedApplication =
    | ConfiguratorApplication
    | EventApplication
    | FinderApplication
    | StandardApplication;

export type CapSupportedModule =
    | EventApplicationModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | ConfiguratorModule
    | StandardApplicationModule;

export type AgreementSupportedModule = CapSupportedModule | StandardApplicationModule | MobilityModule;

// Make the same value with previous one in EvenExcelPurpose
export enum FormatCapPurpose {
    BP = 'BP_UPLOAD',
    BP_LEAD = 'BP_LEAD_UPLOAD',
}

export type AdditionalCapData = {
    appId: string;
    customer: LocalCustomerAggregatedFields;
    assignee: WithId<User> | null;
    preferredSellingDealer: WithId<Dealer> | null;
    preferredServiceDealer: WithId<Dealer> | null;
    consentList: ConsentsAndDeclarations[];
    applicationJourney: ApplicationJourney | null;
    vehicle: WithId<Vehicle> | null;

    // For event
    defaultSalesPerson?: DealershipSetting;

    // For bp lead upload
    primaryInterest?: string;
    medium?: string;
    campaignId?: string;
    leadSource?: string;

    // As reference for selecting excel default values config by country
    companyCountryCode: string | null;
};

export type CapPreparedData = CapSupportedApplication & {
    cap: AdditionalCapData;
};
