import { isEmpty, isNil } from 'lodash/fp';
import { Dictionary } from 'ts-essentials';
import { getCountryEventExportConfig } from '../../../../core/config';
import { ApplicationKind, ApplicationStage, Event, Lead, TradeInVehicle } from '../../../../database';
import { getLocalCustomerAggregatedFields } from '../../../../database/helpers/customers/shared';
import { RowValue } from '../../../../export/type';
import {
    getCapCustomerBirthday,
    getCapTitleCode,
    getMaritalStatusCode,
} from '../../../../integrations/cap/utils/getCustomerDetails';
import {
    getVehicleEngineTypeCode,
    getVehicleEquipmentLineCode,
} from '../../../../integrations/cap/utils/getVehicleDetails';
import createLoaders from '../../../../loaders';
import { DEFAULT_STAGES, getApplicationAssigneeId, getApplicationStage } from '../../../application';
import { companyByIdFromDealer, modelMakeByVariantIdFromApplications } from '../system/utils';
import { BpLeadUploadRow, BpUploadRow, CAP_HEADER_BP, CAP_HEADER_BP_LEAD } from './header';
import {
    AdditionalCapData,
    CapPreparedData,
    CapSupportedApplication,
    CapSupportedModule,
    FormatCapPurpose,
} from './types';
import {
    getCapConsentStatus,
    getCapPrimaryInterest,
    getCapPurchaseMonthAndYear,
    getCapSexCode,
    getCapValuesFromEvent,
    getCapSupportingData,
    getCountryCode,
    getCountryCodeWithFallback,
    getRegionCode,
    getVehicleCondition,
    getVehicleVariantID,
    getCompanyName2,
} from './utils';

type CapSupportingData = {
    modules: CapSupportedModule[];
    stage?: ApplicationStage;
};

const getSpecificCapDataFromApplication = (
    item: CapSupportedApplication,
    lead: Lead,
    { stage, eventById }: CapSupportingData & { eventById: Dictionary<Event> }
): Partial<Omit<AdditionalCapData, 'appId'>> & { appId: string } => {
    switch (item.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
            if (!stage) {
                throw new Error("Stage is required for configurator and finder application's c@p export");
            }

            return {
                appId: getApplicationStage(item, lead, stage)?.value?.identifier ?? '',
            };

        case ApplicationKind.Event: {
            const event = eventById[item.eventId?.toHexString() ?? ''];

            if (!event) {
                throw new Error("Event is required for event application's c@p export");
            }

            if (!stage) {
                throw new Error("Stage is required for event application's c@p export");
            }

            return {
                appId: getApplicationStage(item, lead, stage)?.value?.identifier ?? '',
                defaultSalesPerson: event?.publicSalesPerson,
                ...getCapValuesFromEvent(item.campaignValues, event),
            };
        }

        default:
            throw new Error('Unsupported application kind for c@p export');
    }
};

export const prepareCapDataFromApplications = async (
    purpose: FormatCapPurpose,
    applications: CapSupportedApplication[],
    { modules, stage }: CapSupportingData,
    loaders = createLoaders()
): Promise<CapPreparedData[]> => {
    const {
        customerById,
        assigneeById,
        dealerById,
        vehicleById,
        consentList,
        journeyByApplicationSuiteId,
        eventById,
        leadById,
    } = await getCapSupportingData(applications, modules, loaders);

    const getPreparedDataFromPurpose = await (async () => {
        switch (purpose) {
            case FormatCapPurpose.BP_LEAD: {
                const vehicleInfoByVariantId = await modelMakeByVariantIdFromApplications(applications, loaders);

                return (item: CapSupportedApplication): { primaryInterest?: string } => ({
                    primaryInterest: getCapPrimaryInterest(
                        item.vehicleId?.toHexString()
                            ? (vehicleInfoByVariantId[item.vehicleId.toHexString()]?.model ?? null)
                            : null,
                        item.vehicleId?.toHexString() ? (vehicleById[item.vehicleId.toHexString()] ?? null) : null
                    ),
                });
            }

            default:
                return () => ({});
        }
    })();

    return Promise.all(
        applications.map(async item => {
            const lead = leadById[item.leadId.toHexString()];
            const assigneeId = getApplicationAssigneeId(item, lead, stage ?? DEFAULT_STAGES);
            const companyByDealer = item.dealerId
                ? await companyByIdFromDealer(dealerById[item.dealerId.toHexString()], loaders)
                : null;

            return {
                ...item,
                cap: {
                    ...getSpecificCapDataFromApplication(item, lead, {
                        modules,
                        stage,
                        eventById,
                    }),
                    ...getPreparedDataFromPurpose?.(item),
                    customer: customerById[item.applicantId.toHexString()]
                        ? getLocalCustomerAggregatedFields(customerById[item.applicantId.toHexString()])
                        : null,
                    assignee: assigneeId ? assigneeById[assigneeId.toHexString()] : null,
                    preferredSellingDealer: item.dealerId ? dealerById[item.dealerId.toHexString()] : null,
                    preferredServiceDealer: item.dealerId ? dealerById[item.dealerId.toHexString()] : null,
                    companyCountryCode: companyByDealer ? companyByDealer.countryCode : null,
                    consentList,
                    applicationJourney: journeyByApplicationSuiteId[item._versioning.suiteId.toHexString()] ?? null,
                    vehicle: item.vehicleId ? (vehicleById[item.vehicleId.toHexString()] ?? null) : null,
                },
            };
        })
    );
};

const getCustomerCurrentVehicle = (tradeInVehicle: TradeInVehicle[]) => {
    if (!tradeInVehicle.length) {
        return {
            make: null,
            model: null,
            modelYear: null,
            ownership: null,
            equipmentLine: null,
            purchaseYear: null,
            engineType: null,
        };
    }

    return tradeInVehicle[0];
};

const getCapRowsForBp = (preparedData: CapPreparedData[], tenant: string, timeZone?: string): RowValue[][] => {
    const rows: RowValue[][] = [[...CAP_HEADER_BP]];

    for (const item of preparedData) {
        const {
            appId,
            customer,
            assignee,
            applicationJourney,
            consentList,
            preferredSellingDealer,
            preferredServiceDealer,
            companyCountryCode,
        } = item.cap;

        const {
            EVENT_EXPORT_IDTYPE,
            EVENT_EXPORT_LAST_NAME,
            EVENT_EXPORT_LANGUAGE_CORR,
            EVENT_EXPORT_ADDRESS_TYPE,
            EVENT_EXPORT_MOBILE_TYPE,
            EVENT_EXPORT_MAIL_TYPE,
            EVENT_EXPORT_CUSTOMER_STATUS_P,
        } = getCountryEventExportConfig(tenant, companyCountryCode);

        const [DATA_PROCESSING_CONSENT, MAIL_CONSENT, PHONE_CONSENT, EMAIL_CONSENT, FAX_CONSENT] = getCapConsentStatus(
            consentList,
            applicationJourney,
            FormatCapPurpose.BP,
            companyCountryCode
        );

        const customerTitle = !isEmpty(customer.nonBinaryTitle) ? customer.nonBinaryTitle : customer.title;
        const customerGender = !isEmpty(customer.nonBinaryGender) ? customer.nonBinaryGender : customer.gender;

        const columnByHeader: BpUploadRow = {
            ID_NUMBER: appId,
            TK_IDTYPE: EVENT_EXPORT_IDTYPE,
            TK_TITLE: getCapTitleCode(customerTitle),
            FIRST_NAME: customer.firstName ?? '',
            LAST_NAME: customer.lastNameFront ?? customer.lastName ?? EVENT_EXPORT_LAST_NAME,
            TK_LANGUAGE_CORR: EVENT_EXPORT_LANGUAGE_CORR,
            MIDDLE_NAME: customer.firstNameJapan ?? '',
            LAST_NAME_2: customer.lastNameJapan ?? '',
            TK_ADDRESS_TYPE: EVENT_EXPORT_ADDRESS_TYPE,
            STREET: customer.address ?? '',
            HOUSE_NUMBER: customer.unitNumber ?? '',
            STREET_SUPPL_1: customer.district ?? '',
            CITY: customer.city ?? '',
            POSTAL_CODE: customer.postalCode ?? '',
            TK_REGION: getRegionCode(customer.region) ?? '',
            TK_COUNTRY: getCountryCodeWithFallback(customer.country, companyCountryCode),
            MOBILE_PHONE: customer.phone?.value ?? '',
            TK_MOBILE_TYPE: EVENT_EXPORT_MOBILE_TYPE,
            EMAIL: customer.email ?? '',
            TK_MAIL_TYPE: EVENT_EXPORT_MAIL_TYPE,
            RESP_SALES_DEALER: preferredSellingDealer?.integrationDetails?.dealerCode,
            RESP_SERVICE_DEALER: preferredServiceDealer?.integrationDetails?.dealerCode,
            RESP_SALES_PERSON: assignee?.alias,
            RESP_SERVICE_PERSON: assignee?.alias,
            BIRTHDATE: getCapCustomerBirthday(customer?.birthday, timeZone),
            TK_SEX: getCapSexCode(customerTitle ?? customerGender),
            TK_MARITIAL_STATUS: !isNil(customer.maritalStatus) ? getMaritalStatusCode(customer.maritalStatus) : '',
            DATA_PROCESSING_CONSENT,
            MAIL_CONSENT,
            PHONE_CONSENT,
            EMAIL_CONSENT,
            FAX_CONSENT,
            CUSTOMER_STATUS_P: EVENT_EXPORT_CUSTOMER_STATUS_P,
        };

        rows.push(CAP_HEADER_BP.map(header => columnByHeader[header] || null));
    }

    return rows;
};

const getCapRowsForBpLead = (preparedData: CapPreparedData[], tenant: string, timeZone?: string) => {
    const rows: RowValue[][] = [[...CAP_HEADER_BP_LEAD]];

    for (const item of preparedData) {
        const {
            appId,
            customer,
            assignee,
            applicationJourney,
            consentList,
            preferredSellingDealer,
            preferredServiceDealer,
            primaryInterest,
            medium,
            campaignId,
            leadSource,
            vehicle,
            companyCountryCode,
        } = item.cap;

        const [month, year] = getCapPurchaseMonthAndYear(item._versioning.createdAt, companyCountryCode);

        const { make, model, modelYear, ownership, equipmentLine, purchaseYear, engineType } =
            getCustomerCurrentVehicle(item.tradeInVehicle);

        const { EVENT_EXPORT_IMP, EVENT_EXPORT_SOURCE, EVENT_EXPORT_TYPE, EVENT_EXPORT_LAST_NAME } =
            getCountryEventExportConfig(tenant, companyCountryCode);

        const [YKIS_DATAPROC, YBLOCK_MAIL, YBLOCK_PHONE, YBLOCK_EMAIL, YBLOCK_FAX] = getCapConsentStatus(
            consentList,
            applicationJourney,
            FormatCapPurpose.BP_LEAD,
            companyCountryCode
        );

        const columnByHeader: BpLeadUploadRow = {
            Source: EVENT_EXPORT_SOURCE,
            ID: appId,
            TITLE: getCapTitleCode(customer.title),
            BUSINESS_TITLE: customer.businessTitle ?? '',
            FIRST_NAME: customer.firstName ?? '',
            LAST_NAME: customer.lastNameFront ?? customer.lastName ?? EVENT_EXPORT_LAST_NAME,
            NAME_LST2: customer.lastNameJapan ?? '',
            NAMEMIDDLE: customer.firstNameJapan ?? '',
            COMPANY_1: customer.companyName ?? '',
            COMPANY_2: getCompanyName2(customer),
            STREET: customer.address ?? '',
            STR_SUPPL1: customer.district ?? '',
            HOUSE_NUM1: customer.unitNumber ?? '',
            POST_CODE1: customer.postalCode ?? '',
            CITY1: customer.city ?? '',
            REGION: customer.region ?? '',
            COUNTRY: getCountryCode(customer.country) ?? '',
            SEX: getCapSexCode(customer.title ?? customer.gender),
            BIRTHDATE: getCapCustomerBirthday(customer?.birthday, timeZone),
            TELEPHONE_M: customer.phone?.value ?? '',
            SMTP_ADDR1: customer.email,
            YPREF_SAL_DEALER: preferredSellingDealer?.integrationDetails?.dealerCode,
            YPREF_SRV_DEALER: preferredServiceDealer?.integrationDetails?.dealerCode,
            MEDIUM: medium ?? 'N/A',
            MAKE1: make ?? '',
            MODEL1: model ?? '',
            MODEL_YEAR1: modelYear ?? '',
            yveh_own_vehicle: ownership ? 'X' : '',
            yveh_equipment_line: equipmentLine ? getVehicleEquipmentLineCode(equipmentLine) : '',
            yveh_engine_type: engineType ? getVehicleEngineTypeCode(engineType) : '',
            CAR_PUR_MM: month,
            CAR_PUR_JJJJ: year,
            MODEL_INTEREST1: getVehicleVariantID(vehicle),
            CAMPAIGN_ID: campaignId ?? '',
            LEAD_SOURCE: leadSource ?? 'N/A',
            IMP: EVENT_EXPORT_IMP,
            YKIS_DATAPROC,
            YBLOCK_MAIL,
            YBLOCK_PHONE,
            YBLOCK_EMAIL,
            YBLOCK_FAX,
            TYPE: EVENT_EXPORT_TYPE,
            IDNUMBER: appId,
            YRSALESP: assignee?.alias,
            YRSERVP: assignee?.alias,
            PRIMARY_INTEREST: primaryInterest ?? '',
            TRAN_ID: applicationJourney.deposit?.transactionId ?? '',
            ...getVehicleCondition(vehicle),
        };

        rows.push(CAP_HEADER_BP_LEAD.map(header => columnByHeader[header] || null));
    }

    return rows;
};

export const getApplicationFormatCapRows = (
    purpose: FormatCapPurpose,
    preparedData: CapPreparedData[],
    tenant: string,
    // This timezone will be used to correct DOB value, retrieved from company
    timeZone?: string
) => {
    switch (purpose) {
        case FormatCapPurpose.BP:
            return getCapRowsForBp(preparedData, tenant, timeZone);

        case FormatCapPurpose.BP_LEAD:
            return getCapRowsForBpLead(preparedData, tenant, timeZone);

        default:
            throw new Error('Unknown c@p export purpose: ', purpose);
    }
};

export default getApplicationFormatCapRows;
