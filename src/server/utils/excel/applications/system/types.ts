import { TFunction } from 'i18next';
import {
    Application,
    ApplicationApprovedAuditTrail,
    ApplicationCampaignValues,
    ApplicationDeclinedAuditTrail,
    ApplicationJourney,
    ApplicationResubmittedToBankAuditTrail,
    ApplicationResubmittedToSystemAuditTrail,
    ApplicationSubmittedToBankAuditTrail,
    ApplicationSubmittedToSystemAuditTrail,
    Bank,
    BookingAmendedAuditTrail,
    BookingSubmittedAuditTrail,
    CapValues,
    Company,
    ConfiguratorApplication,
    ConfiguratorModule,
    Dealer,
    EventApplication,
    EventApplicationModule,
    FinanceProduct,
    FinderApplication,
    FinderApplicationPrivateModule,
    FinderApplicationPublicModule,
    GiftVoucher,
    GiftVoucherModule,
    Insurer,
    LaunchPadModule,
    LocalInsuranceProduct,
    LocalMake,
    LocalModel,
    MobilityApplication,
    MobilityModule,
    PromoCode,
    StandardApplication,
    StandardApplicationModule,
    StockInventory,
    User,
    Vehicle,
    SalesOfferModule,
} from '../../../../database/documents';
import type { LocalCustomerAggregatedFields } from '../../../../database/helpers/customers/types';
import { RowValue } from '../../../../export/type';
import { GetApplicationStageReturnType } from '../../../application';
import { CheckConsent } from '../../enums';

export type SystemKycSupportedApplicationModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | MobilityModule
    | GiftVoucherModule
    | LaunchPadModule
    | SalesOfferModule;

export type SystemSupportedApplicationModule =
    | StandardApplicationModule
    | EventApplicationModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule
    | MobilityModule
    | LaunchPadModule
    | SalesOfferModule;

export type KycSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type VehicleSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type DealerSupportedApplication =
    | StandardApplication
    | FinderApplication
    | MobilityApplication
    | EventApplication
    | ConfiguratorApplication;

export type BankSupportedApplication =
    | StandardApplication
    | FinderApplication
    | EventApplication
    | ConfiguratorApplication;

export type PreparedSystemApplicationData = Application & {
    support: {
        company: Company;
        customer: LocalCustomerAggregatedFields;
        module: SystemSupportedApplicationModule;
        journey?: ApplicationJourney;
        submittedAuditTrails?: Array<
            | ApplicationSubmittedToSystemAuditTrail
            | ApplicationResubmittedToSystemAuditTrail
            | ApplicationSubmittedToBankAuditTrail
            | ApplicationResubmittedToBankAuditTrail
            | BookingSubmittedAuditTrail
            | BookingAmendedAuditTrail
        >;
        applicationApproval?: (ApplicationApprovedAuditTrail | ApplicationDeclinedAuditTrail)[];
        referenceApplications: Application[];
        bank?: Bank;
        financeProduct?: FinanceProduct;
        assignee?: User;
        creatorName?: string;
        editorName?: string;
        appStage?: GetApplicationStageReturnType;
        consentData: {
            personal: CheckConsent;
            email: CheckConsent;
            phone: CheckConsent;
            mail: CheckConsent;
            sms: CheckConsent;
            fax: CheckConsent;
        };
        vehicle?: Vehicle;
        model?: LocalModel;
        subModel?: LocalModel;
        make?: LocalMake;
        stock?: StockInventory;
        insurer?: Insurer;
        dealer?: Dealer;
        promoCode?: PromoCode;
        giftVoucher?: GiftVoucher;
        insuranceProduct: LocalInsuranceProduct;
        capValues?: CapValues;
        campaignValues?: ApplicationCampaignValues;
    };
};

export type SystemApplicationColumnSetting = {
    header: string;
    getCellValue: (
        data: PreparedSystemApplicationData,
        support: {
            t: TFunction;
            timeZone?: string;
            currencyCode?: string;
            routerFirstLanguage?: string;
        }
    ) => RowValue;
};
