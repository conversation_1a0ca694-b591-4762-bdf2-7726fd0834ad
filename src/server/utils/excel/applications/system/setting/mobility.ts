import type { TFunction } from 'i18next';
import { keyBy } from 'lodash/fp';
import { ApplicationKind } from '../../../../../database';
import { getMobilityLocationName } from '../../../../../database/helpers/applications';
import { getFormattedDate, getTimeZoneOffset } from '../../../../date';
import type { SystemApplicationColumnSetting } from '../../shared/types';

export enum SystemMobilitySettingKey {
    StartDate = 'startDate',
    EndDate = 'endDate',
    Location = 'location',
}

type SystemMobilityColumnSetting = SystemApplicationColumnSetting & {
    key: SystemMobilitySettingKey;
};
type SystemMobilityKey = { [key in SystemMobilitySettingKey]: SystemMobilityColumnSetting };

type GetSystemMobilityHeaderProps = {
    timeZone?: string;
    t: TFunction;
};

const getSystemMobilitySetting = (header: GetSystemMobilityHeaderProps) => {
    const { t } = header;

    const allSettings: SystemMobilityColumnSetting[] = [
        {
            key: SystemMobilitySettingKey.StartDate,
            header: header.timeZone
                ? t('applicationExcel:main.mobilityStartDateWithTimeZone', {
                      timeZone: getTimeZoneOffset(header.t, header.timeZone),
                  })
                : t('applicationExcel:main.mobilityStartDate'),
            getCellValue: (preparedData, { t }) =>
                preparedData.kind === ApplicationKind.Mobility
                    ? getFormattedDate(
                          t,
                          preparedData.mobilityBookingDetails.period.start,
                          preparedData.support.company?.timeZone
                      )
                    : '',
        },
        {
            key: SystemMobilitySettingKey.EndDate,
            header: header.timeZone
                ? t('applicationExcel:main.mobilityEndDateWithTimeZone', {
                      timeZone: getTimeZoneOffset(header.t, header.timeZone),
                  })
                : t('applicationExcel:main.mobilityEndDate'),
            getCellValue: (preparedData, { t }) =>
                preparedData.kind === ApplicationKind.Mobility
                    ? getFormattedDate(
                          t,
                          preparedData.mobilityBookingDetails.period.end,
                          preparedData.support.company?.timeZone
                      )
                    : '',
        },
        {
            key: SystemMobilitySettingKey.Location,
            header: t('applicationExcel:main.location'),
            getCellValue: (preparedData, { t }) =>
                preparedData.kind === ApplicationKind.Mobility
                    ? getMobilityLocationName(t, preparedData.mobilityBookingDetails.location)
                    : '',
        },
    ];

    return [keyBy('key', allSettings) as SystemMobilityKey, allSettings] as const;
};

export default getSystemMobilitySetting;
