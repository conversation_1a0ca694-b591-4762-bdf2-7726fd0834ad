import { TFunction } from 'i18next';
import { ObjectId } from 'mongodb';
import {
    AdvancedVersioning,
    Company,
    GiftVoucher,
    GiftVoucherJourney,
    GiftVoucherStatus,
    LocalMake,
    LocalModel,
    StockInventory,
    Vehicle,
} from '../../../database/documents';
import type { LocalCustomerAggregatedFields } from '../../../database/helpers/customers/types';
import { RowValue } from '../../../export/type';
import { CheckConsent } from '../enums';

// Using minimal attributes
export type GiftVoucherData = {
    giftCode: string;
    status: GiftVoucherStatus;
    _versioning: AdvancedVersioning;
    referenceApplicationsIdentifier?: string;
    vehicle?: Omit<Vehicle, 'FinderVehicle'>;
    model?: LocalModel;
    subModel?: LocalModel;
    make?: LocalMake;
    agreementsModuleId?: ObjectId;
    purchasedDate?: Date;
    purchaser?: string;
    giftee?: string;
    editorName?: string;
    support: {
        company: Company;
        journey?: GiftVoucherJourney;
        customer: LocalCustomerAggregatedFields;
        consentData: {
            personal: CheckConsent;
            email: CheckConsent;
            phone: CheckConsent;
            mail: CheckConsent;
            sms: CheckConsent;
            fax: CheckConsent;
        };
        vehicle?: Vehicle;
        model?: LocalModel;
        subModel?: LocalModel;
        make?: LocalMake;
        stock?: StockInventory;
        giftVoucher: Pick<GiftVoucher, 'value' | 'numberOfBookingReferenceDays'>;
    };
};

export enum SystemGiftVoucherVehicleSettingKey {
    Make = 'make',
    Model = 'model',
    SubModel = 'subModel',
    VariantName = 'variantName',
    VariantId = 'variantId',
    Price = 'price',
    ChassisNo = 'chassisNo',
    EngineNo = 'engineNo',
}

export type SystemGiftVoucherColumnSetting = {
    header: string;
    getCellValue: (
        data: GiftVoucherData,
        support?: {
            t: TFunction;
            timeZone?: string;
            currencyCode?: string;
        }
    ) => RowValue;
};

export type SupportData = {
    t: TFunction;
    currencyCode?: string;
    timeZone?: string;
};
