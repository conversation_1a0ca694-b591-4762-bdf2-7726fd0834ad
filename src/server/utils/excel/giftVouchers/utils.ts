import { i18n } from 'i18next';
import { keyBy } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { GetTranslations } from '../../../core/translations';
import {
    AuthorKind,
    GiftVoucherModule,
    LocalMake,
    LocalModel,
    MobilityApplication,
    Vehicle,
    VehicleKind,
    AdvancedVersioning,
    ConsentsAndDeclarations,
    ConsentsAndDeclarationsType,
    DataField,
    GiftVoucherJourney,
    ConsentsAndDeclarationsPurpose,
    MobilityStockInventory,
    getKYCPresetsForCustomerModule,
} from '../../../database';
import { getCustomerFullName } from '../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import { Loaders } from '../../../loaders';
import { getGiftVoucherAgreements } from '../../../schema/resolvers/types/applications/shared';
import { getPurchaserFullName, getGifteeFullName } from '../../../schema/resolvers/types/giftVouchers';
import ensureManyFromLoaders from '../../ensureManyFromLoaders';
import { CheckConsent } from '../enums';
import type { GiftVoucherData } from './types';
import type { GiftVoucherResults } from './types/GiftVoucherResults';

export const getAuthorName = async (versioning: AdvancedVersioning, loaders: Loaders, i18n: i18n) => {
    i18n.loadNamespaces(['common']);
    const { t } = i18n;
    switch (versioning.updatedBy?.kind) {
        case AuthorKind.User: {
            const userById = await loaders.userById.load(versioning.updatedBy.id);

            return userById.displayName ?? '';
        }

        case AuthorKind.Customer: {
            const customerById = await loaders.customerById.load(versioning.updatedBy.id);
            const customerByLatestSuiteId = await loaders.customerByLatestSuiteId.load(versioning.suiteId);

            const customerModule = await loaders.moduleById.load(customerById.moduleId);
            const company = await loaders.companyById.load(customerModule.companyId);
            if (!customerById) {
                return '';
            }

            const latestCustomer =
                !customerById._versioning.isLatest && customerByLatestSuiteId ? customerByLatestSuiteId : customerById;

            const kycPresets = getKYCPresetsForCustomerModule(customerModule, latestCustomer._kind);

            return latestCustomer ? getCustomerFullName(t, latestCustomer, company, kycPresets) : '';
        }
        default:
            return 'System';
    }
};

const getGiftVoucherCustomer = async (purchaserId: ObjectId, loaders: Loaders) => {
    if (!purchaserId) {
        return null;
    }

    const customer = await loaders.customerById.load(purchaserId);

    return customer ? getLocalCustomerAggregatedFields(customer) : null;
};

const singleVehicleById = async (item: GiftVoucherResults, loaders: Loaders) => {
    const vehicleById = item.vehicleId ? await loaders.vehicleById.load(item.vehicleId) : null;

    return vehicleById;
};

const singleModelMakeByVariantId = async (loadedVariant: Vehicle, loaders: Loaders) => {
    const variant = loadedVariant._kind === VehicleKind.LocalVariant ? loadedVariant : null;

    if (!variant) {
        return {};
    }

    const model = (await loaders.vehicleById.load(variant.modelId)) as LocalModel;
    const make = (await loaders.vehicleById.load(model.makeId)) as LocalMake;
    const subModel = variant.submodelId ? ((await loaders.vehicleById.load(variant.submodelId)) as LocalModel) : null;

    return { model, subModel, make };
};

const consentDataByGiftVoucherJourney = async (
    giftVoucherJourney: GiftVoucherJourney,
    agreements,
    loaders: Loaders
) => {
    const allConsentIds = agreements.flatMap(agreement => agreement?._id) ?? [];

    if (!allConsentIds.length) {
        return {};
    }

    const allConsents = await loaders.consentById.loadMany(allConsentIds).then(ensureManyFromLoaders);

    const consentById = keyBy(item => item._id.toHexString(), allConsents);

    return {
        [giftVoucherJourney.giftVoucherSuiteId.toHexString()]: getConsentData(agreements, consentById),
    };
};

const getConsentData = (
    agreements: Array<{
        _id: ObjectId;
        isAgreed: boolean;
        isMandatory?: boolean;
        date: Date;
        purpose: ConsentsAndDeclarationsPurpose[];
        platformsAgreed?: {
            email: boolean;
            fax: boolean;
            mail: boolean;
            phone: boolean;
            sms: boolean;
        };
    }>,
    consentById: { [key: string]: ConsentsAndDeclarations }
) => {
    const consentData = {
        personal: CheckConsent.NotExist,
        email: CheckConsent.NotExist,
        fax: CheckConsent.NotExist,
        mail: CheckConsent.NotExist,
        phone: CheckConsent.NotExist,
        sms: CheckConsent.NotExist,
    };

    if (agreements?.length === 0) {
        return consentData;
    }
    const consentsLoaders = agreements.map(agreement => consentById[agreement._id.toHexString()]);

    // get the first checkbox and Data processing checkbox
    const firstCheckBox = consentsLoaders.find(
        consent =>
            consent._type === ConsentsAndDeclarationsType.Checkbox && consent.dataField === DataField.DataProcessing
    );
    if (firstCheckBox) {
        const journeyFirstCheckBox = agreements.find(agreement => agreement._id.equals(firstCheckBox._id));
        if (journeyFirstCheckBox) {
            consentData.personal = journeyFirstCheckBox.isAgreed ? CheckConsent.Yes : CheckConsent.No;
        }
    }

    // get the first marketing consent in the journey
    const firstMarketingCheckbox = consentsLoaders.find(
        consent => consent._type === ConsentsAndDeclarationsType.Marketing
    );
    if (firstMarketingCheckbox) {
        const journeyFirstMarketing = agreements.find(agreement => agreement._id.equals(firstMarketingCheckbox._id));

        if (journeyFirstMarketing?.platformsAgreed) {
            consentData.email = journeyFirstMarketing.platformsAgreed.email ? CheckConsent.Yes : CheckConsent.No;
            consentData.fax = journeyFirstMarketing.platformsAgreed.fax ? CheckConsent.Yes : CheckConsent.No;
            consentData.mail = journeyFirstMarketing.platformsAgreed.mail ? CheckConsent.Yes : CheckConsent.No;
            consentData.phone = journeyFirstMarketing.platformsAgreed.phone ? CheckConsent.Yes : CheckConsent.No;
            consentData.sms = journeyFirstMarketing.platformsAgreed.sms ? CheckConsent.Yes : CheckConsent.No;
        }
    }

    return consentData;
};

const getGiftVoucherConsentData = async (
    item: GiftVoucherResults,
    loaders: Loaders
): Promise<{
    personal: CheckConsent;
    email: CheckConsent;
    phone: CheckConsent;
    mail: CheckConsent;
    sms: CheckConsent;
    fax: CheckConsent;
}> => {
    const giftVoucherJourney = await loaders.giftVoucherJourneyBySuiteId.load(item._versioning.suiteId);
    const agreementsPromises = await getGiftVoucherAgreements(item, 'local', loaders);
    const agreements = await Promise.all(agreementsPromises);
    const consentDataByApplicationSuiteId = await consentDataByGiftVoucherJourney(
        giftVoucherJourney,
        agreements,
        loaders
    );

    return consentDataByApplicationSuiteId[item._versioning.suiteId.toHexString()];
};

export const getVehicleDetails = async (item: GiftVoucherResults, loaders: Loaders) => {
    const vehicleById = await singleVehicleById(item, loaders);
    const modelMakeByVariantId = await singleModelMakeByVariantId(vehicleById, loaders);
    const stockById = (await loaders.stockById.load(item.stockId)) as MobilityStockInventory;

    return {
        vehicle: vehicleById,
        model: modelMakeByVariantId.model,
        subModel: modelMakeByVariantId.subModel,
        make: modelMakeByVariantId.make,
        stock: stockById,
    };
};

export const prepareGiftVoucherData = async (
    results: GiftVoucherResults[],
    loaders: Loaders,
    i18n: i18n,
    getTranslations: GetTranslations
) => {
    const preparedGiftVoucherData = results.map(async (item): Promise<GiftVoucherData> => {
        const { giftCode, value, status, purchasedDate, _versioning, moduleId, numberOfBookingReferenceDays } = item;
        const mobilityApplication = (await loaders.applicationByGiftVoucherSuiteId.load(
            item._versioning.suiteId
        )) as MobilityApplication;
        const giftVoucherModule = (await loaders.moduleById.load(moduleId)) as GiftVoucherModule;

        const purchaser = await getPurchaserFullName(item, loaders, getTranslations);
        const giftee = await getGifteeFullName(item, loaders, getTranslations);
        const editorName = await getAuthorName(item._versioning, loaders, i18n);
        const { agreementsModuleId } = giftVoucherModule;
        const { vehicle, model, subModel, make, stock } = await getVehicleDetails(item, loaders);
        const consentData = await getGiftVoucherConsentData(item, loaders);
        const customer = await getGiftVoucherCustomer(item.purchaserId, loaders);
        const referenceApplicationsIdentifier = mobilityApplication?.mobilityStage?.identifier ?? null;
        const journey = await loaders.giftVoucherJourneyBySuiteId.load(item._versioning.suiteId);

        return {
            giftCode,
            status,
            agreementsModuleId,
            purchasedDate,
            _versioning,
            purchaser,
            giftee,
            editorName,
            referenceApplicationsIdentifier,
            support: {
                company: item.company,
                journey,
                customer,
                consentData,
                vehicle,
                model,
                subModel,
                make,
                stock,
                giftVoucher: {
                    value,
                    numberOfBookingReferenceDays,
                },
            },
        };
    });

    const giftVoucherData = await Promise.all(preparedGiftVoucherData);

    return giftVoucherData;
};
