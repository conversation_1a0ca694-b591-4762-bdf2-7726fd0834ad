/* eslint-disable no-await-in-loop */
import path from 'path';
import dayjs from 'dayjs';
import i18n, { i18n as I18n, TFunction, InitOptions, BackendModule, MultiReadCallback } from 'i18next';
import I18nextFSBackend from 'i18next-fs-backend';
import { get, isString, set } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import config from '../core/config';
import getRedisInstance from '../core/redis';
import { LanguageOrientation } from '../database/documents';
import getDatabaseContext from '../database/getDatabaseContext';
import { getDefaultLocale } from '../database/helpers/shared';
import mergeTranslations from './mergeTranslations';

export type CreateClientReturn = { i18n: I18n; initPromise: Promise<TFunction> };

const localesDirectory = path.resolve(process.cwd(), './public/locales/');

const getBaseOptions = (): InitOptions => ({
    /* global */
    defaultNS: 'common',
    ns: [],
    fallbackLng: [],
    load: 'currentOnly',

    /* interpolation */
    interpolation: {
        escapeValue: false,
        format: (value: any, format?: string): string => {
            if (format === 'uppercase' && isString(value)) {
                return value.toUpperCase();
            }

            if (value instanceof Date) {
                return dayjs(value).format(format);
            }

            return value;
        },
        formatSeparator: ',',
    },

    /* react */
    react: {
        useSuspense: true,
    },
});

const fsBackend = new I18nextFSBackend(null, {
    loadPath: path.join(localesDirectory, '/{{lng}}/{{ns}}.json'),
    addPath: path.join(localesDirectory, '/{{lng}}/{{ns}}.missing.json'),
});

const createDefaultInstance = async (language: string) =>
    i18n.createInstance({ ...getBaseOptions(), lng: language }).use(fsBackend);

const getOrientation = (orientation: LanguageOrientation) => {
    switch (orientation) {
        case LanguageOrientation.LeftToRight:
            return 'ltr';

        case LanguageOrientation.RightToLeft:
            return 'rtl';

        default:
            throw new Error('Unknown orientation');
    }
};

const createInstance = async (languageOrPackId?: string) => {
    const defaultLocale = await getDefaultLocale();
    const isValidPackId = !!languageOrPackId && ObjectId.isValid(languageOrPackId);
    const defaultLanguage = (!isValidPackId && languageOrPackId) || defaultLocale;
    const fsInstance = await createDefaultInstance(defaultLanguage);

    const { collections } = await getDatabaseContext();
    const languagePack = isValidPackId
        ? await collections.languagePacks.findOne({ _id: new ObjectId(languageOrPackId) })
        : null;

    // initialize fs instance ahead
    await fsInstance.init();

    const redis = getRedisInstance();

    const getBundle = (language: string, namespace: string) => {
        // get from fs instance
        const fsBundle = fsInstance.getResourceBundle(defaultLanguage, namespace);

        // the "core" namespace is auto generated
        if (namespace === 'core') {
            const bundle = languagePack
                ? { orientation: getOrientation(languagePack.orientation) }
                : { orientation: 'ltr' };

            return bundle;
        }

        // then form the language pack
        if (languagePack && languagePack._id.toHexString() === language) {
            return mergeTranslations(fsBundle, get([namespace], languagePack.translations));
        }

        return fsBundle;
    };

    const readAny: BackendModule['readMulti'] = async (languages, namespaces, callback) => {
        // load from fs instance as well
        await fsInstance.loadNamespaces(namespaces);

        let result: any = {};

        for (const language of languages) {
            const isLanguagePack = languagePack ? languagePack._id.toHexString() === language : false;
            const useCache = isLanguagePack || !__IS_DEV__;

            for (const namespace of namespaces) {
                let cacheKey = `translations:${language}:${namespace}`;

                if (isLanguagePack) {
                    cacheKey += `:${config.version}:${languagePack?._versioning.updatedAt.getTime()}`;
                } else {
                    cacheKey += `:${config.version}`;
                }

                const cached = useCache ? await redis.get(cacheKey) : null;

                const bundle = cached ? JSON.parse(cached) : getBundle(language, namespace);

                if (useCache && !cached) {
                    // set it in cache
                    // no need to wait for the outcome here
                    redis.setex(cacheKey, 86400, JSON.stringify(bundle));
                }

                // apply it
                result = set([language, namespace], bundle, result);
            }
        }

        return callback(null, result);
    };

    return i18n
        .createInstance({
            ...getBaseOptions(),
            lng: languageOrPackId,
            fallbackLng: await getDefaultLocale(),
        })
        .use({
            type: 'backend',
            init() {},
            read(language: string, namespace: string, callback) {
                return readAny([language], [namespace], (error, data) => {
                    if (error) {
                        return callback(error, null);
                    }

                    return callback(null, get([language, namespace], data));
                });
            },
            readMulti(languages: string[], namespaces: string[], callback: MultiReadCallback) {
                return readAny(languages, namespaces, callback);
            },
        });
};

export default async (languageOrPackId?: string): Promise<CreateClientReturn> => {
    const instance = await createInstance(languageOrPackId);
    const initPromise = instance.init();

    return { i18n: instance, initPromise };
};
