import dayjs from 'dayjs';
import { pick } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { defaultInsuranceAge } from '../../../shared/constants';
import { ApplicationInsurancing, Customer, FinderVehicle, LocalVariant, VehicleKind } from '../../database';
import { getLocalCustomerAggregatedFields } from '../../database/helpers/customers/shared';
import { Loaders } from '../../loaders';
import { calculateInsurancePremium } from '../../schema/resolvers/queries/calculator/getInsurancePremium';
import { computeCalculatorPrices } from './shared/functions';
import { InputPrices, MarketValue } from './shared/typings';

const getDefaultDateOfBirth = (customer: Customer) => {
    if (!customer?.fields.length) {
        return null;
    }

    const fields = getLocalCustomerAggregatedFields(customer);
    if (fields.birthday) {
        return dayjs(fields.birthday);
    }

    return dayjs().subtract(defaultInsuranceAge, 'year');
};

const getVehicleModelName = (selectedVehicle: LocalVariant | FinderVehicle) => {
    switch (selectedVehicle?._kind) {
        case VehicleKind.LocalVariant:
            return selectedVehicle.identifier ?? '';

        case VehicleKind.FinderVehicle:
            return selectedVehicle.listing.vehicle.orderTypeCode;

        default:
            return undefined;
    }
};

interface GetInitialInsuranceParameters {
    customer: Customer;
    prices: InputPrices;
    selectedVehicle: LocalVariant | FinderVehicle;
    selectedInsurerId: ObjectId;
    selectedInsuranceProductId: ObjectId;
    marketValues: MarketValue;
    loaders: Loaders;
}
export const getInitialInsurance = async ({
    customer,
    prices,
    selectedVehicle,
    selectedInsurerId,
    selectedInsuranceProductId,
    marketValues,
    loaders,
}: GetInitialInsuranceParameters): Promise<ApplicationInsurancing> => {
    const { carPrice, totalPrice } = computeCalculatorPrices(prices, marketValues);

    const baseInfo = {
        insurerId: selectedInsurerId,
        carPrice,
        ncd: 50,
        yearsOfDriving: 8,
        dateOfRegistration: dayjs().toDate(),
        dateOfBirth: getDefaultDateOfBirth(customer).toDate(),
    };

    const parameters = {
        ...pick(['carPrice', 'dateOfBirth', 'insurerId', 'ncd'], baseInfo),
        drivingExperience: baseInfo.yearsOfDriving,
        originalRegistrationDate: baseInfo.dateOfRegistration,
        modelName: getVehicleModelName(selectedVehicle),
    };

    const insurancePremium = await calculateInsurancePremium(parameters, loaders);

    return {
        ...marketValues,
        ...baseInfo,
        insuranceProductId: selectedInsuranceProductId,
        totalPrice,
        insurancePremium,
    } as unknown as ApplicationInsurancing;
};

interface GetUpdatedInsuranceParameters {
    prices: InputPrices;
    selectedVehicle: LocalVariant | FinderVehicle;
    existingInsurance: ApplicationInsurancing;
    marketValues: MarketValue;
    loaders: Loaders;
}
// update Calculator -> selectedVehicle and car price will affect insurance values
export const getUpdatedInsurance = async ({
    prices,
    selectedVehicle,
    existingInsurance,
    marketValues,
    loaders,
}: GetUpdatedInsuranceParameters): Promise<ApplicationInsurancing> => {
    const { carPrice, totalPrice } = computeCalculatorPrices(prices, marketValues);

    const baseInfo = {
        ...existingInsurance,
        carPrice,
    };

    const parameters = {
        carPrice: baseInfo.carPrice,
        drivingExperience: baseInfo.yearsOfDriving,
        dateOfBirth: baseInfo.dateOfBirth,
        insurerId: baseInfo.insurerId,
        ncd: baseInfo.ncd,
        originalRegistrationDate: baseInfo.dateOfRegistration,
        modelName: getVehicleModelName(selectedVehicle),
    };

    const insurancePremium = await calculateInsurancePremium(parameters, loaders);

    return {
        ...baseInfo,
        ...marketValues,
        totalPrice,
        insurancePremium: insurancePremium || null,
    } as unknown as ApplicationInsurancing;
};
