import { Collection, Filter, ObjectId } from 'mongodb';
import { ApplicationStage } from '../database/documents/Applications/core';
import type { Application } from '../database/documents/Applications/kinds';
import type { AuditTrail } from '../database/documents/AuditTrail/auditTrail';
import { AuditTrailKind } from '../database/documents/AuditTrail/core';
import type {
    ApplicationSubmittedToSystemAuditTrail,
    ApplicationResubmittedToSystemAuditTrail,
} from '../database/documents/AuditTrail/types/applications';
import type { ApplicationModule } from '../database/documents/modules';
import { getPeriodFilter } from '../export/utils';
import { getPathByStage } from './application';

/**
 * Shared query functions for application exports
 * Used by both streamExportApplications.ts and processApplicationExport.ts
 */

export type ApplicationQueryParams = {
    collections: {
        applications: Collection<Application>;
        auditTrails: Collection<AuditTrail>;
    };
    applicationPermission: Filter<Application>;
    modules: ApplicationModule[];
    eventId?: string;
    dealerIds: ObjectId[];
    stage: ApplicationStage;
    start: Date | null;
    end: Date | null;
    batchSize?: number;
};

/**
 * Get applications using the CAP format query
 */
export const getApplicationByCapFormat = async ({
    collections,
    applicationPermission,
    modules,
    dealerIds,
    stage,
    start,
    end,
    batchSize,
}: ApplicationQueryParams) => {
    // Add additional filtering for certain stages
    const path = getPathByStage(stage);
    const moreFilter = [
        ApplicationStage.Financing,
        ApplicationStage.Insurance,
        ApplicationStage.VisitAppointment,
        ApplicationStage.Appointment,
    ].includes(stage) && {
        $or: [
            { [`${path}.isDraft`]: { $exists: false } },
            { [`${path}.isDraft`]: { $exists: true }, [`${path}.isDraft`]: false },
        ],
    };

    const query = {
        $and: [
            applicationPermission,
            {
                $or: [
                    modules.length && {
                        stages: { $in: [stage] },
                        moduleId: { $in: modules.map(module => module._id) },
                        isDraft: false,
                        dealerId: { $in: dealerIds },
                        ...getPeriodFilter(start, end),
                        ...moreFilter,
                    },
                ].filter(Boolean),
            },
        ],
    };

    // Return cursor if batchSize is provided, otherwise return array
    if (batchSize) {
        return collections.applications.find(query).batchSize(batchSize);
    }

    return collections.applications.find(query).toArray();
};

/**
 * Get applications using the system format query
 */
export const getApplicationBySystemFormat = async ({
    collections,
    applicationPermission,
    modules,
    eventId,
    dealerIds,
    stage,
    start,
    end,
    batchSize,
}: ApplicationQueryParams) => {
    // Add additional filtering for certain stages
    const path = getPathByStage(stage);
    const moreFilter = [
        ApplicationStage.Financing,
        ApplicationStage.Insurance,
        ApplicationStage.VisitAppointment,
        ApplicationStage.Appointment,
    ].includes(stage) && {
        $or: [
            { [`${path}.isDraft`]: { $exists: false } },
            { [`${path}.isDraft`]: { $exists: true }, [`${path}.isDraft`]: false },
        ],
    };

    // Get initial versioning suiteIds first
    const applicationSuiteIds = await collections.applications
        .find({
            $and: [
                applicationPermission,
                {
                    $or: [
                        {
                            stages: { $in: [stage] },
                            moduleId: { $in: modules.map(module => module._id) },
                            ...(eventId && { eventId: new ObjectId(eventId) }),
                            dealerId: { $in: dealerIds },
                            ...getPeriodFilter(start, end),
                            isDraft: false,
                            '_versioning.isLatest': true,
                        },
                    ].filter(Boolean),
                },
            ],
        })
        .map((application: Application) => application._versioning.suiteId)
        .toArray();

    // Get applications by audit trail for internal submissions
    const auditTrailInternalApplicationIds =
        stage !== ApplicationStage.Mobility
            ? await collections.auditTrails
                  .find({
                      ...(start && { _date: { $gte: start } }),
                      _kind: {
                          // For internal would be submission to system, except for mobility
                          $in: [
                              AuditTrailKind.ApplicationSubmittedToSystem,
                              AuditTrailKind.ApplicationResubmittedToSystem,
                          ],
                      },
                      stages: { $in: [stage] },
                      applicationSuiteId: { $in: applicationSuiteIds },
                  })
                  .map(
                      (auditTrail: ApplicationSubmittedToSystemAuditTrail | ApplicationResubmittedToSystemAuditTrail) =>
                          auditTrail.applicationId
                  )
                  .toArray()
            : [];

    const query = {
        $and: [
            applicationPermission,
            {
                // Using the same filter with, inside $or to filter out by submission audit trail
                $or: [
                    {
                        stages: { $in: [stage] },
                        moduleId: { $in: modules.map(module => module._id) },
                        ...(eventId && { eventId: new ObjectId(eventId) }),
                        isDraft: false,
                        dealerId: { $in: dealerIds },
                        ...getPeriodFilter(start, end),
                        '_versioning.suiteId': { $in: applicationSuiteIds },
                        ...moreFilter,

                        $or: [
                            // AN-1561: we currently don't allow insurance resubmission
                            stage !== ApplicationStage.Insurance && { '_versioning.isLatest': true },

                            // For mobility, no need to retrieve it by audit trail
                            // As it only want to have 1 record inside excel report
                            stage !== ApplicationStage.Mobility && {
                                _id: { $in: auditTrailInternalApplicationIds },
                            },
                        ].filter(Boolean),
                    },
                ].filter(Boolean),
            },
        ],
    };

    // Return cursor if batchSize is provided, otherwise return array
    if (batchSize) {
        return collections.applications.find(query).batchSize(batchSize);
    }

    return collections.applications.find(query).toArray();
};
