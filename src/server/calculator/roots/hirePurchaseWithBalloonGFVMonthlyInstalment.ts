import { isNil } from 'lodash/fp';
import { FinanceProductType, SettlementInstalmentOn } from '../../schema/resolvers/enums';
import { InterestRate, KnownNodeType, Loan, MonthlyInstalment, Term } from '../nodes';
import { calculateBalloonPayment, calculateReducedMonthlyInstalment } from './formulas';

export default class HirePurchaseWithBalloonGFVMonthlyInstalment extends MonthlyInstalment {
    public static readonly identifier: string = 'hirePurchaseWithBalloonGFVMonthlyInstalment';

    get dependencies(): KnownNodeType[] {
        return [Term, Loan, InterestRate];
    }

    get reducedMonthlyPaymentRate(): number {
        const {
            calculator: { product },
        } = this;

        if (product.type === FinanceProductType.HirePurchaseWithBalloonGFV) {
            return product.balloon.reducedMonthlyPayment;
        }

        return undefined;
    }

    get settlementInstalmentOn(): SettlementInstalmentOn {
        const {
            calculator: { product },
        } = this;

        if (product.type === FinanceProductType.HirePurchaseWithBalloonGFV) {
            return product.balloon.settlementInstalmentOn;
        }

        return undefined;
    }

    resolve() {
        const {
            calculator: { root },
        } = this;

        const term = root.seek(Term) as Term;
        const loan = root.seek(Loan) as Loan;
        const interestRate = root.seek(InterestRate) as InterestRate;
        const { reducedMonthlyPaymentRate, settlementInstalmentOn } = this;

        if (
            isNil(term?.value) ||
            isNil(loan?.value) ||
            isNil(interestRate?.value) ||
            isNil(reducedMonthlyPaymentRate)
        ) {
            throw new Error(
                `[${HirePurchaseWithBalloonGFVMonthlyInstalment.identifier}] configured with a missing input`
            );
        }

        const reducedMonthlyInstalment: number = calculateReducedMonthlyInstalment(
            term.value,
            loan.value.amount,
            interestRate.value,
            reducedMonthlyPaymentRate
        );

        const balloonPayment: number = calculateBalloonPayment(
            term.value,
            loan.value.amount,
            interestRate.value,
            reducedMonthlyInstalment
        );

        switch (settlementInstalmentOn) {
            case SettlementInstalmentOn.TwoMonth:
            case SettlementInstalmentOn.OneMonth: {
                this._value = this.applyCalculationRounding([
                    reducedMonthlyInstalment,
                    balloonPayment,
                    reducedMonthlyInstalment,
                ]);
                break;
            }

            case SettlementInstalmentOn.Last: {
                this._value = this.applyCalculationRounding([reducedMonthlyInstalment, balloonPayment]);
                break;
            }

            default:
                throw new Error(
                    `[${HirePurchaseWithBalloonGFVMonthlyInstalment.identifier}] invalid SettlementInstalmentOn Field`
                );
        }
    }
}
