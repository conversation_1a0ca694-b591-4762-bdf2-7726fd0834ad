import Decimal from 'decimal.js';
import { isEmpty, isNil, isArray } from 'lodash/fp';
import { AmountUnit, DepositTableCell, FinanceProductType } from '../../database/documents';
import { InvalidFinancialProductError, InvalidQueryError, InvalidQueryReason } from '../errors';
import { CalculatorFinanceProduct, CompoundOptionalValue } from '../types';
import { warn } from '../utils';
import MonthlyInstalment from './monthlyInstalment';
import Node from './node';
import { KnownNodeType } from './shared';
import Term from './term';
import { completeCompoundInput, toCompoundValue } from './utils';

export type DepositValue = CompoundOptionalValue | null | undefined;

export const getDepositSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.Lease) {
        return financeProduct.deposit;
    }

    throw new InvalidFinancialProductError(`[${Deposit.identifier}] settings missing`);
};

export const lookupDepositTable = (table: DepositTableCell[], term: number) =>
    table
        .filter(item => item.term === term)
        .sort((a, b) => a.term - b.term)
        .find(Boolean);

export default class Deposit extends Node {
    public static readonly identifier = 'deposit';

    get dependencies(): KnownNodeType[] {
        const dependencies: KnownNodeType[] = [];

        const { mode } = this;
        if (mode === 'table') {
            dependencies.push(Term);
            dependencies.push(MonthlyInstalment);
        }

        return dependencies;
    }

    private get mode() {
        const {
            calculator: { product },
        } = this;

        return getDepositSettings(product).type;
    }

    private get range() {
        const {
            calculator: { product },
        } = this;

        const depositSettings = getDepositSettings(product);

        if (depositSettings.type === 'range') {
            return depositSettings;
        }

        throw new InvalidFinancialProductError(`[${Deposit.identifier}] range settings missing`);
    }

    private get table() {
        const {
            calculator: { product },
        } = this;

        const depositSettings = getDepositSettings(product);

        if (depositSettings.type === 'table') {
            return depositSettings.table;
        }

        throw new InvalidFinancialProductError(`[${Deposit.identifier}] table settings missing`);
    }

    private get unit() {
        const {
            calculator: { product },
        } = this;

        const depositSettings = getDepositSettings(product);

        if (depositSettings.type === 'range') {
            return depositSettings.unit;
        }

        throw new InvalidFinancialProductError(`[${Deposit.identifier}] unit is missing`);
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.deposit;
    }

    private resolveForRange() {
        const {
            input,
            unit,
            range: { min, max, default: defaultValue },
            calculator: {
                query: { totalPrice },
            },
        } = this;

        // resolve ahead for fixed configuration
        if (min === max) {
            if (input && unit === AmountUnit.Percentage ? input?.percentage !== min : input?.amount !== min) {
                warn(Deposit.identifier, `ignore input ${JSON.stringify(input)} as fixed to ${min}`);
            }

            this._value = toCompoundValue(min, unit, totalPrice, Deposit.identifier);

            return;
        }

        if (input && !isEmpty(input)) {
            this._value = completeCompoundInput(input, totalPrice, Deposit.identifier);
        } else {
            this._value = toCompoundValue(defaultValue, unit, totalPrice, Deposit.identifier);
        }
    }

    private resolveForTable() {
        const { table } = this;

        if (isNil(table)) {
            throw new InvalidFinancialProductError(
                `[${Deposit.identifier}] configured to use table but the table is not found`
            );
        }

        const {
            calculator: { root },
        } = this;
        const term = root.seek(Term) as Term;

        if (isNil(term?.value)) {
            throw new Error(`[${Deposit.identifier}] configured to use table but the term is not found`);
        }

        const target: DepositTableCell | undefined = lookupDepositTable(table, term.value);

        if (isNil(target)) {
            throw new InvalidQueryError(
                'configured to use table but the entry cannot be resolved',
                Deposit.identifier,
                InvalidQueryReason.General
            );
        }

        const monthlyInstalment = root.seek(MonthlyInstalment) as MonthlyInstalment;
        if (isNil(monthlyInstalment.value) || isArray(monthlyInstalment.value)) {
            throw new Error(`[${Deposit.identifier}] fail to resolve as monthly instalment is not a single value`);
        }

        this._value = { amount: new Decimal(monthlyInstalment.value).mul(target.value).toNumber() };
    }

    resolve(): void {
        const { mode } = this;

        switch (mode) {
            case 'range':
                this.resolveForRange();
                break;

            case 'table':
                this.resolveForTable();
                break;

            default:
                throw new InvalidFinancialProductError(`[${Deposit.identifier}] configured with invalid mode: ${mode}`);
        }
    }

    private _value: DepositValue;

    get value(): DepositValue {
        return this._value;
    }

    reset(): void {
        this._value = null;
    }
}
