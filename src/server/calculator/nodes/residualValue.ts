import { isNil } from 'lodash/fp';
import {
    AmountUnit,
    ResidualValueTableCell,
    ResidualValueTableCellForVehicleParameterFilter,
} from '../../database/documents';
import { CompoundValue } from '../types';
import Mileage, { getResidualValueSettings } from './mileage';
import Node from './node';
import { KnownNodeType } from './shared';
import Term from './term';
import { toCompoundValue } from './utils';

export type ResidualValueType = CompoundValue | null;

export default class ResidualValue extends Node {
    public static readonly identifier: string = 'Residual Value';

    public get dependencies(): KnownNodeType[] {
        const dependencies: KnownNodeType[] = [Term];
        dependencies.push(Mileage);

        return dependencies;
    }

    private _value: ResidualValueType;

    public get value(): ResidualValueType {
        return this._value;
    }

    private get base(): number {
        const {
            calculator: {
                query: { carPrice },
            },
        } = this;

        return carPrice;
    }

    private get unit(): AmountUnit {
        const {
            calculator: { product },
        } = this;

        const residualValueSetting = getResidualValueSettings(product);

        return residualValueSetting.residualValueUnit;
    }

    private get table(): ResidualValueTableCell[] {
        const {
            calculator: { product },
        } = this;

        const residualValueSetting = getResidualValueSettings(product);

        return residualValueSetting.table;
    }

    private get tableParameter(): ResidualValueTableCellForVehicleParameterFilter[] {
        const {
            calculator: { product },
        } = this;

        const residualValueSetting = getResidualValueSettings(product);

        return residualValueSetting.vehicleFilterParameterTable;
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.residualValue;
    }

    private resolveForInput() {
        const { input, base, unit } = this;

        this._value = toCompoundValue(input, unit, base, ResidualValue.identifier);
    }

    private resolveForTable() {
        const { table, tableParameter } = this;

        if (isNil(table)) {
            throw new Error(`[${ResidualValue.identifier}] configured to use table but the table is not found`);
        }

        const { calculator, base, unit } = this;
        const { root, query } = calculator;
        const { variantSuiteId, vehicleKind } = query;

        const term = root.seek(Term) as Term;

        const mileage = root.seek(Mileage) as Mileage;

        const target: ResidualValueTableCell = table.find(
            (entry: ResidualValueTableCell) =>
                entry.variantSuiteId === variantSuiteId &&
                entry.term === term.value &&
                (isNil(mileage) ? true : entry.mileage === mileage.value)
        );

        if (isNil(target)) {
            const target: ResidualValueTableCellForVehicleParameterFilter = tableParameter.find(
                (entry: ResidualValueTableCellForVehicleParameterFilter) =>
                    entry.type === vehicleKind &&
                    entry.term === term.value &&
                    (isNil(mileage) ? true : entry.mileage === mileage.value)
            );

            this._value = toCompoundValue(!isNil(target) ? target.value : 0, unit, base, ResidualValue.identifier);
        } else {
            this._value = toCompoundValue(!isNil(target) ? target.value : 0, unit, base, ResidualValue.identifier);
        }
    }

    resolve(): void {
        const value = this.input;

        if (!isNil(value)) {
            this.resolveForInput();
        } else {
            this.resolveForTable();
        }
    }

    public reset(): void {
        this._value = null;
    }
}
