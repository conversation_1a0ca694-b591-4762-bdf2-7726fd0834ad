import { isNil } from 'lodash/fp';
import { FinanceProductType } from '../../database/documents';
import { InvalidFinancialProductError } from '../errors';
import { CalculatorFinanceProduct } from '../types';
import Node from './node';
import { KnownNodeType } from './shared';
import Term from './term';
import { isNaturalFloat } from './utils';

export type TaxLossValue = number;

const TOTAL_PRICE_LIMIT = 5250000;

export const getTaxLossSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.UCCLLeasing) {
        return financeProduct.taxLoss;
    }

    throw new Error(`[${TaxLoss.identifier}] settings missing`);
};

export default class TaxLoss extends Node {
    public static readonly identifier: string = 'taxLoss';

    // eslint-disable-next-line class-methods-use-this
    public get dependencies(): KnownNodeType[] {
        return [Term];
    }

    private _value: number;

    public get value(): TaxLossValue {
        return this._value;
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.taxLoss;
    }

    resolveForInput(): void {
        this._value = this.input;

        this.validate();
    }

    resolveForComputing(): void {
        const { query, root } = this.calculator;

        if (query.totalPrice < TOTAL_PRICE_LIMIT) {
            this._value = 0;
        } else {
            const term = root.seek(Term) as Term;

            const computed = ((query.totalPrice - TOTAL_PRICE_LIMIT) * 0.2) / term.value;

            this._value = computed;
        }

        this.validate();
    }

    resolve(): void {
        if (!isNil(this.input)) {
            this.resolveForInput();
        } else {
            this.resolveForComputing();
        }
    }

    public reset(): void {
        this._value = null;
    }

    private validate() {
        const { value } = this;

        if (!isNil(value) && !isNaturalFloat(value)) {
            throw new InvalidFinancialProductError(`[${TaxLoss.identifier}] invalid value ${value}`);
        }
    }
}
