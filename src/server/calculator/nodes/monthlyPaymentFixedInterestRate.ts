import { CalculationMode, DeriveMethod } from '../../database/documents';
import { InvalidFinancialProductError } from '../errors';
import { ucclSimple } from '../roots/formulas';
import { CalculatorFinanceProduct } from '../types';
import Commission from './commission';
import InterestRate from './interestRate';
import LicensePlateFee from './licensePlateFee';
import Loan from './loan';
import Node from './node';
import { KnownNodeType } from './shared';
import Term from './term';

export type MonthlyPaymentFixedInterestRateValue = number;

const ensureFormulaSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.deriveMethod === DeriveMethod.Formula) {
        return financeProduct;
    }

    throw new InvalidFinancialProductError('[formula] settings missing');
};

export default class MonthlyPaymentFixedInterestRate extends Node {
    public static readonly identifier: string = 'monthlyPaymentFixedInterestRate';

    protected _value: MonthlyPaymentFixedInterestRateValue;

    public get value(): MonthlyPaymentFixedInterestRateValue {
        return this._value;
    }

    public get dependencies(): KnownNodeType[] {
        return [Term, Loan, InterestRate, LicensePlateFee, Commission];
    }

    protected get paymentMode() {
        const {
            calculator: {
                product,
                query: { paymentMode },
            },
        } = this;

        const productWithFormulaSettings = ensureFormulaSettings(product);
        if (paymentMode && productWithFormulaSettings.payment.isEditable) {
            return paymentMode;
        }

        return productWithFormulaSettings.payment.mode;
    }

    protected get calculationMode() {
        const {
            calculator: { product },
        } = this;

        return ensureFormulaSettings(product).calculationMode;
    }

    protected get deriveMethod() {
        const {
            calculator: { product },
        } = this;

        return product.deriveMethod;
    }

    public reset(): void {
        this._value = null;
    }

    resolve() {
        this._value = this.calculate();
    }

    private calculate() {
        const {
            calculationMode,
            calculator: {
                root,
                query: { totalPrice },
            },
        } = this;

        const term = root.seek(Term) as Term;
        const loan = root.seek(Loan) as Loan;
        const interestRate = root.seek(InterestRate) as InterestRate;
        const commission = root.seek(Commission) as Commission;
        const licensePlateFee = root.seek(LicensePlateFee) as LicensePlateFee;

        const commissionFee = (commission.value * totalPrice) / 100;

        switch (calculationMode) {
            case CalculationMode.Flat: {
                return ucclSimple(
                    interestRate.value,
                    term.value,
                    -(loan.value.amount + commissionFee + licensePlateFee.value)
                );
            }

            default:
                throw new Error(
                    // eslint-disable-next-line max-len
                    `[${MonthlyPaymentFixedInterestRate.identifier}] configured with invalid calculation mode: ${calculationMode}`
                );
        }
    }
}
