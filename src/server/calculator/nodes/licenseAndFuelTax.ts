import { isNil } from 'lodash/fp';
import { FinanceProductType } from '../../database/documents';
import { InvalidFinancialProductError } from '../errors';
import { CalculatorFinanceProduct } from '../types';
import Displacement from './displacement';
import Node from './node';
import { KnownNodeType } from './shared';
import { isNaturalFloat } from './utils';

export type LicenseAndFuelTaxValue = number;

export const getLicenseAndFuelTaxSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.UCCLLeasing) {
        return financeProduct.licenseAndFuelTax;
    }

    throw new Error(`[${LicenseAndFuelTax.identifier}] settings missing`);
};

export default class LicenseAndFuelTax extends Node {
    public static readonly identifier: string = 'licenseAndFuelTax';

    // eslint-disable-next-line class-methods-use-this
    public get dependencies(): KnownNodeType[] {
        return [Displacement];
    }

    private _value: number;

    public get value(): LicenseAndFuelTaxValue {
        return this._value;
    }

    private get fuelTable() {
        const {
            calculator: { product },
        } = this;

        return getLicenseAndFuelTaxSettings(product).fuelTable;
    }

    private get licenseTable() {
        const {
            calculator: { product },
        } = this;

        return getLicenseAndFuelTaxSettings(product).licenseTable;
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.licenseAndFuelTax;
    }

    resolve(): void {
        const {
            input,
            fuelTable,
            licenseTable,
            calculator: { root },
        } = this;

        if (!isNil(input)) {
            this._value = input;

            return;
        }

        const displacement = root.seek(Displacement) as Displacement;

        const licenseTax = licenseTable.find(
            item => item.min <= displacement.value && item.max >= displacement.value
        )?.value;

        const fuelTax = fuelTable.find(item => item.min <= displacement.value && item.max >= displacement.value)?.value;

        if (licenseTax === undefined || fuelTax === undefined) {
            this._value = undefined;
        } else {
            this._value = (licenseTax + fuelTax) / 12;
        }

        this.validate();
    }

    public reset(): void {
        this._value = null;
    }

    private validate() {
        const { value } = this;

        if (typeof value !== 'undefined' && !isNaturalFloat(value)) {
            throw new InvalidFinancialProductError(`[${LicenseAndFuelTax.identifier}] invalid value ${value}`);
        }
    }
}
