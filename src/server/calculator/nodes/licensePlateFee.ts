import { isNil } from 'lodash/fp';
import { FinanceProductType } from '../../database/documents';
import { InvalidFinancialProductError } from '../errors';
import { CalculatorFinanceProduct } from '../types';
import { warn } from '../utils';
import Node from './node';
import { KnownNodeType } from './shared';
import { isNaturalInteger } from './utils';

export type LicensePlateFeeValue = number | null;

export const getLicensePlateFeeSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.UCCLLeasing) {
        return financeProduct.licensePlateFee;
    }

    throw new Error(`[${LicensePlateFee.identifier}] settings missing`);
};

export default class LicensePlateFee extends Node {
    public static readonly identifier: string = 'licensePlateFee';

    // eslint-disable-next-line class-methods-use-this
    public get dependencies(): KnownNodeType[] {
        return [];
    }

    private _value: number;

    public get value(): LicensePlateFeeValue {
        return this._value;
    }

    private get range() {
        const {
            calculator: { product },
        } = this;

        return getLicensePlateFeeSettings(product);
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.licensePlateFee;
    }

    resolve(): void {
        const {
            input,
            range: { min, max, default: defaultValue },
        } = this;

        // resolve ahead for fixed configuration
        if (min === max) {
            if (input && input !== min) {
                warn(LicensePlateFee.identifier, `ignore input ${input} as fixed to ${min}`);
            }

            this._value = min;

            return;
        }

        this._value = isNil(input) ? defaultValue : input;

        this.validate();
    }

    public reset(): void {
        this._value = null;
    }

    private validate() {
        const { value } = this;

        if (!isNaturalInteger(value)) {
            throw new InvalidFinancialProductError(`[${LicensePlateFee.identifier}] invalid value ${value}`);
        }
    }
}
