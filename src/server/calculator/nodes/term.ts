import { isNil } from 'lodash/fp';
import { InvalidFinancialProductError } from '../errors';
import { warn } from '../utils';
import Node from './node';
import { KnownNodeType } from './shared';
import { isNaturalInteger, isWithinRangeAndOnStep } from './utils';

export type TermValue = number | null;

export default class Term extends Node {
    public static readonly identifier: string = 'term';

    public get dependencies(): KnownNodeType[] {
        return [];
    }

    private get range() {
        const {
            calculator: { product },
        } = this;

        return product.term;
    }

    private _value: TermValue;

    public get value(): TermValue {
        return this._value;
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.term;
    }

    resolve(): void {
        const {
            input,
            range: { min, max, default: defaultValue },
        } = this;

        // resolve ahead for fixed configuration
        if (min === max) {
            if (input !== min) {
                warn(Term.identifier, `ignore input ${input} as fixed to ${min}`);
            }

            this._value = min;

            this.validate();

            return;
        }

        this._value = isNil(input) ? defaultValue : input;

        this.validate();
    }

    private validate() {
        const {
            value,
            range,
            calculator: {
                query: { finderRestrictedValue },
            },
        } = this;
        const { min, max } = range;

        if (!isNaturalInteger(value)) {
            throw new InvalidFinancialProductError(`[${Term.identifier}] invalid value ${value}`);
        }

        if (min !== max && (!value || !isWithinRangeAndOnStep(value, range, finderRestrictedValue))) {
            throw new InvalidFinancialProductError(
                `[${Term.identifier}] configured with range: ${JSON.stringify(range)}, but the value is ${value}`
            );
        }
    }

    public reset(): void {
        this._value = null;
    }
}
