import { isEmpty, isFinite, isNil } from 'lodash/fp';
import { AmountUnit, FinanceProductType, LoanSettings } from '../../database/documents';
import { InvalidFinancialProductError } from '../errors';
import { CalculatorFinanceProduct, CompoundValue } from '../types';
import { warn } from '../utils';
import DownPayment from './downPayment';
import Node from './node';
import { KnownNodeType } from './shared';
import {
    completeCompoundInput,
    getFinanceProductBasedOnSetting,
    shouldResolveDownPaymentFirst,
    toCompoundValue,
    validateCompoundValue,
} from './utils';

export type LoanValue = CompoundValue | null | undefined;

export const getLoanSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (
        financeProduct.type === FinanceProductType.HirePurchase ||
        financeProduct.type === FinanceProductType.HirePurchaseWithBalloon ||
        financeProduct.type === FinanceProductType.HirePurchaseWithBalloonGFV ||
        financeProduct.type === FinanceProductType.DeferredPrincipal ||
        financeProduct.type === FinanceProductType.UCCLLeasing ||
        financeProduct.type === FinanceProductType.LeasePurchase
    ) {
        return financeProduct.loan;
    }

    throw new InvalidFinancialProductError(`[${Loan.identifier}] settings missing`);
};

export const getLoanRangeSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (
        (financeProduct.type === FinanceProductType.HirePurchase ||
            financeProduct.type === FinanceProductType.HirePurchaseWithBalloon ||
            financeProduct.type === FinanceProductType.HirePurchaseWithBalloonGFV ||
            financeProduct.type === FinanceProductType.DeferredPrincipal ||
            financeProduct.type === FinanceProductType.UCCLLeasing ||
            financeProduct.type === FinanceProductType.LeasePurchase) &&
        financeProduct.downPayment.type === 'range'
    ) {
        return financeProduct.loan as LoanSettings;
    }

    throw new InvalidFinancialProductError(`[${Loan.identifier}] settings missing`);
};

export default class Loan extends Node {
    public static readonly identifier: string = 'loan';

    public get dependencies(): KnownNodeType[] {
        const {
            calculator: { root, query, product },
        } = this;

        const dependencies: KnownNodeType[] = [];
        const downPayment = root.seek(DownPayment) as DownPayment;
        if (shouldResolveDownPaymentFirst(query, downPayment.mode, getFinanceProductBasedOnSetting(product))) {
            dependencies.push(DownPayment);
        }

        return dependencies;
    }

    public get unit() {
        const {
            calculator: { product },
        } = this;

        return getLoanRangeSettings(product).unit;
    }

    private _value: LoanValue;

    public get value(): LoanValue {
        return this._value;
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.loan;
    }

    private get range() {
        const {
            calculator: { product },
        } = this;

        const settings = getLoanRangeSettings(product);
        const { unit } = settings;
        const { min, minUnit = unit, max, maxUnit = unit, default: defaultValue } = settings;

        if (
            !isFinite(min) ||
            !isFinite(max) ||
            (minUnit === maxUnit && min > max) ||
            (unit === minUnit && defaultValue < min) ||
            (unit === maxUnit && defaultValue > max)
        ) {
            throw new InvalidFinancialProductError(`[${Loan.identifier}] range settings invalid`);
        }

        return settings;
    }

    public resolve(): void {
        const {
            calculator: {
                root,
                query: { totalPrice },
            },
        } = this;

        // down payment is resolved firstly
        const downPayment = root.seek(DownPayment) as DownPayment;
        if (!isNil(downPayment.value)) {
            this._value = {
                percentage: 100 - downPayment.value.percentage,
                amount: totalPrice - downPayment.value.amount,
            };
            this.validate();

            return;
        }

        const {
            input,
            unit,
            range: { min, minUnit, max, maxUnit, default: defaultValue },
        } = this;

        // resolve ahead for fixed configuration
        if (min === max && minUnit && minUnit === maxUnit && (min !== 0 || minUnit !== AmountUnit.Currency)) {
            if (input && minUnit === AmountUnit.Percentage ? input?.percentage !== min : input?.amount !== min) {
                warn(Loan.identifier, `ignore input ${JSON.stringify(input)} as fixed to ${min}`);
            }

            this._value = toCompoundValue(min, minUnit, totalPrice, Loan.identifier);

            return;
        }

        if (input && !isEmpty(input)) {
            this._value = completeCompoundInput(input, totalPrice, Loan.identifier);
        } else {
            this._value = toCompoundValue(defaultValue, unit, totalPrice, Loan.identifier);
        }
        this.validate();
    }

    public reset(): void {
        this._value = null;
    }

    private validate() {
        const { value } = this;

        validateCompoundValue(value, Loan.identifier);
    }
}
