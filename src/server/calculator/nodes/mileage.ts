import { isNil } from 'lodash/fp';
import { FinanceProductType } from '../../database/documents';
import { CalculatorFinanceProduct } from '../types';
import Node from './node';
import { KnownNodeType } from './shared';

export type MileageValueType = number | null;

export const getResidualValueSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.LeasePurchase) {
        return financeProduct.residualValue;
    }

    throw new Error(`[Residual Value] settings missing`);
};

export default class Mileage extends Node {
    public static readonly identifier: string = 'Mileage';

    public get dependencies(): KnownNodeType[] {
        return [];
    }

    private _value: MileageValueType;

    public get value(): MileageValueType {
        return this._value;
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.mileage;
    }

    resolve(): void {
        const {
            input,
            calculator: { product },
        } = this;
        const settings = getResidualValueSettings(product);
        const {
            averageMileage: { default: defaultValue },
        } = settings;

        if (!isNil(input)) {
            this._value = input;
        } else {
            this._value = defaultValue;
        }
    }

    public reset(): void {
        this._value = null;
    }
}
