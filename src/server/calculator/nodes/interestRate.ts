import Decimal from 'decimal.js';
import { isNil } from 'lodash/fp';
import {
    AmountUnit,
    FinanceProductType,
    InterestRateTableBasedOn,
    InterestRateTableCell,
} from '../../database/documents';
import { InvalidFinancialProductError, InvalidQueryError, InvalidQueryReason } from '../errors';
import { CalculatorFinanceProduct } from '../types';
import { warn } from '../utils';
import DownPayment from './downPayment';
import Loan from './loan';
import Node from './node';
import { KnownNodeType } from './shared';
import Term from './term';
import { isNaturalFloat } from './utils';

export type InterestRateValue = number | null;

export const getInterestRateSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (
        financeProduct.type === FinanceProductType.HirePurchase ||
        financeProduct.type === FinanceProductType.HirePurchaseWithBalloon ||
        financeProduct.type === FinanceProductType.HirePurchaseWithBalloonGFV ||
        financeProduct.type === FinanceProductType.DeferredPrincipal ||
        financeProduct.type === FinanceProductType.UCCLLeasing ||
        financeProduct.type === FinanceProductType.LeasePurchase
    ) {
        return financeProduct.interestRate;
    }

    throw new Error(`[${InterestRate.identifier}] settings missing`);
};

export const lookupInterestRateTable = (
    table: InterestRateTableCell[],
    term: number,
    base: number,
    step: number | null | undefined
) => {
    // round to the floor interval, step can be empty when not generating steps
    const floored =
        Number.isFinite(step) && step !== 0 ? new Decimal(base).div(step).floor().mul(step).toNumber() : base;

    return table
        .filter(item => item.term >= term && item.base >= floored)
        .sort((a, b) => a.term - b.term)
        .find(Boolean);
};

export default class InterestRate extends Node {
    public static readonly identifier = 'interestRate';

    public get dependencies(): KnownNodeType[] {
        const dependencies: KnownNodeType[] = [];

        const { mode } = this;

        if (mode === 'table') {
            dependencies.push(Term);

            switch (this.basedOn) {
                case InterestRateTableBasedOn.DownPayment:
                    dependencies.push(DownPayment);
                    break;

                case InterestRateTableBasedOn.Loan:
                    dependencies.push(Loan);
                    break;

                default:
                    throw new InvalidFinancialProductError(`[${InterestRate.identifier}] invalid table based on`);
            }
        }

        return dependencies;
    }

    public get mode() {
        const {
            calculator: { product },
        } = this;

        return getInterestRateSettings(product).type;
    }

    private _value: InterestRateValue;

    public get value(): InterestRateValue {
        return this._value;
    }

    private get input(): number | undefined {
        const {
            calculator: { query },
        } = this;

        return query.interestRate;
    }

    private get range() {
        const {
            calculator: { product },
        } = this;

        const interestRateSettings = getInterestRateSettings(product);

        if (interestRateSettings.type === 'range') {
            return interestRateSettings;
        }

        throw new InvalidFinancialProductError(`[${InterestRate.identifier}] range settings missing`);
    }

    private get table() {
        const {
            calculator: { product },
        } = this;

        const interestRateSettings = getInterestRateSettings(product);

        if (interestRateSettings.type === 'table') {
            return interestRateSettings.table;
        }

        throw new InvalidFinancialProductError(`[${InterestRate.identifier}] table settings missing`);
    }

    private get flat() {
        const {
            calculator: { product },
        } = this;

        const interestRateSettings = getInterestRateSettings(product);

        if (interestRateSettings.type === 'fixed') {
            return interestRateSettings.default;
        }

        throw new InvalidFinancialProductError(`[${InterestRate.identifier}] flat settings missing`);
    }

    private get basedOn() {
        const {
            calculator: { product },
        } = this;

        const interestRateSettings = getInterestRateSettings(product);

        if (interestRateSettings.type === 'table') {
            return interestRateSettings.basedOn;
        }

        throw new InvalidFinancialProductError(`[${InterestRate.identifier}] table based on missing`);
    }

    private get step() {
        const {
            calculator: { product },
        } = this;

        const interestRateSettings = getInterestRateSettings(product);

        if (interestRateSettings.type === 'fixed') {
            throw new InvalidFinancialProductError(`[${InterestRate.identifier}] settings missing`);
        }

        if (interestRateSettings.type === 'table') {
            return interestRateSettings.isGenerateStepsInTable ? interestRateSettings.step : undefined;
        }

        if (interestRateSettings.type === 'range') {
            return interestRateSettings.step;
        }

        throw new InvalidFinancialProductError(`[${InterestRate.identifier}] step missing`);
    }

    private get unit() {
        const {
            calculator: { product },
        } = this;

        const interestRateSettings = getInterestRateSettings(product);

        if (interestRateSettings.type === 'table') {
            return interestRateSettings.unit;
        }

        throw new InvalidFinancialProductError(`[${InterestRate.identifier}] table unit missing`);
    }

    private get base(): number {
        const {
            basedOn,
            calculator: { root },
        } = this;

        switch (basedOn) {
            case InterestRateTableBasedOn.DownPayment: {
                const downPayment = root.seek(DownPayment) as DownPayment;

                if (downPayment?.value === null || downPayment?.value === undefined) {
                    throw new InvalidFinancialProductError(`[${InterestRate.identifier}] downpayment is missing`);
                }

                switch (this.unit) {
                    case AmountUnit.Currency:
                        return downPayment.value.amount;

                    default:
                        return downPayment.value.percentage;
                }
            }

            case InterestRateTableBasedOn.Loan: {
                const downPayment = root.seek(DownPayment) as DownPayment;
                const loan = root.seek(Loan) as Loan;

                if (loan?.value === null || loan?.value === undefined) {
                    throw new InvalidFinancialProductError(`[${InterestRate.identifier}] loan is missing`);
                }

                if (downPayment.mode === 'table' || this.unit === AmountUnit.Percentage) {
                    return loan.value.percentage;
                }

                if (this.unit === AmountUnit.Currency) {
                    return loan.value.amount;
                }

                throw new InvalidFinancialProductError(
                    `[${InterestRate.identifier}] invalid unit ${loan.unit} from loan`
                );
            }

            default:
                throw new Error(`[${InterestRate.identifier}] configured with invalid based on: ${basedOn}`);
        }
    }

    resolve(): void {
        const { mode } = this;

        switch (mode) {
            case 'range':
                this.resolveForRange();
                break;

            case 'table':
                this.resolveForTable();
                break;

            case 'fixed':
                this.resolveForFlat();
                break;

            default:
                throw new Error(`[${InterestRate.identifier}] configured with invalid mode ${mode}`);
        }

        this.validate();
    }

    reset(): void {
        this._value = null;
    }

    private resolveForFlat() {
        const { flat, input } = this;

        if (!isNil(input) && input !== flat) {
            warn(InterestRate.identifier, `ignore input ${input} as fixed to ${flat}`);
        }

        this._value = flat;
    }

    private resolveForRange() {
        const {
            input,
            range: { min, max },
        } = this;

        // resolve ahead for fixed configuration
        if (min === max) {
            if (input && input !== min) {
                warn(InterestRate.identifier, `ignore input ${input} as fixed to ${min}`);
            }

            this._value = min;

            return;
        }

        const {
            range: { default: defaultValue },
        } = this;
        this._value = isNil(input) ? defaultValue : input;
    }

    private resolveForTable() {
        const { table, base, step } = this;

        if (isNil(table)) {
            throw new Error(`[${InterestRate.identifier}] configured to use table but the table is not found`);
        }

        const {
            calculator: { root },
        } = this;

        const term = root.seek(Term) as Term;

        if (isNil(term?.value)) {
            throw new Error(`[${InterestRate.identifier}] configured to use table but the term is not found`);
        }

        const target: InterestRateTableCell | undefined = lookupInterestRateTable(table, term.value, base, step);

        if (isNil(target)) {
            throw new InvalidQueryError(
                'configured to use table but the entry cannot be resolved',
                InterestRate.identifier,
                InvalidQueryReason.General
            );
        }

        this._value = target.value;
    }

    private validate() {
        const { value } = this;

        if (!isNaturalFloat(value)) {
            throw new Error(`[${InterestRate.identifier}] invalid value ${value}`);
        }
    }
}
