import { isNil } from 'lodash/fp';
import { FinanceProductType } from '../../database/documents';
import { InvalidFinancialProductError } from '../errors';
import { CalculatorFinanceProduct } from '../types';
import Node from './node';
import { KnownNodeType } from './shared';
import { isNaturalInteger } from './utils';

export type DisplacementValue = number | null;

export const getDisplacementSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.UCCLLeasing) {
        return financeProduct.displacement;
    }

    throw new Error(`[${Displacement.identifier}] settings missing`);
};

export default class Displacement extends Node {
    public static readonly identifier: string = 'displacement';

    // eslint-disable-next-line class-methods-use-this
    public get dependencies(): KnownNodeType[] {
        return [];
    }

    private _value: number;

    public get value(): DisplacementValue {
        return this._value;
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.displacement;
    }

    resolve(): void {
        const { input } = this;
        this._value = input;
        this.validate();
    }

    public reset(): void {
        this._value = null;
    }

    private validate() {
        const { value } = this;

        if (!isNil(value) && !isNaturalInteger(value)) {
            throw new InvalidFinancialProductError(`[${Displacement.identifier}] invalid value ${value}`);
        }
    }
}
