import { isEmpty, isFinite, isNil } from 'lodash/fp';
import {
    AmountUnit,
    BalloonBasedOn,
    FinanceProductType,
    BalloonValueTableCellForVehicleParameterFilter,
} from '../../database/documents';
import { InvalidFinancialProductError } from '../errors';
import { CalculatorFinanceProduct, CompoundValue } from '../types';
import { warn } from '../utils';
import Loan from './loan';
import Node from './node';
import { KnownNodeType } from './shared';
import Term from './term';
import { completeCompoundInput, toCompoundValue, toUnitValue, validateCompoundValue } from './utils';

export type BalloonPaymentValue = CompoundValue;

export const getBalloonSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.HirePurchaseWithBalloon) {
        return financeProduct.balloon;
    }

    throw new Error(`[${BalloonPayment.identifier}] settings missing`);
};

export default class BalloonPayment extends Node {
    public static readonly identifier: string = 'balloonPayment';

    // eslint-disable-next-line class-methods-use-this
    public get dependencies(): KnownNodeType[] {
        const dependencies: KnownNodeType[] = [];

        if (this.mode === 'table') {
            dependencies.push(Term);
        }

        if (this.balloonSettings.basedOn === BalloonBasedOn.LoanAmount) {
            dependencies.push(Loan);
        }

        return dependencies;
    }

    private _value: CompoundValue;

    public get value(): BalloonPaymentValue {
        return this._value;
    }

    private get balloonSettings() {
        const { calculator } = this;

        return getBalloonSettings(calculator.product);
    }

    private get mode() {
        return this.balloonSettings.type;
    }

    private get basedOnValue(): number {
        const { balloonSettings, calculator } = this;
        const { basedOn } = balloonSettings;

        const { root, query } = calculator;

        if (basedOn === BalloonBasedOn.TotalPrice) {
            return query.totalPrice;
        }

        if (basedOn === BalloonBasedOn.CarPrice) {
            return query.carPrice;
        }

        if (basedOn === BalloonBasedOn.LoanAmount) {
            const loan = root.seek(Loan) as Loan;

            return loan.value.amount;
        }

        throw new Error(`[${BalloonPayment.identifier}] configured with invalid base ${basedOn}`);
    }

    private get table() {
        const { balloonSettings } = this;

        if (balloonSettings.type !== 'table') {
            throw new Error(`[${BalloonPayment.identifier}] type is not a table`);
        }

        if (isNil(balloonSettings.table)) {
            throw new InvalidFinancialProductError(
                `[${BalloonPayment.identifier}] configured with invalid table: ${balloonSettings.table}`
            );
        }

        return balloonSettings.table;
    }

    private get tableParameter(): BalloonValueTableCellForVehicleParameterFilter[] {
        const {
            calculator: { product },
        } = this;

        const balloonValueSetting = getBalloonSettings(product);

        if (balloonValueSetting.type === 'table') {
            return balloonValueSetting.vehicleFilterParameterTable;
        }

        return [];
    }

    private get range() {
        const { balloonSettings, basedOnValue } = this;

        if (balloonSettings.type !== 'range') {
            throw new Error(`[${BalloonPayment.identifier}] type is not a range`);
        }

        const { unit } = balloonSettings;
        const { min, minUnit = unit, max, maxUnit = unit, default: defaultValue } = balloonSettings;

        const minimum = toUnitValue(min, basedOnValue, minUnit, unit);
        const maximum = toUnitValue(max, basedOnValue, maxUnit, unit);
        if (
            !isFinite(minimum) ||
            !isFinite(maximum) ||
            minimum > maximum ||
            defaultValue < minimum ||
            defaultValue > maximum
        ) {
            throw new InvalidFinancialProductError(`[${BalloonPayment.identifier}] range settings invalid`);
        }

        return balloonSettings;
    }

    private get input() {
        const { calculator } = this;

        return calculator.query.balloonPayment;
    }

    private resolveForRange() {
        const { basedOnValue, range, input } = this;

        const { min, minUnit, max, maxUnit, unit, default: defaultValue } = range;

        // resolve ahead for fixed configuration
        if (min === max && minUnit && minUnit === maxUnit && (min !== 0 || minUnit !== AmountUnit.Currency)) {
            if (input && unit === AmountUnit.Percentage ? input?.percentage !== min : input?.amount !== min) {
                warn(BalloonPayment.identifier, `ignore input ${JSON.stringify(input)} as fixed to ${min}`);
            }

            this._value = toCompoundValue(min, unit, basedOnValue, BalloonPayment.identifier);

            return;
        }

        if (input && !isEmpty(input)) {
            this._value = completeCompoundInput(input, basedOnValue, BalloonPayment.identifier);
        } else {
            this._value = toCompoundValue(defaultValue, unit, basedOnValue, BalloonPayment.identifier);
        }
    }

    private resolveForTable() {
        const { basedOnValue, calculator, table, balloonSettings, tableParameter } = this;
        const { query, root } = calculator;

        const term = root.seek(Term) as Term;
        if (isNil(term?.value)) {
            throw new Error(`[${BalloonPayment.identifier}] configured to use table but the term is not found`);
        }

        const { variantSuiteId } = query;
        if (isNil(variantSuiteId)) {
            throw new Error(`[${BalloonPayment.identifier}] variant is missing`);
        }
        const target = table
            .filter(item => item.term >= term.value && item.variantSuiteId.toString() === variantSuiteId.toString())
            .sort((a, b) => a.term - b.term)
            .find(Boolean);

        if (isNil(target)) {
            const targetFromVehicleParameter: BalloonValueTableCellForVehicleParameterFilter = tableParameter.find(
                (entry: BalloonValueTableCellForVehicleParameterFilter) =>
                    entry.type === query.vehicleKind && entry.term === term.value
            );

            this._value = toCompoundValue(
                targetFromVehicleParameter.value,
                balloonSettings.unit,
                basedOnValue,
                BalloonPayment.identifier
            );
        } else {
            this._value = toCompoundValue(target.value, balloonSettings.unit, basedOnValue, BalloonPayment.identifier);
        }
    }

    resolve(): void {
        switch (this.mode) {
            case 'range':
                this.resolveForRange();
                break;

            case 'table':
                this.resolveForTable();
                break;

            default:
                throw new InvalidFinancialProductError(
                    `[${BalloonPayment.identifier}] configured with invalid mode: ${this.mode}`
                );
        }

        this.validate();
    }

    public reset(): void {
        this._value = null;
    }

    private validate() {
        const { value } = this;

        if (!isNil(value)) {
            validateCompoundValue(value, BalloonPayment.identifier);
        }
    }
}
