/* eslint-disable max-len */
import { isEmpty, isFinite, isNil } from 'lodash/fp';
import { AmountUnit, DownPaymentTableCell, FinanceProductType } from '../../database/documents';
import { InvalidFinancialProductError, InvalidQueryError, InvalidQueryReason } from '../errors';
import { CalculatorFinanceProduct, CompoundValue } from '../types';
import { warn } from '../utils';
import Loan from './loan';
import Node from './node';
import { KnownNodeType } from './shared';
import Term from './term';
import {
    completeCompoundInput,
    getFinanceProductBasedOnSetting,
    shouldResolveLoanFirst,
    toCompoundValue,
    validateCompoundValue,
} from './utils';

export type DownPaymentValue = CompoundValue | null | undefined;

export const getDownPaymentSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (
        financeProduct.type === FinanceProductType.HirePurchase ||
        financeProduct.type === FinanceProductType.HirePurchaseWithBalloon ||
        financeProduct.type === FinanceProductType.HirePurchaseWithBalloonGFV ||
        financeProduct.type === FinanceProductType.DeferredPrincipal ||
        financeProduct.type === FinanceProductType.UCCLLeasing ||
        financeProduct.type === FinanceProductType.LeasePurchase
    ) {
        return financeProduct.downPayment;
    }

    throw new InvalidFinancialProductError(`[${DownPayment.identifier}] settings missing`);
};

export const lookupDownPaymentTable = (table: DownPaymentTableCell[], term: number) =>
    table
        .filter(item => item.term >= term)
        .sort((a, b) => a.term - b.term)
        .find(Boolean);

export default class DownPayment extends Node {
    public static readonly identifier: string = 'downPayment';

    public get dependencies(): KnownNodeType[] {
        const {
            mode,
            calculator: { query, product },
        } = this;

        const dependencies: KnownNodeType[] = [];

        if (mode === 'table') {
            dependencies.push(Term);
        }

        if (shouldResolveLoanFirst(query, mode, getFinanceProductBasedOnSetting(product))) {
            dependencies.push(Loan);
        }

        return dependencies;
    }

    public get mode() {
        const {
            calculator: { product },
        } = this;

        return getDownPaymentSettings(product).type;
    }

    public get unit() {
        const {
            calculator: { product },
        } = this;

        const downPaymentSettings = getDownPaymentSettings(product);

        // Regardless down payment range or table
        // Unit used for each row (in table) is the same.
        return downPaymentSettings.unit;
    }

    private _value: DownPaymentValue;

    public get value(): DownPaymentValue {
        return this._value;
    }

    private get range() {
        const {
            calculator: { product },
        } = this;

        const downPaymentSettings = getDownPaymentSettings(product);
        if (downPaymentSettings.type === 'range') {
            const { unit } = downPaymentSettings;
            const { min, minUnit = unit, max, maxUnit = unit, default: defaultValue } = downPaymentSettings;

            if (
                !isFinite(min) ||
                !isFinite(max) ||
                (minUnit === maxUnit && min > max) ||
                (unit === minUnit && defaultValue < min) ||
                (unit === maxUnit && defaultValue > max)
            ) {
                throw new InvalidFinancialProductError(`[${DownPayment.identifier}] range settings invalid`);
            }

            return downPaymentSettings;
        }

        throw new InvalidFinancialProductError(`[${DownPayment.identifier}] range settings missing`);
    }

    private get table() {
        const {
            calculator: { product },
        } = this;

        const downPaymentSettings = getDownPaymentSettings(product);
        if (downPaymentSettings.type === 'table') {
            return downPaymentSettings.table;
        }

        throw new InvalidFinancialProductError(`[${DownPayment.identifier}] table settings missing`);
    }

    private get input() {
        const {
            calculator: { query },
        } = this;

        return query.downPayment;
    }

    resolve(): void {
        const {
            calculator: {
                root,
                query: { totalPrice },
            },
        } = this;
        const loan = root.seek(Loan) as Loan;

        // loan is resolved first
        if (!isNil(loan.value)) {
            this._value = {
                percentage: 100 - loan.value.percentage,
                amount: totalPrice - loan.value.amount,
            };
            this.validate();

            return;
        }

        const { mode } = this;
        switch (mode) {
            case 'range':
                this.resolveForRange();
                break;

            case 'table':
                this.resolveForTable();
                break;

            default:
                throw new InvalidFinancialProductError(
                    `[${DownPayment.identifier}] configured with invalid mode: ${mode}`
                );
        }
        this.validate();
    }

    public reset(): void {
        this._value = null;
    }

    private resolveForRange() {
        const {
            input,
            unit,
            range: { min, minUnit, max, maxUnit, default: defaultValue },
            calculator: {
                query: { totalPrice },
            },
        } = this;

        // resolve ahead for fixed configuration
        if (min === max && minUnit && minUnit === maxUnit && (min !== 0 || minUnit !== AmountUnit.Currency)) {
            if (input && minUnit === AmountUnit.Percentage ? input?.percentage !== min : input?.amount !== min) {
                warn(DownPayment.identifier, `ignore input ${JSON.stringify(input)} as fixed to ${min}`);
            }

            this._value = toCompoundValue(min, minUnit, totalPrice, DownPayment.identifier);

            return;
        }

        if (input && !isEmpty(input)) {
            this._value = completeCompoundInput(input, totalPrice, DownPayment.identifier);
        } else {
            this._value = toCompoundValue(defaultValue, unit, totalPrice, DownPayment.identifier);
        }
    }

    private resolveForTable() {
        const { table } = this;
        if (isNil(table)) {
            throw new InvalidFinancialProductError(
                `[${DownPayment.identifier}] configured to use table but the table is not found`
            );
        }

        const {
            calculator: { root },
        } = this;

        const term = root.seek(Term) as Term;

        if (isNil(term?.value)) {
            throw new Error(`[${DownPayment.identifier}] configured to use table but the term is not found`);
        }

        const target: DownPaymentTableCell | undefined = lookupDownPaymentTable(table, term.value);

        if (isNil(target)) {
            throw new InvalidQueryError(
                'configured to use table but the entry cannot be resolved',
                DownPayment.identifier,
                InvalidQueryReason.General
            );
        }

        const {
            calculator: {
                query: { totalPrice },
            },
        } = this;
        this._value = toCompoundValue(target.value, this.unit, totalPrice, DownPayment.identifier);
    }

    private validate() {
        const { value } = this;
        validateCompoundValue(value, DownPayment.identifier);
    }
}
