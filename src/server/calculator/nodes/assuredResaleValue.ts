import { isNil } from 'lodash/fp';
import {
    BalloonTableCell,
    FinanceProductType,
    BalloonValueTableCellForVehicleParameterFilter,
} from '../../database/documents';
import { InvalidFinancialProductError, InvalidQueryError, InvalidQueryReason } from '../errors';
import { CalculatorFinanceProduct, CompoundValue } from '../types';
import Node from './node';
import { KnownNodeType } from './shared';
import Term from './term';
import { toCompoundValue, validateCompoundValue } from './utils';

export type AssuredResaleValueValue = CompoundValue | null;

export const getAssuredResaleSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.HirePurchaseWithBalloonGFV) {
        return financeProduct.balloon;
    }

    throw new Error(`[${AssuredResaleValue.identifier}] settings missing`);
};

export default class AssuredResaleValue extends Node {
    public static readonly identifier: string = 'Assured Resale';

    // eslint-disable-next-line class-methods-use-this
    public get dependencies(): KnownNodeType[] {
        return [Term];
    }

    private _value: AssuredResaleValueValue;

    public get value(): AssuredResaleValueValue {
        return this._value;
    }

    public get unit() {
        const {
            calculator: { product },
        } = this;

        const balloon = getAssuredResaleSettings(product);

        return balloon.residualValueUnit;
    }

    private get table() {
        const {
            calculator: { product },
        } = this;

        const balloon = getAssuredResaleSettings(product);

        return balloon.table;
    }

    private get tableParameter(): BalloonValueTableCellForVehicleParameterFilter[] {
        const {
            calculator: { product },
        } = this;

        const balloonValueSetting = getAssuredResaleSettings(product);

        return balloonValueSetting.vehicleFilterParameterTable;
    }

    resolve(): void {
        const {
            table,
            unit,
            calculator: {
                query: { variantSuiteId, totalPrice, vehicleKind },
                root,
            },
            tableParameter,
        } = this;

        if (isNil(variantSuiteId)) {
            throw new Error(`[${AssuredResaleValue.identifier}] variant is missing`);
        }

        if (isNil(table)) {
            throw new InvalidFinancialProductError(
                `[${AssuredResaleValue.identifier}] configured to use table but the table is not found`
            );
        }

        const term = root.seek(Term) as Term;

        if (isNil(term?.value)) {
            throw new Error(`[${AssuredResaleValue.identifier}] configured to use table but the term is not found`);
        }

        const target: BalloonTableCell | undefined = table.find(
            item => item.term === term.value && item.variantSuiteId.toHexString() === variantSuiteId.toHexString()
        );

        if (target) {
            this._value = toCompoundValue(target.value, unit, totalPrice);
            this.validate();

            return;
        }

        const targetFromVehicleParameter = tableParameter.find(
            entry => entry.type === vehicleKind && entry.term === term.value
        );

        if (targetFromVehicleParameter) {
            this._value = toCompoundValue(targetFromVehicleParameter.value, unit, totalPrice);
            this.validate();

            return;
        }

        throw new InvalidQueryError(
            'configured to use table but the entry cannot be resolved',
            AssuredResaleValue.identifier,
            InvalidQueryReason.General
        );
    }

    public reset(): void {
        this._value = null;
    }

    private validate() {
        const { value } = this;

        validateCompoundValue(value);
    }
}
