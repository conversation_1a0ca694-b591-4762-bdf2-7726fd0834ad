import { ApplicationStage } from '../../database';
import { Stage } from './enums';

// eslint-disable-next-line import/prefer-default-export
export const getBEApplicationStage = (stage: Stage): ApplicationStage => {
    switch (stage) {
        case 'Financing':
            return ApplicationStage.Financing;

        case 'Mobility':
            return ApplicationStage.Mobility;

        case 'Reservation':
            return ApplicationStage.Reservation;

        case 'Lead':
            return ApplicationStage.Lead;

        case 'Appointment':
            return ApplicationStage.Appointment;

        case 'VisitAppointment':
            return ApplicationStage.VisitAppointment;

        default:
            return ApplicationStage.Financing;
    }
};
