import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash/fp';
import { ObjectId, ReturnDocument } from 'mongodb';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../core/config';
import defaultLogoMetaData from '../../datasets/defaultLogoMetaData';
import type {
    GraphQLMaybankIntegrationUpdateSettings,
    GraphQLPorscheRetainModuleSettings,
} from '../../schema/resolvers/definitions';
import {
    AllowSearchEnginesSetting,
    Company,
    CompanySMTPSetting,
    CompanyTwilioSetting,
    DbsBankIntegrationSetting,
    DefaultEmailContextSetting,
    DefaultLocaleSetting,
    EazyInsurerIntegrationSetting,
    HlfBankIntegrationSetting,
    HlfBankV2IntegrationSetting,
    MaybankIntegrationSetting,
    NamirialSetting,
    PorscheRetainModuleIntegrationSetting,
    SettingId,
    TradeInSetting,
    UobBankIntegrationSetting,
} from '../documents';
import getDatabaseContext from '../getDatabaseContext';

export type EmailLogo = {
    url: string;
    width: number | undefined;
    height: number | undefined;
};

export type EmailContext = {
    companyName: string;
    company?: Company;
    fontUrl?: string | null;
    fontBoldUrl?: string | null;
    sender: string;
    logo: EmailLogo;
};

export const defaultLogoUrl = urljoin(`${config.applicationEndpoint}`, 'public/logos/v5SafeAppFullLogo.png');
export const defaultFontRegularUrl = urljoin(
    `${config.storage.publicEndpoint}`,
    'defaults/fonts/PorscheNextTT-Regular.ttf'
);
export const defaultFontBoldUrl = urljoin(`${config.storage.publicEndpoint}`, 'defaults/fonts/PorscheNextTT-Bold.ttf');

const withLocalCache = <Output>(getter: () => Promise<Output>) => {
    // cache holder
    let cache:
        | { resolved: false; promise: Promise<Output> }
        | { resolved: true; value: Output; validUntil: Date }
        | null = null;

    return () => {
        if (cache) {
            if (cache.resolved === false) {
                // simply return the promise
                return cache.promise;
            }

            // verify validity
            if (new Date() <= cache.validUntil) {
                return Promise.resolve(cache.value);
            }
        }

        // instance the promise and wait for its resolution
        const promise = getter()
            .then(value => {
                // update cache
                // valid for 10s
                cache = { resolved: true, value, validUntil: dayjs().add(10, 's').toDate() };

                return value;
            })
            .catch(error => {
                // print the error
                console.error(error);
                // empty cache
                cache = null;

                // throw it back
                return Promise.reject(error);
            });

        // update cache
        cache = { resolved: false, promise };

        return promise;
    };
};

export const getDefaultLocale = withLocalCache(async () => {
    const { collections } = await getDatabaseContext();
    const setting = (await collections.settings.findOne({
        settingId: SettingId.DefaultLocale,
    })) as DefaultLocaleSetting;

    if (!setting) {
        // return english by default
        return 'en';
    }

    return setting.locale;
});

export const getSearchEnginesConfiguration = withLocalCache(async () => {
    const { collections } = await getDatabaseContext();
    const setting = (await collections.settings.findOne({
        settingId: SettingId.AllowSearchEngines,
    })) as AllowSearchEnginesSetting;

    if (!setting) {
        return false;
    }

    return setting.isAllow;
});

export const getCompanySMTPSetting = async (companyId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const setting = (await collections.settings.findOne({
        settingId: SettingId.CompanySMTP,
        companyId,
    })) as CompanySMTPSetting;

    if (!setting) {
        throw new Error('SMTP settings not found');
    }

    return setting.secrets;
};

export const getDefaultEmailContext = async (): Promise<EmailContext> => {
    const { collections } = await getDatabaseContext();
    const setting = (await collections.settings.findOne({
        settingId: SettingId.DefaultEmailContext,
    })) as DefaultEmailContextSetting;

    if (!setting) {
        throw new Error('Default emailContext settings not found');
    }

    const { width, height } = defaultLogoMetaData;

    return {
        companyName: setting.companyName,
        logo: {
            url: defaultLogoUrl,
            width,
            height,
        },
        fontBoldUrl: defaultFontBoldUrl,
        fontUrl: defaultFontRegularUrl,
        sender: config.smtp.sender,
    };
};

export const updateCompanySMTPSetting = async (companyId: ObjectId, settings: CompanySMTPSetting['secrets']) => {
    const { collections } = await getDatabaseContext();

    return collections.settings.updateOne(
        { settingId: SettingId.CompanySMTP, companyId },
        {
            $set: {
                settingId: SettingId.CompanySMTP,
                companyId,
                secrets: settings,
                date: new Date(),
            },
        },
        { upsert: true }
    );
};

export const deleteCompanySMTPSetting = async (companyId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    await collections.settings.deleteOne({ settingId: SettingId.CompanySMTP, companyId });
};

export const getTwilioSMTPSetting = async (companyId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const setting = (await collections.settings.findOne({
        settingId: SettingId.CompanyTwilio,
        companyId,
    })) as CompanyTwilioSetting;

    if (!setting) {
        throw new Error('Twilio settings not found');
    }

    return setting.secrets;
};

export const updateCompanyTwilioSetting = async (companyId: ObjectId, settings: CompanyTwilioSetting['secrets']) => {
    const { collections } = await getDatabaseContext();

    return collections.settings.updateOne(
        { settingId: SettingId.CompanyTwilio, companyId },
        {
            $set: {
                settingId: SettingId.CompanyTwilio,
                companyId,
                secrets: settings,
                date: new Date(),
            },
        },
        { upsert: true }
    );
};

export const deleteCompanyTwilioSetting = async (companyId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    await collections.settings.deleteOne({ settingId: SettingId.CompanyTwilio, companyId });
};

const getBankIntegrationSetting = async (
    bankId: ObjectId,
    settingId:
        | SettingId.HlfBankIntegration
        | SettingId.UobBankIntegration
        | SettingId.DbsBankIntegration
        | SettingId.MaybankIntegration
) => {
    const { collections } = await getDatabaseContext();

    const setting = (await collections.settings.findOne({
        settingId,
        bankId,
    })) as
        | HlfBankIntegrationSetting
        | UobBankIntegrationSetting
        | DbsBankIntegrationSetting
        | MaybankIntegrationSetting;

    if (!setting) {
        throw new Error(`Bank integration settings not found, settingId: ${settingId}`);
    }

    return setting.secrets;
};

export const getHlfBankIntegrationSetting = (bankId: ObjectId) =>
    getBankIntegrationSetting(bankId, SettingId.HlfBankIntegration) as Promise<HlfBankIntegrationSetting['secrets']>;

export const getUobBankIntegrationSetting = (bankId: ObjectId) =>
    getBankIntegrationSetting(bankId, SettingId.UobBankIntegration) as Promise<UobBankIntegrationSetting['secrets']>;

export const updateUobBankIntegrationSetting = async (
    bankId: ObjectId,
    settings: UobBankIntegrationSetting['secrets']
) => {
    const { collections } = await getDatabaseContext();

    return collections.settings.findOneAndUpdate(
        { settingId: SettingId.UobBankIntegration, bankId },
        {
            $set: {
                settingId: SettingId.UobBankIntegration,
                bankId,
                secrets: settings,
                date: new Date(),
            },
        },
        { upsert: true, returnDocument: ReturnDocument.AFTER }
    );
};

export const updateHlfBankIntegrationSetting = async (
    bankId: ObjectId,
    settings: HlfBankIntegrationSetting['secrets']
) => {
    const { collections } = await getDatabaseContext();

    return collections.settings.findOneAndUpdate(
        { settingId: SettingId.HlfBankIntegration, bankId },
        {
            $set: {
                settingId: SettingId.HlfBankIntegration,
                bankId,
                secrets: settings,
                date: new Date(),
            },
        },
        { upsert: true, returnDocument: ReturnDocument.AFTER }
    );
};

export const updateHlfBankV2IntegrationSetting = async (
    bankId: ObjectId,
    settings: HlfBankV2IntegrationSetting['secrets']
) => {
    const { collections } = await getDatabaseContext();

    return collections.settings.findOneAndUpdate(
        { settingId: SettingId.HlfBankV2Integration, bankId },
        {
            $set: {
                settingId: SettingId.HlfBankV2Integration,
                bankId,
                secrets: settings,
                date: new Date(),
            },
        },
        { upsert: true, returnDocument: ReturnDocument.AFTER }
    );
};

export const deleteHlfBankIntegrationSetting = async (bankId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const { deletedCount } = await collections.settings.deleteOne({ settingId: SettingId.HlfBankIntegration, bankId });

    return deletedCount > 0;
};

export const getDbsBankIntegrationSetting = (bankId: ObjectId) =>
    getBankIntegrationSetting(bankId, SettingId.DbsBankIntegration) as Promise<DbsBankIntegrationSetting['secrets']>;

export const updateDbsBankIntegrationSetting = async (
    bankId: ObjectId,
    settings: DbsBankIntegrationSetting['secrets'] & { mappings: DbsBankIntegrationSetting['mappings'] }
) => {
    const { collections } = await getDatabaseContext();

    const { mappings, ...rest } = settings;

    return collections.settings.findOneAndUpdate(
        { settingId: SettingId.DbsBankIntegration, bankId },
        {
            $set: {
                settingId: SettingId.DbsBankIntegration,
                bankId,
                secrets: rest,
                mappings,
                date: new Date(),
            },
        },
        { upsert: true, returnDocument: ReturnDocument.AFTER }
    );
};

export const getNamirialSetting = async (namirialModuleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const setting = (await collections.settings.findOne({
        settingId: SettingId.NamirialSetting,
        namirialModuleId,
    })) as NamirialSetting;

    if (!setting) {
        throw new Error('Namirial Module settings not found');
    }

    return setting;
};

export const updateNamirialSetting = async (namirialModuleId: ObjectId, settings: NamirialSetting['secrets']) => {
    const { collections } = await getDatabaseContext();

    await collections.settings.updateOne(
        { settingId: SettingId.NamirialSetting, namirialModuleId },
        {
            $set: {
                settingId: SettingId.NamirialSetting,
                namirialModuleId,
                secrets: settings,
                date: new Date(),
            },
        },
        { upsert: true }
    );
};

export const getMaybankIntegrationSetting = (bankId: ObjectId) =>
    getBankIntegrationSetting(bankId, SettingId.MaybankIntegration) as Promise<MaybankIntegrationSetting['secrets']>;

export const updateMaybankIntegrationSetting = async (
    bankId: ObjectId,
    settings: GraphQLMaybankIntegrationUpdateSettings,
    upsert: boolean
) => {
    const { collections } = await getDatabaseContext();

    if (upsert) {
        const sets = Object.fromEntries(
            Object.entries(settings)
                .filter(([, v]) => !isEmpty(v))
                .map(([k, v]) => [`secrets.${k}`, v])
        );

        const updated = await collections.settings.findOneAndUpdate(
            {
                settingId: SettingId.MaybankIntegration,
                bankId,
            },
            {
                $set: sets,
            },
            { returnDocument: ReturnDocument.AFTER }
        );

        return updated;
    }

    const salt = await bcrypt.genSalt(10);
    const generatedSecrets = await bcrypt.hash(nanoid(20), salt);

    const setting: MaybankIntegrationSetting = {
        _id: new ObjectId(),
        settingId: SettingId.MaybankIntegration,
        date: new Date(),
        bankId,
        secrets: {
            ...settings,
            encryptionPrivateKey: settings.encryptionPrivateKey!,
            signaturePrivateKey: settings.signaturePrivateKey!,
            generatedId: nanoid(10),
            generatedSecrets,
            dealerCode: settings.dealerCode,
        },
    };

    await collections.settings.insertOne(setting);

    return setting;
};

export const updateEazyInsurerIntegrationSetting = async (
    insurerId: ObjectId,
    settings: EazyInsurerIntegrationSetting['secrets']
) => {
    const { collections } = await getDatabaseContext();

    return collections.settings.findOneAndUpdate(
        { settingId: SettingId.EazyInsurerIntegration, insurerId },
        {
            $set: {
                settingId: SettingId.EazyInsurerIntegration,
                insurerId,
                secrets: settings,
                date: new Date(),
            },
        },
        { upsert: true, returnDocument: ReturnDocument.AFTER }
    );
};

export const updateTradeInSetting = async (
    tradeInModuleId: ObjectId,
    secrets: Pick<TradeInSetting['secrets'], 'baseUrl' | 'clientId' | 'clientSecret'>
) => {
    const { collections } = await getDatabaseContext();

    await collections.settings.updateOne(
        { settingId: SettingId.TradeIn, moduleId: tradeInModuleId },
        {
            $set: {
                settingId: SettingId.TradeIn,
                moduleId: tradeInModuleId,
                date: new Date(),
                secrets,
            },
        },
        { upsert: true }
    );
};

export const updatePorscheRetainModuleIntegrationSetting = async (
    moduleId: ObjectId,
    settings: GraphQLPorscheRetainModuleSettings,
    upsert: boolean
) => {
    const { collections } = await getDatabaseContext();

    if (upsert) {
        const updated = await collections.settings.findOneAndUpdate(
            {
                settingId: SettingId.porscheRetainModuleIntegration,
                moduleId,
            },
            {
                $set: {
                    'secrets.notificationUrl': settings.notificationUrl,
                    'secrets.publicCertRetain': settings.publicCertRetain,
                    date: new Date(),
                },
            },
            { returnDocument: ReturnDocument.AFTER }
        );

        return updated._id;
    }

    const keyPair = crypto.generateKeyPairSync('rsa', {
        modulusLength: 2048,
        publicKeyEncoding: { type: 'spki', format: 'pem' },
        privateKeyEncoding: { type: 'pkcs8', format: 'pem' },
    });

    const setting: PorscheRetainModuleIntegrationSetting = {
        _id: new ObjectId(),
        settingId: SettingId.porscheRetainModuleIntegration,
        date: new Date(),
        moduleId,
        secrets: {
            notificationUrl: settings.notificationUrl,
            publicCertRetain: settings.publicCertRetain,
            privateKey: keyPair.privateKey,
            publicKey: keyPair.publicKey,
        },
    };

    await collections.settings.insertOne(setting);

    return setting._id;
};
