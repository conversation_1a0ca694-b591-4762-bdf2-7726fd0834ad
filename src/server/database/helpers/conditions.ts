import { isBoolean, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { hasPaymentScenario } from '../../journeys/common/helpers';
import createLoaders, { Loaders } from '../../loaders';
import type { GiftVoucherResults } from '../../utils/excel/giftVouchers/types/GiftVoucherResults';
import {
    Application,
    ApplicationKind,
    Condition,
    ConditionType,
    ConsentsAndDeclarations,
    ConsentsAndDeclarationsType,
    CustomerKind,
    GiftVoucher,
    Lead,
    LeadStatus,
    MobilityBookingLocationType,
    ModuleType,
    SalesOfferAgreementKind,
    UseMyinfo,
} from '../documents';

export type ConditionContext = {
    bankId?: ObjectId;
    dealerId?: ObjectId;
    insurerId?: ObjectId;
    applicationModuleId?: ObjectId;
    giftVoucherModuleId?: ObjectId;
    isApplyingForFinancing?: boolean;
    isApplyingForInsurance?: boolean;
    customerKind: 'guarantor' | 'local' | 'corporate';
    hasPayment?: boolean;
    testDrive?: boolean;
    showroomVisit?: boolean;
    tradeIn?: boolean;
    useMyinfo?: UseMyinfo;
    locationId?: ObjectId;
    isHomeDelivery?: boolean;
    testDriveProcess?: boolean;
    isCapQualification?: boolean; // Add this new property
    salesOfferAgreementFeature?: SalesOfferAgreementKind;
};

export const isMatchingCondition = (condition: Condition, context: ConditionContext) => {
    switch (condition.type) {
        case ConditionType.And:
            return condition.children.every(child => isMatchingCondition(child, context));

        case ConditionType.Or:
            return condition.children.some(child => isMatchingCondition(child, context));

        case ConditionType.IsApplicationModule:
            return !!context.applicationModuleId && condition.moduleId.equals(context.applicationModuleId);

        case ConditionType.IsBank:
            return !!context.bankId && condition.bankId.equals(context.bankId);

        case ConditionType.IsApplyingForFinancing:
            return !!context.isApplyingForFinancing;

        case ConditionType.IsApplyingForInsurance:
            return !!context.isApplyingForInsurance && condition.type === ConditionType.IsApplyingForInsurance;

        case ConditionType.IsApplicant:
            return context.customerKind === 'local';

        case ConditionType.IsGuarantor:
            return context.customerKind === 'guarantor';

        case ConditionType.IsCorporate:
            return context.customerKind === 'corporate';

        case ConditionType.IsDealer:
            return !!context.dealerId && condition.dealerId.equals(context.dealerId);

        case ConditionType.IsInsurer:
            return !!context.insurerId && condition.insurerId.equals(context.insurerId);

        case ConditionType.IsTradeIn:
            return !!context.tradeIn;

        case ConditionType.IsTestDrive:
            return !!context.testDrive;

        case ConditionType.IsTestDriveProcess:
            return context.testDriveProcess;

        case ConditionType.IsShowroomVisit:
            return !!context.showroomVisit;

        case ConditionType.isLocation: {
            return !!context.locationId && condition.locationId.equals(context.locationId);
        }

        case ConditionType.IsGiftVoucher: {
            return !!context.giftVoucherModuleId && condition.moduleId.equals(context.giftVoucherModuleId);
        }

        case ConditionType.WithoutMyinfo: {
            if (isNil(context.useMyinfo)) {
                return false;
            }

            return (
                (context.customerKind === 'local' && !context.useMyinfo.customer) ||
                (context.customerKind === 'guarantor' && !context.useMyinfo.guarantor)
            );
        }

        case ConditionType.WithMyinfo: {
            if (isNil(context.useMyinfo)) {
                return false;
            }

            return (
                (context.customerKind === 'local' && context.useMyinfo.customer) ||
                (context.customerKind === 'guarantor' && context.useMyinfo.guarantor)
            );
        }

        case ConditionType.ForCapQualification:
            return !!context.isCapQualification;

        case ConditionType.SalesOfferAgreements: {
            return !!context.salesOfferAgreementFeature && context.salesOfferAgreementFeature === condition.feature;
        }

        default:
            throw new Error('Condition type not implemented yet');
    }
};

// empty means always
export const isMatchingConditions = (conditions: Condition[], context: ConditionContext) =>
    conditions.length === 0 || conditions.every(condition => isMatchingCondition(condition, context));

export const hasTestDriveProcess = async (appointmentModuleId: ObjectId, loader: Loaders) => {
    if (!appointmentModuleId) {
        return false;
    }

    const appointmentModule = await loader.moduleById.load(appointmentModuleId);
    if (!appointmentModule || appointmentModule._type !== ModuleType.AppointmentModule) {
        return false;
    }

    return appointmentModule.hasTestDriveProcess;
};

export const hasTestDriveProcessForApplication = async (
    appointmentModuleId: ObjectId,
    loader: Loaders,
    applicationSuiteId: ObjectId
) => {
    const journey = await loader.applicationJourneyBySuiteId.load(applicationSuiteId);
    const application = await loader.applicationBySuiteId.load(applicationSuiteId);

    if (!appointmentModuleId) {
        return false;
    }

    const appointmentModule = await loader.moduleById.load(appointmentModuleId);
    if (!appointmentModule || appointmentModule._type !== ModuleType.AppointmentModule) {
        return false;
    }

    // launchpad create appointment shows test drive process immediately
    // this skips the part on test drive process pages
    if (application?.kind === ApplicationKind.Launchpad) {
        return appointmentModule.hasTestDriveProcess;
    }

    return (
        appointmentModule.hasTestDriveProcess &&
        !isNil(journey?.isTestDriveProcessStarted) &&
        isBoolean(journey?.isTestDriveProcessStarted) &&
        journey?.isTestDriveProcessStarted
    );
};

export const hasTestDriveProcessCondition = (conditions: Condition[]) =>
    conditions.some(condition =>
        condition.type === ConditionType.Or
            ? condition.children.some(child => child.type === ConditionType.IsTestDriveProcess)
            : condition.type === ConditionType.IsTestDriveProcess
    );

export const hasTestDriveProcessOrTestDriveCondition = (conditions: Condition[]) =>
    conditions.some(condition =>
        condition.type === ConditionType.Or
            ? condition.children.some(
                  child => child.type === ConditionType.IsTestDriveProcess || child.type === ConditionType.IsTestDrive
              )
            : condition.type === ConditionType.IsTestDriveProcess || condition.type === ConditionType.IsTestDrive
    );

export const hasShowroomVisitCondition = (conditions: Condition[]) =>
    conditions.some(condition =>
        condition.type === ConditionType.Or
            ? condition.children.some(child => child.type === ConditionType.IsShowroomVisit)
            : condition.type === ConditionType.IsShowroomVisit
    );

export const hasCapQualificationCondition = (conditions: Condition[]) =>
    conditions.some(condition =>
        condition.type === ConditionType.Or
            ? condition.children.some(child => child.type === ConditionType.ForCapQualification)
            : condition.type === ConditionType.ForCapQualification
    );

export const buildConditionContextFromApplication = async (
    application: Application,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
): Promise<ConditionContext> => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder: {
            const testDriveProcess = await hasTestDriveProcessForApplication(
                application.appointmentStage?.appointmentModuleId,
                loaders,
                application._versioning.suiteId
            );

            return {
                bankId: application.bankId,
                applicationModuleId: application.moduleId,
                isApplyingForFinancing: application.configuration.withFinancing,
                customerKind,
                hasPayment: hasPaymentScenario(application.scenarios),
                isApplyingForInsurance: application.configuration.withInsurance,
                insurerId: application.insurancing?.insurerId,
                testDrive: application.configuration.testDrive,
                tradeIn: application.configuration.tradeIn,
                dealerId: application.dealerId,
                useMyinfo: application.useMyinfo,
                testDriveProcess,
            };
        }

        case ApplicationKind.Launchpad: {
            const testDriveProcess = await hasTestDriveProcessForApplication(
                application.appointmentStage?.appointmentModuleId,
                loaders,
                application._versioning.suiteId
            );

            return {
                applicationModuleId: application.moduleId,
                customerKind,
                testDrive: application.configuration.testDrive,
                showroomVisit: application.configuration.visitAppointment,
                dealerId: application.dealerId,
                testDriveProcess,
            };
        }

        case ApplicationKind.Event: {
            const event = await loaders.eventById.load(application.eventId);
            const testDriveProcess = await hasTestDriveProcessForApplication(
                application.appointmentStage?.appointmentModuleId,
                loaders,
                application._versioning.suiteId
            );

            return {
                applicationModuleId: application.moduleId,
                customerKind,
                hasPayment: hasPaymentScenario(event.scenarios),
                testDrive: application.configuration.testDrive,
                showroomVisit: application.configuration.visitAppointment,
                tradeIn: application.configuration.tradeIn,
                dealerId: application.dealerId,
                testDriveProcess,
            };
        }

        case ApplicationKind.Mobility: {
            return {
                applicationModuleId: application.moduleId,
                customerKind,
                hasPayment: hasPaymentScenario(application.scenarios),
                isHomeDelivery: application.mobilityBookingDetails.location._type === MobilityBookingLocationType.Home,
                locationId: application.mobilityBookingDetails.location._id,
            };
        }

        case ApplicationKind.SalesOffer: {
            return {
                bankId: application.bankId,
                applicationModuleId: application.moduleId,
                isApplyingForFinancing: application.configuration.withFinancing,
                customerKind,
                isApplyingForInsurance: application.configuration.withInsurance,
                insurerId: application.insurancing?.insurerId,
                dealerId: application.dealerId,
            };
        }

        default:
            return { customerKind };
    }
};

export const buildConditionContextFromGiftVoucher = async (
    giftVoucher: GiftVoucher | GiftVoucherResults,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
): Promise<ConditionContext> => {
    const module = await loaders.moduleById.load(giftVoucher.moduleId);

    if (!module || module._type !== ModuleType.GiftVoucherModule) {
        throw new Error('Gift voucher has an invalid module');
    }

    return {
        hasPayment: !isNil(module.paymentSettingsId),
        customerKind,
        giftVoucherModuleId: giftVoucher.moduleId,
    };
};

export const buildConditionContextFromLead = (lead: Lead): ConditionContext => ({
    applicationModuleId: lead.moduleId,
    customerKind: CustomerKind.Local,
    /** KYC enabled for qualify should be displayed when qualify action has been performed on a contact */
    isCapQualification:
        lead.isLead ||
        [
            LeadStatus.SubmissionFailed,
            LeadStatus.SubmittedWithError,
            LeadStatus.SubmittingToCap,
            LeadStatus.SubmittedToCap,
        ].includes(lead.status),
});

export const getDefaultAgreementValues = (agreements: ConsentsAndDeclarations[]) =>
    agreements.map(item => {
        switch (item._type) {
            case ConsentsAndDeclarationsType.Text:
                return {
                    ...item,
                    isAgreed: true, // Text consent agreed by default
                };

            case ConsentsAndDeclarationsType.Checkbox:
                return {
                    ...item,
                    isAgreed: false,
                };

            case ConsentsAndDeclarationsType.Marketing:
                return {
                    ...item,
                    isAgreed: false,
                    platformsAgreed: {
                        email: false,
                        fax: false,
                        mail: false,
                        phone: false,
                        sms: false,
                    },
                };

            default:
                throw new Error('Application agreement type not supported');
        }
    });
