import { pick, omit } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getAdvancedVersioningBySystemForUpdate } from '../../utils/versioning';
import { Application } from '../documents/Applications';
import { ApplicationKind } from '../documents/Applications/core';
import { Company } from '../documents/Company';
import { Customer } from '../documents/Customer';
import { ConfiguratorLead, EventLead, LaunchpadLead, Lead, LeadCore, LeadStatus } from '../documents/Lead';
import { AuthorKind } from '../documents/Versioning';
import { CounterMethod } from '../documents/moduleShared';
import getDatabaseContext from '../getDatabaseContext';
import { increaseCompanyCounter, parseCounterPrefix } from './counters';

export const isLeadType = (lead: Lead) => !!(lead.capValues?.businessPartnerGuid && lead.capValues?.leadGuid);

/**
 * Merges source lead to target lead and reassigning the application to the target lead.
 *
 * @param {Lead} sourceLead - The lead that will be merged into the target lead.
 * @param {Lead} targetLead - The lead that will remain after the merge.
 * @returns {Promise<void>} A promise that resolves when the merge operation is complete.
 */
export const mergeLeads = async (sourceLead: Lead, targetLead: Lead): Promise<Lead> => {
    // TODO: review merge logic
    const { collections } = await getDatabaseContext();

    const [sourceCustomer, targetCustomer] = await Promise.all([
        collections.customers.findOne({ _id: sourceLead.customerId }),
        collections.customers.findOne({ _id: targetLead.customerId }),
    ]);
    const mergedCustomer: Customer = {
        // initial fields from target
        ...targetCustomer,

        // override source to target
        ...pick(['fields', 'kycPresetIds'], sourceCustomer),
        customerCiamId: sourceCustomer.customerCiamId || targetCustomer.customerCiamId,
        _id: new ObjectId(),
        _versioning: {
            updatedAt: new Date(),
            updatedBy: { kind: AuthorKind.System },
            // since we are merging source to target,
            // we retain the history of the target customer
            ...pick(['suiteId', 'createdAt', 'createdBy'], targetCustomer._versioning),
            isLatest: true,
        },
    };

    await collections.customers.insertOne(mergedCustomer);
    await collections.customers.updateOne({ _id: targetLead.customerId }, { $set: { '_versioning.isLatest': false } });
    await collections.customers.updateOne(
        { _id: sourceCustomer._id },
        // copy reference so we know where the customer was merged to
        { $set: { '_versioning.isLatest': false, mergedToCustomerSuiteId: targetCustomer._versioning.suiteId } }
    );

    await collections.leads.updateOne(
        { _id: sourceLead._id },
        {
            $set: {
                // copy reference so we know where the lead was merged to
                mergedToLeadSuiteId: targetLead._versioning.suiteId,
                '_versioning.isLatest': false,
                ...getAdvancedVersioningBySystemForUpdate(),
            },
        }
    );

    const baseLead: LeadCore<ApplicationKind> = {
        // copy everything from target except the fields that will be conditionally added depending on type
        ...omit(
            [
                'mergedToLeadSuiteId',
                // all types
                'vehicleId',
                // configurator
                'configuratorId',
                'configuratorBlocks',
                // event
                'eventId',
                // launchpad
                'vehicleCondition',
                'purchaseIntention',
            ],
            targetLead
        ),

        // copy everything from source lead to override target
        // ----- Fields overridden/added from sourceLead -----
        ...sourceLead,
        assigneeId: targetLead.assigneeId,
        identifier: targetLead.identifier,

        // join these together
        documents: [...sourceLead.documents, ...targetLead.documents],
        // since this is merged, this is always false
        isDraft: false,

        // create new for the following:
        _id: new ObjectId(),
        customerId: mergedCustomer._id,
        status: LeadStatus.SubmittedToCap,
        isLead: true,
        _versioning: {
            // we retain the history from target lead
            ...targetLead._versioning,
            isLatest: true,
            updatedAt: new Date(),
            updatedBy: { kind: AuthorKind.System },
        },
    };

    const leadTypeSpecificFields: Partial<Lead> = (() => {
        switch (sourceLead.kind) {
            case ApplicationKind.Configurator:
                return {
                    vehicleId: sourceLead.vehicleId,
                    configuratorId: (sourceLead as ConfiguratorLead).configuratorId,
                    configuratorBlocks: (sourceLead as ConfiguratorLead).configuratorBlocks,
                };
            case ApplicationKind.Event:
                return {
                    vehicleId: sourceLead.vehicleId,
                    eventId: (sourceLead as EventLead).eventId,
                };
            case ApplicationKind.Launchpad:
                return {
                    vehicleId: sourceLead.vehicleId ?? targetLead.vehicleId,
                    vehicleCondition: (sourceLead as LaunchpadLead).vehicleCondition,
                    purchaseIntention: (sourceLead as LaunchpadLead).purchaseIntention,
                };
            case ApplicationKind.Mobility:
            case ApplicationKind.Finder:
            case ApplicationKind.Standard:
                return {
                    vehicleId: sourceLead.vehicleId,
                };
            default:
                return {};
        }
    })();

    const mergedLead: Lead = { ...baseLead, ...leadTypeSpecificFields } as Lead;

    await collections.leads.insertOne(mergedLead);
    // outdated the target lead
    await collections.leads.updateOne({ _id: targetLead._id }, { $set: { '_versioning.isLatest': false } });
    // outdated source, and update the mergedToLeadSuiteId
    await collections.leads.updateOne(
        { _id: sourceLead._id },
        { $set: { '_versioning.isLatest': false, mergedToLeadSuiteId: targetLead._versioning.suiteId } }
    );

    // find all applications under the source suite, and assign them the applications
    const leadIds = await collections.leads
        .find({
            '_versioning.suiteId': {
                $in: [sourceLead._versioning.suiteId, targetLead._versioning.suiteId],
            },
        })
        .map(l => l._id)
        .toArray();

    // find all those applications and update them to point to the merged lead
    // Step 1: Retrieve all applications associated with the source lead's suite ID and are the latest version
    const applications = await collections.applications
        .find({ leadId: { $in: leadIds }, '_versioning.isLatest': true })
        .toArray();

    // Step 2: Update the `_versioning.isLatest` field to `false` for these applications
    await collections.applications.updateMany(
        { leadId: { $in: leadIds } },
        { $set: { '_versioning.isLatest': false } }
    );

    // Step 3: Create a new version of each application, associating it with the merged lead
    const newApplications: Application[] = applications.map(application => ({
        ...application,
        _id: new ObjectId(),
        leadId: mergedLead._id,
        _versioning: {
            ...application._versioning,
            isLatest: true,
            updatedAt: new Date(),
            updatedBy: { kind: AuthorKind.System },
        },
    }));

    await collections.applications.insertMany(newApplications);

    return mergedLead;
};

export const getExistingLeadWithSameCapValues = async (lead: Lead): Promise<Lead | null> => {
    const { collections } = await getDatabaseContext();

    if (!lead.capValues?.businessPartnerGuid || !lead.capValues?.leadGuid) {
        return null;
    }

    return collections.leads.findOne({
        'capValues.businessPartnerGuid': lead.capValues.businessPartnerGuid,
        'capValues.leadGuid': lead.capValues.leadGuid,
        '_versioning.isLatest': true,
        isDraft: false,
        _id: { $ne: lead._id },
    });
};

export const getLeadIdentifier = async (company: Company) => {
    // TO DO: Later need to check wheter it's Contact/Lead or Appointment.
    // Prefix is hardcoded based on VF-609. Prefix: Contact/Lead: L{YY}{MM}; Appointment: P{YY}{MM}
    const prefix = 'L{YY}{MM}';

    // For counter, it'll be hardcoded following VF-609 requirement. Method: Monthly
    const method = CounterMethod.Monthly;

    // For padding, it'll be hardcoded following VF-609 requirement. Padding: Monthly
    const padding = 4;

    // get the index on the counter
    // the index is based on the raw prefix to ensure global appliance
    const index = await increaseCompanyCounter(company._id, prefix, method, company.timeZone);

    return [
        // first part is the computed prefix
        parseCounterPrefix(prefix, company.timeZone),
        // second part is the allocated index with leading zeros
        index.toString().padStart(padding + 1, '0'),
    ].join('');
};

export const createNewLeadVersion = async (lead: Lead): Promise<Lead> => {
    const { collections } = await getDatabaseContext();

    // outdate all existing versions, there should only be one which is updated
    await collections.leads.bulkWrite([
        {
            updateMany: {
                filter: { '_versioning.suiteId': lead._versioning.suiteId },
                update: { $set: { '_versioning.isLatest': false } },
            },
        },
        { insertOne: { document: lead } },
    ]);

    return lead;
};
