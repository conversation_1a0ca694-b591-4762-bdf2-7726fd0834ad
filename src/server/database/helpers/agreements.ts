import { head, isNil } from 'lodash/fp';
import createLoaders from '../../loaders';
import {
    Application,
    ApplicationJourneyAgreements,
    ConsentFeatureType,
    ConsentsAndDeclarations,
    ConsentsAndDeclarationsPurpose,
    ConsentsAndDeclarationsType,
    FinanceProduct,
    InsuranceProduct,
    Module,
    ModuleType,
    SalesOffer,
    SalesOfferAgreementKind,
    SalesOfferFeatureKind,
    SalesOfferModule,
} from '../documents';
import getDatabaseContext from '../getDatabaseContext';
import { buildConditionContextFromApplication, ConditionContext, isMatchingConditions } from './conditions';

export const retrieveInitialAgreements = (item: ConsentsAndDeclarations) => {
    switch (item._type) {
        case ConsentsAndDeclarationsType.Text:
            return {
                consentId: item._id,
                isAgreed: false,
                date: new Date(),
                purpose: item.purpose,
            };

        case ConsentsAndDeclarationsType.Checkbox:
            return {
                consentId: item._id,
                isAgreed: false,
                date: new Date(),
                purpose: item.purpose,
                isMandatory: item.isMandatory,
            };

        case ConsentsAndDeclarationsType.Marketing:
            return {
                consentId: item._id,
                isAgreed: false,
                date: new Date(),
                purpose: item.purpose,
                isMandatory: item.isMandatory,
                platformsAgreed: {
                    email: false,
                    fax: false,
                    mail: false,
                    phone: false,
                    sms: false,
                },
            };

        default:
            throw new Error('Application agreement type is not supported');
    }
};

const getAgreementModuleIdFromApplicationModule = (applicationModule: Module) => {
    switch (applicationModule._type) {
        case ModuleType.StandardApplicationModule:
        case ModuleType.EventApplicationModule:
        case ModuleType.ConfiguratorModule:
            return applicationModule.agreementsModuleId;

        default:
            // either not supported or not yet implemented
            return null;
    }
};

const getAgreementsForApplication = async (
    application: Application,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
): Promise<ConsentsAndDeclarations[]> => {
    const { collections } = await getDatabaseContext();

    // get the application module
    const applicationModule = await loaders.moduleById.load(application.moduleId);

    if (!applicationModule) {
        // should never happen
        throw new Error('Application module not found');
    }

    // get the agreement module ID
    const agreementsModuleId = getAgreementModuleIdFromApplicationModule(applicationModule);

    if (!agreementsModuleId) {
        // without module there's no agreement
        return [];
    }

    // get all latest agreements for the module
    const agreements = await collections.consentsAndDeclarations
        .find({
            moduleId: agreementsModuleId,
            isActive: true,
            isDeleted: false,
            '_versioning.isLatest': true,
        })
        .sort({ orderNumber: 1 })
        .toArray();

    const conditionContext = await buildConditionContextFromApplication(application, customerKind);

    return agreements.filter(agreement => {
        if (
            !conditionContext.hasPayment &&
            agreement.purpose.length === 1 &&
            head(agreement.purpose) === ConsentsAndDeclarationsPurpose.Payment
        ) {
            // payment agreements are not requested when there's no payment
            return false;
        }

        return isMatchingConditions(agreement.conditions, conditionContext);
    });
};

export type ApplicationAgreements = ApplicationJourneyAgreements['agreements'];

export const getInitialAgreementsForApplication = async (
    application: Application,
    customerKind: ConditionContext['customerKind']
): Promise<ApplicationAgreements> => {
    const agreements = await getAgreementsForApplication(application, customerKind);

    return agreements.map(item => retrieveInitialAgreements(item));
};

export const updateAgreementsForApplication = async (
    application: Application,
    customerKind: ConditionContext['customerKind'],
    loaders = createLoaders()
): Promise<ApplicationJourneyAgreements['agreements']> => {
    const journey = await loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId);

    const initialAgreements = ((): ApplicationJourneyAgreements['agreements'] => {
        if (!journey) {
            return [];
        }

        switch (customerKind) {
            case 'local':
                return journey.applicantAgreements?.agreements || [];

            case 'guarantor':
            default:
                return [];
        }
    })();

    // list suite IDs of agreements already there to bot get duplicates later on
    const agreementsPreviouslyProvided = await Promise.all(
        initialAgreements.map(async agreement => {
            // get the exact agreement version
            const agreementDocument = await loaders.consentById.load(agreement.consentId);

            return agreementDocument._versioning.suiteId;
        })
    );

    // we want to know all the agreements expected now
    const expectedAgreements = await getAgreementsForApplication(application, customerKind, loaders);

    const checkIsMandatory = (agreement: ConsentsAndDeclarations) => {
        switch (agreement._type) {
            case ConsentsAndDeclarationsType.Text:
                return null;

            case ConsentsAndDeclarationsType.Checkbox:
            case ConsentsAndDeclarationsType.Marketing:
                return agreement.isMandatory;

            default:
                throw new Error('Agreement type not supported');
        }
    };

    return expectedAgreements.reduce((acc, agreement) => {
        if (agreementsPreviouslyProvided.some(item => item.equals(agreement._versioning.suiteId))) {
            // already there
            return acc;
        }

        return [
            ...acc,
            {
                consentId: agreement._id,
                isAgreed: false,
                date: new Date(),
                purpose: agreement.purpose,
                isMandatory: checkIsMandatory(agreement),
            },
        ];
    }, initialAgreements);
};

export const buildConditionForSalesOffer = (
    salesOffer: SalesOffer,
    salesOfferModule: SalesOfferModule,
    feature: SalesOfferFeatureKind,
    financeProduct: FinanceProduct,
    insuranceProduct: InsuranceProduct,
    salesOfferAgreementFeature: SalesOfferAgreementKind | null = null
): ConditionContext => {
    switch (feature) {
        case SalesOfferFeatureKind.Finance: {
            const hasFinance = salesOffer?.finance.isEnabled && !!salesOffer?.finance?.finance;
            if (hasFinance) {
                return {
                    customerKind: 'local',
                    bankId: financeProduct.bankId,
                    isApplyingForFinancing: true,
                };
            }

            return {
                customerKind: 'local',
            };
        }

        case SalesOfferFeatureKind.Insurance: {
            const hasInsurance = salesOffer?.insurance.isEnabled && !!salesOffer?.insurance?.insurance;
            if (hasInsurance) {
                return {
                    customerKind: 'local',
                    insurerId: insuranceProduct.insurerId,
                    isApplyingForInsurance: true,
                };
            }

            return {
                customerKind: 'local',
            };
        }

        case SalesOfferFeatureKind.MainDetails:
        case SalesOfferFeatureKind.Vehicle:
        case SalesOfferFeatureKind.VSA: {
            return {
                customerKind: 'local',
                applicationModuleId: salesOfferModule._id,
                ...(!isNil(salesOfferAgreementFeature) && { salesOfferAgreementFeature }),
            };
        }

        default:
            return {
                customerKind: 'local',
            };
    }
};

const getAgreementsForSalesOffer = async (
    salesOffer: SalesOffer,
    feature: SalesOfferFeatureKind,
    customerKind: ConditionContext['customerKind'],
    salesOfferAgreementFeature: SalesOfferAgreementKind = null,
    loaders = createLoaders()
): Promise<ConsentsAndDeclarations[]> => {
    const { collections } = await getDatabaseContext();

    const lead = await loaders.leadBySuiteId.load(salesOffer.leadSuiteId);

    if (!lead) {
        // should never happen
        throw new Error('Lead not found');
    }
    const launchpadModule = await loaders.moduleById.load(lead.moduleId);
    if (isNil(launchpadModule) || launchpadModule._type !== ModuleType.LaunchPadModule) {
        // should never happen
        throw new Error('Launchpad module not found');
    }

    const financeProductId = salesOffer.finance?.finance?.financeProductId;
    const insuranceProductId = salesOffer.insurance?.insurance?.insuranceProductId;
    const [financeProduct, insuranceProduct, salesOfferModule] = await Promise.all([
        financeProductId ? loaders.financeProductById.load(financeProductId) : null,
        insuranceProductId ? loaders.insuranceProductById.load(insuranceProductId) : null,
        loaders.moduleById.load(salesOffer.moduleId),
    ]);

    // get the application module
    const module = await loaders.moduleById.load(salesOffer.moduleId);

    if (!module) {
        // should never happen
        throw new Error('Application module not found');
    }

    const { agreementsModuleId } = launchpadModule;
    if (!agreementsModuleId) {
        // without module there's no agreement
        return [];
    }

    // get all latest agreements for the module
    const agreements = await collections.consentsAndDeclarations
        .find({
            moduleId: agreementsModuleId,
            isActive: true,
            isDeleted: false,
            '_versioning.isLatest': true,
            'featurePurpose.type': ConsentFeatureType.Module,
            'featurePurpose.featureId': agreementsModuleId,
        })
        .sort({ orderNumber: 1 })
        .toArray();

    const conditionContext = buildConditionForSalesOffer(
        salesOffer,
        salesOfferModule as SalesOfferModule,
        feature,
        financeProduct as FinanceProduct,
        insuranceProduct as InsuranceProduct,
        salesOfferAgreementFeature
    );

    return agreements.filter(agreement => {
        if (
            !conditionContext.hasPayment &&
            agreement.purpose.length === 1 &&
            head(agreement.purpose) === ConsentsAndDeclarationsPurpose.Payment
        ) {
            // payment agreements are not requested when there's no payment
            return false;
        }

        return isMatchingConditions(agreement.conditions, conditionContext);
    });
};

export const getInitialAgreementsForSalesOffer = async (
    salesOffer: SalesOffer,
    feature: SalesOfferFeatureKind,
    customerKind: ConditionContext['customerKind'],
    salesOfferAgreementFeature: SalesOfferAgreementKind = null
): Promise<ApplicationAgreements> => {
    const agreements = await getAgreementsForSalesOffer(salesOffer, feature, customerKind, salesOfferAgreementFeature);

    return agreements.map(item => retrieveInitialAgreements(item));
};
