export enum SalesOfferFeatureKind {
    Finance = 'finance',
    Insurance = 'insurance',
    MainDetails = 'mainDetails',
    Vehicle = 'vehicle',
    Deposit = 'deposit',
    TradeIn = 'tradeIn',
    VSA = 'vsa',
}

export enum SalesOfferFeatureStatus {
    Updated = 'updated',
    PendingManager = 'pendingManager',
    PendingCustomer = 'pendingCustomer',
    PaymentCompleted = 'paymentCompleted',
    Signed = 'signed',
}

export enum SalesOfferDocumentKind {
    Finance = 'finance',
    Insurance = 'insurance',
    MainDetails = 'mainDetails',
    Vehicle = 'vehicle',
    Deposit = 'deposit',
    TradeIn = 'tradeIn',
    VSA = 'vsa',
    Others = 'others',
}

export enum SalesOfferDocumentStatus {
    Signed = 'signed',
    Expired = 'expired',
    PendingCustomer = 'pendingCustomer',
}

export enum SalesOfferDepositMethod {
    Online = 'online',
    Offline = 'offline',
}
