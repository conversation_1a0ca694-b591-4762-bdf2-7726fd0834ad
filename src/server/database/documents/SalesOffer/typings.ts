// eslint-disable-next-line max-len
import { ObjectId } from 'mongodb';
// eslint-disable-next-line max-len
// eslint-disable-next-line max-len
import { CustomerSpecifiedVehicleByConfigurationQuery } from '../../../integrations/porscheVehicleData/graphql/api.graphql';
import { ApplicationFinancing, ApplicationInsurancing } from '../Applications';
import { UploadedFileWithPreview } from '../UploadedFile';
import {
    SalesOfferDepositMethod,
    SalesOfferDocumentKind,
    SalesOfferFeatureKind,
    SalesOfferFeatureStatus,
} from './enums';

export type SalesOfferFeatureConfigurationCore<
    FeatureKind extends SalesOfferFeatureKind,
    AgreementKind extends SalesOfferDocumentKind,
> = {
    lastUpdatedAt: Date;
    kind: FeatureKind;
    status: SalesOfferFeatureStatus;
    documents: SalesOfferDocument<AgreementKind>[];

    // determine whether this feature is turning on in the CI
    isEnabled: boolean;
};

export type SalesOfferDocument<Kind extends SalesOfferDocumentKind> = UploadedFileWithPreview & {
    kind: Kind;
    lastUpdatedAt: Date;
    status: SalesOfferFeatureStatus;
};

type FittedOptions = {
    label: string;
    price: number;
};

export type FinanceSalesOffer = SalesOfferFeatureConfigurationCore<
    SalesOfferFeatureKind.Finance,
    SalesOfferDocumentKind.Finance
> & {
    finance: ApplicationFinancing;
};
export type InsuranceSalesOffer = SalesOfferFeatureConfigurationCore<
    SalesOfferFeatureKind.Insurance,
    SalesOfferDocumentKind.Insurance
> & {
    insurance: ApplicationInsurancing;
};
export type TradeInSalesOffer = SalesOfferFeatureConfigurationCore<
    SalesOfferFeatureKind.TradeIn,
    SalesOfferDocumentKind.TradeIn
>;

export type VehicleSalesOffer = SalesOfferFeatureConfigurationCore<
    SalesOfferFeatureKind.Vehicle,
    SalesOfferDocumentKind.Vehicle
> & {
    // Commission Number
    commissionNumber?: string;
    // Chassis Number
    chassisNumber?: string;
    // Engine / Motor Number
    engineMotorNumber?: string;

    // vehicle miscenlaneous add-ons
    localFittedOptions: FittedOptions[];

    orderTypeCode: string;

    // this will be LocalVariant after mapped from  <> `LocalVariant.Identifier`
    vehicleId: ObjectId;

    resultAPI: CustomerSpecifiedVehicleByConfigurationQuery;
};

export enum CoeCategory {
    B = 'B',
    E = 'E',
}
export type MainDetailsSalesOffer = SalesOfferFeatureConfigurationCore<
    SalesOfferFeatureKind.MainDetails,
    SalesOfferDocumentKind.MainDetails
> & {
    optionsSubsidy: number;
    estimatedDeliveryDate?: Date;
    coeAmount?: number;
    coeCategory?: CoeCategory;
    numberConsecutiveBids?: number;
    remarks?: string;
};
export type DepositSalesOffer = SalesOfferFeatureConfigurationCore<
    SalesOfferFeatureKind.Deposit,
    SalesOfferDocumentKind.Deposit
> & {
    depositMethod: SalesOfferDepositMethod;
    depositAmount?: number;
};

export type VSASalesOffer = SalesOfferFeatureConfigurationCore<SalesOfferFeatureKind.VSA, SalesOfferDocumentKind.VSA>;
