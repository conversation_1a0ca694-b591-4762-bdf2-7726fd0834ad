import { ObjectId } from 'mongodb';

export enum ConditionType {
    And = 'and',
    Or = 'or',
    IsApplicationModule = 'isApplicationModule',
    IsBank = 'isBank',
    IsApplyingForFinancing = 'isApplyingForFinancing',
    IsApplyingForInsurance = 'isApplyingForInsurance',
    IsApplicant = 'isApplicant',
    IsGuarantor = 'isGuarantor',
    IsTestDrive = 'isTestDrive',
    IsShowroomVisit = 'isShowroomVisit',
    IsTradeIn = 'isTradeIn',
    IsDealer = 'isDealer',
    IsInsurer = 'isInsurer',
    IsCorporate = 'isCorporate',
    WithMyinfo = 'withMyinfo',
    WithoutMyinfo = 'withoutMyinfo',
    isLocation = 'isLocation',
    IsTestDriveProcess = 'isTestDriveProcess',
    IsGiftVoucher = 'isGiftVoucher',
    ForCapQualification = 'forCapQualification',
    SalesOfferAgreements = 'salesOfferAgreements',
}

export enum SalesOfferAgreementKind {
    COE = 'coe',
    Specification = 'specification',
    VSA = 'vsa',
}

export type LogicCondition = { type: ConditionType.And | ConditionType.Or; children: Condition[] };
export type ApplicationModuleCondition = { type: ConditionType.IsApplicationModule; moduleId: ObjectId };
export type BankCondition = { type: ConditionType.IsBank; bankId: ObjectId };
export type DealerCondition = { type: ConditionType.IsDealer; dealerId: ObjectId };
export type InsurerCondition = { type: ConditionType.IsInsurer; insurerId: ObjectId };
export type LocationCondition = { type: ConditionType.isLocation; locationId?: ObjectId; isHomeDelivery: boolean };
export type GiftVoucherCondition = { type: ConditionType.IsGiftVoucher; moduleId: ObjectId };
export type SalesOfferAgreementsCondition = {
    type: ConditionType.SalesOfferAgreements;
    feature: SalesOfferAgreementKind;
};
export type ContextualCondition = {
    type:
        | ConditionType.IsApplyingForFinancing
        | ConditionType.IsApplyingForInsurance
        | ConditionType.IsApplicant
        | ConditionType.IsGuarantor
        | ConditionType.IsCorporate
        | ConditionType.IsTestDrive
        | ConditionType.IsShowroomVisit
        | ConditionType.IsTradeIn
        | ConditionType.WithMyinfo
        | ConditionType.WithoutMyinfo
        | ConditionType.IsTestDriveProcess
        | ConditionType.ForCapQualification;
};

export type Condition =
    | LogicCondition
    | ApplicationModuleCondition
    | BankCondition
    | ContextualCondition
    | DealerCondition
    | InsurerCondition
    | LocationCondition
    | GiftVoucherCondition
    | SalesOfferAgreementsCondition;
