import { LeadStatus } from './shared';

/** Statuses that indicates a lead is a contact */
export const ContactStatuses: ReadonlyArray<LeadStatus> = [
    LeadStatus.PendingQualify,
    LeadStatus.Unqualified,
    LeadStatus.SubmissionFailed,
    LeadStatus.Drafted,
    LeadStatus.Shared,
    LeadStatus.SubmittingToCap,
    LeadStatus.Contacted,
] as const;

/** Statuses that indicates a lead is a lead */
export const LeadStatuses: ReadonlyArray<LeadStatus> = [
    LeadStatus.SubmittedWithError,
    LeadStatus.SubmittedToCap,
    LeadStatus.Lost,
    LeadStatus.Completed,
] as const;

export * from './shared';
export * from './kinds';
