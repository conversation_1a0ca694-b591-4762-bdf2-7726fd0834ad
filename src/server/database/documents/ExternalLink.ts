import { ObjectId } from 'mongodb';
import type { Origin } from '../../journeys/types';
import { ApplicationStage } from './Applications';
import { SalesOfferFeatureKind } from './SalesOffer';

export type ExternalLinkCore<TKind, TData> = {
    _id: ObjectId;
    _kind: TKind;
    expiresAt: Date;
    secret: string;
    data: TData;
    deleteOnFetch: boolean;
};

export enum ExternalLinkKind {
    ResetPassword = 'resetPassword',
    CreateNewUser = 'createNewUser',
    DownloadApplicationDocument = 'downloadApplicationDocument',
    DownloadLeadDocument = 'downloadLeadDocument',
    MyInfoCallback = 'myInfoCallback',
    NamirialSigning = 'namirialSigning',
    AdyenRedirection = 'adyenRedirection',
    ConfiguratorApplication = 'configuratorApplication',
    VerifyEmailUpdate = 'verifyEmailUpdate',
    StandardApplication = 'standardApplication',
    EventApplication = 'eventApplication',
    FinderApplication = 'finderApplication',
    ProceedWithCustomer = 'proceedWithCustomer',
    PorschePaymentRedirection = 'porschePaymentRedirection',
    MobilityApplicationAmendment = 'MobilityApplicationAmendment',
    MobilityApplicationCancellation = 'mobilityApplicationCancellation',
    FiservPaymentRedirection = 'fiservPaymentRedirection',
    PayGatePaymentRedirection = 'paygatePaymentRedirection',
    TtbPaymentRedirection = 'ttbPaymentRedirection',
    CTSFinderRedirection = 'ctsFinderRedirection',
    TestDriveProcessRedirection = 'testDriveProcessRedirection',
    TestDriveProcessNamirialRedirection = 'testDriveProcessNamirialRedirection',
    GiftVoucherAdyenRedirection = 'giftVoucherAdyenRedirection',
    GiftVoucherPorschePaymentRedirection = 'giftVoucherPorschePaymentRedirection',
    GiftVoucherFiservPaymentRedirection = 'giftVoucherFiservPaymentRedirection',
    GiftVoucherPayGatePaymentRedirection = 'giftVoucherPaygatePaymentRedirection',
    GiftVoucherTtbPaymentRedirection = 'GiftVoucherTtbPaymentRedirection',
    ApplyNewRedirection = 'applyNewRedirection',
    PorscheIdCallback = 'porshcheIdCallback',
    SendSalesOffer = 'sendSalesOffer',
}

export type StandardApplicationLink = ExternalLinkCore<
    ExternalLinkKind.StandardApplication,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type EventApplicationLink = ExternalLinkCore<
    ExternalLinkKind.EventApplication,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type FinderApplicationLink = ExternalLinkCore<
    ExternalLinkKind.FinderApplication,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type ConfiguratorApplicationLink = ExternalLinkCore<
    ExternalLinkKind.ConfiguratorApplication,
    {
        applicationId: ObjectId;
        applicationSuiteId: ObjectId;
        routerId: ObjectId;
        endpointId: ObjectId;
        modelConfiguratorId: ObjectId;
        variantConfiguratorId: ObjectId;
    }
>;

export type ResetPasswordLink = ExternalLinkCore<ExternalLinkKind.ResetPassword, { userId: ObjectId }>;

export type CreateNewUserLink = ExternalLinkCore<ExternalLinkKind.CreateNewUser, { userId: ObjectId }>;

export type VerifyEmailUpdateLink = ExternalLinkCore<
    ExternalLinkKind.VerifyEmailUpdate,
    { userId: ObjectId; email: string }
>;

export type AdyenRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.AdyenRedirection,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type PorschePaymentRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.PorschePaymentRedirection,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type FiservPaymentRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.FiservPaymentRedirection,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type PayGatePaymentRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.PayGatePaymentRedirection,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type TtbPaymentRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.TtbPaymentRedirection,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId; userId?: ObjectId }
>;

export type DownloadApplicationDocumentLink = ExternalLinkCore<
    ExternalLinkKind.DownloadApplicationDocument,
    {
        userId: ObjectId;
        applicationId: ObjectId;
        applicationSuiteId: ObjectId;
        documentId: ObjectId;
        encrypt: boolean;
        password?: string;
        stage?: ApplicationStage;
    }
>;

export type DownloadLeadDocumentLink = ExternalLinkCore<
    ExternalLinkKind.DownloadLeadDocument,
    {
        userId: ObjectId;
        leadId: ObjectId;
        leadSuiteId: ObjectId;
        documentId: ObjectId;
        encrypt: boolean;
        password?: string;
    }
>;

export type MyInfoCallbackLink = ExternalLinkCore<
    ExternalLinkKind.MyInfoCallback,
    { verifier: string; nonce: string; applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type NamirialSigningLink = ExternalLinkCore<
    ExternalLinkKind.NamirialSigning,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId; origin: Origin }
>;

export type ProceedWithCustomerLink = ExternalLinkCore<
    ExternalLinkKind.ProceedWithCustomer,
    { applicationId: ObjectId; applicationSuiteId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type MobilityApplicationAmendmentLink = ExternalLinkCore<
    ExternalLinkKind.MobilityApplicationAmendment,
    {
        userId?: ObjectId | null | undefined;
        applicationSuiteId: ObjectId;
    }
>;

export type MobilityApplicationCancellationLink = ExternalLinkCore<
    ExternalLinkKind.MobilityApplicationCancellation,
    { userId?: ObjectId | null | undefined; applicationSuiteId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type CTSFinderRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.CTSFinderRedirection,
    {
        applicationId: ObjectId;
        routerId: ObjectId;
        endpointId: ObjectId;
        vehicleId: string;
        type: string;
        settingId: ObjectId;
    }
>;

export type TestDriveProcessRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.TestDriveProcessRedirection,
    {
        applicationSuiteId: ObjectId;
        userId: ObjectId;
    }
>;

export type GiftVoucherAdyenRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.GiftVoucherAdyenRedirection,
    { giftVoucherId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type GiftVoucherPorschePaymentRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.GiftVoucherPorschePaymentRedirection,
    { giftVoucherId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type GiftVoucherFiservPaymentRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.GiftVoucherFiservPaymentRedirection,
    { giftVoucherId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type GiftVoucherPayGatePaymentRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.GiftVoucherPayGatePaymentRedirection,
    { giftVoucherId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type GiftVoucherTtbPaymentRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.GiftVoucherTtbPaymentRedirection,
    { giftVoucherId: ObjectId; routerId: ObjectId; endpointId: ObjectId }
>;

export type ApplyNewRedirectionLink = ExternalLinkCore<
    ExternalLinkKind.ApplyNewRedirection,
    {
        applicationId: ObjectId;
        routerId: ObjectId;
        endpointId: ObjectId;
    }
>;

export type PorscheIdCallbackLink = ExternalLinkCore<
    ExternalLinkKind.PorscheIdCallback,
    { applicationId: ObjectId; routerId: ObjectId; endpointId: ObjectId; verifier: string }
>;

export type SendSalesOfferLink = ExternalLinkCore<
    ExternalLinkKind.SendSalesOffer,
    {
        routerId: ObjectId;
        endpointId: ObjectId;
        featureKinds: SalesOfferFeatureKind[];
        salesOfferId: ObjectId;
        leadSuiteId: ObjectId;
    }
>;

export type ExternalLink =
    | ResetPasswordLink
    | CreateNewUserLink
    | DownloadApplicationDocumentLink
    | DownloadLeadDocumentLink
    | MyInfoCallbackLink
    | NamirialSigningLink
    | AdyenRedirectionLink
    | ConfiguratorApplicationLink
    | VerifyEmailUpdateLink
    | ProceedWithCustomerLink
    | StandardApplicationLink
    | EventApplicationLink
    | PorschePaymentRedirectionLink
    | MobilityApplicationCancellationLink
    | MobilityApplicationAmendmentLink
    | FiservPaymentRedirectionLink
    | FinderApplicationLink
    | PayGatePaymentRedirectionLink
    | TtbPaymentRedirectionLink
    | CTSFinderRedirectionLink
    | TestDriveProcessRedirectionLink
    | GiftVoucherAdyenRedirectionLink
    | GiftVoucherPorschePaymentRedirectionLink
    | GiftVoucherFiservPaymentRedirectionLink
    | GiftVoucherPayGatePaymentRedirectionLink
    | GiftVoucherTtbPaymentRedirectionLink
    | ApplyNewRedirectionLink
    | PorscheIdCallbackLink
    | SendSalesOfferLink;
