import { ObjectId } from 'mongodb';
import { ConsentsAndDeclarationsPurpose } from '../ConsentsAndDeclarations';

export type PlatformsAgreedJourney = {
    email: boolean;
    fax: boolean;
    mail: boolean;
    phone: boolean;
    sms: boolean;
};
export type AgreementsJourney = {
    /* ID of the consent and declaration document */
    consentId: ObjectId;
    /* was it agreed upon */
    isAgreed: boolean;
    /* is it mandatory */
    isMandatory?: boolean;
    /* Date at which it was agreed upon */
    date: Date;
    /* Purpose */
    purpose: ConsentsAndDeclarationsPurpose[];
    /* Agreed marketing platforms */
    platformsAgreed?: PlatformsAgreedJourney;
};
export type ApplicationJourneyAgreements = {
    /* Consent and declaration module ID */
    moduleId: ObjectId;

    /* list of agreements agreed upon by the user */
    agreements: Array<AgreementsJourney>;
};
