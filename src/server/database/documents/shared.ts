import { SimpleVersioning } from './Versioning';

export enum AmountUnit {
    Currency = 'currency',
    Percentage = 'percentage',
}

export type Phone = {
    value: string;
    prefix: number;
};

export type OptionalPhone = {
    value?: string | null | undefined;
    prefix?: number | null | undefined;
};

export type Rounding = {
    decimals: number;
};

export type Roundings = {
    amount: Rounding;
    percentage: Rounding;
};

export type Period = {
    start: Date;
    end: Date;
};

export type Authorization = {
    accessKey: string;
    secretKey: string;
    _versioning: SimpleVersioning;
    lastUsage: Date;
};

export enum Title {
    MR = 'MR',
    MS = 'MS',
    MX = 'MX',
}

export enum Salutation {
    DR = 'DR',
    MR = 'MR',
    MDM = 'MDM',
    MRS = 'MRS',
    MS = 'MS',
}

export enum DayOfWeek {
    Monday = 'monday',
    Tuesday = 'tuesday',
    Wednesday = 'wednesday',
    Thursday = 'thursday',
    Friday = 'friday',
    Saturday = 'saturday',
    Sunday = 'sunday',
}

export enum CitizenshipType {
    SingaporeanOrPr = 'Singapore Citizen/PR',
    Malaysian = 'Malaysian',
    Others = 'Other Nationality',
}

export enum DateTimeUnit {
    Days = 'days',
    Hours = 'hours',
}

export enum PreferenceValue {
    Yes = 'yes',
    No = 'no',
    Optional = 'optional',
}

export type DateUnit = {
    value: number;
    unit: DateTimeUnit;
};

export enum LeadStageOption {
    Lead = 'lead',
    Contact = 'contact',
    LeadAndContact = 'leadAndContact',
}

export type ApplicationCampaignValues = {
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;

    utmUrl?: string;

    capCampaignId?: string;
    capLeadSource?: EventLeadOriginType;
    capLeadOrigin?: EventMediumType;
};

export enum EventLeadOriginType {
    Dealer = 'dealer',
    Internet = 'internet',
}

export enum EventMediumType {
    Walkin = 'walkin',
    Internet = 'internet',
    Event = 'event',
}

/**
 * Represents a GeoJSON Point object.
 * The `coordinates` follows the GeoJSON order: [longitude, latitude].
 */
export type GeoJSONPoint = {
    type: 'Point';
    coordinates: [number, number];
};
