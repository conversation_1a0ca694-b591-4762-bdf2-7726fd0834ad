import { ObjectId } from 'mongodb';
import { TranslatedString } from './LanguagePack';
import { UploadedFile, UploadedFileWithPreview } from './UploadedFile';
import { SimpleVersioning } from './Versioning';
import { Phone, Roundings } from './shared';

export enum CompanyTheme {
    Default = 'default',
    Admin = 'admin',
    Porsche = 'porsche',
    Volkswagen = 'volkswagen',
    Skoda = 'skoda',
    PorscheV3 = 'porscheV3',
}

export enum EmailProvider {
    System = 'system',
    Smtp = 'smtp',
}

export enum MFASettingsType {
    SmsOTP = 'smsOTP',
}

export enum MaskDirection {
    None = 'None',
    Front = 'Front',
    Back = 'Back',
}

type EmailCore<TProvider extends EmailProvider> = { provider: TProvider };

export type SMTPEmailSettings = EmailCore<EmailProvider.Smtp> & { settingId: ObjectId };

export type SystemEmailSettings = EmailCore<EmailProvider.System>;

export type EmailSettings = SMTPEmailSettings | SystemEmailSettings;

export enum SmsProvider {
    System = 'system',
    Twilio = 'twilio',
}

export enum PasswordConfiguration {
    IdentityAndDateOfBirth = 'identityAndDateOfBirth',
    DateOfBirth = 'dateOfBirth',
    Random = 'random',
    Off = 'off',
}

type SmsCore<TProvider extends SmsProvider> = { provider: TProvider };

// we only have system now
export type SystemSmsSettings = SmsCore<SmsProvider.System>;

export type TwilioSmsSettings = SmsCore<SmsProvider.Twilio> & { settingId: ObjectId };
export type SmsSettings = SystemSmsSettings | TwilioSmsSettings;

export type MFASettings = {
    type: MFASettingsType;
    enabledOn: Date;
};

export type EdmEmailSocialMedia = {
    _id: ObjectId;
    url: string;
    altText?: string | null;
    icon?: UploadedFileWithPreview | null;
};

export type EdmEmailFooter = {
    connectText: TranslatedString;
    disclaimerText: TranslatedString;
    privacyPolicyUrl?: string;
    legalNoticeUrl?: string;
    arrowIcon?: UploadedFileWithPreview | null;
    emailIcon?: UploadedFileWithPreview | null;
    phoneIcon?: UploadedFileWithPreview | null;
    socialMedia: EdmEmailSocialMedia[];
    copyRight: TranslatedString;
};

export type MaskSettings = {
    count: number;
    direction: MaskDirection;
};

export enum CalculationRounding {
    None = 'none',
    Tens = 'tens',
    Hundreds = 'hundreds',
    Thousands = 'thousands',
}

export type Company = {
    _id: ObjectId;

    // display name to be shown in application
    displayName: string;
    // company name to be shown in PDF
    companyName: TranslatedString;

    legalName: TranslatedString;

    // country settings
    countryCode: string;
    // market code is mandatory for UAE
    marketCode?: string;
    timeZone: string;
    currency: string;
    roundings: Roundings;
    calculationRounding: CalculationRounding;

    mfaSettings?: MFASettings;
    // COE value for singapore
    coe: number;
    // PPSR value for New Zealand
    ppsr: number;
    // EstFee value for New Zealand
    estFee: number;

    // contact details
    phone?: Phone | null;
    email: string;
    address?: string | null;

    // company details
    description?: TranslatedString | null;
    color: string;
    copyright: TranslatedString;
    logo?: UploadedFileWithPreview | null;
    logoNonWhiteBackground?: UploadedFileWithPreview | null;
    mobileLogo?: UploadedFileWithPreview | null;
    favicon?: UploadedFileWithPreview | null;
    font?: UploadedFile | null;
    fontBold?: UploadedFile | null;
    theme: CompanyTheme;

    // system
    emailSettings: EmailSettings;
    smsSettings: SmsSettings;
    languages: ObjectId[]; // language packs IDs

    edmEmailFooter: EdmEmailFooter;

    sessionTimeout: number;
    passwordConfiguration: PasswordConfiguration;

    isActive: boolean;
    isDeleted: boolean;

    mask: MaskSettings;

    isDataPurgeEnabled: boolean;
    dataPurgeAfter?: number; // in years

    /* Versioning */
    _versioning: SimpleVersioning;

    enableContentRefinement: boolean;

    isInstantApprovalStatsEnabled: boolean;

    allowLimitDealerFeature: boolean;

    addressAutofill?: boolean | null;
};
