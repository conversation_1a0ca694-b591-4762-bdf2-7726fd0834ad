import { ObjectId } from 'mongodb';
import { TranslatedString } from './LanguagePack';
import { UploadedFileWithPreview } from './UploadedFile';
import { SimpleVersioning } from './Versioning';
import { OptionalPhone, type GeoJSONPoint } from './shared';

export type DealerSocialMedia = {
    _id: ObjectId;
    title: TranslatedString;
    content: string;
    icon?: UploadedFileWithPreview | null;
};

export type AdditionalIntegrationParameter = {
    purpose: string;
    value: string;
};

export type DealerAutoplay = {
    dealerId: number;
    yardId: number;
};

export type Dealer = {
    _id: ObjectId;
    displayName: string;
    legalName: TranslatedString;
    isActive: boolean;
    companyId: ObjectId;
    // COE value for singapore
    coe: number;
    // PPSR value for New Zealand
    ppsr: number;
    // EstFee value for New Zealand
    estFee: number;

    /**
     * Location coordinates in GeoJSON Point format for MongoDB geospatial queries
     */
    location: GeoJSONPoint;

    contact: {
        telephone?: OptionalPhone | null | undefined;
        email?: string | null | undefined;
        address?: TranslatedString | null | undefined;
        additionalInfo?: TranslatedString | null | undefined;
        socialMedia: DealerSocialMedia[];
    };

    integrationDetails: {
        dealerCode?: string | null | undefined;
        partnerNumber?: string | null | undefined;
        assortment?: string | null | undefined;
        autoplay?: DealerAutoplay | null | undefined;

        additionalParameter: AdditionalIntegrationParameter[];
    };
    _versioning: SimpleVersioning;
    isDeleted: boolean;

    limitFeature: boolean;
};
