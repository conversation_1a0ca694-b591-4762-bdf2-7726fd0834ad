import { AuditTrailKind } from '../core';
import { LeadAuditTrailCore } from './leads';

export type CapAuditTrailKind =
    | AuditTrailKind.CapBPIsExist
    | AuditTrailKind.CapBPCreated
    | AuditTrailKind.CapBPUpdated
    | AuditTrailKind.CapCompetitorVehicleCreated
    | AuditTrailKind.CapCompetitorVehicleUpdated
    | AuditTrailKind.CapLeadIsExist
    | AuditTrailKind.CapLeadCampaignNotFound
    | AuditTrailKind.CapLeadCreated
    | AuditTrailKind.CapLeadUpdated
    | AuditTrailKind.CapActivitySubmitted
    | AuditTrailKind.CapActivityEndTestDriveSubmitted
    | AuditTrailKind.CapActivityPlannedTestDriveSubmitted
    | AuditTrailKind.CapActivityPlannedShowroomVisitSubmitted
    | AuditTrailKind.CapActivityCompleteShowroomVisitSubmitted
    | AuditTrailKind.CapConsentNotFound
    | AuditTrailKind.CapConsentSubmitted
    | AuditTrailKind.CapBPSearchFailed
    | AuditTrailKind.CapLeadSearchFailed
    | AuditTrailKind.CapCustomerAttributeCreated
    | AuditTrailKind.CapCustomerAttributeUpdated
    | AuditTrailKind.CapCustomerAttributeDeleted
    | AuditTrailKind.CapCustomerAttributeSearchFailed;

export type CapBPIsExist = LeadAuditTrailCore<AuditTrailKind.CapBPIsExist> & {
    success: boolean;
    id: string;
};

export type CapBPCreated = LeadAuditTrailCore<AuditTrailKind.CapBPCreated> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};
export type CapBPUpdated = LeadAuditTrailCore<AuditTrailKind.CapBPUpdated> & {
    success: boolean;
    id: string;
    errorMessage?: string;
};
export type CapCompetitorVehicleCreated = LeadAuditTrailCore<AuditTrailKind.CapCompetitorVehicleCreated> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};

export type CapCompetitorVehicleUpdated = LeadAuditTrailCore<AuditTrailKind.CapCompetitorVehicleUpdated> & {
    success: boolean;
    id: string;
    errorMessage?: string;
};

export type CapLeadIsExist = LeadAuditTrailCore<AuditTrailKind.CapLeadIsExist> & {
    success: boolean;
    id: string;
};
export type CapLeadCampaignNotFound = LeadAuditTrailCore<AuditTrailKind.CapLeadCampaignNotFound> & {
    success: boolean;
    id: string;
    errorMessage: string;
};
export type CapLeadCreated = LeadAuditTrailCore<AuditTrailKind.CapLeadCreated> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};
export type CapLeadUpdated = LeadAuditTrailCore<AuditTrailKind.CapLeadUpdated> & {
    success: boolean;
    id: string;
    errorMessage?: string;
};
export type CapActivitySubmitted = LeadAuditTrailCore<AuditTrailKind.CapActivitySubmitted> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};
export type CapActivityEndTestDriveSubmitted = LeadAuditTrailCore<AuditTrailKind.CapActivityEndTestDriveSubmitted> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};

export type CapActivityPlannedTestDriveSubmitted =
    LeadAuditTrailCore<AuditTrailKind.CapActivityPlannedTestDriveSubmitted> & {
        success: boolean;
        id?: string;
        errorMessage?: string;
    };

export type CapActivityPlannedShowroomVisitSubmitted =
    LeadAuditTrailCore<AuditTrailKind.CapActivityPlannedShowroomVisitSubmitted> & {
        success: boolean;
        id?: string;
        errorMessage?: string;
    };

export type CapActivityCompleteShowroomVisitSubmitted =
    LeadAuditTrailCore<AuditTrailKind.CapActivityCompleteShowroomVisitSubmitted> & {
        success: boolean;
        id?: string;
        errorMessage?: string;
    };

export type CapConsentNotFound = LeadAuditTrailCore<AuditTrailKind.CapConsentNotFound> & {
    success: boolean;
    id: string;
};

export type CapConsentSubmitted = LeadAuditTrailCore<AuditTrailKind.CapConsentSubmitted> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};

export type CapBPSearchFailed = LeadAuditTrailCore<AuditTrailKind.CapBPSearchFailed> & {
    errorMessage: string;
};

export type CapLeadSearchFailed = LeadAuditTrailCore<AuditTrailKind.CapLeadSearchFailed> & {
    errorMessage: string;
};

export type CapCustomerAttributeCreated = LeadAuditTrailCore<AuditTrailKind.CapCustomerAttributeCreated> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};

export type CapCustomerAttributeUpdated = LeadAuditTrailCore<AuditTrailKind.CapCustomerAttributeUpdated> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};

export type CapCustomerAttributeDeleted = LeadAuditTrailCore<AuditTrailKind.CapCustomerAttributeDeleted> & {
    success: boolean;
    id?: string;
    errorMessage?: string;
};

export type CapCustomerAttributeSearchFailed = LeadAuditTrailCore<AuditTrailKind.CapCustomerAttributeSearchFailed> & {
    errorMessage: string;
};

export type CapAuditTrail =
    | CapBPIsExist
    | CapBPCreated
    | CapBPUpdated
    | CapCompetitorVehicleCreated
    | CapCompetitorVehicleUpdated
    | CapLeadIsExist
    | CapLeadCampaignNotFound
    | CapLeadCreated
    | CapLeadUpdated
    | CapActivitySubmitted
    | CapActivityEndTestDriveSubmitted
    | CapActivityPlannedTestDriveSubmitted
    | CapActivityPlannedShowroomVisitSubmitted
    | CapActivityCompleteShowroomVisitSubmitted
    | CapConsentNotFound
    | CapConsentSubmitted
    | CapBPSearchFailed
    | CapLeadSearchFailed
    | CapCustomerAttributeCreated
    | CapCustomerAttributeUpdated
    | CapCustomerAttributeDeleted
    | CapCustomerAttributeSearchFailed;
