import { ObjectId } from 'mongodb';
import { AudienceMessage } from '../../../../emails/type';
import { ApplicationStage } from '../../Applications';
import { Author } from '../../Versioning';
import { AuditTrailCore, AuditTrailKind } from '../core';
import { KnownChange } from './applications';

export type LeadAuditTrailCore<TKind extends AuditTrailKind> = AuditTrailCore<TKind> & {
    // lead ID
    leadId: ObjectId;
    // lead suite ID
    leadSuiteId: ObjectId;
    // application ID
    applicationId?: ObjectId;
    // application suite ID
    applicationSuiteId?: ObjectId;
    stages?: ApplicationStage[];
};

export type LeadCustomerAgreedOnCnDAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadCustomerAgreedOnCnD> & {
    // customer type
    customerKind: 'applicant' | 'guarantor';
    // consent & declaration ID
    consentId: ObjectId;
    // author of the agreements
    author: Author;
    // ip address of the customer
    ip: string;
};

export type LeadDraftedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadDrafted> & {
    author: Author;
};

export type LeadSubmittedToSystemAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadSubmittedToSystem>;

export type LeadEmailSentAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadEmailSent> & {
    audience: AudienceMessage;
};

export type LeadSharedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadShared> & {
    author: Author;
};

export type LeadQualifiedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadQualified> & {
    author: Author;
};

export type LeadUnqualifiedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadUnqualified> & {
    author: Author;
};

export type LeadLostAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadLost> & {
    author: Author;
};

export type LeadCompletedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadCompleted> & {
    author: Author;
};

export type LeadAmendedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadAmended> & {
    changes: Array<KnownChange>;
    author: Author;
};

export type LeadSubmittedWithErrorAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadSubmittedWithError>;

export type LeadSubmissionFailedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadSubmissionFailed>;

export type LeadIntentAndAssignAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadIntentAndAssign> & {
    author: Author;
};

export type LeadFollowedUpAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadFollowedUp> & {
    author: Author;
    scheduledDate: Date;
    remarks?: string;
};

export type LeadTestDriveCreatedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadTestDriveCreated> & {
    author: Author;
};

export type LeadShowroomVisitBookedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadShowroomVisitBooked> & {
    author: Author;
};

export type LeadStockAssignedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadStockAssigned> & {
    author: Author;
};

export type LeadKYCReceivedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadKYCReceived> & {
    // customer ID
    customerId: ObjectId;
    // customer suite ID
    customerSuiteID: ObjectId;
    // author on the KYC
    author: Author;
};

export type LeadCreatedByPorscheRetainAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadCreatedByPorscheRetain> & {
    author: Author;
};

export type LeadUpdatedByPorscheRetainAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadUpdatedByPorscheRetain> & {
    author: Author;
};

export type LeadContactedAuditTrail = LeadAuditTrailCore<AuditTrailKind.LeadContacted> & {
    author: Author;
};

export type LeadAuditTrail =
    | LeadDraftedAuditTrail
    | LeadSubmittedToSystemAuditTrail
    | LeadCustomerAgreedOnCnDAuditTrail
    | LeadSharedAuditTrail
    | LeadEmailSentAuditTrail
    | LeadIntentAndAssignAuditTrail
    | LeadFollowedUpAuditTrail
    | LeadTestDriveCreatedAuditTrail
    | LeadShowroomVisitBookedAuditTrail
    | LeadStockAssignedAuditTrail
    | LeadQualifiedAuditTrail
    | LeadUnqualifiedAuditTrail
    | LeadLostAuditTrail
    | LeadCompletedAuditTrail
    | LeadAmendedAuditTrail
    | LeadSubmittedWithErrorAuditTrail
    | LeadSubmissionFailedAuditTrail
    | LeadKYCReceivedAuditTrail
    | LeadCreatedByPorscheRetainAuditTrail
    | LeadUpdatedByPorscheRetainAuditTrail
    | LeadContactedAuditTrail;
