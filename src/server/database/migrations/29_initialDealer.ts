import { ObjectId, AnyBulkWriteOperation } from 'mongodb';
import { getSimpleVersioningBySystemForCreation } from '../../utils/versioning';
import { Dealer } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '29_initialDealer',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const simpleVersioning = getSimpleVersioningBySystemForCreation();
        const operations = await db
            .collection('companies')
            .find({})
            .map((company): AnyBulkWriteOperation<Dealer> => {
                const dealer: Dealer = {
                    _id: new ObjectId(),
                    displayName: `Default Dealer ${company.displayName}`,
                    legalName: { defaultValue: `Default Dealer ${company.displayName}`, overrides: [] },
                    isActive: true,
                    companyId: company._id,
                    coe: 0,
                    ppsr: 0,
                    estFee: 0,
                    isDeleted: false,

                    contact: {
                        telephone: { prefix: 65, value: '' },
                        email: company.email,
                        address: { defaultValue: '', overrides: [] },
                        additionalInfo: { defaultValue: '', overrides: [] },
                        socialMedia: [],
                    },

                    integrationDetails: {
                        dealerCode: '',
                        partnerNumber: '',
                        assortment: '',

                        additionalParameter: [],
                    },
                    _versioning: simpleVersioning,
                    limitFeature: false,
                    location: {
                        type: 'Point',
                        coordinates: [0, 0], // Default coordinates
                    },
                };

                return {
                    insertOne: {
                        document: dealer,
                    },
                };
            })
            .toArray();

        if (operations.length) {
            await db.collection<Dealer>('dealers').bulkWrite(operations);
        }
    },
};
