import { LeadStatus, ApplicationStatus } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

interface PreStatusCounts {
    inProgress: number;
    new: number;
    pendingQualify: number;
    drafted: number;
    shared: number;
    newWithDrafted: number;
    newWithShared: number;
    pendingQualifyWithDrafted: number;
    total: number;
    withStageStatus: number;
    newWithDraft: number;
    inProgressWithDraft: number;
    pendingQualifyWithDraft: number;
    submissionFailedWithDraft: number;
    submittedWithErrorWithDraft: number;
    unqualifiedWithDraft: number;
    draftedWithDraft: number;
}

interface PostStatusCounts {
    inProcess: number;
    new: number;
    pendingQualify: number;
    drafted: number;
    shared: number;
    total: number;
    withStageStatus: number;
}

enum DeprecatedLeadStatus {
    Drafted = 'drafted',
    Shared = 'shared',
    New = 'new', // this will be deleted on this migration
    PendingQualify = 'pendingQualify',
    InProgress = 'inProgress', // this is migrated to InProcess hence deprecated
    Unqualified = 'unqualified',
    SubmittedWithError = 'submittedWithError',
    SubmissionFailed = 'submissionFailed',
    Lost = 'lost',
    Completed = 'completed',
}

export default {
    identifier: '258_updateLeadStatuses',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const preMigrationCounts = await getPreStatusCounts(db);

        const bulkOp = db.collection('leads').initializeOrderedBulkOp();

        // 1. First handle documents with stageStatus to prevent overwrites
        bulkOp
            .find({
                stageStatus: ApplicationStatus.Shared,
            })
            .update({
                $set: { status: LeadStatus.Shared },
            });

        // `isDraft` = true means it is always in draft state
        bulkOp
            .find({
                // this is true for all
                isDraft: true,
                status: { $in: ['new', 'pendingQualify'] },
            })
            .update({
                $set: { status: LeadStatus.Drafted },
            });

        // 2. Then update remaining New status to PendingQualify
        bulkOp
            .find({
                status: DeprecatedLeadStatus.New,
                stageStatus: {
                    $nin: [ApplicationStatus.Shared],
                },
                // only update those that are not drafted and actual contacts already
                isDraft: false,
            })
            .update({
                $set: { status: LeadStatus.PendingQualify },
            });

        // 3. Update remaining new to pendingQualify
        bulkOp.find({ status: DeprecatedLeadStatus.New }).update({ $set: { status: LeadStatus.PendingQualify } });

        await bulkOp.execute();

        // 4. Drop stageStatus field from all documents after updates
        await db.collection('leads').updateMany({}, { $unset: { stageStatus: '' } });

        // 5. Rename inProgress to inProcess
        await db
            .collection('leads')
            .updateMany({ status: DeprecatedLeadStatus.InProgress, isDraft: true }, { $set: { status: 'inProcess' } });

        const postMigrationCounts = await getPostStatusCounts(db);
        const verificationResults = verifyMigrationResults(preMigrationCounts, postMigrationCounts);

        if (!verificationResults.success) {
            console.error('Migration verification failed:', verificationResults.errors);
            throw new Error(`Migration verification failed: ${JSON.stringify(verificationResults.errors)}`);
        }
    },
};

async function getPostStatusCounts(db: any): Promise<PostStatusCounts> {
    const [inProcessCount, newCount, pendingQualifyCount, draftedCount, sharedCount, totalCount, withStageStatusCount] =
        await Promise.all([
            db.collection('leads').countDocuments({ status: 'inProcess' }),
            db.collection('leads').countDocuments({ status: DeprecatedLeadStatus.New }),
            db.collection('leads').countDocuments({ status: LeadStatus.PendingQualify }),
            db.collection('leads').countDocuments({ status: LeadStatus.Drafted }),
            db.collection('leads').countDocuments({ status: LeadStatus.Shared }),
            db.collection('leads').countDocuments({}),
            db.collection('leads').countDocuments({ stageStatus: { $exists: true } }),
        ]);

    return {
        inProcess: inProcessCount,
        new: newCount,
        pendingQualify: pendingQualifyCount,
        drafted: draftedCount,
        shared: sharedCount,
        total: totalCount,
        withStageStatus: withStageStatusCount,
    };
}

async function getPreStatusCounts(db: any): Promise<PreStatusCounts> {
    const [
        // Base status counts
        newCount,
        pendingQualifyCount,
        draftedCount,
        sharedCount,
        inProgressCount,

        // Counts by isDraft
        newWithDraftCount,
        pendingQualifyWithDraftCount,
        inProgressWithDraftCount,

        // Counts for additional draft states
        submissionFailedWithDraftCount,
        submittedWithErrorWithDraftCount,
        unqualifiedWithDraftCount,
        draftedWithDraftCount,

        // Counts by stageStatus
        newWithSharedCount,
        newWithDraftedStatusCount,
        pendingQualifyWithDraftedCount,

        totalCount,
        withStageStatusCount,
    ] = await Promise.all([
        // Base status counts
        db.collection('leads').countDocuments({ status: DeprecatedLeadStatus.New }),
        db.collection('leads').countDocuments({ status: LeadStatus.PendingQualify }),
        db.collection('leads').countDocuments({ status: LeadStatus.Drafted }),
        db.collection('leads').countDocuments({ status: LeadStatus.Shared }),
        db.collection('leads').countDocuments({ status: DeprecatedLeadStatus.InProgress }),

        // Counts by isDraft
        db.collection('leads').countDocuments({
            status: DeprecatedLeadStatus.New,
            isDraft: true,
        }),
        db.collection('leads').countDocuments({
            status: LeadStatus.PendingQualify,
            isDraft: true,
        }),
        db.collection('leads').countDocuments({
            status: DeprecatedLeadStatus.InProgress,
            isDraft: true,
        }),

        // Additional draft states
        db.collection('leads').countDocuments({
            status: DeprecatedLeadStatus.SubmissionFailed,
            isDraft: true,
        }),
        db.collection('leads').countDocuments({
            status: DeprecatedLeadStatus.SubmittedWithError,
            isDraft: true,
        }),
        db.collection('leads').countDocuments({
            status: DeprecatedLeadStatus.Unqualified,
            isDraft: true,
        }),
        db.collection('leads').countDocuments({
            status: LeadStatus.Drafted,
            isDraft: true,
        }),

        // Stage status counts
        db.collection('leads').countDocuments({
            status: DeprecatedLeadStatus.New,
            stageStatus: ApplicationStatus.Shared,
            isDraft: false,
        }),
        db.collection('leads').countDocuments({
            status: DeprecatedLeadStatus.New,
            stageStatus: ApplicationStatus.Drafted,
        }),
        db.collection('leads').countDocuments({
            status: LeadStatus.PendingQualify,
            stageStatus: ApplicationStatus.Drafted,
        }),

        db.collection('leads').countDocuments({}),
        db.collection('leads').countDocuments({ stageStatus: { $exists: true } }),
    ]);

    return {
        new: newCount,
        pendingQualify: pendingQualifyCount,
        drafted: draftedCount,
        shared: sharedCount,
        inProgress: inProgressCount,

        // Draft counts
        newWithDraft: newWithDraftCount,
        pendingQualifyWithDraft: pendingQualifyWithDraftCount,
        inProgressWithDraft: inProgressWithDraftCount,
        submissionFailedWithDraft: submissionFailedWithDraftCount,
        submittedWithErrorWithDraft: submittedWithErrorWithDraftCount,
        unqualifiedWithDraft: unqualifiedWithDraftCount,
        draftedWithDraft: draftedWithDraftCount,

        // Stage status counts
        newWithShared: newWithSharedCount,
        newWithDrafted: newWithDraftedStatusCount,
        pendingQualifyWithDrafted: pendingQualifyWithDraftedCount,

        total: totalCount,
        withStageStatus: withStageStatusCount,
    };
}

function verifyMigrationResults(pre: PreStatusCounts, post: PostStatusCounts) {
    // For "Drafted" status:
    // - All draft documents originally in "new" or "pendingQualify" become "Drafted".
    // - Additionally, any document that already had status "drafted" remains as "Drafted".
    const expectedDrafted = pre.newWithDraft + pre.pendingQualifyWithDraft + pre.drafted;

    // Only draft inProgress docs become InProcess.
    const expectedInProcess = pre.inProgressWithDraft;

    // For "PendingQualify":
    // - Non-draft documents originally with status "new" (and not with stageStatus "Shared")
    //   are updated to "PendingQualify", along with those that were already non-draft "pendingQualify".
    const expectedPendingQualify =
        pre.new - pre.newWithDraft - pre.newWithShared + (pre.pendingQualify - pre.pendingQualifyWithDraft);

    // For "Shared":
    // - Documents with stageStatus "Shared" become "Shared".
    const expectedShared = pre.newWithShared;

    const errors: string[] = [];

    if (post.total !== pre.total) {
        errors.push(`Total document count changed: ${pre.total} -> ${post.total}`);
    }

    if (post.new !== 0) {
        errors.push(`Some documents still have 'new' status: ${post.new}`);
    }

    if (post.withStageStatus !== 0) {
        errors.push(`Some documents still have 'stageStatus' field: ${post.withStageStatus}`);
    }

    if (post.pendingQualify !== expectedPendingQualify) {
        errors.push(
            `Unexpected pendingQualify count: ${post.pendingQualify}, expected ${expectedPendingQualify} ` +
                `(original new: ${pre.new}, with draft: ${pre.newWithDraft}, with shared: ${pre.newWithShared}, ` +
                `original pendingQualify: ${pre.pendingQualify}, with draft: ${pre.pendingQualifyWithDraft})`
        );
    }

    if (post.drafted !== expectedDrafted) {
        errors.push(
            `Unexpected drafted count: ${post.drafted}, expected ${expectedDrafted} ` +
                `(new with draft: ${pre.newWithDraft}, pendingQualify with draft: ${pre.pendingQualifyWithDraft}, ` +
                `already drafted (all): ${pre.drafted})`
        );
    }

    if (post.shared !== expectedShared) {
        errors.push(
            `Unexpected shared count: ${post.shared}, expected ${expectedShared} ` +
                `(new with shared: ${pre.newWithShared})`
        );
    }

    if (post.inProcess !== expectedInProcess) {
        errors.push(
            `Unexpected inProcess count: ${post.inProcess}, expected ${expectedInProcess} ` +
                `(original inProgress: ${pre.inProgress})`
        );
    }

    return {
        success: errors.length === 0,
        errors,
        expected: {
            pendingQualify: expectedPendingQualify,
            drafted: expectedDrafted,
            shared: expectedShared,
            total: pre.total,
            new: 0,
            withStageStatus: 0,
            inProcess: expectedInProcess,
        },
        actual: post,
    };
}
