import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { isEmpty, isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import {
    ApplicationJourney,
    ApplicationStage,
    Customer,
    Dealer,
    Event,
    EventApplication,
    LocalCustomerManagementModule,
    LocalModel,
    Vehicle,
} from '../database/documents';
import { getKYCPresetsForCustomerModule } from '../database/helpers';
import { getCustomerEmail, getCustomerNameInfo } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import { getVehicleName } from '../database/helpers/vehicles';
import { ApplicationStagesLink } from '../queues/implementations/shared/getApplicationStagesLink';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import { EventEmailContextResult } from '../utils/getEventEmailContext';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import { CustomerContent, DealerInformationSection } from './components/common';
import { Copyright, DetailSection } from './components/common/ui';
import EventEmailSummary from './components/summary/EventEmailSummary';
import { AudienceMessage } from './type';
import useTranslatedString from './utils/useTranslatedString';

export type SubmitEventApplicationProps = {
    emailContext: EmailContext;
    dealerEmailContext: DealerEmailContext;
    eventEmailContext: EventEmailContextResult;
    edmEmailFooterContext: EdmEmailFooterContext;
    dealer: Dealer;
    event: Event;

    variant?: Vehicle;
    applicant: Customer;
    customerModule: LocalCustomerManagementModule;
    application: EventApplication;
    journey: ApplicationJourney;
    guarantor: Customer;
    model: LocalModel;

    audience: AudienceMessage;
    submissionType: string;
    subject: string;
    applicationId: string;
    stages: ApplicationStage[];
    link?: string;
    applicationStagesLink?: ApplicationStagesLink;
};

const SubmitEventApplication = ({
    emailContext,
    dealerEmailContext,
    eventEmailContext,
    edmEmailFooterContext,
    dealer,
    event,

    variant,
    applicant,
    application,
    journey,
    guarantor,
    model,

    audience,
    submissionType,
    subject,
    applicationId,
    stages,
    link,
    customerModule,
    applicationStagesLink,
}: SubmitEventApplicationProps) => {
    const { t } = useTranslation(['common', 'emails']);
    const translate = useTranslatedString();

    const { company, companyName } = emailContext;

    const { images, emailContents } = eventEmailContext;
    const { submitOrder } = emailContents;

    const dealerId = dealer._id.toHexString();

    // Connect text
    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText)
        : 'emails:event.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    // disclaimerText
    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText)
        : 'emails:event.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerNameInfo = getCustomerNameInfo(t, applicant, company, kycPresets);
    const customerEmail = getCustomerEmail(t, applicant);

    const introTitleDealerLevel = translate(
        submitOrder?.introTitle.overrides?.find(
            ({ dealerId: applicationDealerId }) => applicationDealerId.toHexString() === dealerId
        )?.value
    )?.replaceAll('\n', '<br />');

    const introTitleModuleLevel = translate(submitOrder?.introTitle.defaultValue)?.replaceAll('\n', '<br />');

    const introTitle =
        !isNil(introTitleDealerLevel) && !isEmpty(introTitleDealerLevel)
            ? introTitleDealerLevel
            : introTitleModuleLevel;

    const contentTextDealerLevel = translate(
        submitOrder?.contentText.overrides?.find(
            ({ dealerId: applicationDealerId }) => applicationDealerId.toHexString() === dealerId
        )?.value
    )?.replaceAll('\n', '<br />');

    const contentTextModuleLevel = translate(submitOrder?.contentText.defaultValue)?.replaceAll('\n', '<br />');

    const contentText =
        !isNil(contentTextDealerLevel) && !isEmpty(contentTextDealerLevel)
            ? contentTextDealerLevel
            : contentTextModuleLevel;

    const variantName = variant ? translate(getVehicleName(variant)) : null;
    const modelName = translate(model?.name);

    const isStageViewable = (stage: ApplicationStage) => stages.includes(stage);

    const sendBookingSection = !!(
        (isStageViewable(ApplicationStage.Appointment) && application.appointmentStage) ||
        (isStageViewable(ApplicationStage.VisitAppointment) && application.visitAppointmentStage)
    );
    const sendPaymentSection = isStageViewable(ApplicationStage.Reservation) && journey.deposit;

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const displayDetailsSection = useMemo(
        () =>
            sendBookingSection ||
            sendPaymentSection ||
            event.showDealership ||
            !isNil(variant) ||
            event.hasVehicleIntegration ||
            application.customizedFields.length > 0,
        [
            application.customizedFields?.length,
            event.hasVehicleIntegration,
            event.showDealership,
            sendBookingSection,
            sendPaymentSection,
            variant,
        ]
    );

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText="emails:event.general.copyright"
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{ url: company.edmEmailFooter.legalNoticeUrl, text: t('emails:event.general.legalNotice') }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:event.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title={subject}
        >
            {!isNil(images.submitOrder.intro) && <VehicleImageSection url={images.submitOrder.intro} />}

            <MjmlWrapper padding="40px 32px 60px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        {audience === AudienceMessage.Customer && (
                            <CustomerContent
                                applicant={applicant}
                                applicationId={applicationId}
                                company={company}
                                companyName={companyName}
                                contentText={contentText}
                                customerModule={customerModule}
                                introTitle={introTitle}
                                submissionType={submissionType}
                                variantName={variantName}
                            />
                        )}

                        {audience === AudienceMessage.Salesperson && (
                            <>
                                <Text align="left" className="salespersonIntroTitle" level="p" paddingTop={0}>
                                    {t('emails:event.submitOrder.introSalesperson', {
                                        variantName,
                                        companyName,
                                        submissionType,
                                        applicationId,
                                        ...customerNameInfo,
                                    })}
                                </Text>
                                <Text align="left" className="salespersonContentTitle" level="p" paddingTop="20px">
                                    <Trans
                                        components={{
                                            // eslint-disable-next-line max-len
                                            // eslint-disable-next-line jsx-a11y/anchor-has-content,jsx-a11y/control-has-associated-label
                                            url: <a href={link} style={{ color: '#0000FF' }} />,
                                        }}
                                        i18nKey="emails:event.submitOrder.contentTextSalesperson"
                                        values={{
                                            variantName,
                                            companyName,
                                            submissionType,
                                            applicationId,
                                            link,
                                            ...customerNameInfo,
                                        }}
                                    />
                                </Text>
                            </>
                        )}
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {displayDetailsSection && (
                <DetailSection>
                    <EventEmailSummary
                        application={application}
                        applicationStagesLink={applicationStagesLink}
                        audience={audience}
                        company={company}
                        customerEmail={customerEmail}
                        customerNameInfo={customerNameInfo}
                        dealerEmailContext={dealerEmailContext}
                        event={event}
                        journey={journey}
                        modelName={modelName}
                        sendBookingSection={sendBookingSection}
                        sendPaymentSection={sendPaymentSection}
                        t={t}
                        variantName={variantName}
                    />
                </DetailSection>
            )}

            <DealerInformationSection
                contactUsText="emails:event.general.contactUs"
                dealerEmailContext={dealerEmailContext}
                showContactUs={audience === AudienceMessage.Customer}
            />
        </EDMLayout>
    );
};

export default SubmitEventApplication;
