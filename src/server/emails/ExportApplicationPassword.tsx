import { MjmlText } from '@faire/mjml-react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { User, Company } from '../database';
import type { EmailContext } from '../database/helpers/settings';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type ExportApplicationPasswordProps = {
    user: User;
    password: string;
    applicationType: string;
    documentType: 'Excel' | 'Pdf' | 'Documents';
    date: Date;
    emailContext: EmailContext;
    company: Company;
};

const ExportApplicationPassword = ({
    user,
    password,
    date,
    emailContext,
    applicationType,
    documentType,
    company,
}: ExportApplicationPasswordProps) => {
    const { t } = useTranslation(['emails', 'common']);

    return (
        <GeneralEDMLayout emailContext={emailContext} withTextWrapper>
            <MjmlText>{t('emails:exportApplicationPassword.greeting', { userName: user.displayName })}</MjmlText>
            <MjmlText lineHeight="30px">
                {t('emails:exportApplicationPassword.body.typeAndDate', {
                    applicationType,
                    documentType: documentType.toLowerCase(),
                    date: dayjs(date).tz(company.timeZone).format(t('common:formats.dateTimeFormat')),
                })}
            </MjmlText>
            <MjmlText fontWeight="900">{t('emails:exportApplicationPassword.body.password', { password })}</MjmlText>
        </GeneralEDMLayout>
    );
};

export default ExportApplicationPassword;
