import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import { Application } from '../database/documents';
import { EmailContext } from '../database/helpers/settings';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type SendPasswordForInsuranceAgreementToInsurerProps = {
    emailContext: EmailContext;
    application: Application;
    randomPassword: string;
};

const SendPasswordForInsuranceAgreementToInsurer = ({
    emailContext,
    application,
    randomPassword,
}: SendPasswordForInsuranceAgreementToInsurerProps) => {
    const { t } = useTranslation('emails');

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection fullWidth>
                <MjmlColumn>
                    <MjmlText>
                        {t('emails:insurerSubmission.passwordBody', {
                            applicationId: application.insuranceStage?.identifier,
                        })}
                    </MjmlText>
                    <MjmlText fontWeight="900" lineHeight="30px">
                        {randomPassword}
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default SendPasswordForInsuranceAgreementToInsurer;
