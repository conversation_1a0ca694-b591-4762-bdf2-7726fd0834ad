import { MjmlColumn, MjmlGroup, MjmlImage, MjmlSection } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import useFormats from '../../../app/utilities/useFormats';
import type { EmailContext } from '../../database/helpers/settings';
import { allowedVideoExtensions } from '../../utils/extensions';
import { ConfiguratorSection, ConfiguratorSectionGroup } from '../../utils/getConfiguratorEmailContext';
import { Text } from './TextSection';

const grayTextColor = '#737278';

type ConfiguratorBlocksSectionsProps = {
    section: ConfiguratorSection;
    company: EmailContext['company'];
    backgroundColor: string;
};

type ImageSectionProps = {
    group: ConfiguratorSectionGroup;
};

const ImageSection = ({ group }: ImageSectionProps) => {
    if (group.boxSource?.url) {
        const extension = group.boxSource.url.split('.').pop() || '';
        const isVideo = allowedVideoExtensions.includes(extension ? `.${extension}` : '');

        return (
            <MjmlColumn padding={0} width="21%">
                {isVideo ? (
                    <Text height={90} paddingRight="20px">
                        {/* eslint-disable-next-line jsx-a11y/media-has-caption */}
                        <video
                            autoPlay={false}
                            controls={false}
                            src={group.boxSource.url}
                            style={{ width: '100%', height: 90 }}
                        />
                    </Text>
                ) : (
                    <MjmlImage align="center" height="auto" padding={0} paddingRight={20} src={group.boxSource.url} />
                )}
            </MjmlColumn>
        );
    }

    if (group.boxSource?.hex) {
        return (
            <MjmlColumn padding="0" paddingRight="20px" width="21%">
                <Text containerBackgroundColor={`#${group.boxSource?.hex}`} height={90} />
            </MjmlColumn>
        );
    }

    return (
        <MjmlColumn padding="0" paddingRight="20px" width="21%">
            <Text height={90} />
        </MjmlColumn>
    );
};

// old ConfiguratorBlockSections
/**
 * @deprecated ConfiguratorBlocksSections has an updated version used by Submission
 * This is only currently being used by resume
 */
const ConfiguratorBlocksSections = ({ section, company, backgroundColor }: ConfiguratorBlocksSectionsProps) => {
    const { t } = useTranslation(['emails']);

    const { formatAmountWithCurrency } = useFormats(
        company?.currency || '',
        company?.roundings?.amount?.decimals || 0,
        company?.roundings?.percentage?.decimals || 0
    );

    return (
        <>
            <MjmlSection backgroundColor={backgroundColor} padding="12px 24px 8px">
                <MjmlColumn>
                    <Text level="h4">{t(section.sectionTitle)}</Text>
                </MjmlColumn>
            </MjmlSection>
            {section.groups?.map((group, index) => (
                <MjmlSection
                    key={`${group.title}-${index.toString()}`}
                    backgroundColor={backgroundColor}
                    padding="8px 24px"
                    paddingBottom="48px"
                >
                    <MjmlGroup>
                        <ImageSection group={group} />
                        <MjmlColumn padding={0} width="50%">
                            <Text level="h4">{group.title}</Text>
                            {group.description?.map((text, index) => (
                                <Text key={`${group.title}.${text}`} color={grayTextColor} level="small">
                                    {text}
                                </Text>
                            ))}
                        </MjmlColumn>
                    </MjmlGroup>
                </MjmlSection>
            ))}
        </>
    );
};

export default ConfiguratorBlocksSections;
