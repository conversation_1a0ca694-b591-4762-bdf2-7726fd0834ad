import { MjmlImage } from '@faire/mjml-react';
import { isNil } from 'lodash/fp';
import { ReactElement } from 'react';
import { EmailContext } from '../../database/helpers/settings';

export type EmailContextLogoProps = { emailContext: EmailContext };

type Dimensions = {
    width: number;
    height: number;
};

type MaxDimensions = {
    width?: number;
    height?: number;
};

export const resizeEmailLogo = (original: Dimensions, max?: MaxDimensions): Dimensions => {
    // Check if resizing is not needed
    if (
        !max ||
        ((isNil(max.width) || original.width <= max.width) && (isNil(max.height) || original.height <= max.height))
    ) {
        return original;
    }

    let newWidth: number;
    let newHeight: number;

    const originalRatio = original.width / original.height;

    if (!isNil(max.width) && !isNil(max.height)) {
        // Both max width and height are defined
        const maxRatio = max.width / max.height;
        if (originalRatio > maxRatio) {
            newWidth = Math.min(original.width, max.width);
            newHeight = newWidth / originalRatio;
        } else {
            newHeight = Math.min(original.height, max.height);
            newWidth = newHeight * originalRatio;
        }
    } else if (!isNil(max.width)) {
        // Only max width is defined
        newWidth = Math.min(original.width, max.width);
        newHeight = newWidth / originalRatio;
    } else if (!isNil(max.height)) {
        // Only max height is defined
        newHeight = Math.min(original.height, max.height);
        newWidth = newHeight * originalRatio;
    } else {
        // Neither is defined, return original dimensions
        newWidth = original.width;
        newHeight = original.height;
    }

    return { width: newWidth, height: newHeight };
};
const EmailContextLogo = ({ emailContext }: EmailContextLogoProps): ReactElement => {
    // This follow the css that defined in the layout
    const resizedLogo = resizeEmailLogo(emailContext.logo, { height: 60, width: 240 });

    return (
        <MjmlImage
            alt={emailContext.companyName}
            css-class="logo"
            height={resizedLogo.height}
            src={emailContext.logo.url}
            width={resizedLogo.width}
        />
    );
};

export default EmailContextLogo;
