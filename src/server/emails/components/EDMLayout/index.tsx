import { Mjml, MjmlBody, MjmlColumn, MjmlSection } from '@faire/mjml-react';
import { ReactElement, ReactNode } from 'react';
import urlJoin from 'url-join';
import config from '../../../core/config';
import type { EmailContext } from '../../../database/helpers/settings';
import { EdmSocialMedia } from '../../../utils/getEdmEmailFooterContext';
import EmailContextLogo from '../EmailContextLogo';
import EmailThemeProvider from '../themes/EmailThemeProvider';
import EDMFooter from './EDMFooter';
import EDMHead from './EDMHead';

type EDMLayoutProps = {
    emailContext: EmailContext;
    children: ReactNode;
    title: string;
    hideFooter?: boolean;
    privacyPolicy: { text: string; url: string };
    legalNotice: { text: string; url: string };
    disclaimerText: string;
    copyrightText: string;
    copyrightRender?: () => ReactNode;
    connectText: string;
    socialMedia: EdmSocialMedia[];
};

const EDMLayout = ({
    emailContext,
    children,
    title,
    hideFooter = false,
    legalNotice,
    privacyPolicy,
    copyrightText,
    copyrightRender,
    disclaimerText,
    connectText,
    socialMedia,
}: EDMLayoutProps): ReactElement => (
    <EmailThemeProvider value={emailContext}>
        <Mjml>
            <EDMHead title={title} />
            <MjmlBody width={640}>
                <MjmlSection backgroundUrl={urlJoin(config.publicPath, 'Solid_white.png')} cssClass="logo-container">
                    <MjmlColumn>
                        <EmailContextLogo emailContext={emailContext} />
                    </MjmlColumn>
                </MjmlSection>

                {children}

                {!hideFooter && (
                    <EDMFooter
                        connectText={connectText}
                        disclaimerText={disclaimerText}
                        emailContext={emailContext}
                        legalNotice={legalNotice}
                        privacyPolicy={privacyPolicy}
                        socialMedia={socialMedia}
                    />
                )}
            </MjmlBody>
        </Mjml>
    </EmailThemeProvider>
);

export default EDMLayout;
