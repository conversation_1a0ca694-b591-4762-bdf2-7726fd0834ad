import { MjmlColumn, MjmlSection, MjmlText, MjmlSocial, MjmlSocialElement, MjmlWrapper } from '@faire/mjml-react';
import type { EmailContext } from '../../../database/helpers/settings';
import { EdmSocialMedia } from '../../../utils/getEdmEmailFooterContext';
import useTranslatedString from '../../utils/useTranslatedString';
import DividerSection from '../DividerSection';
import { SectionSpacer } from '../common/ui';
import { useThemeTokens } from '../themes/EmailThemeProvider';

type EDMFooterProps = {
    disclaimerText: string;
    emailContext: EmailContext;
    legalNotice: { text: string; url: string };
    privacyPolicy: { text: string; url: string };
    connectText: string;
    socialMedia: EdmSocialMedia[];
};

const DefaultEDMFooter = ({
    legalNotice,
    privacyPolicy,
    emailContext,
    disclaimerText,
    connectText,
    socialMedia,
}: EDMFooterProps) => {
    const { fontSizes } = useThemeTokens();

    const translate = useTranslatedString();

    const translatedCopyright = emailContext.company?.edmEmailFooter?.copyRight
        ? translate(emailContext.company?.edmEmailFooter?.copyRight, { currentyear: new Date().getFullYear() })
        : '';

    return (
        <>
            <SectionSpacer height={35} />

            <MjmlWrapper backgroundColor="#000000" borderRadius={0} padding="40px 32px 92px 32px">
                {socialMedia.length > 0 && (
                    <MjmlSection padding={0}>
                        <MjmlColumn backgroundColor="#000000" padding={0}>
                            <MjmlText
                                color="#FFFFFF"
                                fontSize={fontSizes.default}
                                fontWeight="900"
                                lineHeight="20px"
                                padding="0"
                            >
                                {connectText}
                            </MjmlText>

                            <MjmlSocial align="left" padding="16px 0 0">
                                {socialMedia
                                    .map(
                                        media =>
                                            media && (
                                                <MjmlSocialElement
                                                    key={media._id.toHexString()}
                                                    alt={media.altText}
                                                    borderRadius="4px"
                                                    href={`https://${media.url}`}
                                                    iconSize="54px"
                                                    src={media.icon}
                                                />
                                            )
                                    )
                                    .filter(Boolean)}
                            </MjmlSocial>
                        </MjmlColumn>
                    </MjmlSection>
                )}

                {socialMedia.length > 0 && (
                    <DividerSection borderColor="#343438" borderWidth={1} padding={0} section={{ padding: '44px 0' }} />
                )}

                <MjmlSection padding={0}>
                    <MjmlColumn backgroundColor="#000000">
                        {(translatedCopyright || legalNotice.url || privacyPolicy.url) && (
                            <MjmlText color="#FFFFFF" fontSize={fontSizes.description} padding={0}>
                                <>
                                    {!!translatedCopyright && translatedCopyright}
                                    <span>&nbsp;&nbsp;</span>
                                    {legalNotice.url && (
                                        <a color="#FFFFFF" href={`//${legalNotice.url}`} style={{ color: '#fff' }}>
                                            {legalNotice.text}
                                        </a>
                                    )}
                                    <span>&nbsp;&nbsp;</span>
                                    {privacyPolicy.url && (
                                        <a color="#FFFFFF" href={`//${privacyPolicy.url}`} style={{ color: '#fff' }}>
                                            {privacyPolicy.text}
                                        </a>
                                    )}
                                </>
                            </MjmlText>
                        )}

                        <MjmlText
                            color="#FFFFFF"
                            fontSize={fontSizes.description}
                            padding={0}
                            paddingTop={translatedCopyright || legalNotice.url || privacyPolicy.url ? '24px' : '0px'}
                        >
                            {disclaimerText}
                        </MjmlText>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>
        </>
    );
};

export default DefaultEDMFooter;
