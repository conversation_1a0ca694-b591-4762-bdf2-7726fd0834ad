import { MjmlColumn, MjmlGroup, MjmlImage, MjmlSection } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import type { EmailContext } from '../../../database/helpers/settings';
import { allowedVideoExtensions } from '../../../utils/extensions';
import { ConfiguratorSection, ConfiguratorSectionGroup } from '../../../utils/getConfiguratorEmailContext';
import useFormats from '../../utils/useFormats';
import { SubTitleText, Text } from '../TextSection';
import TitleSection from '../TitleSection';
import { useThemeTokens } from '../themes/EmailThemeProvider';
import { ColorBox, ImageBox } from '../vehicleTechnicalData/TechnicalDataLayout';

type ConfiguratorBlocksSectionsProps = {
    section: ConfiguratorSection;
    company: EmailContext['company'];
};

type ImageSectionProps = {
    group: ConfiguratorSectionGroup;
    width?: string | number;
    paddingRight?: string | number;
};

const ImageSection = ({ group, width = '30%', paddingRight = '20px' }: ImageSectionProps) => {
    const { borderRadius, configuratorBoxSize } = useThemeTokens();

    if (group.boxSource?.url) {
        const extension = group.boxSource.url.split('.').pop() || '';
        const isVideo = allowedVideoExtensions.includes(extension ? `.${extension}` : '');

        return (
            <MjmlColumn cssClass="image-box" padding={0} width={width}>
                {isVideo ? (
                    <Text height={configuratorBoxSize} paddingRight={paddingRight}>
                        {/* eslint-disable-next-line jsx-a11y/media-has-caption */}
                        <video
                            autoPlay={false}
                            controls={false}
                            src={group.boxSource.url}
                            style={{ width: configuratorBoxSize, height: configuratorBoxSize }}
                        />
                    </Text>
                ) : (
                    <ImageBox src={group.boxSource.url} />
                )}
            </MjmlColumn>
        );
    }

    if (group.boxSource?.hex) {
        return (
            <MjmlColumn cssClass="image-box" padding="0" paddingRight={paddingRight} width={width}>
                <ColorBox color={`#${group.boxSource?.hex}`} />
            </MjmlColumn>
        );
    }

    return (
        <MjmlColumn cssClass="image-box" padding="0" paddingRight={paddingRight} width={width}>
            <MjmlImage borderRadius={borderRadius} height={configuratorBoxSize} />
        </MjmlColumn>
    );
};

const ConfiguratorBlocksSections = ({ section, company }: ConfiguratorBlocksSectionsProps) => {
    const { t } = useTranslation(['emails']);

    const { formatAmountWithCurrency } = useFormats(
        company?.currency || '',
        company?.roundings?.amount?.decimals || 0,
        company?.roundings?.percentage?.decimals || 0
    );

    return (
        <>
            <TitleSection>{t(section.sectionTitle)}</TitleSection>
            {section.groups?.map((group, index) => (
                <MjmlSection
                    key={`${group.title}-${index.toString()}`}
                    paddingBottom={0}
                    paddingTop={index > 0 ? '16px' : 0}
                >
                    <MjmlGroup cssClass="configurator-box" width="100%">
                        <ImageSection group={group} paddingRight={0} />
                        <MjmlColumn padding={0} paddingLeft="16px" width="70%">
                            <SubTitleText>{group.title}</SubTitleText>

                            <SubTitleText variant="secondary">
                                {group.rightData?.price
                                    ? formatAmountWithCurrency(group.rightData.price)
                                    : group.rightData?.text}
                            </SubTitleText>

                            {group.description?.length === 1 &&
                                group.description.map(
                                    text =>
                                        text && (
                                            <SubTitleText
                                                key={`${group.title}.${text}`}
                                                paddingTop={0}
                                                variant="secondary"
                                            >
                                                {text}
                                            </SubTitleText>
                                        )
                                )}
                        </MjmlColumn>
                    </MjmlGroup>

                    {group.description?.length > 1 && (
                        <MjmlGroup cssClass="configurator-box" width="100%">
                            <MjmlColumn cssClass="image-box" padding={0} width="30%" />
                            <MjmlColumn padding={0} paddingLeft="16px" width="70%">
                                {group.description.map(
                                    (text, index) =>
                                        text && (
                                            <SubTitleText
                                                key={`${group.title}.${text}`}
                                                paddingTop={index > 0 ? '10px' : '6px'}
                                                variant="secondary"
                                            >
                                                <div className="list-item">{text}</div>
                                            </SubTitleText>
                                        )
                                )}
                            </MjmlColumn>
                        </MjmlGroup>
                    )}
                </MjmlSection>
            ))}
        </>
    );
};

export default ConfiguratorBlocksSections;
