import { MjmlColumn, MjmlSection } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import {
    Company,
    MobilityBookingLocation,
    LocalVariant,
    Customer,
    Dealer,
    DealerTranslationText,
} from '../../../database';
import { getMobilityLocationData } from '../../../database/helpers/applications';
import { MobilityAsset } from '../../../schema/resolvers/enums';
import renderMarkdown from '../../../utils/renderMarkdown';
import useApplyMobilityFields from '../../utils/useApplyMobilityFields';
import useTranslatedString from '../../utils/useTranslatedString';
import { Text } from '../TextSection';
import { BookingButtonsSection } from './BookingButtonsSection';

type IntroductionSectionProps = {
    customerName: string;
    introTitle: DealerTranslationText;
    variant: LocalVariant;
    location: MobilityBookingLocation;
    start: string;
    end: string;
    currentYear: string;
    contentText: DealerTranslationText;
    company: Company;
    amendLink: string;
    cancelLink: string;
    mobilityAsset: MobilityAsset;
    mobilityBookingID: string;
    applicant: Customer;
    dealer: Dealer;
};

const IntroductionSection = ({
    applicant,
    customerName,
    introTitle,
    variant,
    start,
    end,
    contentText,
    location,
    company,
    amendLink,
    cancelLink,
    currentYear,
    mobilityAsset,
    mobilityBookingID,
    dealer,
}: IntroductionSectionProps) => {
    const { t } = useTranslation(['emails', 'common']);
    const translate = useTranslatedString();

    const getAppliedText = useApplyMobilityFields();

    const locationInfo = getMobilityLocationData(t, location, applicant);

    const dealerId = dealer._id.toHexString();

    const otherData = {
        companyName: translate(company.companyName),
        variantName: translate(variant.name),
        reservedVariantName: translate(variant.name),
        pickupTime: start,
        rentalPeriod: end,
        locationAddress: locationInfo.address,
        locationUrl: locationInfo.url,
        locationPhone: locationInfo.phone,
        locationName: locationInfo.name,
        locationEmail: locationInfo.email,
        companyPhone: `+${company.phone.prefix} ${company.phone.value}`,
        companyEmail: company.email,
        currentYear,
        customerName,
        mobilityBookingID,
    };

    const translatedIntroTitle = getAppliedText({
        value:
            introTitle.overrides?.find(
                ({ dealerId: applicationDealerId }) => applicationDealerId.toHexString() === dealerId
            )?.value ?? introTitle.defaultValue,
        ...otherData,
    });

    const translatedIntroContent = getAppliedText({
        value:
            contentText.overrides?.find(
                ({ dealerId: applicationDealerId }) => applicationDealerId.toHexString() === dealerId
            )?.value ?? contentText.defaultValue,
        ...otherData,
    });

    return (
        <>
            <MjmlSection>
                <MjmlColumn>
                    <Text align="left" css-class="blue-link">
                        {translatedIntroTitle ? renderMarkdown(translatedIntroTitle) : ''}
                    </Text>

                    <Text align="left" css-class="blue-link" lineHeight="30px">
                        <>
                            <p>{t('emails:createUser.greeting', { displayName: customerName })}</p>
                            <p>{translatedIntroContent ? renderMarkdown(translatedIntroContent) : ''}</p>
                        </>
                    </Text>
                </MjmlColumn>
            </MjmlSection>
            {(mobilityAsset === MobilityAsset.BookingConfirmation ||
                mobilityAsset === MobilityAsset.BookingAmendment) && (
                <BookingButtonsSection amendLink={amendLink} cancelLink={cancelLink} company={company} />
            )}
        </>
    );
};

export default IntroductionSection;
