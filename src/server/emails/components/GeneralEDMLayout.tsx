import { MjmlColumn, MjmlSection } from '@faire/mjml-react';
import { isEmpty } from 'lodash/fp';
import { ReactElement, ReactNode, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { getUrlForPublicUpload } from '../../core/storage';
import { EmailContext } from '../../database/helpers/settings';
import useTranslatedString from '../utils/useTranslatedString';
import EDMLayout from './EDMLayout';
import { Copyright } from './common/ui';

export type GeneralEDMLayoutProps = {
    children: ReactNode | ReactElement;
    emailContext: EmailContext;
    withTextWrapper?: boolean;
};

const GeneralEDMLayout = ({ children, emailContext, withTextWrapper }: GeneralEDMLayoutProps): ReactElement => {
    const { t } = useTranslation(['common', 'emails']);
    const translate = useTranslatedString();
    const { company, companyName } = emailContext;

    const connectTextWithVariables = company?.edmEmailFooter?.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:generalEdm.footer.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const disclaimerTextWithVariables = company?.edmEmailFooter?.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:generalEdm.footer.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const socialMedia = (company?.edmEmailFooter?.socialMedia || [])
        .map(socialMedia => {
            if (!socialMedia.url || !socialMedia.icon) {
                return null;
            }

            return { ...socialMedia, icon: getUrlForPublicUpload(socialMedia.icon) };
        })
        .filter(Boolean);

    const content = withTextWrapper ? (
        <MjmlSection fullWidth>
            <MjmlColumn>{children}</MjmlColumn>
        </MjmlSection>
    ) : (
        children
    );

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:generalEdm.footer.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            hideFooter={isEmpty(company)}
            legalNotice={{
                url: company?.edmEmailFooter?.legalNoticeUrl,
                text: t('emails:generalEdm.footer.legalNotice'),
            }}
            privacyPolicy={{
                url: company?.edmEmailFooter?.privacyPolicyUrl,
                text: t('emails:generalEdm.footer.privacyPolicy'),
            }}
            socialMedia={socialMedia}
            title="subject"
        >
            {content}
        </EDMLayout>
    );
};

export default GeneralEDMLayout;
