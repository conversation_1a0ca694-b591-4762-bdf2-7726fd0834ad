import { MjmlColumn, MjmlSection, MjmlTable } from '@faire/mjml-react';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getKYCPresetsForCustomerModule } from '../../../database';
import {
    ApplicationInsurancing,
    ApplicationKind,
    ApplicationModule,
    Bank,
    Customer,
    Dealer,
    InsuranceProductType,
    Insurer,
    LocalCustomerManagementModule,
    LocalInsuranceProduct,
} from '../../../database/documents';
import { Application, ApplicationWithDocuments } from '../../../database/documents/Applications/kinds';
import { Company } from '../../../database/documents/Company';
import { getInsuranceCustomerData } from '../../../integrations/insurance/eazy';
import useFormats from '../../utils/useFormats';
import useGetDisclaimerData from '../../utils/useGetDisclaimerData';
import useTranslatedString from '../../utils/useTranslatedString';
import TitleSection from '../TitleSection';
import DisclaimerSection from './DisclaimerSection';
import StageIdentifier from './StageIdentifier';
import { SectionInfoRow } from './ui';

type InsuranceApplicationSummaryProps = {
    application: ApplicationWithDocuments;
    applicationModule: ApplicationModule;
    company: Company;
    customer: Customer;
    dealer: Dealer;
    insurer: Insurer;
    bank: Bank;
    link?: string;
    customerModule: LocalCustomerManagementModule;
    insuranceProduct: LocalInsuranceProduct;
};

export const getInsuranceDetails = (application: Application) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
        case ApplicationKind.SalesOffer:
            return application.insurancing;

        default:
            return null;
    }
};

export const getCommentsToInsurer = (application: Application) => {
    if (!application) {
        return null;
    }

    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
            return application.commentsToInsurer;

        default:
            return null;
    }
};

export const shouldDisplayDateOfBirthDisclaimer = (
    insurancing: ApplicationInsurancing,
    insuranceProduct: LocalInsuranceProduct,
    customer: { dateOfBirth: Date }
) => {
    const isErgoInsuranceProduct = insuranceProduct?.type === InsuranceProductType.ErgoLookupTable;

    return !isErgoInsuranceProduct && !dayjs(insurancing.dateOfBirth).isSame(dayjs(customer.dateOfBirth), 'day');
};

const InsuranceApplicationSummary = ({
    application,
    applicationModule,
    bank,
    company,
    dealer,
    insurer,
    customer,
    link,
    customerModule,
    insuranceProduct,
}: InsuranceApplicationSummaryProps) => {
    const { t } = useTranslation(['common', 'emails']);
    const translate = useTranslatedString();
    const { formatAmountWithCurrency, formatAmount } = useFormats(
        company?.currency || '',
        company?.roundings?.amount?.decimals || 0,
        company?.roundings?.percentage?.decimals || 0
    );
    const insuranceDetails = getInsuranceDetails(application);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);
    const commentsToInsurer = getCommentsToInsurer(application);

    const customerData = getInsuranceCustomerData(customer, company, kycPresets, t, commentsToInsurer);
    const displayDateOfBirthDisclaimer = shouldDisplayDateOfBirthDisclaimer(
        insuranceDetails,
        insuranceProduct,
        customerData
    );

    const { defaultDisclaimer, dealerDisclaimer, insurancePremium, sumInsured } = useMemo(() => {
        const defaultDisclaimer = insurer.insuranceDisclaimer?.defaultValue;
        const dealerDisclaimer = insurer.insuranceDisclaimer?.overrides?.find(insuranceDisclaimer =>
            insuranceDisclaimer.dealerId.equals(dealer._id)
        )?.value;
        const { insurancePremium, sumInsured } = insuranceDetails;

        return { defaultDisclaimer, dealerDisclaimer, insurancePremium, sumInsured };
    }, [dealer, insuranceDetails, insurer]);

    const disclaimerText = useGetDisclaimerData(
        applicationModule,
        company,
        dealer,
        bank,
        defaultDisclaimer,
        dealerDisclaimer,
        undefined,
        insurancePremium,
        sumInsured
    );

    return (
        <>
            <TitleSection>{t('emails:component.title.insuranceApplicationSummary')}</TitleSection>
            <MjmlSection padding={0}>
                <MjmlColumn>
                    <MjmlTable padding={0}>
                        <SectionInfoRow
                            className="insuranceIdentifier"
                            label={t('emails:component.insurance.id')}
                            value={<StageIdentifier identifier={application.insuranceStage?.identifier} link={link} />}
                        />
                        <SectionInfoRow
                            className="insuranceProduct"
                            label={t('emails:component.insurance.legalName')}
                            value={translate(insuranceProduct.legalName)}
                        />
                        <SectionInfoRow
                            className="insurancePremium"
                            label={t('emails:component.insurance.premium')}
                            value={formatAmountWithCurrency(insuranceDetails.insurancePremium)}
                        />
                        <SectionInfoRow
                            className="sumInsured"
                            label={t('emails:component.insurance.sumInsured')}
                            value={formatAmountWithCurrency(insuranceDetails.sumInsured)}
                        />
                    </MjmlTable>
                </MjmlColumn>
            </MjmlSection>
            {displayDateOfBirthDisclaimer && (
                <DisclaimerSection
                    disclaimers={[
                        t('emails:insurance.dateOfBirthDisclaimer', {
                            oldDateOfBirth: t('common:formats.date', { date: insuranceDetails.dateOfBirth }),
                            suminsured: insuranceDetails.sumInsured ? formatAmount(insuranceDetails.sumInsured) : '',
                        }),
                    ]}
                />
            )}
            {disclaimerText && <DisclaimerSection disclaimers={disclaimerText} />}
        </>
    );
};
export default InsuranceApplicationSummary;
