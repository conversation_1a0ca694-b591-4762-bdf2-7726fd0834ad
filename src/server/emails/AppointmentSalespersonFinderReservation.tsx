import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Customer, FinderVehicle, LocalCustomerManagementModule, getKYCPresetsForCustomerModule } from '../database';
import { getCustomerName, getCustomerTitleOrSalutation } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import { getVehicleName } from '../database/helpers/vehicles';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import renderMarkdown from '../utils/renderMarkdown';
import replaceCurlyBracketString from '../utils/replaceCurlyBracketString';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import ListAppointmentBookingSection, {
    ListAppointmentBookingSectionProps,
} from './components/appointment/ListAppointmentBookingSection';
import { DealerInformationSection } from './components/common';
import { Copyright, DetailSection } from './components/common/ui';
import useTranslatedString from './utils/useTranslatedString';

export type AppointmentSalespersonFinderReservationProps = {
    // contexts
    emailContext: EmailContext;
    edmEmailFooterContext: EdmEmailFooterContext;
    dealerEmailContext: DealerEmailContext;

    // relations
    variant: FinderVehicle;
    applicant: Customer;
    cancelledAppointments: ListAppointmentBookingSectionProps['appointments'];

    // others
    introImageUrl: string;
    introTitle: string;
    contentText: string;

    listTestDriveTitle?: string;
    listTestDriveItem?: string;
    isSummaryVehicleVisible?: boolean;

    customerModule: LocalCustomerManagementModule;
};

const AppointmentSalespersonFinderReservation = (props: AppointmentSalespersonFinderReservationProps) => {
    const {
        emailContext,
        edmEmailFooterContext,
        dealerEmailContext,

        variant,
        applicant,

        introImageUrl,
        introTitle,
        contentText,
        cancelledAppointments,

        customerModule,
        listTestDriveTitle,
        listTestDriveItem,
        isSummaryVehicleVisible,
    } = props;

    const { t } = useTranslation(['common', 'emails']);
    const translate = useTranslatedString();
    const { company, companyName } = emailContext;

    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:appointment.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:appointment.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const variantName = translate(getVehicleName(variant));

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerName = getCustomerName(t, applicant, company, kycPresets);
    const customerTitleOrSalutation = getCustomerTitleOrSalutation(t, applicant);

    const translatedIntroTitle = replaceCurlyBracketString(introTitle, {
        vehicleName: variantName,
        companyName,
        customerName,
        title: customerTitleOrSalutation,
        salutation: customerTitleOrSalutation,
    });

    const translatedContentText = replaceCurlyBracketString(contentText, {
        title: customerTitleOrSalutation,
        salutation: customerTitleOrSalutation,
        customerName,
        companyName,
        vehicleName: variantName,
    });

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:appointment.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{
                url: company.edmEmailFooter.legalNoticeUrl,
                text: t('emails:appointment.general.legalNotice'),
            }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:appointment.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}

            <MjmlWrapper padding="40px 32px 60px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        <Text align="left" level="h2" paddingBottom={0} paddingTop={0}>
                            {renderMarkdown(translatedIntroTitle)}
                        </Text>
                        <Text align="left" level="p" paddingTop="40px">
                            {renderMarkdown(translatedContentText)}
                        </Text>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {isSummaryVehicleVisible && (
                <DetailSection>
                    <ListAppointmentBookingSection
                        appointments={cancelledAppointments}
                        itemText={listTestDriveItem}
                        titleText={listTestDriveTitle}
                    />
                </DetailSection>
            )}

            <DealerInformationSection
                contactUsText="emails:appointment.general.contactUs"
                contentAlign="center"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};
export default AppointmentSalespersonFinderReservation;
