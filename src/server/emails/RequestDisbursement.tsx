import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { ObjectId } from 'mongodb';
import { useTranslation } from 'react-i18next';
import { Bank, LocalVariant, FinanceProduct, Application, User, Module } from '../database';
import type { EmailContext } from '../database/helpers/settings';
import FinancingDetails from './FinancingDetails';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type RequestDisbursementProps = {
    emailContext: EmailContext;
    variant: LocalVariant;
    bank: Bank;
    financeProduct: FinanceProduct;
    application: Application;
    assignee: User;
    hint?: string;
    module: Module;
    dealerId: ObjectId;
};

const RequestDisbursement = ({ emailContext, assignee, hint, ...props }: RequestDisbursementProps) => {
    const { t } = useTranslation('emails');
    const { company } = emailContext;

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:requestDisbursement.body.topic', {
                                initiatorName: assignee.displayName,
                                identifier: props.application.financingStage?.identifier,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
            <FinancingDetails {...props} company={company} />
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:requestDisbursement.body.content', {
                                hint,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default RequestDisbursement;
