import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import urlJoin from 'url-join';
import config from '../core/config';
import { CreateNewUserLink, User } from '../database';
import type { EmailContext } from '../database/helpers/settings';
import Button from './components/Button';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import useTranslatedString from './utils/useTranslatedString';

export type CreateNewUserEmailProps = {
    user: User;
    link: CreateNewUserLink;
    emailContext: EmailContext;
    pathname?: string;
    origin?: string;
};

const CreateNewUser = ({ user, link, emailContext, pathname = '', origin }: CreateNewUserEmailProps) => {
    const url = urlJoin(origin || config.applicationEndpoint, pathname, `l/${link.secret}`);
    const { t } = useTranslation('emails');
    const translate = useTranslatedString();

    const { company, companyName } = emailContext;

    const disclaimerTextWithVariables = company?.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:customerCancelConfirmation.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    return (
        <EDMLayout
            connectText=""
            copyrightText={t('emails:customerCancelConfirmation.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{
                url: company?.edmEmailFooter.legalNoticeUrl,
                text: t(`emails:createUser.general.legalNotice`),
            }}
            privacyPolicy={{
                url: company?.edmEmailFooter.privacyPolicyUrl,
                text: t(`emails:createUser.general.privacyPolicy`),
            }}
            socialMedia={[]}
            title="subject"
        >
            <MjmlWrapper padding="48px 32px 5px">
                <MjmlSection paddingBottom="34px" paddingTop={0}>
                    <MjmlColumn>
                        <Text align="left" level="h1" paddingTop={0}>
                            <span style={{ whiteSpace: 'pre-wrap' }}>{t('emails:createUser.introTitle')}</span>
                        </Text>
                    </MjmlColumn>
                </MjmlSection>

                <MjmlSection padding={0}>
                    <MjmlColumn>
                        <Text align="left" level="p">
                            {t('emails:createUser.greeting', { displayName: user.displayName })}
                        </Text>
                        <Text align="left" level="p" paddingTop="20px">
                            {t('emails:createUser.body.welcome')}
                        </Text>
                    </MjmlColumn>
                </MjmlSection>

                <MjmlSection paddingBottom={0} paddingTop={44}>
                    <MjmlColumn>
                        <Button align="left" href={url} variant="primary">
                            <Text align="left" level="p">
                                {t('emails:createUser.link.name')}
                            </Text>
                        </Button>
                    </MjmlColumn>
                </MjmlSection>

                <MjmlSection paddingBottom={0} paddingTop={44}>
                    <MjmlColumn>
                        <Text align="left" level="p">
                            {t('emails:createUser.link.expiration')}
                        </Text>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>
        </EDMLayout>
    );
};

export default CreateNewUser;
