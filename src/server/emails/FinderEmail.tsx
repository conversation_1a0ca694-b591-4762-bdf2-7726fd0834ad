import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import {
    ApplicationJourney,
    ApplicationStage,
    Bank,
    Customer,
    Dealer,
    FinderApplication,
    FinderApplicationModule,
    FinderVehicle,
    Insurer,
    LocalCustomerManagementModule,
    LocalFinanceProduct,
    LocalInsuranceProduct,
    PromoCode,
    getKYCPresetsForCustomerModule,
} from '../database';
import { getCustomerEmail, getCustomerName } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import { getVehicleName } from '../database/helpers/vehicles';
import { ApplicationStagesLink } from '../queues/implementations/shared/getApplicationStagesLink';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import {
    BookingInformation,
    CustomerContent,
    DealerInformationSection,
    FinanceApplicationSummary,
    InsuranceApplicationSummary,
    PaymentInformation,
    PriceSummary,
} from './components/common';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';
import TechnicalDataSection from './components/vehicleTechnicalData';
import { AudienceMessage } from './type';
import useFormats from './utils/useFormats';
import useScenarioViewable from './utils/useScenarioViewable';
import useTranslatedString from './utils/useTranslatedString';

type AudienceSpecificProps =
    | {
          audience: AudienceMessage.Customer;
          introTitle: string;
          contentText: string;
          isSummaryVehicleVisible?: boolean;
      }
    | {
          audience: AudienceMessage.Salesperson;
          applicationStagesLink: ApplicationStagesLink;
      };

export type FinderEmailProps = {
    // contexts
    emailContext: EmailContext;
    edmEmailFooterContext: EdmEmailFooterContext;
    dealerEmailContext: DealerEmailContext;

    // relations
    dealer: Dealer;
    variant: FinderVehicle;
    application: FinderApplication;
    applicationModule: FinderApplicationModule;
    financeProduct: LocalFinanceProduct;
    journey: ApplicationJourney;
    applicant: Customer;
    customerModule: LocalCustomerManagementModule;
    bank?: Bank;
    insurer?: Insurer;
    promoCode?: PromoCode;
    insuranceProduct: LocalInsuranceProduct;

    // others
    submissionType: string;
    introImageUrl: string;
    applicationId: string;
    isBankViewable: boolean;
    applyNewSubmission?: ApplicationJourney['applyNewSubmission'];
    currentStages: ApplicationStage[];
} & AudienceSpecificProps;

const FinderEmail = (props: FinderEmailProps) => {
    const {
        emailContext,
        edmEmailFooterContext,
        dealerEmailContext,
        variant,
        dealer,
        application,
        applicationModule,
        financeProduct,
        journey,
        applicant,
        bank,
        insurer,
        promoCode,
        submissionType,
        introImageUrl,
        applicationId,
        isBankViewable,
        applyNewSubmission,
        customerModule,
        currentStages,
        insuranceProduct,
    } = props;

    const alias = props;

    const { t } = useTranslation(['common', 'emails']);
    const translate = useTranslatedString();
    const { company, companyName } = emailContext;

    const { formatAmountWithCurrency } = useFormats(
        company?.currency || '',
        company?.roundings?.amount?.decimals || 0,
        company?.roundings?.percentage?.decimals || 0
    );

    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:finder.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:finder.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const variantName = translate(getVehicleName(variant));

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerName = getCustomerName(t, applicant, company, kycPresets);
    const customerEmail = getCustomerEmail(t, applicant);

    const { sendBookingSection, sendFinancingSection, sendInsuranceSection, sendPaymentSection } = useScenarioViewable(
        currentStages,
        applyNewSubmission,
        application,
        journey,
        bank,
        insurer
    );

    const handleIsSummaryVehicle = alias.audience === AudienceMessage.Customer ? alias?.isSummaryVehicleVisible : true;

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:finder.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{ url: company.edmEmailFooter.legalNoticeUrl, text: t('emails:standard.general.legalNotice') }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:finder.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}

            <MjmlWrapper padding="40px 32px 60px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        {alias.audience === AudienceMessage.Customer && (
                            <>
                                <CustomerContent
                                    applicant={applicant}
                                    applicationId={applicationId}
                                    company={company}
                                    companyName={companyName}
                                    contentText={alias.contentText}
                                    customerModule={customerModule}
                                    introTitle={alias.introTitle}
                                    reservationAmount={
                                        journey.deposit?.amount ? formatAmountWithCurrency(journey.deposit.amount) : ''
                                    }
                                    submissionType={submissionType}
                                    variantName={variantName}
                                />
                                <Text align="left" fontWeight="900" level="p" paddingTop="20px">
                                    {t('emails:finderEmail.porscheFinder.referenceNumber', {
                                        applicationId,
                                    })}
                                </Text>
                            </>
                        )}
                        {alias.audience === AudienceMessage.Salesperson && (
                            <Text align="left" level="p" paddingTop="20px">
                                <Trans
                                    i18nKey="emails:finderEmail.porscheFinder.introSalesperson"
                                    values={{
                                        variantName,
                                        companyName,
                                        submissionType,
                                        applicationId,
                                        customerName,
                                    }}
                                >
                                    {submissionType} {applicationId} has been submitted by {customerName}
                                </Trans>
                            </Text>
                        )}
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {handleIsSummaryVehicle && (
                <DetailSection>
                    <TechnicalDataSection variant={variant} />

                    <PriceSummary
                        application={application}
                        applicationModule={applicationModule}
                        bank={bank}
                        company={company}
                        dealer={dealer}
                        link={
                            alias.audience === AudienceMessage.Salesperson
                                ? alias.applicationStagesLink[ApplicationStage.Reservation]
                                : undefined
                        }
                        promoCode={promoCode}
                        variant={variant}
                    />

                    {sendBookingSection && (
                        <>
                            <SectionSpacer />
                            <BookingInformation
                                appointmentDate={application.appointmentStage?.bookingTimeSlot?.slot}
                                appointmentId={application.appointmentStage?.identifier}
                                link={
                                    alias.audience === AudienceMessage.Salesperson
                                        ? alias.applicationStagesLink[ApplicationStage.Appointment]
                                        : undefined
                                }
                                timeZone={company.timeZone}
                                visitAppointmentDate={application.visitAppointmentStage?.bookingTimeSlot?.slot}
                                visitAppointmentId={application.visitAppointmentStage?.identifier}
                                visitLink={
                                    alias.audience === AudienceMessage.Salesperson
                                        ? alias.applicationStagesLink[ApplicationStage.VisitAppointment]
                                        : undefined
                                }
                            />
                        </>
                    )}

                    {sendPaymentSection && (
                        <>
                            <SectionSpacer />
                            <PaymentInformation
                                application={application}
                                company={company}
                                customerEmail={customerEmail}
                                customerFullName={customerName}
                                deposit={journey.deposit}
                            />
                        </>
                    )}

                    {sendFinancingSection && (
                        <>
                            <SectionSpacer />
                            <FinanceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                dealer={dealer}
                                financeProduct={financeProduct}
                                link={
                                    alias.audience === AudienceMessage.Salesperson
                                        ? alias.applicationStagesLink[ApplicationStage.Financing]
                                        : undefined
                                }
                                showBank={isBankViewable}
                            />
                        </>
                    )}

                    {sendInsuranceSection && (
                        <>
                            <SectionSpacer />
                            <InsuranceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                customer={applicant}
                                customerModule={customerModule}
                                dealer={dealer}
                                insuranceProduct={insuranceProduct}
                                insurer={insurer}
                                link={
                                    alias.audience === AudienceMessage.Salesperson
                                        ? alias.applicationStagesLink[ApplicationStage.Insurance]
                                        : undefined
                                }
                            />
                        </>
                    )}
                </DetailSection>
            )}

            <DealerInformationSection
                contactUsText="emails:finder.general.contactUs"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};
export default FinderEmail;
