import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import {
    AllowedApplicationForFinancing,
    ApplicationJourney,
    ApplicationKind,
    ApplicationModule,
    ApplicationStage,
    Dealer,
    LocalCustomerManagementModule,
    PromoCode,
} from '../database/documents';
import { getKYCPresetsForCustomerModule } from '../database/helpers';
import { getCustomerEmail, getCustomerName } from '../database/helpers/customers';
import { getApplicationIdentifier } from '../utils/application';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import renderMarkdown from '../utils/renderMarkdown';
import replaceCurlyBracketString from '../utils/replaceCurlyBracketString';
import { SalespersonSubmissionConfirmationProps } from './SalespersonSubmissionConfirmation';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import {
    BookingInformation,
    DealerInformationSection,
    FinanceApplicationSummary,
    PaymentInformation,
    PriceSummary,
} from './components/common';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';
import TechnicalDataSection from './components/vehicleTechnicalData';
import useTranslatedString from './utils/useTranslatedString';

type SalespersonCancelConfirmationProps = Omit<
    SalespersonSubmissionConfirmationProps,
    'application' | 'module' | 'dealerId'
> & {
    edmEmailFooterContext: EdmEmailFooterContext;
    dealerEmailContext: DealerEmailContext;
    journey: ApplicationJourney;
    introImageUrl: string;
    dealer: Dealer;
    applicationModule: ApplicationModule;
    application: AllowedApplicationForFinancing;
    stage: ApplicationStage;
    isBankViewable: boolean;
    promoCode?: PromoCode;
    customerModule: LocalCustomerManagementModule;
    introTitle?: string;
    contentText?: string;
    isSummaryVehicleVisible?: boolean;
    // only applicable to appointment stages
    hasTestDriveProcess: boolean;
};

const SalespersonCancelConfirmation = ({
    emailContext,
    assignee,
    customer,
    link,
    edmEmailFooterContext,
    variant,
    bank,
    financeProduct,
    journey,
    introImageUrl,
    application,
    dealerEmailContext,
    dealer,
    applicationModule,
    stage,
    isBankViewable,
    promoCode,
    customerModule,
    introTitle,
    contentText,
    isSummaryVehicleVisible,
    lead,
    hasTestDriveProcess,
}: SalespersonCancelConfirmationProps) => {
    const { t } = useTranslation(['emails', 'common']);
    const translate = useTranslatedString();

    const { company, companyName } = emailContext;

    const fullName = assignee.displayName;
    const firstName = assignee.displayName;
    const lastName = '';
    const identifier = getApplicationIdentifier(application, lead, stage);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, company, kycPresets);
    const customerEmail = getCustomerEmail(t, customer);

    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:salesPersonCancelConfirmation.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:salesPersonCancelConfirmation.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const isSummaryVehicleVisibleCheck = application.kind === ApplicationKind.Standard ? isSummaryVehicleVisible : true;

    const translatedIntroTitle =
        application.kind === ApplicationKind.Standard
            ? replaceCurlyBracketString(introTitle, {
                  initiatorName: t('emails:recipientName', {
                      fullName,
                      firstName,
                      lastName,
                      customerName,
                  }),
              })
            : t('emails:salesPersonCancelConfirmation.body.topic', {
                  initiatorName: t('emails:recipientName', {
                      fullName,
                      firstName,
                      lastName,
                      customerName,
                  }),
                  identifier,
              });

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:salesPersonCancelConfirmation.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{
                url: company.edmEmailFooter.legalNoticeUrl,
                text: t('emails:salesPersonCancelConfirmation.general.legalNotice'),
            }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:salesPersonCancelConfirmation.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}
            <MjmlWrapper padding="40px 32px 60px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        <Text align="left" level="p" paddingTop={0}>
                            <span style={{ whiteSpace: 'pre-wrap' }}>{translatedIntroTitle}</span>
                        </Text>
                        <Text align="left" css-class="content-text-link" level="p" paddingTop="20px">
                            <span style={{ whiteSpace: 'pre-wrap' }}>
                                {application.kind === ApplicationKind.Standard ? (
                                    renderMarkdown(contentText)
                                ) : (
                                    <Trans
                                        components={{
                                            // eslint-disable-next-line max-len
                                            // eslint-disable-next-line jsx-a11y/anchor-has-content,jsx-a11y/control-has-associated-label
                                            url: <a href={link} style={{ color: '#0000FF' }} />,
                                        }}
                                        i18nKey="emails:salesPersonCancelConfirmation.body.content"
                                        values={{ link }}
                                    >
                                        You can access this application using the following link:{' '}
                                        <a href={link}>{link}</a>
                                    </Trans>
                                )}
                            </span>
                        </Text>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {isSummaryVehicleVisibleCheck && (
                <DetailSection>
                    <TechnicalDataSection variant={variant} />

                    {(stage !== ApplicationStage.Appointment || hasTestDriveProcess) && (
                        <PriceSummary
                            application={application}
                            applicationModule={applicationModule}
                            bank={bank}
                            company={company}
                            dealer={dealer}
                            promoCode={promoCode}
                            variant={variant}
                        />
                    )}

                    {stage === ApplicationStage.Appointment && !!application.appointmentStage && (
                        <>
                            {/* use section spacer when price summary is shown */}
                            {/* will need to refactor this that spacing shouldn't be this rigid for all */}
                            {hasTestDriveProcess && <SectionSpacer />}
                            <BookingInformation
                                appointmentDate={application.appointmentStage?.bookingTimeSlot?.slot}
                                appointmentId={application.appointmentStage?.identifier}
                                timeZone={company.timeZone}
                            />
                        </>
                    )}

                    {stage === ApplicationStage.VisitAppointment && !!application.visitAppointmentStage && (
                        <>
                            <SectionSpacer />
                            <BookingInformation
                                timeZone={company.timeZone}
                                visitAppointmentDate={application.visitAppointmentStage?.bookingTimeSlot?.slot}
                                visitAppointmentId={application.visitAppointmentStage?.identifier}
                            />
                        </>
                    )}

                    {stage === ApplicationStage.Reservation && journey.deposit && (
                        <>
                            <SectionSpacer />
                            <PaymentInformation
                                application={application}
                                company={company}
                                customerEmail={customerEmail}
                                customerFullName={customerName}
                                deposit={journey.deposit}
                            />
                        </>
                    )}

                    {stage === ApplicationStage.Financing && application.configuration.withFinancing && (
                        <>
                            <SectionSpacer />
                            <FinanceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                dealer={dealer}
                                financeProduct={financeProduct}
                                showBank={isBankViewable}
                            />
                        </>
                    )}
                </DetailSection>
            )}
            <DealerInformationSection
                contactUsText="emails:salesPersonCancelConfirmation.general.contactUs"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};

export default SalespersonCancelConfirmation;
