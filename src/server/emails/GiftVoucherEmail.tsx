import dayjs from 'dayjs';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { LocalVariant, DealerTranslationText, Dealer, GiftVoucher } from '../database';
import type { EmailContext } from '../database/helpers/settings';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import EDMLayout from './components/EDMLayout';
import VehicleImageSection from './components/VehicleImageSection';
import DealerInformationSection from './components/common/DealerInformationSection';
import AdditionalInformation from './components/giftVoucher/AdditionalInformation';
import GiftVoucherDescription from './components/giftVoucher/GiftVoucherDescription';
import IntroductionSection from './components/giftVoucher/IntroductionSection';
import RedemptionSection from './components/giftVoucher/RedemptionSection';
import Copyright from './components/mobility/Copyright';

export type GiftVoucherEmailProps = {
    emailContext: EmailContext;
    customerName: string;
    variant: LocalVariant;
    edmEmailFooterContext: EdmEmailFooterContext;
    introImageUrl: string;
    introTitle: DealerTranslationText;
    contentText: DealerTranslationText;
    dealerEmailContext: DealerEmailContext;
    dealer: Dealer;
    voucher: GiftVoucher;
    vehicleUrl: string;
};

const GiftVoucherEmail = ({
    emailContext,
    customerName,
    variant,
    edmEmailFooterContext,
    introImageUrl,
    introTitle,
    contentText,
    dealerEmailContext,
    dealer,
    voucher,
    vehicleUrl,
}: GiftVoucherEmailProps) => {
    const { t } = useTranslation(['common', 'emails']);
    const { company, companyName } = emailContext;

    const currentYear = dayjs().tz(company.timeZone).year().toString();

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} currentYear={currentYear} />,
        [company?.edmEmailFooter?.copyRight, currentYear]
    );

    return (
        <EDMLayout
            connectText={t('emails:standard.general.connectText', { companyName })}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:standard.general.copyright')}
            disclaimerText={t('emails:standard.general.disclaimerText', { companyName })}
            emailContext={emailContext}
            legalNotice={{ url: company.edmEmailFooter.legalNoticeUrl, text: t('emails:standard.general.legalNotice') }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:standard.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}

            <IntroductionSection
                company={company}
                contentText={contentText}
                customerName={customerName}
                dealer={dealer}
                introTitle={introTitle}
                variant={variant}
                vehicleUrl={vehicleUrl}
                voucher={voucher}
            />

            <GiftVoucherDescription company={company} variant={variant} voucher={voucher} />
            <RedemptionSection vehicleUrl={vehicleUrl} voucher={voucher} />
            <AdditionalInformation voucher={voucher} />

            <DealerInformationSection
                contactUsText="emails:standard.general.contactUs"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};

export default GiftVoucherEmail;
