import { MjmlText } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import urlJoin from 'url-join';
import config from '../core/config';
import { VerifyEmailUpdateLink, User } from '../database/documents';
import { EmailContext } from '../database/helpers/settings';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type ResetPasswordEmailProps = {
    user: User;
    link: VerifyEmailUpdateLink;
    emailContext: EmailContext;
    origin?: string;
};

const VerifyEmailUpdate = ({ user, link, emailContext, origin }: ResetPasswordEmailProps) => {
    const url = urlJoin(origin || config.applicationEndpoint, `/l/${link.secret}`);
    const { t } = useTranslation('emails');

    return (
        <GeneralEDMLayout emailContext={emailContext} withTextWrapper>
            <MjmlText>{t('emails:verifyEmailUpdate.greeting', { displayName: user.displayName })}</MjmlText>
            <MjmlText lineHeight="30px">{t('emails:verifyEmailUpdate.body.description')}</MjmlText>
            <MjmlText>
                <a href={url} style={{ color: '#0000FF' }}>
                    {t('emails:verifyEmailUpdate.body.link.name')}
                </a>
            </MjmlText>
            <MjmlText>{t('emails:verifyEmailUpdate.body.ignore')}</MjmlText>
        </GeneralEDMLayout>
    );
};

export default VerifyEmailUpdate;
