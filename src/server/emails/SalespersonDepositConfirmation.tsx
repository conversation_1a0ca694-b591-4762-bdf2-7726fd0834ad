import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import {
    Application,
    ApplicationJourney,
    ApplicationKind,
    ApplicationStage,
    Company,
    Customer,
    Lead,
    LocalCustomerManagementModule,
    LocalVariant,
    User,
} from '../database/documents';
import { getKYCPresetsForCustomerModule } from '../database/helpers';
import { getCustomerName } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import { getApplicationStage } from '../utils/application';
import GeneralEDMLayout from './components/GeneralEDMLayout';
import useFormats from './utils/useFormats';

export type SalespersonDepositConfirmationProps = {
    emailContext: EmailContext;
    application: Application;
    lead: Lead;
    variant: LocalVariant;
    journey: ApplicationJourney;
    customer: Customer;
    assignee: User;
    company: Company;
    customerModule: LocalCustomerManagementModule;
};

const getApplicationType = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.Financing:
            return 'Application';

        case ApplicationStage.Lead:
            return 'Lead';

        case ApplicationStage.Reservation:
            return 'Reservation';

        default:
            throw new Error('Invalid stage');
    }
};

const PaymentSection = ({
    application,
    lead,
    journey,
    variant,
    company,
}: Pick<SalespersonDepositConfirmationProps, 'application' | 'journey' | 'variant' | 'company' | 'lead'>) => {
    const { t } = useTranslation(['emails', 'common']);

    if (!journey.deposit || application.kind === ApplicationKind.Mobility) {
        return null;
    }

    const { deposit } = journey;

    const { formatAmountWithCurrency } = useFormats(
        company?.currency || '',
        company?.roundings?.amount?.decimals || 0,
        company?.roundings?.percentage?.decimals || 0
    );

    const stage = getApplicationStage(application, lead, [ApplicationStage.Financing, ApplicationStage.Reservation]);

    return (
        <>
            <p style={{ fontWeight: 'bold' }}>
                {t('emails:salesPersonDepositConfirmation.body.paymentSection.title', {
                    applicationStage: getApplicationType(stage.stage),
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:salesPersonDepositConfirmation.body.paymentSection.applicationId', {
                    applicationStage: getApplicationType(stage.stage),
                    identifier: stage.value?.identifier,
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:salesPersonDepositConfirmation.body.paymentSection.paymentMethod', {
                    paymentMethod: deposit.paymentMethod,
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:salesPersonDepositConfirmation.body.paymentSection.paymentDate', {
                    paymentDate: dayjs(deposit.completedAt)
                        .tz(company.timeZone)
                        .format(t('common:formats.dateTimeFormat')),
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:salesPersonDepositConfirmation.body.paymentSection.amountPaid', {
                    paymentAmount: formatAmountWithCurrency(deposit.amount),
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:salesPersonDepositConfirmation.body.paymentSection.selectedVehicle', {
                    vehicle: variant.name.defaultValue,
                })}
            </p>
        </>
    );
};

const SalespersonDepositConfirmation = ({
    emailContext,
    assignee,
    customerModule,
    customer,
    ...props
}: SalespersonDepositConfirmationProps) => {
    const { t } = useTranslation('emails');
    const fullName = assignee.displayName;
    const firstName = assignee.displayName;
    const lastName = '';

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, props.company, kycPresets);

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:salesPersonDepositConfirmation.body.introduction', {
                                customerName: t('emails:recipientName', {
                                    fullName,
                                    firstName,
                                    lastName,
                                    customerName,
                                }),
                            })}
                        </p>
                        <PaymentSection {...props} />
                        <p>
                            {t('emails:salesPersonDepositConfirmation.body.contact', {
                                assignee: {
                                    name: assignee.displayName,
                                    phone: `+${assignee.mobile.prefix} ${assignee.mobile.value}`,
                                    email: assignee.email,
                                },
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default SalespersonDepositConfirmation;
