import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FinderVehicle, LocalVariant, TestDriveApplication } from '../database';
import type { EmailContext } from '../database/helpers/settings';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import renderMarkdown from '../utils/renderMarkdown';
import replaceCurlyBracketString from '../utils/replaceCurlyBracketString';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import { DealerInformationSection } from './components/common';
import { Copyright, DetailSection } from './components/common/ui';
import AppointmentEmailSummary from './components/summary/AppointmentEmailSummary';
import useTranslatedString from './utils/useTranslatedString';

export type AppointmentEndTestDriveReminderProps = {
    // contexts
    emailContext: EmailContext;
    edmEmailFooterContext: EdmEmailFooterContext;
    dealerEmailContext: DealerEmailContext;

    // relations
    variant: FinderVehicle | LocalVariant;
    application: TestDriveApplication;

    // others
    introImageUrl: string;
    isSummaryVehicleVisible: boolean;

    introTitle: string;
    contentText: string;
    link: string;
    timeToShowReminder: number;
};

const AppointmentEndTestDriveReminder = (props: AppointmentEndTestDriveReminderProps) => {
    const {
        emailContext,
        edmEmailFooterContext,
        dealerEmailContext,

        variant,
        application,

        introImageUrl,
        isSummaryVehicleVisible,

        introTitle,
        link,
        contentText,
        timeToShowReminder = '0',
    } = props;

    const { t } = useTranslation(['emails', 'common']);
    const translate = useTranslatedString();
    const { company, companyName } = emailContext;

    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:appointment.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:appointment.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const translatedIntroTitle = replaceCurlyBracketString(introTitle, {});

    const translatedContentText = replaceCurlyBracketString(contentText, {
        hours: timeToShowReminder,
        link,
    });

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:appointment.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{
                url: company.edmEmailFooter.legalNoticeUrl,
                text: t('emails:appointment.general.legalNotice'),
            }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:appointment.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}
            <MjmlWrapper padding="27px 0px 47px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        <>
                            <Text align="left" level="h2" paddingBottom={0} paddingTop={0}>
                                {renderMarkdown(translatedIntroTitle)}
                            </Text>
                            <Text align="left" css-class="content-text-link" level="p" paddingTop="8px">
                                {renderMarkdown(translatedContentText)}
                            </Text>
                        </>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {isSummaryVehicleVisible && (
                <DetailSection>
                    <AppointmentEmailSummary
                        application={application}
                        company={company}
                        link={link}
                        t={t}
                        variant={variant}
                    />
                </DetailSection>
            )}

            <DealerInformationSection
                contactUsText="emails:appointment.general.contactUs"
                contentAlign="center"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};
export default AppointmentEndTestDriveReminder;
