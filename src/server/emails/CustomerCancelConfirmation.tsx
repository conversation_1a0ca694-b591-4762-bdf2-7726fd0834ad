import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
    ApplicationStage,
    Bank,
    Customer,
    FinanceProduct,
    FinderVehicle,
    LocalVariant,
    User,
    getKYCPresetsForCustomerModule,
} from '../database';
import {
    AllowedApplicationForFinancing,
    ApplicationJourney,
    ApplicationKind,
    ApplicationModule,
    Dealer,
    LocalCustomerManagementModule,
    PromoCode,
} from '../database/documents';
import { getCustomerEmail, getCustomerName } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import {
    BookingInformation,
    DealerInformationSection,
    FinanceApplicationSummary,
    PaymentInformation,
    PriceSummary,
} from './components/common';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';
import TechnicalDataSection from './components/vehicleTechnicalData';
import useTranslatedString from './utils/useTranslatedString';

export type CustomerCancelConfirmationProps = {
    emailContext: EmailContext;
    variant: LocalVariant | FinderVehicle;
    bank: Bank;
    financeProduct: FinanceProduct;
    assignee: User;
    customer: Customer;
    edmEmailFooterContext: EdmEmailFooterContext;
    dealerEmailContext: DealerEmailContext;
    journey: ApplicationJourney;
    introImageUrl: string;
    dealer: Dealer;
    applicationModule: ApplicationModule;
    application: AllowedApplicationForFinancing;
    stage: ApplicationStage;
    isBankViewable: boolean;
    promoCode?: PromoCode;
    customerModule: LocalCustomerManagementModule;
    introTitle?: string;
    contentText?: string;
    isSummaryVehicleVisible?: boolean;
    // only applicable to appointment stages
    hasTestDriveProcess: boolean;
};

const CustomerCancelConfirmation = ({
    emailContext,
    assignee,
    customer,
    edmEmailFooterContext,
    variant,
    bank,
    financeProduct,
    journey,
    introImageUrl,
    application,
    dealerEmailContext,
    dealer,
    applicationModule,
    stage,
    isBankViewable,
    promoCode,
    customerModule,
    introTitle,
    contentText,
    isSummaryVehicleVisible,
    hasTestDriveProcess,
}: CustomerCancelConfirmationProps) => {
    const { t } = useTranslation(['common', 'emails']);
    const translate = useTranslatedString();
    const { company, companyName } = emailContext;

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, company, kycPresets);
    const customerEmail = getCustomerEmail(t, customer);

    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:customerCancelConfirmation.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:customerCancelConfirmation.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const isSummaryVehicleVisibleCheck = application.kind === ApplicationKind.Standard ? isSummaryVehicleVisible : true;

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:customerCancelConfirmation.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{
                url: company.edmEmailFooter.legalNoticeUrl,
                text: t('emails:customerCancelConfirmation.general.legalNotice'),
            }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:customerCancelConfirmation.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}
            <MjmlWrapper padding="40px 32px 60px">
                {application.kind === ApplicationKind.Standard && introTitle && (
                    <MjmlSection paddingBottom="30px">
                        <MjmlColumn>
                            <Text align="left" level="h1" paddingTop={0}>
                                <span style={{ whiteSpace: 'pre-wrap' }}>{introTitle}</span>
                            </Text>
                        </MjmlColumn>
                    </MjmlSection>
                )}
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        <Text align="left" level="p" paddingTop={0}>
                            <span style={{ whiteSpace: 'pre-wrap' }}>
                                {t('emails:customerCancelConfirmation.greeting', {
                                    customerName,
                                })}
                            </span>
                        </Text>
                        <Text align="left" level="p" paddingTop="20px">
                            <span style={{ whiteSpace: 'pre-wrap' }}>
                                {t('emails:customerCancelConfirmation.introduction')}
                            </span>
                        </Text>
                        <Text align="left" level="p" paddingTop="20px">
                            {application.kind === ApplicationKind.Standard ? (
                                <span style={{ whiteSpace: 'pre-wrap' }}>{contentText}</span>
                            ) : (
                                <span style={{ whiteSpace: 'pre-wrap' }}>
                                    {t('emails:customerCancelConfirmation.body.contact', {
                                        assignee: {
                                            name: assignee.displayName,
                                            phone: `+${assignee.mobile.prefix} ${assignee.mobile.value}`,
                                            email: assignee.email,
                                        },
                                    })}
                                </span>
                            )}
                        </Text>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {isSummaryVehicleVisibleCheck && (
                <DetailSection>
                    <TechnicalDataSection variant={variant} />

                    {(stage !== ApplicationStage.Appointment || hasTestDriveProcess) && (
                        <PriceSummary
                            application={application}
                            applicationModule={applicationModule}
                            bank={bank}
                            company={company}
                            dealer={dealer}
                            promoCode={promoCode}
                            variant={variant}
                        />
                    )}

                    {stage === ApplicationStage.Appointment && !!application.appointmentStage && (
                        <>
                            {/* use section spacer when price summary is shown */}
                            {/* will need to refactor this that spacing shouldn't be this rigid for all */}
                            {hasTestDriveProcess && <SectionSpacer />}
                            <BookingInformation
                                appointmentDate={application.appointmentStage?.bookingTimeSlot?.slot}
                                appointmentId={application.appointmentStage?.identifier}
                                timeZone={company.timeZone}
                            />
                        </>
                    )}

                    {stage === ApplicationStage.VisitAppointment && !!application.visitAppointmentStage && (
                        <>
                            <SectionSpacer />
                            <BookingInformation
                                timeZone={company.timeZone}
                                visitAppointmentDate={application.visitAppointmentStage?.bookingTimeSlot?.slot}
                                visitAppointmentId={application.visitAppointmentStage?.identifier}
                            />
                        </>
                    )}

                    {stage === ApplicationStage.Reservation && journey.deposit && (
                        <>
                            <SectionSpacer />
                            <PaymentInformation
                                application={application}
                                company={company}
                                customerEmail={customerEmail}
                                customerFullName={customerName}
                                deposit={journey.deposit}
                            />
                        </>
                    )}

                    {stage === ApplicationStage.Financing && application.configuration.withFinancing && (
                        <>
                            <SectionSpacer />
                            <FinanceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                dealer={dealer}
                                financeProduct={financeProduct}
                                showBank={isBankViewable}
                            />
                        </>
                    )}
                </DetailSection>
            )}

            <DealerInformationSection
                contactUsText="emails:customerCancelConfirmation.general.contactUs"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};

export default CustomerCancelConfirmation;
