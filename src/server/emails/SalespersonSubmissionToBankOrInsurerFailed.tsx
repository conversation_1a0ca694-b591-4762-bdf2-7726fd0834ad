import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
    AllowedApplicationForFinancing,
    ApplicationModule,
    ApplicationWithDocuments,
    Bank,
    Customer,
    Dealer,
    FinanceProduct,
    FinderVehicle,
    Insurer,
    LocalCustomerManagementModule,
    LocalInsuranceProduct,
    LocalVariant,
} from '../database';
import type { EmailContext } from '../database/helpers/settings';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import renderMarkdown from '../utils/renderMarkdown';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import { DealerInformationSection, FinanceApplicationSummary, InsuranceApplicationSummary } from './components/common';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';
import TechnicalDataSection from './components/vehicleTechnicalData';
import useTranslatedString from './utils/useTranslatedString';

type BankProps = {
    type: 'bank';
    financeProduct: FinanceProduct;
    isBankViewable: boolean;
    application: AllowedApplicationForFinancing;
};

type InsuranceProps = {
    type: 'insurance';
    customer: Customer;
    customerModule: LocalCustomerManagementModule;
    insuranceProduct: LocalInsuranceProduct;
    insurer: Insurer;
    application: ApplicationWithDocuments;
};

export type SalespersonSubmissionToBankOrInsurerFailedProps = {
    emailContext: EmailContext;
    edmEmailFooterContext: EdmEmailFooterContext;
    dealerEmailContext: DealerEmailContext;
    variant: FinderVehicle | LocalVariant;
    isSummaryVehicleVisible?: boolean;
    link: string;
    introTitle: string;
    contentText: string;
    applicationModule: ApplicationModule;
    bank: Bank;
    dealer: Dealer;
} & (BankProps | InsuranceProps);

const SalespersonSubmissionToBankOrInsurerFailed = (props: SalespersonSubmissionToBankOrInsurerFailedProps) => {
    const {
        emailContext,
        edmEmailFooterContext,
        dealerEmailContext,
        variant,
        application,
        applicationModule,
        isSummaryVehicleVisible = true,
        type,
        link,
        introTitle,
        contentText,
        bank,
        dealer,
    } = props;

    const { financeProduct = undefined, isBankViewable = undefined } = type === 'bank' ? props : {};

    const {
        customer = undefined,
        customerModule = undefined,
        insuranceProduct = undefined,
        insurer = undefined,
    } = type === 'insurance' ? props : {};

    const { t } = useTranslation(['emails', 'common']);
    const translate = useTranslatedString();
    const { company, companyName } = emailContext;

    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:appointment.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:appointment.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:appointment.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{
                url: company.edmEmailFooter.legalNoticeUrl,
                text: t('emails:appointment.general.legalNotice'),
            }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:appointment.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            <MjmlWrapper padding="27px 0px 47px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        <>
                            <Text align="left" level="h2" paddingBottom={0} paddingTop={0}>
                                {introTitle}
                            </Text>
                            <Text align="left" css-class="content-text-link" level="p" paddingTop="8px">
                                {renderMarkdown(contentText)}
                            </Text>
                        </>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {isSummaryVehicleVisible && (
                <DetailSection>
                    <TechnicalDataSection variant={variant} />
                    {type === 'bank' ? (
                        <>
                            <SectionSpacer />
                            <FinanceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                dealer={dealer}
                                financeProduct={financeProduct}
                                link={link}
                                showBank={isBankViewable}
                            />
                        </>
                    ) : (
                        <>
                            <SectionSpacer />
                            <InsuranceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                customer={customer}
                                customerModule={customerModule}
                                dealer={dealer}
                                insuranceProduct={insuranceProduct}
                                insurer={insurer}
                                link={link}
                            />
                        </>
                    )}
                </DetailSection>
            )}

            <DealerInformationSection
                contactUsText="emails:appointment.general.contactUs"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};
export default SalespersonSubmissionToBankOrInsurerFailed;
