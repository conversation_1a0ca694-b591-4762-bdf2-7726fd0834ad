import { MjmlText } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import { Application } from '../database';
import type { EmailContext } from '../database/helpers/settings';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type SendPasswordForAgreementToBankProps = {
    emailContext: EmailContext;
    application: Application;
    randomPassword: string;
};

const SendPasswordForAgreementToBank = ({
    emailContext,
    application,
    randomPassword,
}: SendPasswordForAgreementToBankProps) => {
    const { t } = useTranslation('emails');

    return (
        <GeneralEDMLayout emailContext={emailContext} withTextWrapper>
            <MjmlText>
                {t('emails:bankSubmission.passwordBody', { applicationId: application.financingStage?.identifier })}
            </MjmlText>
            <MjmlText fontWeight="900" lineHeight="30px">
                {randomPassword}
            </MjmlText>
        </GeneralEDMLayout>
    );
};

export default SendPasswordForAgreementToBank;
