import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import type { EmailContext } from '../database/helpers/settings';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type AssigneeReassignmentProps = {
    emailContext: EmailContext;
    assignedBy: {
        displayName: string;
    };
    previousAssignee: {
        displayName: string;
    };
    newAssignee: {
        displayName: string;
    };
    assignedEntities: {
        applications: { identifier: string; link: string }[];
        leadGenForms: { identifier: string; link: string }[];
        modules: { identifier: string; link: string }[];
    };
};

const CommaSeparatedIdentifiers = ({ entities }: { entities: { identifier: string; link: string }[] }) => (
    <p>
        {entities.map((entity, index) => (
            <span key={entity.identifier}>
                <a href={entity.link} rel="noopener noreferrer" style={{ color: '#0000FF' }} target="_blank">
                    {entity.identifier}
                </a>
                {index < entities.length - 1 && ', '}
            </span>
        ))}
    </p>
);

const AssigneeReassignment = ({
    emailContext,
    assignedBy,
    previousAssignee,
    newAssignee,
    assignedEntities,
}: AssigneeReassignmentProps) => {
    const { t } = useTranslation('emails');

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:assigneeReassignment.greeting', {
                                newAssignee: newAssignee.displayName,
                            })}
                        </p>
                        <p>
                            {t('emails:assigneeReassignment.body.information', {
                                assignedBy: assignedBy.displayName,
                                previousAssignee: previousAssignee.displayName,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>

            <MjmlSection>
                <MjmlColumn>
                    {assignedEntities.applications.length > 0 ? (
                        <MjmlText align="left">
                            <p>
                                <b>{t('emails:assigneeReassignment.body.assignedApplications')}</b>
                            </p>
                            <CommaSeparatedIdentifiers entities={assignedEntities.applications} />
                        </MjmlText>
                    ) : null}

                    {assignedEntities.modules.length > 0 ? (
                        <MjmlText align="left">
                            <p>
                                <b>{t('emails:assigneeReassignment.body.assignedModules')}</b>
                            </p>
                            <CommaSeparatedIdentifiers entities={assignedEntities.modules} />
                        </MjmlText>
                    ) : null}

                    {assignedEntities.leadGenForms.length > 0 ? (
                        <MjmlText align="left">
                            <p>
                                <b>{t('emails:assigneeReassignment.body.assignedLeadGenForms')}</b>
                            </p>
                            <CommaSeparatedIdentifiers entities={assignedEntities.leadGenForms} />
                        </MjmlText>
                    ) : null}
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default AssigneeReassignment;
