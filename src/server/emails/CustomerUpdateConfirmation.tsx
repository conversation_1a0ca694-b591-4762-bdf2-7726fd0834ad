import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationStage } from '../database/documents';
import { getKYCPresetsForCustomerModule } from '../database/helpers';
import { getCustomerName } from '../database/helpers/customers';
import { type CustomerSubmissionConfirmationProps } from './CustomerSubmissionConfirmation';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import { DealerInformationSection, FinanceApplicationSummary, PriceSummary } from './components/common';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';
import TechnicalDataSection from './components/vehicleTechnicalData';
import { AudienceMessage } from './type';
import useScenarioViewable from './utils/useScenarioViewable';
import useTranslatedString from './utils/useTranslatedString';

type CustomerUpdateConfirmationProps = CustomerSubmissionConfirmationProps;

const CustomerUpdateConfirmation = (props: CustomerUpdateConfirmationProps) => {
    const {
        emailContext,
        edmEmailFooterContext,
        dealerEmailContext,

        variant,
        bank,
        financeProduct,
        application,
        customer,
        dealer,
        applicationModule,
        promoCode,

        introImageUrl,
        identifier,
        isBankViewable,
        customerModule,
        currentStages,
        applyNewSubmission,
        journey,
    } = props;

    const alias = props;

    const { t } = useTranslation(['common', 'emails']);
    const { company, companyName } = emailContext;
    const translate = useTranslatedString();

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, company, kycPresets);

    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:standard.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:standard.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const { sendFinancingSection } = useScenarioViewable(currentStages, applyNewSubmission, application, journey, bank);

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:standard.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{ url: company.edmEmailFooter.legalNoticeUrl, text: t('emails:standard.general.legalNotice') }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:standard.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}
            <MjmlWrapper padding="40px 32px 60px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        {alias.audience === AudienceMessage.Customer && (
                            <>
                                <Text align="left" level="p" paddingTop={0}>
                                    <span style={{ whiteSpace: 'pre-wrap' }}>
                                        {t('emails:customerUpdateConfirmation.greeting', {
                                            customerName,
                                        })}
                                    </span>
                                </Text>
                                <Text align="left" level="p" paddingTop="20px">
                                    <span style={{ whiteSpace: 'pre-wrap' }}>
                                        {t('emails:customerUpdateConfirmation.introduction')}
                                    </span>
                                </Text>
                                <Text align="left" level="p" paddingTop="20px">
                                    <span style={{ whiteSpace: 'pre-wrap' }}>
                                        {t('emails:customerUpdateConfirmation.body.content', {
                                            hint: alias.hint,

                                            // hint is provided, but it's needed for the next text to be in new line
                                            // contain password or attachment hint
                                            hintLineBreak: alias.hint ? '\n\n' : '',
                                        })}
                                    </span>
                                </Text>
                            </>
                        )}
                        {alias.audience === AudienceMessage.Salesperson && (
                            <Text align="left" level="p" paddingTop={0}>
                                <span style={{ whiteSpace: 'pre-wrap' }}>
                                    {t('emails:salesPersonUpdateConfirmation.body.topic', {
                                        initiatorName: alias.assignee.displayName,
                                        identifier,
                                    })}
                                </span>
                            </Text>
                        )}
                        <Text align="left" fontWeight="900" level="p" paddingTop="20px">
                            {t('emails:customerSubmissionConfirmation.financingTitle', {
                                identifier,
                            })}
                        </Text>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            <DetailSection>
                <TechnicalDataSection variant={variant} />

                <PriceSummary
                    application={application}
                    applicationModule={applicationModule}
                    bank={bank}
                    company={company}
                    dealer={dealer}
                    link={
                        alias.audience === AudienceMessage.Salesperson
                            ? alias.applicationStagesLink?.[ApplicationStage.Reservation]
                            : undefined
                    }
                    promoCode={promoCode}
                    variant={variant}
                />

                {sendFinancingSection && (
                    <>
                        <SectionSpacer />
                        <FinanceApplicationSummary
                            application={application}
                            applicationModule={applicationModule}
                            bank={bank}
                            company={company}
                            dealer={dealer}
                            financeProduct={financeProduct}
                            link={
                                alias.audience === AudienceMessage.Salesperson
                                    ? alias.applicationStagesLink?.[ApplicationStage.Financing]
                                    : undefined
                            }
                            showBank={isBankViewable}
                        />
                    </>
                )}
            </DetailSection>

            <DealerInformationSection
                contactUsText="emails:standard.general.contactUs"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};

export default CustomerUpdateConfirmation;
