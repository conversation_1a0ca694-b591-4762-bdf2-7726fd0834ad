import { MjmlColumn, MjmlText, MjmlSection } from '@faire/mjml-react';
import { Trans, useTranslation } from 'react-i18next';
import {
    Application,
    ApplicationStage,
    Bank,
    Customer,
    FinanceProduct,
    Lead,
    LocalCustomerManagementModule,
    LocalVariant,
    User,
} from '../database/documents';
import { getKYCPresetsForCustomerModule } from '../database/helpers';
import { getCustomerName } from '../database/helpers/customers';
import { EmailContext } from '../database/helpers/settings';
import { getApplicationIdentifier } from '../utils/application';
import FinancingDetailsStandard from './FinancingDetailsStandard';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type SalespersonApplicationSubmissionConfirmationProps = {
    emailContext: EmailContext;
    variant: LocalVariant;
    bank: Bank;
    financeProduct: FinanceProduct;
    application: Application;
    lead: Lead;
    assignee: User;
    customer: Customer;
    link: string;
    customerModule: LocalCustomerManagementModule;
};

const SalespersonApplicationSubmissionConfirmation = ({
    emailContext,
    assignee,
    customer,
    link,
    customerModule,
    lead,
    ...props
}: SalespersonApplicationSubmissionConfirmationProps) => {
    const { t } = useTranslation('emails');
    const { company } = emailContext;
    const fullName = assignee.displayName;
    const firstName = assignee.displayName;
    const lastName = '';
    const { application } = props;

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, company, kycPresets);

    const identifier = getApplicationIdentifier(application, lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Lead,
    ]);

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p style={{ fontWeight: 'bold' }}>
                            {t('emails:salesPersonSubmissionConfirmation.body.topic', {
                                initiatorName: t('emails:recipientName', {
                                    fullName,
                                    firstName,
                                    lastName,
                                    customerName,
                                }),
                                identifier,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
            <FinancingDetailsStandard {...props} company={company} />
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left" cssClass="blue-link">
                        <p>
                            <Trans i18nKey="emails:salesPersonSubmissionConfirmation.body.content" values={{ link }}>
                                You can access this application using the following link: <a href={link}>{link}</a>
                            </Trans>
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default SalespersonApplicationSubmissionConfirmation;
