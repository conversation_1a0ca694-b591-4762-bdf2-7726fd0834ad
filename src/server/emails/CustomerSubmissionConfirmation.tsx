import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
    ApplicationJourney,
    ApplicationStage,
    Bank,
    Customer,
    Dealer,
    FinanceProduct,
    Insurer,
    LocalCustomerManagementModule,
    LocalInsuranceProduct,
    LocalVariant,
    PromoCode,
    StandardApplication,
    StandardApplicationModule,
    User,
} from '../database/documents';
import { getKYCPresetsForCustomerModule } from '../database/helpers';
import { getCustomerEmail, getCustomerName } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import { ApplicationStagesLink } from '../queues/implementations/shared/getApplicationStagesLink';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import renderMarkdown from '../utils/renderMarkdown';
import EDMLayout from './components/EDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import {
    BookingInformation,
    DealerInformationSection,
    FinanceApplicationSummary,
    InsuranceApplicationSummary,
    PaymentInformation,
    PriceSummary,
} from './components/common';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';
import TechnicalDataSection from './components/vehicleTechnicalData';
import { AudienceMessage } from './type';
import useScenarioViewable from './utils/useScenarioViewable';
import useTranslatedString from './utils/useTranslatedString';

type AudienceSpecificProps =
    | {
          audience: AudienceMessage.Customer;
          hint: string;
      }
    | {
          audience: AudienceMessage.Salesperson;
          assignee: User;
          applicationStagesLink: ApplicationStagesLink;
      };

export type CustomerSubmissionConfirmationProps = {
    // contexts
    emailContext: EmailContext;
    edmEmailFooterContext: EdmEmailFooterContext;
    dealerEmailContext: DealerEmailContext;

    // relations
    variant: LocalVariant;
    bank: Bank;
    financeProduct: FinanceProduct;
    application: StandardApplication;
    customer: Customer;
    customerModule: LocalCustomerManagementModule;
    dealer: Dealer;
    applicationModule: StandardApplicationModule;
    journey: ApplicationJourney;
    insurer?: Insurer;
    promoCode?: PromoCode;

    insuranceProduct: LocalInsuranceProduct;
    // other
    introImageUrl: string;
    identifier: string;
    isBankViewable: boolean;
    applyNewSubmission?: ApplicationJourney['applyNewSubmission'];
    currentStages: ApplicationStage[];

    introTitle?: string;
    contentText?: string;
    isSummaryVehicleVisible?: boolean;
} & AudienceSpecificProps;

const CustomerSubmissionConfirmation = (props: CustomerSubmissionConfirmationProps) => {
    const {
        emailContext,
        edmEmailFooterContext,
        dealerEmailContext,

        variant,
        bank,
        financeProduct,
        application,
        customer,
        dealer,
        applicationModule,
        journey,
        insurer,
        promoCode,

        introImageUrl,
        identifier,
        isBankViewable,
        applyNewSubmission,
        customerModule,
        currentStages,
        insuranceProduct,

        introTitle,
        contentText,
        isSummaryVehicleVisible,
    } = props;

    // avoid eslint errors on `react/destructuring-assignment`
    const alias = props;

    const { t } = useTranslation(['common', 'emails']);
    const { company, companyName } = emailContext;
    const translate = useTranslatedString();
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, company, kycPresets);
    const customerEmail = getCustomerEmail(t, customer);

    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText, { companyName })
        : 'emails:standard.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText, { companyName })
        : 'emails:standard.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const { sendBookingSection, sendFinancingSection, sendInsuranceSection, sendPaymentSection } = useScenarioViewable(
        currentStages,
        applyNewSubmission,
        application,
        journey,
        bank,
        insurer
    );

    const isSummaryVehicleVisibleCheck = alias.audience === AudienceMessage.Customer ? isSummaryVehicleVisible : true;

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:standard.general.copyright')}
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{ url: company.edmEmailFooter.legalNoticeUrl, text: t('emails:standard.general.legalNotice') }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:standard.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}
            <MjmlWrapper padding="40px 32px 60px">
                {alias.audience === AudienceMessage.Customer && introTitle && (
                    <MjmlSection padding={0}>
                        <MjmlColumn>
                            <Text align="left" css-class="blue-link" level="h1" paddingTop={0}>
                                <span style={{ whiteSpace: 'pre-wrap' }}>{renderMarkdown(introTitle)}</span>
                            </Text>
                        </MjmlColumn>
                    </MjmlSection>
                )}
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        {alias.audience === AudienceMessage.Customer && (
                            <>
                                <Text align="left" level="p" paddingTop={0}>
                                    <span style={{ whiteSpace: 'pre-wrap' }}>
                                        {t('emails:customerSubmissionConfirmation.greeting', {
                                            customerName,
                                        })}
                                    </span>
                                </Text>
                                <Text align="left" level="p" paddingTop="20px">
                                    <span style={{ whiteSpace: 'pre-wrap' }}>
                                        {t('emails:customerSubmissionConfirmation.introduction')}
                                    </span>
                                </Text>
                                <Text align="left" css-class="blue-link" level="p" paddingTop="20px">
                                    <span style={{ whiteSpace: 'pre-wrap' }}>{renderMarkdown(contentText)}</span>
                                </Text>
                                <Text align="left" fontWeight="900" level="p" paddingTop="20px">
                                    {t('emails:customerSubmissionConfirmation.financingTitle', {
                                        identifier,
                                    })}
                                </Text>
                            </>
                        )}
                        {alias.audience === AudienceMessage.Salesperson && (
                            <Text align="left" level="p" paddingTop={0}>
                                <span style={{ whiteSpace: 'pre-wrap' }}>
                                    {t('emails:salesPersonSubmissionConfirmation.body.topic', {
                                        initiatorName: alias.assignee.displayName,
                                        identifier,
                                    })}
                                </span>
                            </Text>
                        )}
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {isSummaryVehicleVisibleCheck && (
                <DetailSection>
                    <TechnicalDataSection variant={variant} />

                    <PriceSummary
                        application={application}
                        applicationModule={applicationModule}
                        bank={bank}
                        company={company}
                        dealer={dealer}
                        link={
                            alias.audience === AudienceMessage.Salesperson
                                ? alias.applicationStagesLink[ApplicationStage.Reservation]
                                : undefined
                        }
                        promoCode={promoCode}
                        variant={variant}
                    />

                    {sendBookingSection && (
                        <>
                            <SectionSpacer />
                            <BookingInformation
                                appointmentDate={application.appointmentStage?.bookingTimeSlot?.slot}
                                appointmentId={application.appointmentStage?.identifier}
                                link={
                                    alias.audience === AudienceMessage.Salesperson
                                        ? alias.applicationStagesLink[ApplicationStage.Appointment]
                                        : undefined
                                }
                                timeZone={company.timeZone}
                                visitAppointmentDate={application.visitAppointmentStage?.bookingTimeSlot?.slot}
                                visitAppointmentId={application.visitAppointmentStage?.identifier}
                                visitLink={
                                    alias.audience === AudienceMessage.Salesperson
                                        ? alias.applicationStagesLink[ApplicationStage.VisitAppointment]
                                        : undefined
                                }
                            />
                        </>
                    )}

                    {sendPaymentSection && (
                        <>
                            <SectionSpacer />
                            <PaymentInformation
                                application={application}
                                company={company}
                                customerEmail={customerEmail}
                                customerFullName={customerName}
                                deposit={journey.deposit}
                            />
                        </>
                    )}

                    {sendFinancingSection && (
                        <>
                            <SectionSpacer />
                            <FinanceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                dealer={dealer}
                                financeProduct={financeProduct}
                                link={
                                    alias.audience === AudienceMessage.Salesperson
                                        ? alias.applicationStagesLink[ApplicationStage.Financing]
                                        : undefined
                                }
                                showBank={isBankViewable}
                            />
                        </>
                    )}

                    {sendInsuranceSection && (
                        <>
                            <SectionSpacer />
                            <InsuranceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                customer={customer}
                                customerModule={customerModule}
                                dealer={dealer}
                                insuranceProduct={insuranceProduct}
                                insurer={insurer}
                                link={
                                    alias.audience === AudienceMessage.Salesperson
                                        ? alias.applicationStagesLink[ApplicationStage.Insurance]
                                        : undefined
                                }
                            />
                        </>
                    )}
                </DetailSection>
            )}

            <DealerInformationSection
                contactUsText="emails:standard.general.contactUs"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};

export default CustomerSubmissionConfirmation;
