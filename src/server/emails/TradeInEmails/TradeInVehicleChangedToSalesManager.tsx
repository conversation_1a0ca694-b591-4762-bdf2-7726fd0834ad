import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { isNil } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import type { EmailContext } from '../../database/helpers/settings';
import BasicEDMLayout from '../components/BasicEDMLayout';
import mileageFormat from '../utils/helper';

export type TradeInVehicleChangedToSalesManagerPros = {
    emailContext: EmailContext;
    salesManagerName: string;
    salesConsultantName: string;
    customerName: string;
    vehicleMakeModel: string;
    vehicleYear: number;
    vin: string;
    mileage: number;
    transfersCount: number;
    vehicleHandoverDate: string;
    tradeInDetailLink: string;
};

const listType = {
    listStyleType: 'square',
    fontWeight: 'bold',
};
const TradeInVehicleChangedToSalesManager = ({
    emailContext,
    salesManagerName,
    salesConsultantName,
    customerName,
    vehicleMakeModel,
    vehicleYear,
    vin,
    mileage,
    transfersCount,
    vehicleHandoverDate,
    tradeInDetailLink,
    ...props
}: TradeInVehicleChangedToSalesManagerPros) => {
    const { t } = useTranslation('emails');

    return (
        <BasicEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left" fontWeight="bold">
                        <h3>{t('emails:tradeInVehicleChangedToSalesManager.title')}</h3>
                    </MjmlText>
                    <MjmlText align="left">
                        <p>
                            {t('emails:tradeInVehicleChangedToSalesManager.greeting', {
                                salesManagerName,
                            })}
                        </p>
                        <p>
                            {t('emails:tradeInVehicleChangedToSalesManager.introduction', {
                                salesConsultantName,
                                customerName,
                            })}
                        </p>
                        <p>
                            <ul style={{ lineHeight: '30px' }}>
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesManager.body.vehicleMakeModel', {
                                        vehicleMakeModel,
                                    })}
                                </li>
                                {!isNil(vehicleYear) && (
                                    <li style={listType}>
                                        {t('emails:tradeInVehicleChangedToSalesManager.body.vehicleYear', {
                                            vehicleYear,
                                        })}
                                    </li>
                                )}
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesManager.body.vin', {
                                        vin,
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesManager.body.mileage', {
                                        mileage: mileageFormat(mileage),
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesManager.body.transfersCount', {
                                        transfersCount,
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesManager.body.vehicleHandoverDate', {
                                        vehicleHandoverDate,
                                    })}
                                </li>
                            </ul>
                        </p>
                        <p>{t('emails:tradeInVehicleChangedToSalesManager.linkContent.message')}</p>
                        <p>
                            <a
                                href={tradeInDetailLink}
                                rel="noopener noreferrer"
                                style={{ color: '#000' }}
                                target="_blank"
                            >
                                {tradeInDetailLink}
                            </a>
                        </p>
                    </MjmlText>
                    <MjmlText align="left" fontWeight="bold" paddingTop="16px">
                        {t('emails:tradeInVehicleChangedToSalesManager.signature')}
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </BasicEDMLayout>
    );
};

export default TradeInVehicleChangedToSalesManager;
