import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { isNil } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import type { EmailContext } from '../../database/helpers/settings';
import BasicEDMLayout from '../components/BasicEDMLayout';
import mileageFormat from '../utils/helper';

export type TradeInPendingToSalesManagerPros = {
    emailContext: EmailContext;
    salesManagerName: string;
    salesConsultantName: string;
    customerName: string;
    vehicleMakeModel: string;
    vehicleYear: number;
    vin: string;
    mileage: number;
    transfersCount: number;
    vehicleHandoverDate: string;
    tradeInDetailLink: string;
};

const listType = {
    listStyleType: 'square',
    fontWeight: 'bold',
};
const TradeInPendingToSalesManager = ({
    emailContext,
    salesManagerName,
    salesConsultantName,
    customerName,
    vehicleMakeModel,
    vehicleYear,
    vin,
    mileage,
    transfersCount,
    vehicleHandoverDate,
    tradeInDetailLink,
    ...props
}: TradeInPendingToSalesManagerPros) => {
    const { t } = useTranslation('emails');

    return (
        <BasicEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left" fontWeight="bold">
                        <h3>{t('emails:tradeInPendingToSalesManager.title')}</h3>
                    </MjmlText>
                    <MjmlText align="left">
                        <p>
                            {t('emails:tradeInPendingToSalesManager.greeting', {
                                salesManagerName,
                            })}
                        </p>
                        <p>
                            {t('emails:tradeInPendingToSalesManager.introduction', {
                                salesConsultantName,
                                customerName,
                            })}
                        </p>
                        <p>
                            <ul style={{ lineHeight: '30px' }}>
                                <li style={listType}>
                                    {t('emails:tradeInPendingToSalesManager.body.vehicleMakeModel', {
                                        vehicleMakeModel,
                                    })}
                                </li>
                                {!isNil(vehicleYear) && (
                                    <li style={listType}>
                                        {t('emails:tradeInPendingToSalesManager.body.vehicleYear', {
                                            vehicleYear,
                                        })}
                                    </li>
                                )}
                                <li style={listType}>
                                    {t('emails:tradeInPendingToSalesManager.body.vin', {
                                        vin,
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInPendingToSalesManager.body.mileage', {
                                        mileage: mileageFormat(mileage),
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInPendingToSalesManager.body.transfersCount', {
                                        transfersCount,
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInPendingToSalesManager.body.vehicleHandoverDate', {
                                        vehicleHandoverDate,
                                    })}
                                </li>
                            </ul>
                        </p>
                        <p>{t('emails:tradeInPendingToSalesManager.linkContent.message')}</p>
                        <p>
                            <a
                                href={tradeInDetailLink}
                                rel="noopener noreferrer"
                                style={{ color: '#000' }}
                                target="_blank"
                            >
                                {tradeInDetailLink}
                            </a>
                        </p>
                    </MjmlText>
                    <MjmlText align="left" fontWeight="bold" paddingTop="16px">
                        {t('emails:tradeInPendingToSalesManager.signature')}
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </BasicEDMLayout>
    );
};

export default TradeInPendingToSalesManager;
