import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { isNil } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import type { EmailContext } from '../../database/helpers/settings';
import BasicEDMLayout from '../components/BasicEDMLayout';
import mileageFormat from '../utils/helper';
import useFormats from '../utils/useFormats';

export type TradeInVehicleChangedToSalesConsultantPros = {
    emailContext: EmailContext;
    salesManagerName: string;
    salesConsultantName: string;
    customerName: string;
    vehicleMakeModel: string;
    vehicleYear: number;
    vin: string;
    mileage: number;
    transfersCount: number;
    vehicleHandoverDate: string;
    tradeInValue: number;
    tradeInDetailLink: string;
};

const listType = {
    listStyleType: 'square',
};
const TradeInVehicleChangedToSalesConsultant = ({
    emailContext,
    salesManagerName,
    salesConsultantName,
    customerName,
    vehicleMakeModel,
    vehicleYear,
    vin,
    mileage,
    transfersCount,
    vehicleHandoverDate,
    tradeInValue,
    tradeInDetailLink,
    ...props
}: TradeInVehicleChangedToSalesConsultantPros) => {
    const { t } = useTranslation('emails');
    const { formatAmountWithCurrency } = useFormats(
        emailContext.company?.currency || '',
        emailContext.company?.roundings?.amount?.decimals || 0,
        emailContext.company?.roundings?.percentage?.decimals || 0
    );

    return (
        <BasicEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left" fontWeight="bold">
                        <h3>{t('emails:tradeInVehicleChangedToSalesConsultant.title')}</h3>
                    </MjmlText>
                    <MjmlText align="left">
                        <p>
                            {t('emails:tradeInVehicleChangedToSalesConsultant.greeting', {
                                salesConsultantName,
                            })}
                        </p>
                        <p>
                            {t('emails:tradeInVehicleChangedToSalesConsultant.introduction', {
                                salesManagerName,
                                customerName,
                            })}
                        </p>
                        <p>
                            <ul style={{ lineHeight: '30px' }}>
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesConsultant.body.vehicleMakeModel', {
                                        vehicleMakeModel,
                                    })}
                                </li>
                                {!isNil(vehicleYear) && (
                                    <li style={listType}>
                                        {t('emails:tradeInVehicleChangedToSalesConsultant.body.vehicleYear', {
                                            vehicleYear,
                                        })}
                                    </li>
                                )}
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesConsultant.body.vin', {
                                        vin,
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesConsultant.body.mileage', {
                                        mileage: mileageFormat(mileage),
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesConsultant.body.transfersCount', {
                                        transfersCount,
                                    })}
                                </li>
                                <li style={listType}>
                                    {t('emails:tradeInVehicleChangedToSalesConsultant.body.vehicleHandoverDate', {
                                        vehicleHandoverDate,
                                    })}
                                </li>
                                <li style={listType}>
                                    <strong>
                                        {t('emails:tradeInVehicleChangedToSalesConsultant.body.tradeInValue', {
                                            tradeInValue: formatAmountWithCurrency(tradeInValue),
                                        })}
                                    </strong>
                                </li>
                            </ul>
                        </p>
                        <p>{t('emails:tradeInVehicleChangedToSalesConsultant.linkContent.message')}</p>
                        <p>
                            <a
                                href={tradeInDetailLink}
                                rel="noopener noreferrer"
                                style={{ color: '#000' }}
                                target="_blank"
                            >
                                {tradeInDetailLink}
                            </a>
                        </p>
                    </MjmlText>
                    <MjmlText align="left" fontWeight="bold" paddingTop="16px">
                        {t('emails:tradeInVehicleChangedToSalesConsultant.signature')}
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </BasicEDMLayout>
    );
};

export default TradeInVehicleChangedToSalesConsultant;
