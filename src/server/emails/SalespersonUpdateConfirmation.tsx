import { MjmlColumn, MjmlText, MjmlSection } from '@faire/mjml-react';
import { useTranslation, Trans } from 'react-i18next';
import { ApplicationStage } from '../database/documents';
import { getKYCPresetsForCustomerModule } from '../database/helpers';
import { getCustomerName } from '../database/helpers/customers';
import { getApplicationIdentifier } from '../utils/application';
import FinancingDetails from './FinancingDetails';
import { SalespersonSubmissionConfirmationProps } from './SalespersonSubmissionConfirmation';
import GeneralEDMLayout from './components/GeneralEDMLayout';

type SalespersonUpdateConfirmationProps = SalespersonSubmissionConfirmationProps;

const SalespersonUpdateConfirmation = ({
    emailContext,
    assignee,
    customer,
    customerModule,
    link,
    ...props
}: SalespersonUpdateConfirmationProps) => {
    const { t } = useTranslation('emails');
    const { company } = emailContext;

    const fullName = assignee.displayName;
    const firstName = assignee.displayName;
    const lastName = '';
    const identifier = getApplicationIdentifier(props.application, props.lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Lead,
    ]);

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, company, kycPresets);

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:salesPersonUpdateConfirmation.body.topic', {
                                initiatorName: t('emails:recipientName', {
                                    fullName,
                                    firstName,
                                    lastName,
                                    customerName,
                                }),
                                identifier,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
            <FinancingDetails {...props} company={company} />
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left" cssClass="blue-link">
                        <p>
                            <Trans i18nKey="emails:salesPersonUpdateConfirmation.body.content" values={{ link }}>
                                You can access this application using the following link: <a href={link}>{link}</a>
                            </Trans>
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default SalespersonUpdateConfirmation;
