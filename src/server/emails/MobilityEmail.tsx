import { MjmlWrapper } from '@faire/mjml-react';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
    ApplicationJourneyDeposit,
    MobilityApplication,
    LocalVariant,
    TranslatedString,
    Customer,
    PromoCode,
    DealerTranslationText,
    Dealer,
    GiftVoucher,
} from '../database';
import { EmailContext } from '../database/helpers/settings';
import { MobilityAsset, MobilityRecipient } from '../schema/resolvers/enums';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import EDMLayout from './components/EDMLayout';
import VehicleImageSection from './components/VehicleImageSection';
import DealerInformationSection from './components/common/DealerInformationSection';
import Copyright from './components/mobility/Copyright';
import IntroductionSection from './components/mobility/IntroductionSection';
import MobilityEmailSummary from './components/summary/MobillityEmailSummary';

export type MobilityEmailProps = {
    emailContext: EmailContext;
    mobilityRecipient: MobilityRecipient;
    mobilityAsset: MobilityAsset;
    customerName: string;
    customerFullName: string;
    customerEmail: string;
    modelName: TranslatedString;
    application: MobilityApplication;
    variant: LocalVariant;
    edmEmailFooterContext: EdmEmailFooterContext;
    introImageUrl: string;
    introTitle: DealerTranslationText;
    contentText: DealerTranslationText;
    payment: ApplicationJourneyDeposit;
    amendLink: string;
    cancelLink: string;
    dealerEmailContext: DealerEmailContext;
    applicant: Customer;
    promoCode?: PromoCode;
    rentalDisclaimer?: TranslatedString;
    dealer: Dealer;
    giftVoucher?: GiftVoucher;
};

const grayColor = '#f0f0f0';

const MobilityEmail = ({
    emailContext,
    mobilityRecipient,
    mobilityAsset,
    customerName,
    customerFullName,
    customerEmail,
    variant,
    edmEmailFooterContext,
    application,
    introImageUrl,
    modelName,
    introTitle,
    contentText,
    payment,
    amendLink,
    cancelLink,
    dealerEmailContext,
    applicant,
    rentalDisclaimer,
    promoCode,
    dealer,
    giftVoucher,
}: MobilityEmailProps) => {
    const { t } = useTranslation(['common', 'emails']);
    const { company, companyName } = emailContext;
    const { mobilityBookingDetails, mobilitySnapshots } = application as MobilityApplication;
    const identifier = application.mobilityStage?.identifier;
    const { location } = mobilityBookingDetails;

    const start = dayjs(mobilityBookingDetails.period.start)
        .tz(company.timeZone)
        .format(t('common:formats.dateTimeFormat'));

    const end = dayjs(mobilityBookingDetails.period.end)
        .tz(company.timeZone)
        .format(t('common:formats.dateTimeFormat'));

    const currentYear = dayjs().tz(company.timeZone).year().toString();

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} currentYear={currentYear} />,
        [company?.edmEmailFooter?.copyRight, currentYear]
    );

    return (
        <EDMLayout
            connectText={t('emails:standard.general.connectText', { companyName })}
            copyrightRender={copyrightRender}
            copyrightText={t('emails:standard.general.copyright')}
            disclaimerText={t('emails:standard.general.disclaimerText', { companyName })}
            emailContext={emailContext}
            legalNotice={{ url: company.edmEmailFooter.legalNoticeUrl, text: t('emails:standard.general.legalNotice') }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:standard.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}

            <MjmlWrapper>
                <IntroductionSection
                    amendLink={amendLink}
                    applicant={applicant}
                    cancelLink={cancelLink}
                    company={company}
                    contentText={contentText}
                    currentYear={currentYear}
                    customerName={customerName}
                    dealer={dealer}
                    end={end}
                    introTitle={introTitle}
                    location={location}
                    mobilityAsset={mobilityAsset}
                    mobilityBookingID={identifier}
                    start={start}
                    variant={variant}
                />
            </MjmlWrapper>

            {mobilityAsset !== MobilityAsset.BookingComplete && mobilityAsset !== MobilityAsset.BookingCancellation && (
                <MjmlWrapper backgroundColor={grayColor} paddingLeft="25px" paddingRight="25px">
                    <MobilityEmailSummary
                        application={application}
                        company={company}
                        customerEmail={customerEmail}
                        customerFullName={customerFullName}
                        end={end}
                        giftVoucher={giftVoucher}
                        mobilityBookingDetails={mobilityBookingDetails}
                        mobilitySnapshots={mobilitySnapshots}
                        payment={payment}
                        promoCode={promoCode}
                        rentalDisclaimer={rentalDisclaimer}
                        start={start}
                        variant={variant}
                    />
                </MjmlWrapper>
            )}
            <DealerInformationSection
                contactUsText="emails:standard.general.contactUs"
                dealerEmailContext={dealerEmailContext}
            />
        </EDMLayout>
    );
};

export default MobilityEmail;
