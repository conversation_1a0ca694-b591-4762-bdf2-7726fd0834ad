import { MjmlColumn, MjmlGroup, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { Fragment, useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Vehicle } from '../database/documents';
import { EmailContext } from '../database/helpers/settings';
import { getVehicleName } from '../database/helpers/vehicles';
import { ConfiguratorEmailContext } from '../utils/getConfiguratorEmailContext';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import Button from './components/Button';
import EDMLayout from './components/EDMLayout';
import { HeaderText, Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import DealerInformationSection from './components/common/DealerInformationSection';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';
import ConfiguratorBlocksSections from './components/journey/ConfiguratorBlockSections';
import useTranslatedString from './utils/useTranslatedString';

export type ResumeConfiguratorApplicationProps = {
    resumeUrl: string;
    emailContext: EmailContext;
    dealerEmailContext: DealerEmailContext;
    configuratorEmailContext: ConfiguratorEmailContext;
    subject: string;
    edmEmailFooterContext: EdmEmailFooterContext;
    introTitle: string;
    contentText: string;
    variant: Vehicle;
};

const ResumeConfiguratorApplication = ({
    resumeUrl,
    emailContext,
    dealerEmailContext,
    configuratorEmailContext,
    subject,
    edmEmailFooterContext,
    introTitle,
    contentText,
    variant,
}: ResumeConfiguratorApplicationProps) => {
    const { t } = useTranslation();
    const translate = useTranslatedString();

    const { company, companyName } = emailContext;

    const { sections, images } = configuratorEmailContext;

    const { edmEmailFooter } = company;

    const connectTextWithVariables = edmEmailFooter.connectText
        ? translate(edmEmailFooter.connectText)
        : 'emails:configurator.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    const disclaimerTextWithVariables = edmEmailFooter.disclaimerText
        ? translate(edmEmailFooter.disclaimerText)
        : 'emails:configurator.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const variantName = translate(getVehicleName(variant));

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText="emails:configurator.general.copyright"
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{ url: edmEmailFooter.legalNoticeUrl, text: t('emails:configurator.general.legalNotice') }}
            privacyPolicy={{
                url: edmEmailFooter.privacyPolicyUrl,
                text: t('emails:configurator.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title={subject}
        >
            {images.saveOrder.banner && <VehicleImageSection url={images.saveOrder.banner} />}

            <MjmlWrapper padding="40px 32px 60px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        <Text align="left" level="h2" paddingBottom={0} paddingTop={0}>
                            <Trans defaults={introTitle} />
                        </Text>
                        <Text align="left" level="p" paddingTop="40px">
                            <Trans defaults={contentText} />
                        </Text>
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            {images.saveOrder.intro && <VehicleImageSection url={images.saveOrder.intro} />}

            <DetailSection>
                <MjmlSection paddingBottom="32px" paddingTop={0}>
                    <MjmlGroup>
                        <MjmlColumn>
                            <HeaderText>
                                {t('emails:configurator.submitOrder.orderSummary', {
                                    variantName,
                                })}
                            </HeaderText>
                        </MjmlColumn>
                    </MjmlGroup>
                </MjmlSection>

                {sections?.map((section, index) => (
                    <Fragment key={section.sectionTitle}>
                        <ConfiguratorBlocksSections company={company} section={section} />
                        {index < sections.length - 1 ? <SectionSpacer /> : null}
                    </Fragment>
                ))}

                <MjmlSection padding="48px 0 16px">
                    <MjmlColumn>
                        <Button align="left" href={resumeUrl} variant="primary">
                            {t('emails:configurator.saveOrder.resumeApplication', {
                                companyName,
                            })}
                        </Button>
                    </MjmlColumn>
                </MjmlSection>
            </DetailSection>
            <DealerInformationSection
                contactUsText="emails:configurator.general.contactUs"
                dealerEmailContext={dealerEmailContext}
                showContactUs
            />
        </EDMLayout>
    );
};

export default ResumeConfiguratorApplication;
