import { MjmlText } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import type { EmailContext } from '../database/helpers/settings';
import { OTP } from '../utils';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type ApprovalWithOTPProps = {
    emailContext: EmailContext;
    otp: OTP;
};

const ApprovalWithOTP = ({ emailContext, otp }: ApprovalWithOTPProps) => {
    const { t } = useTranslation('emails');

    return (
        <GeneralEDMLayout emailContext={emailContext} withTextWrapper>
            <MjmlText lineHeight="30px">{t('emails:otp.code', { code: otp.code })}</MjmlText>
            <MjmlText lineHeight="30px">{t('emails:otp.expiration', { expiration: otp.lifeTime })}</MjmlText>
        </GeneralEDMLayout>
    );
};

export default ApprovalWithOTP;
