import { TFunction } from 'i18next';
import { MobilityApplication, Company, LocalVariant, MobilityModule } from '../../../database';
import { getCustomerEmail, getCustomerFullNameWithTitle } from '../../../database/helpers/customers';
import { Loaders } from '../../../loaders';
import { getRentalDisclaimer } from '../../../queues/implementations/sendMobilityEmail';
import getKycPresetsForEmailSummary from './shared';

const getMobilitySummaryDetails = async (
    t: TFunction,
    application: MobilityApplication,
    company: Company,
    loaders: Loaders
) => {
    const module = (await loaders.moduleById.load(application.moduleId)) as MobilityModule;
    const applicant = await loaders.customerById.load(application.applicantId);
    const journey = await loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId);
    const kycPresets = await getKycPresetsForEmailSummary(applicant, loaders);
    const customerEmail = getCustomerEmail(t, applicant);
    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const { mobilityBookingDetails, mobilitySnapshots } = application as MobilityApplication;
    const promoCode = application.promoCodeId ? loaders.promoCodeById.load(application.promoCodeId) : null;
    const giftVoucher = application.giftVoucherSuiteId
        ? loaders.giftVoucherBySuiteId.load(application.giftVoucherSuiteId)
        : null;
    const payment = journey.deposit;
    const rentalDisclaimer = getRentalDisclaimer(module.rentalDisclaimer, application.dealerId.toString());
    const variant = (await loaders.vehicleById.load(application.vehicleId)) as LocalVariant;

    return {
        customerEmail,
        customerFullName,
        giftVoucher,
        mobilityBookingDetails,
        mobilitySnapshots,
        payment,
        promoCode,
        rentalDisclaimer,
        variant,
    };
};

export default getMobilitySummaryDetails;
