import { TFunction } from 'i18next';
import { EventApplication, LocalVariant, ApplicationStage, Company, Lead } from '../../../database';
import { getCustomerEmail, getCustomerNameInfo } from '../../../database/helpers/customers';
import { Loaders } from '../../../loaders';
import getApplicationStagesLink from '../../../queues/implementations/shared/getApplicationStagesLink';
import { getApplicationStages, sortStages } from '../../../utils/application';
import { AudienceMessage } from '../../type';
import getKycPresetsForEmailSummary from './shared';

const getStages = async (application: EventApplication, lead: Lead) => {
    const availableStages = getApplicationStages(application, lead).filter(stage => !!stage?.value?.assigneeId);
    const stages = sortStages(availableStages.map(({ stage }) => stage));

    return stages;
};

export const isStageViewable = (stages, stage: ApplicationStage) => stages.includes(stage);

const getEventSummaryDetails = async (
    t: TFunction,
    application: EventApplication,
    company: Company,
    loaders: Loaders
) => {
    const lead = await loaders.leadById.load(application.leadId);
    const audience = AudienceMessage.Salesperson;
    const applicant = await loaders.customerById.load(application.applicantId);
    const applicationStagesLink = await getApplicationStagesLink(application, lead);
    const kycPresets = await getKycPresetsForEmailSummary(applicant, loaders);
    const customerNameInfo = getCustomerNameInfo(t, applicant, company, kycPresets);
    const customerEmail = getCustomerEmail(t, applicant);
    const event = await loaders.eventById.load(application.eventId);
    const journey = await loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId);
    const variant = application.vehicleId
        ? await (loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>)
        : null;
    const checkModel = variant?.modelId ? await loaders.loadModelById.load(variant.modelId) : null;
    const model = checkModel?.parentModelId ? await loaders.loadModelById.load(checkModel.parentModelId) : checkModel;
    const stages = await getStages(application, lead);
    const sendBookingSection = isStageViewable(stages, ApplicationStage.Appointment) && application.appointmentStage;
    const sendPaymentSection = isStageViewable(stages, ApplicationStage.Reservation) && journey.deposit;

    return {
        applicationStagesLink,
        audience,
        customerNameInfo,
        customerEmail,
        event,
        journey,
        model,
        sendBookingSection,
        sendPaymentSection,
        variant,
    };
};

export default getEventSummaryDetails;
