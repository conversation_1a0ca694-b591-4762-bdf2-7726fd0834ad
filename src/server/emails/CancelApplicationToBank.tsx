import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { ObjectId } from 'mongodb';
import { useTranslation } from 'react-i18next';
import { Bank, LocalVariant, FinanceProduct, Application, User, FinderVehicle, Module } from '../database';
import type { EmailContext } from '../database/helpers/settings';
import FinancingDetails from './FinancingDetails';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type SubmitApplicationToBankProps = {
    emailContext: EmailContext;
    variant: LocalVariant | FinderVehicle;
    bank: Bank;
    financeProduct: FinanceProduct;
    application: Application;
    assignee: User;
    module: Module;
    dealerId: ObjectId;
};

const CancelApplicationToBank = ({ emailContext, assignee, ...props }: SubmitApplicationToBankProps) => {
    const { t } = useTranslation('emails');
    const { company } = emailContext;

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t(`emails:bankCancellation.body.topic`, {
                                initiatorName: assignee.displayName,
                                identifier: props.application.financingStage?.identifier,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
            <FinancingDetails {...props} company={company} />
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t(`emails:bankCancellation.body.content`, {
                                assignee: {
                                    name: assignee.displayName,
                                    phone: `+${assignee.mobile.prefix} ${assignee.mobile.value}`,
                                    email: assignee.email,
                                },
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default CancelApplicationToBank;
