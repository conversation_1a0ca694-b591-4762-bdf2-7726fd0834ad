import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { ObjectId } from 'mongodb';
import { useTranslation, Trans } from 'react-i18next';
import { Bank, LocalVariant, FinanceProduct, Application, User, PromoCode, Module } from '../database/documents';
import type { EmailContext } from '../database/helpers/settings';
import FinancingDetails from './FinancingDetails';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type SubmitApplicationToBankProps = {
    emailContext: EmailContext;
    variant: LocalVariant;
    bank: Bank;
    financeProduct: FinanceProduct;
    application: Application;
    assignee: User;
    link: string;
    promoCode?: PromoCode;
    hint?: string;
    module: Module;
    dealerId: ObjectId;
};

const SubmitApplicationToBank = ({ emailContext, assignee, hint, link, ...props }: SubmitApplicationToBankProps) => {
    const { t } = useTranslation('emails');
    const { company } = emailContext;

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left" cssClass="blue-link">
                        <Trans
                            components={{
                                // eslint-disable-next-line jsx-a11y/anchor-has-content
                                url: <a href={link} />,
                            }}
                            i18nKey="emails:bankSubmission.body.topic"
                            values={{
                                initiatorName: assignee.displayName,
                                identifier: props.application.financingStage?.identifier,
                                link,
                            }}
                        />
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
            <FinancingDetails {...props} company={company} />
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>{t('emails:bankSubmission.body.content', { hint })}</p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default SubmitApplicationToBank;
