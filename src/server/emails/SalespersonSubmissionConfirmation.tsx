import { MjmlColumn, MjmlText, MjmlSection } from '@faire/mjml-react';
import { ObjectId } from 'mongodb';
import { useTranslation, Trans } from 'react-i18next';
import {
    Application,
    ApplicationStage,
    Bank,
    Customer,
    FinanceProduct,
    FinderVehicle,
    LocalVariant,
    User,
    Module,
    LocalCustomerManagementModule,
    Lead,
} from '../database';
import type { EmailContext } from '../database/helpers/settings';
import { getApplicationIdentifier } from '../utils/application';
import FinancingDetails from './FinancingDetails';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type SalespersonSubmissionConfirmationProps = {
    emailContext: EmailContext;
    variant: LocalVariant | FinderVehicle;
    bank: Bank;
    financeProduct: FinanceProduct;
    application: Application;
    lead: Lead;
    assignee: User;
    customer: Customer;
    link: string;
    module: Module;
    dealerId: ObjectId;
    customerModule: LocalCustomerManagementModule;
};

const SalespersonSubmissionConfirmation = ({
    emailContext,
    assignee,
    customer,
    link,
    customerModule,
    lead,
    ...props
}: SalespersonSubmissionConfirmationProps) => {
    const { t } = useTranslation('emails');
    const { company } = emailContext;
    const identifier = getApplicationIdentifier(props.application, lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Lead,
    ]);

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:salesPersonSubmissionConfirmation.body.topic', {
                                initiatorName: assignee.displayName,
                                identifier,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
            <FinancingDetails {...props} company={company} />

            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left" cssClass="blue-link">
                        <p>
                            <Trans i18nKey="emails:salesPersonSubmissionConfirmation.body.content" values={{ link }}>
                                You can access this application using the following link: <a href={link}>{link}</a>
                            </Trans>
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default SalespersonSubmissionConfirmation;
