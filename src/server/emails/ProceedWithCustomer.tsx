import { useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { getKYCPresetsForCustomerModule } from '../database';
import {
    ApplicationStage,
    Bank,
    FinanceProduct,
    User,
    ApplicationModule,
    ApplicationEnableProceedWithCustomerDevice,
    ApplicationKind,
    LocalInsuranceProduct,
    LocalCustomerManagementModule,
    Insurer,
    Customer,
    Lead,
} from '../database/documents';
import { getCustomerName } from '../database/helpers/customers';
import { EmailContext } from '../database/helpers/settings';
import { getApplicationIdentifier } from '../utils/application';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import EDMLayout from './components/EDMLayout';
import TextSection from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import { DealerInformationSection, FinanceApplicationSummary, InsuranceApplicationSummary } from './components/common';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';

export type ProceedWithCustomerDeviceProps = {
    emailContext: EmailContext;
    dealerEmailContext: DealerEmailContext;
    bank: Bank;
    financeProduct: FinanceProduct;
    application: ApplicationEnableProceedWithCustomerDevice;
    lead: Lead;
    introImageUrl: string;
    variantName: string;
    resumeUrl: string;
    edmEmailFooterContext: EdmEmailFooterContext;
    assignee: User;
    applicationModule: ApplicationModule;
    isBankViewable: boolean;
    customer: Customer;
    insurer?: Insurer;
    customerModule?: LocalCustomerManagementModule;
    insuranceProduct?: LocalInsuranceProduct;
    introTitle?: string;
    contentText?: string;
    isSummaryVehicleVisible?: boolean;
};

const ProceedWithCustomer = (props: ProceedWithCustomerDeviceProps) => {
    const { t } = useTranslation();

    const {
        emailContext,
        dealerEmailContext,
        bank,
        financeProduct,
        application,
        introImageUrl,
        variantName,
        resumeUrl,
        edmEmailFooterContext,
        assignee,
        applicationModule,
        isBankViewable,
        insurer,
        customerModule,
        insuranceProduct,
        customer,
        introTitle,
        contentText,
        isSummaryVehicleVisible,
        lead,
    } = props;

    const { company, companyName } = emailContext;

    const applicationId = getApplicationIdentifier(application, lead, [
        ApplicationStage.Financing,
        ApplicationStage.Insurance,
        ApplicationStage.Reservation,
    ]);

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);
    const customerName = getCustomerName(t, customer, company, kycPresets);

    const contentTextTranslate =
        application.kind === ApplicationKind.Standard
            ? contentText
            : t('emails:standard.proceedOrder.contentText', {
                  customerName,
                  variantName,
                  applicationId,
              }).replaceAll('\n', '<br />');

    const contentTextContact = t('emails:standard.proceedOrder.contentTextContact', {
        prefix: assignee.mobile.prefix,
        phone: assignee.mobile.value,
        applicationId,
        companyName,
        salesPersonName: assignee.displayName,
    }).replaceAll('\n', '<br />');

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    const shouldIncludeInsuranceSummary = !!insurer && !!insuranceProduct && !!customerModule;

    const introTitleTranslated =
        application.kind === ApplicationKind.Standard ? introTitle : t('emails:standard.proceedOrder.introTitle');

    const isSummaryVehicleVisibleCheck = application.kind === ApplicationKind.Standard ? isSummaryVehicleVisible : true;

    return (
        <EDMLayout
            connectText={t('emails:standard.general.connectText', { companyName })}
            copyrightRender={copyrightRender}
            copyrightText="emails:standard.general.copyright"
            disclaimerText={t('emails:standard.general.disclaimerText', { companyName })}
            emailContext={emailContext}
            legalNotice={{ url: company.edmEmailFooter.legalNoticeUrl, text: t('emails:standard.general.legalNotice') }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:standard.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title="subject"
        >
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}

            <TextSection level="h1" padding="0" sectionPadding="30px 30px 0px">
                <Trans defaults={introTitleTranslated} />
            </TextSection>
            <TextSection padding={0} sectionPadding="16px 30px 0px">
                <Trans
                    components={{
                        b: <strong />,
                    }}
                    defaults={contentTextTranslate}
                />
            </TextSection>
            <TextSection color="#0000FF" padding={0} sectionPadding="16px 30px 0px">
                <a href={resumeUrl} style={{ color: '#000000', textDecorationColor: '#000000' }}>
                    {resumeUrl}
                </a>
            </TextSection>
            <TextSection padding={0} sectionPadding="16px 30px 40px">
                <Trans defaults={contentTextContact} />
            </TextSection>

            {isSummaryVehicleVisibleCheck && (
                <DetailSection>
                    {financeProduct && application.kind !== ApplicationKind.Event && (
                        <FinanceApplicationSummary
                            application={application}
                            applicationModule={applicationModule}
                            bank={bank}
                            company={company}
                            dealer={dealerEmailContext.dealer}
                            financeProduct={financeProduct}
                            showBank={isBankViewable}
                        />
                    )}

                    {shouldIncludeInsuranceSummary && (
                        <>
                            <SectionSpacer />
                            <InsuranceApplicationSummary
                                application={application}
                                applicationModule={applicationModule}
                                bank={bank}
                                company={company}
                                customer={customer}
                                customerModule={customerModule}
                                dealer={dealerEmailContext.dealer}
                                insuranceProduct={insuranceProduct}
                                insurer={insurer}
                            />
                        </>
                    )}
                </DetailSection>
            )}

            <DealerInformationSection
                contactUsText="emails:standard.general.contactUs"
                dealerEmailContext={dealerEmailContext}
                showContactUs
            />
        </EDMLayout>
    );
};

export default ProceedWithCustomer;
