import { MjmlText } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import { Application, ApplicationStage, Lead } from '../database/documents';
import type { EmailContext } from '../database/helpers/settings';
import { getApplicationIdentifier } from '../utils/application';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type SendPasswordToCustomerProps = {
    emailContext: EmailContext;
    application: Application;
    lead: Lead;
    password: string;
};

const SendPasswordToCustomer = ({ emailContext, application, password, lead }: SendPasswordToCustomerProps) => {
    const { t } = useTranslation('emails');
    const applicationId = getApplicationIdentifier(application, lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Lead,
    ]);

    return (
        <GeneralEDMLayout emailContext={emailContext} withTextWrapper>
            <MjmlText>{t('emails:sendPasswordToCustomer.body', { applicationId })}</MjmlText>
            <MjmlText fontWeight="900" lineHeight="30px">
                {password}
            </MjmlText>
        </GeneralEDMLayout>
    );
};

export default SendPasswordToCustomer;
