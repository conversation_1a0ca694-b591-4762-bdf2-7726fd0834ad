import { MjmlColumn, MjmlSection } from '@faire/mjml-react';
import { TFunction } from 'i18next';
import {
    User,
    Dealer,
    Company,
    LocalVariant,
    Customer,
    LocalCustomerManagementModule,
    getKYCPresetsForCustomerModule,
    KycFieldPurpose,
} from '../database';
import { getCustomerFirstName, getCustomerLastName, getCustomerName } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import renderMarkdown from '../utils/renderMarkdown';
import replaceCurlyBracketString from '../utils/replaceCurlyBracketString';
import GeneralEDMLayout from './components/GeneralEDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';

export type ShareDetailsProps = {
    emailContext: EmailContext;
    introImageUrl: string;
    introTitle: string;
    contentText: string;
};

export const getSalesPersonFooter = (dealer?: Dealer, user?: User) => {
    if (user) {
        return {
            name: user.displayName,
            phoneNumber: `(+${user.mobile.prefix}) - ${user.mobile.value}`,
            email: user.email,
        };
    }

    if (dealer) {
        return {
            name: dealer.displayName,
            phoneNumber: dealer.contact?.telephone?.value
                ? `(+${dealer.contact.telephone.prefix}) - ${dealer.contact.telephone.value}`
                : '',
            email: dealer.contact?.email,
        };
    }

    return null;
};

type ShareTranslationProps = {
    company: Company;
    dealer?: Dealer;
    user?: User | undefined;
    vehicle: LocalVariant;
    customer: Customer;
    customerModule: LocalCustomerManagementModule;
};

export const getShareTranslationParams = (
    t: TFunction,
    { company, dealer, user, vehicle, customer, customerModule }: ShareTranslationProps
) => {
    const assignee = getSalesPersonFooter(dealer, user);

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    return {
        companyName: company.displayName,
        vehicleName: vehicle.name.defaultValue,
        customerName: getCustomerName(t, customer, company, kycPresets, KycFieldPurpose.Share),
        firstName: getCustomerFirstName(customer),
        lastName: getCustomerLastName(customer),
        assignee,
        // fallback to previous footer
        salePerson: assignee?.name,
        phoneNumber: assignee?.phoneNumber,
        email: assignee?.email,
    };
};

const ShareDetails = ({ emailContext, introTitle, contentText, introImageUrl }: ShareDetailsProps) => {
    const translatedIntroTitle = replaceCurlyBracketString(introTitle, {});

    const translatedContentText = replaceCurlyBracketString(contentText, {});

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}
            <MjmlSection padding={0}>
                <MjmlColumn>
                    {introTitle && (
                        <Text align="left" level="h2" paddingBottom={0} paddingTop={0}>
                            {renderMarkdown(translatedIntroTitle)}
                        </Text>
                    )}
                    <Text align="left" css-class="content-text-link" level="p" paddingTop="8px">
                        {renderMarkdown(translatedContentText)}
                    </Text>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default ShareDetails;
