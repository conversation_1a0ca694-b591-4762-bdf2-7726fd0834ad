import { MjmlSection, MjmlColumn } from '@faire/mjml-react';
import type { EmailContext } from '../database/helpers/settings';
import renderMarkdown from '../utils/renderMarkdown';
import replaceCurlyBracketString from '../utils/replaceCurlyBracketString';
import GeneralEDMLayout from './components/GeneralEDMLayout';
import { Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';

export type ShareDetailsProps = {
    emailContext: EmailContext;
    introImageUrl: string;
    introTitle: string;
    contentText: string;
};

const ComparisonShareDetails = ({ emailContext, introImageUrl, introTitle, contentText }: ShareDetailsProps) => {
    const translatedIntroTitle = replaceCurlyBracketString(introTitle, {});

    const translatedContentText = replaceCurlyBracketString(contentText, {});

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            {introImageUrl && <VehicleImageSection url={introImageUrl} />}
            <MjmlSection padding={0}>
                <MjmlColumn>
                    {introTitle && (
                        <Text align="left" level="h2" paddingBottom={0} paddingTop={0}>
                            {renderMarkdown(translatedIntroTitle)}
                        </Text>
                    )}
                    <Text align="left" css-class="content-text-link" level="p" paddingTop="8px">
                        {renderMarkdown(translatedContentText)}
                    </Text>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default ComparisonShareDetails;
