import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import {
    Application,
    Customer,
    Insurer,
    LocalCustomerManagementModule,
    LocalInsuranceProduct,
    User,
    Vehicle,
} from '../database/documents';
import type { EmailContext } from '../database/helpers/settings';
import InsurancingDetails from './InsurancingDetails';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type SubmitApplicationToInsurerProps = {
    emailContext: EmailContext;
    variant: Vehicle;
    insurer: Insurer;
    application: Application;
    assignee: User;
    customer: Customer;
    customerModule: LocalCustomerManagementModule;
    hint?: string;
    insuranceProduct: LocalInsuranceProduct;
};

const SubmitApplicationToInsurer = ({
    emailContext,
    assignee,
    customer,
    hint,
    customerModule,
    ...props
}: SubmitApplicationToInsurerProps) => {
    const { t } = useTranslation('emails');
    const { company } = emailContext;

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:insurerSubmission.body.topic', {
                                initiatorName: assignee.displayName,
                                identifier: props.application.financingStage?.identifier,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
            <InsurancingDetails {...props} company={company} customer={customer} customerModule={customerModule} />
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>{t('emails:insurerSubmission.body.content', { hint })}</p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default SubmitApplicationToInsurer;
