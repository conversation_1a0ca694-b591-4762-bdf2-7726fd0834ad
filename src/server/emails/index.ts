import ApplicationApproveConfirmation from './ApplicationApproveConfirmation';
import ApplicationCompleteConfirmation from './ApplicationCompleteConfirmation';
import ApplicationExportFail from './ApplicationExportFail';
import ApplicationExportPassword from './ApplicationExportPassword';
import ApplicationExportReady from './ApplicationExportReady';
import ApplicationRejectConfirmation from './ApplicationRejectConfirmation';
import AppointmentCustomerBookingAmendment from './AppointmentCustomerBookingAmendment';
import AppointmentCustomerBookingConfirmation from './AppointmentCustomerBookingConfirmation';
import AppointmentCustomerEndTestDrive from './AppointmentCustomerEndTestDrive';
import AppointmentEndTestDriveReminder from './AppointmentEndTestDriveReminder';
import AppointmentSalespersonFinderReservation from './AppointmentSalespersonFinderReservation';
import AppointmentSubmitConfirmation from './AppointmentSubmitConfirmation';
import ApprovalWithOTP from './ApprovalWithOTP';
import AssigneeReassignment from './AssigneeReassignment';
import CancelApplicationToBank from './CancelApplicationToBank';
import ComparisonShareDetails from './ComparisonShareDetails';
import CreateNewUser from './CreateNewUser';
import CustomerCancelConfirmation from './CustomerCancelConfirmation';
import CustomerDepositConfirmation from './CustomerDepositConfirmation';
import CustomerExportFail from './CustomerExportFail';
import CustomerExportPassword from './CustomerExportPassword';
import CustomerExportReady from './CustomerExportReady';
import CustomerSubmissionConfirmation from './CustomerSubmissionConfirmation';
import CustomerUpdateConfirmation from './CustomerUpdateConfirmation';
import EventLeadExportFail from './EventLeadExportFail';
import EventLeadExportPassword from './EventLeadExportPassword';
import EventLeadExportReady from './EventLeadExportReady';
import ExportApplicationPassword from './ExportApplicationPassword';
import FinderEmail from './FinderEmail';
import GiftVoucherEmail from './GiftVoucherEmail';
import LeadExportFail from './LeadExportFail';
import LeadExportPassword from './LeadExportPassword';
import LeadExportReady from './LeadExportReady';
import MobilityEmail from './MobilityEmail';
import OperatorLocationUpdate from './OperatorLocationUpdate';
import ProceedWithCustomer from './ProceedWithCustomer';
import RequestDisbursement from './RequestDisbursement';
import RequestReleaseLetter from './RequestReleaseLetter';
import ResetPasswordEmail from './ResetPassword';
import ResumeConfiguratorApplication from './ResumeConfiguratorApplication';
import PreOfferSingleEmail from './SalesOffer/PreOfferSingleEmail';
import SalesOfferCombinedEmail from './SalesOffer/SalesOfferCombinedEmail';

import SalespersonApplicationSubmissionConfirmation from './SalespersonApplicationSubmissionConfirmation';
import SalespersonCancelConfirmation from './SalespersonCancelConfirmation';
import SalespersonDepositConfirmation from './SalespersonDepositConfirmation';
import SalespersonLeadUpdate from './SalespersonLeadUpdate';
import SalespersonSubmissionConfirmation from './SalespersonSubmissionConfirmation';
import SalespersonSubmissionToBankOrInsurerFailed from './SalespersonSubmissionToBankOrInsurerFailed';
import SalespersonUpdate from './SalespersonUpdate';
import SalespersonUpdateConfirmation from './SalespersonUpdateConfirmation';
import SendPasswordForAgreementToBank from './SendPasswordForAgreementToBank';
import SendPasswordForInsuranceAgreementToInsurer from './SendPasswordForInsuranceAgreementToInsurer';
import SendPasswordToCustomer from './SendPasswordToCustomer';
import ShareDetails from './ShareDetails';
import SubmitApplicationToBank from './SubmitApplicationToBank';
import SubmitApplicationToInsurer from './SubmitApplicationToInsurer';
import SubmitConfiguratorApplication from './SubmitConfiguratorApplication';
import SubmitEventApplication from './SubmitEventApplication';
import TradeInPendingToSalesManager from './TradeInEmails/TradeInPendingToSalesManager';
import TradeInVehicleChangedToSalesConsultant from './TradeInEmails/TradeInVehicleChangedToSalesConsultant';
import TradeInVehicleChangedToSalesManager from './TradeInEmails/TradeInVehicleChangedToSalesManager';
import VerifyEmailUpdate from './VerifyEmailUpdate';
import VisitAppointmentCustomerBookingAmendment from './VisitAppointmentCustomerBookingAmendment';
import VisitAppointmentCustomerBookingComplete from './VisitAppointmentCustomerBookingComplete';
import VisitAppointmentCustomerBookingConfirmation from './VisitAppointmentCustomerBookingConfirmation';
import VisitAppointmentSubmitConfirmation from './VisitAppointmentSubmitConfirmation';

import createSender from './createSender';

export const sendResetPasswordEmail = createSender(ResetPasswordEmail);
export const sendCreateNewUserEmail = createSender(CreateNewUser);
export const sendEmailToBank = createSender(SubmitApplicationToBank);
export const sendCancellationEmailToBank = createSender(CancelApplicationToBank);
export const sendEmailToInsurer = createSender(SubmitApplicationToInsurer);
export const sendPasswordForAgreementToBank = createSender(SendPasswordForAgreementToBank);
export const sendPasswordForInsuranceAgreementToInsurer = createSender(SendPasswordForInsuranceAgreementToInsurer);
export const sendShareDetails = createSender(ShareDetails);
export const sendComparisonShareDetails = createSender(ComparisonShareDetails);
export const sendApprovalWithOTPEmail = createSender(ApprovalWithOTP);
export const sendResumeConfiguratorApplication = createSender(ResumeConfiguratorApplication);
export const sendVerifyEmailUpdate = createSender(VerifyEmailUpdate);
export const sendSubmitConfiguratorApplication = createSender(SubmitConfiguratorApplication);
export const sendSubmitEventApplication = createSender(SubmitEventApplication);
export const sendProceedWithCustomer = createSender(ProceedWithCustomer);
export const sendCustomerSubmissionConfirmation = createSender(CustomerSubmissionConfirmation);
export const sendPasswordToCustomer = createSender(SendPasswordToCustomer);
export const sendCustomerDepositConfirmation = createSender(CustomerDepositConfirmation);
export const sendCustomerUpdateConfirmation = createSender(CustomerUpdateConfirmation);
export const sendCustomerCancelConfirmation = createSender(CustomerCancelConfirmation);
export const sendSalespersonSubmissionConfirmation = createSender(SalespersonSubmissionConfirmation);
export const sendExportApplicationPassword = createSender(ExportApplicationPassword);
export const sendSalespersonUpdateConfirmation = createSender(SalespersonUpdateConfirmation);
export const sendSalespersonCancelConfirmation = createSender(SalespersonCancelConfirmation);
export const sendSalespersonSubmissionToBankOrInsurerFailed = createSender(SalespersonSubmissionToBankOrInsurerFailed);
export const sendOperatorLocationUpdateEmail = createSender(OperatorLocationUpdate);
export const sendApplicationApproveConfirmation = createSender(ApplicationApproveConfirmation);
export const sendApplicationCompleteConfirmation = createSender(ApplicationCompleteConfirmation);
export const sendApplicationRejectConfirmation = createSender(ApplicationRejectConfirmation);
export const sendSalespersonUpdate = createSender(SalespersonUpdate);
export const sendSalespersonLeadUpdate = createSender(SalespersonLeadUpdate);
export const sendRequestReleaseLetter = createSender(RequestReleaseLetter);
export const sendRequestDisbursement = createSender(RequestDisbursement);
export const sendSalespersonApplicationSubmissionConfirmation = createSender(
    SalespersonApplicationSubmissionConfirmation
);
export const sendSalespersonDepositConfirmation = createSender(SalespersonDepositConfirmation);
export const sendMobilityEmail = createSender(MobilityEmail);
export const sendFinderEmail = createSender(FinderEmail);
export const sendAssigneeReassignment = createSender(AssigneeReassignment);
export const sendAppointmentSubmitConfirmation = createSender(AppointmentSubmitConfirmation);
export const sendAppointmentEndTestDriveReminder = createSender(AppointmentEndTestDriveReminder);
export const sendAppointmentCustomerEndTestDrive = createSender(AppointmentCustomerEndTestDrive);
export const sendAppointmentCustomerBookingConfirmation = createSender(AppointmentCustomerBookingConfirmation);
export const sendAppointmentSalespersonFinderReservation = createSender(AppointmentSalespersonFinderReservation);
export const sendVisitAppointmentSubmitConfirmation = createSender(VisitAppointmentSubmitConfirmation);
export const sendVisitAppointmentCustomerBookingConfirmation = createSender(
    VisitAppointmentCustomerBookingConfirmation
);
export const sendVisitAppointmentCustomerBookingAmendment = createSender(VisitAppointmentCustomerBookingAmendment);
export const sendVisitAppointmentCustomerBookingComplete = createSender(VisitAppointmentCustomerBookingComplete);
export const sendGiftVoucherConfirmation = createSender(GiftVoucherEmail);
export const sendAppointmentCustomerBookingAmendment = createSender(AppointmentCustomerBookingAmendment);

export const sendTradeInPendingToSalesManager = createSender(TradeInPendingToSalesManager);
export const sendTradeInVehicleChangedToSalesManager = createSender(TradeInVehicleChangedToSalesManager);
export const sendTradeInVehicleChangedToSalesConsultant = createSender(TradeInVehicleChangedToSalesConsultant);
export const sendLeadExportReady = createSender(LeadExportReady);
export const sendLeadExportPassword = createSender(LeadExportPassword);
export const sendLeadExportFail = createSender(LeadExportFail);
export const sendEventLeadExportReady = createSender(EventLeadExportReady);
export const sendEventLeadExportPassword = createSender(EventLeadExportPassword);
export const sendEventLeadExportFail = createSender(EventLeadExportFail);
export const sendApplicationExportReady = createSender(ApplicationExportReady);
export const sendApplicationExportPassword = createSender(ApplicationExportPassword);
export const sendApplicationExportFail = createSender(ApplicationExportFail);
export const sendCustomerExportReady = createSender(CustomerExportReady);
export const sendCustomerExportPassword = createSender(CustomerExportPassword);
export const sendCustomerExportFail = createSender(CustomerExportFail);

export const sendSalesOfferCombinedEmail = createSender(SalesOfferCombinedEmail);
export const sendPreOfferSingleEmail = createSender(PreOfferSingleEmail);
export * from './reminders';
export * from './transporters';
export * from './type';
