import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { ObjectId } from 'mongodb';
import { Trans, useTranslation } from 'react-i18next';
import { Bank, LocalVariant, FinanceProduct, Application, User, ApplicationKind, Module } from '../database/documents';
import { EmailContext } from '../database/helpers/settings';
import FinancingDetails from './FinancingDetails';
import GeneralEDMLayout from './components/GeneralEDMLayout';

export type RequestReleaseLetterProps = {
    emailContext: EmailContext;
    variant: LocalVariant;
    bank: Bank;
    financeProduct: FinanceProduct;
    application: Extract<
        Application,
        {
            kind:
                | ApplicationKind.Standard
                | ApplicationKind.Event
                | ApplicationKind.Configurator
                | ApplicationKind.Finder;
        }
    >;
    assignee: User;
    hint?: string;
    module: Module;
    dealerId: ObjectId;
};

const RequestReleaseLetter = ({ emailContext, assignee, hint, ...props }: RequestReleaseLetterProps) => {
    const { t } = useTranslation('emails');
    const { company } = emailContext;

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:requestReleaseLetter.body.topic', {
                                initiatorName: assignee.displayName,
                                identifier: props.application.financingStage?.identifier,
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
            <FinancingDetails {...props} company={company} />
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            <Trans
                                components={{ br: <br /> }}
                                i18nKey="emails:requestReleaseLetter.body.content"
                                values={{
                                    chassisNo: props.application.otherVehicleInformation.chassisNo,
                                    engineNo: props.application.otherVehicleInformation.engineNo,
                                    hint,
                                }}
                            />
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default RequestReleaseLetter;
