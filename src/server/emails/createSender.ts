import { i18n as I18n } from 'i18next';
import mjml2html from 'mjml';
import { MJMLParsingOptions } from 'mjml-core';
import { Transporter, SendMailOptions } from 'nodemailer';
import Mail from 'nodemailer/lib/mailer';
import { ComponentType, createElement } from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { I18nextProvider } from 'react-i18next';
import config from '../core/config';
import getDatabaseContext from '../database/getDatabaseContext';
import { defaultTransporter } from './transporters/createSMTPTransports';

export type SenderOptions<Props> = Omit<SendMailOptions, 'text' | 'html'> & {
    data: Props;
    i18n: I18n;
    mjmlOptions?: MJMLParsingOptions | null;
};

export const defaultMjmlOptions: MJMLParsingOptions = {};

type EmailAddress = {
    address: string;
    name?: string;
};

const getEmailFromRecipient = (to: string | EmailAddress | Array<string | EmailAddress>): string[] => {
    if (!to) {
        return [];
    }

    if (typeof to === 'string') {
        return [to];
    }

    if (Array.isArray(to)) {
        return to.map(recipient => (typeof recipient === 'string' ? recipient : recipient.address));
    }

    return [(to as EmailAddress).address];
};

const filterRecipients = async (recipients: string | Mail.Address | (string | Mail.Address)[]) => {
    if (!recipients) {
        return null;
    }

    const { collections } = await getDatabaseContext();
    const recipientEmails = getEmailFromRecipient(recipients);
    const inactiveEmails = new Set<string>();

    await Promise.all(
        recipientEmails.map(async email => {
            const user = await collections.users.findOne({
                email: email.toLowerCase(),
                isActive: true,
            });

            if (!user) {
                inactiveEmails.add(email.toLowerCase());
            }
        })
    );

    if (inactiveEmails.size === recipientEmails.length) {
        return null;
    }

    const filteredRecipients = Array.isArray(recipients)
        ? recipients.filter(recipient => {
              const email = typeof recipient === 'string' ? recipient : recipient.address;

              return !inactiveEmails.has(email.toLowerCase());
          })
        : recipients;

    if (!Array.isArray(filteredRecipients)) {
        const recipientEmail =
            typeof filteredRecipients === 'string'
                ? filteredRecipients.toLowerCase()
                : filteredRecipients.address.toLowerCase();

        if (inactiveEmails.has(recipientEmail)) {
            return null;
        }
    }

    return filteredRecipients;
};

const createSender =
    <Props = any>(rootComponent: ComponentType<Props>) =>
    async (
        { data, mjmlOptions = null, i18n, to, ...options }: SenderOptions<Props>,
        transporter: Transporter = defaultTransporter,
        sender: string = config.smtp.sender
    ) => {
        const filteredRecipients = await filterRecipients(to);

        if (!filteredRecipients || (Array.isArray(filteredRecipients) && filteredRecipients.length === 0)) {
            return Promise.resolve();
        }

        // we forcefully disable Suspense on React as we are truly looking for markup here
        const clonedI18n = i18n.cloneInstance({ react: { useSuspense: false } });
        await clonedI18n.init();

        const mailElement = createElement(rootComponent, data);
        const rootElement = createElement(I18nextProvider, { i18n: clonedI18n }, mailElement);
        const markup = renderToStaticMarkup(rootElement);

        const { html, errors } = mjml2html(markup, {
            keepComments: false,
            validationLevel: 'strict',
            ...mjmlOptions,
        });

        if (errors?.length) {
            throw errors[0];
        }

        return transporter.sendMail({
            sender,
            from: sender,
            to: filteredRecipients,
            ...options,
            html,
        });
    };

export default createSender;
