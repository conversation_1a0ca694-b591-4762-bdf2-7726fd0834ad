import { MjmlColumn, MjmlGroup, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { Fragment, useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import {
    ApplicationJourney,
    ApplicationStage,
    Bank,
    ConfiguratorApplication,
    ConfiguratorModule,
    Customer,
    Dealer,
    FinanceProduct,
    Insurer,
    LocalCustomerManagementModule,
    LocalInsuranceProduct,
    PromoCode,
    Vehicle,
} from '../database/documents';
import { getKYCPresetsForCustomerModule } from '../database/helpers';
import { getCustomerEmail, getCustomerName } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import { getVehicleName } from '../database/helpers/vehicles';
import { ApplicationStagesLink } from '../queues/implementations/shared/getApplicationStagesLink';
import { ConfiguratorEmailContext } from '../utils/getConfiguratorEmailContext';
import { DealerEmailContext } from '../utils/getDealerEmailContext';
import { EdmEmailFooterContext } from '../utils/getEdmEmailFooterContext';
import EDMLayout from './components/EDMLayout';
import { HeaderText, Text } from './components/TextSection';
import VehicleImageSection from './components/VehicleImageSection';
import {
    BookingInformation,
    CustomerContent,
    DealerInformationSection,
    FinanceApplicationSummary,
    InsuranceApplicationSummary,
    PaymentInformation,
    PriceSummary,
} from './components/common';
import { Copyright, DetailSection, SectionSpacer } from './components/common/ui';
import ConfiguratorBlocksSections from './components/journey/ConfiguratorBlockSections';
import { AudienceMessage } from './type';
import useScenarioViewable from './utils/useScenarioViewable';
import useTranslatedString from './utils/useTranslatedString';

type AudienceSpecificProps =
    | {
          audience: AudienceMessage.Customer;
          applicationStagesLink?: ApplicationStagesLink;
          introTitle: string;
          contentText: string;
      }
    | {
          audience: AudienceMessage.Salesperson;
          applicationStagesLink: ApplicationStagesLink;
      };

export type SubmitConfiguratorApplicationProps = {
    // contexts
    emailContext: EmailContext;
    dealerEmailContext: DealerEmailContext;
    configuratorEmailContext: ConfiguratorEmailContext;
    edmEmailFooterContext: EdmEmailFooterContext;

    // relations
    variant: Vehicle;
    applicant: Customer;
    application: ConfiguratorApplication;
    applicationModule: ConfiguratorModule;
    bank: Bank;
    financeProduct: FinanceProduct;
    journey: ApplicationJourney;
    dealer: Dealer;
    insurer?: Insurer;
    promoCode?: PromoCode;

    // others
    audience: AudienceMessage;
    submissionType: string;
    subject: string;
    applicationId: string;
    isBankViewable: boolean;
    isRequestForFinancing?: boolean;
    applyNewSubmission?: ApplicationJourney['applyNewSubmission'];
    currentStages: ApplicationStage[];
    customerModule: LocalCustomerManagementModule;
    insuranceProduct: LocalInsuranceProduct;
} & AudienceSpecificProps;

const SubmitConfiguratorApplication = (props: SubmitConfiguratorApplicationProps) => {
    const {
        emailContext,
        dealerEmailContext,
        configuratorEmailContext,
        edmEmailFooterContext,

        variant,
        applicant,
        application,
        bank,
        financeProduct,
        journey,
        applicationModule,
        dealer,
        insurer,
        promoCode,

        audience,
        subject,
        submissionType,
        applicationId,
        isBankViewable,
        isRequestForFinancing,
        customerModule,
        applicationStagesLink,
        applyNewSubmission,
        currentStages,
        insuranceProduct,
    } = props;

    const alias = props;

    const { t } = useTranslation(['common', 'emails']);
    const translate = useTranslatedString();
    const { company, companyName } = emailContext;

    const { sections, images } = configuratorEmailContext;

    // Connect text
    const connectTextWithVariables = company.edmEmailFooter.connectText
        ? translate(company.edmEmailFooter.connectText)
        : 'emails:configurator.general.connectText';
    const connectText = t(connectTextWithVariables, { companyName });

    // disclaimerText
    const disclaimerTextWithVariables = company.edmEmailFooter.disclaimerText
        ? translate(company.edmEmailFooter.disclaimerText)
        : 'emails:configurator.general.disclaimerText';
    const disclaimerText = t(disclaimerTextWithVariables, { companyName });

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerName = getCustomerName(t, applicant, company, kycPresets);

    const customerEmail = getCustomerEmail(t, applicant);

    const variantName = translate(getVehicleName(variant));

    const { sendBookingSection, sendFinancingSection, sendInsuranceSection, sendPaymentSection } = useScenarioViewable(
        currentStages,
        applyNewSubmission,
        application,
        journey,
        bank,
        insurer
    );

    const copyrightRender = useCallback(
        () => <Copyright copyRightText={company?.edmEmailFooter?.copyRight} />,
        [company?.edmEmailFooter?.copyRight]
    );

    return (
        <EDMLayout
            connectText={connectText}
            copyrightRender={copyrightRender}
            copyrightText="emails:configurator.general.copyright"
            disclaimerText={disclaimerText}
            emailContext={emailContext}
            legalNotice={{
                url: company.edmEmailFooter.legalNoticeUrl,
                text: t('emails:configurator.general.legalNotice'),
            }}
            privacyPolicy={{
                url: company.edmEmailFooter.privacyPolicyUrl,
                text: t('emails:configurator.general.privacyPolicy'),
            }}
            socialMedia={edmEmailFooterContext.socialMedia}
            title={subject}
        >
            {images.submitOrder.intro && <VehicleImageSection url={images.submitOrder.intro} />}

            <MjmlWrapper padding="40px 32px 60px">
                <MjmlSection padding={0}>
                    <MjmlColumn>
                        {alias.audience === AudienceMessage.Customer && (
                            <CustomerContent
                                applicant={applicant}
                                applicationId={applicationId}
                                company={company}
                                companyName={companyName}
                                contentText={alias.contentText}
                                customerModule={customerModule}
                                introTitle={alias.introTitle}
                                submissionType={submissionType}
                                variantName={variantName}
                            />
                        )}

                        {audience === AudienceMessage.Salesperson && !isRequestForFinancing && (
                            <Text align="left" level="p" paddingTop={0}>
                                <Trans
                                    i18nKey="emails:configurator.submitOrder.introSalesperson.default"
                                    values={{
                                        variantName,
                                        companyName,
                                        submissionType,
                                        applicationId,
                                        customerName,
                                    }}
                                >
                                    {submissionType} {applicationId} has been submitted by {customerName}
                                </Trans>
                            </Text>
                        )}

                        {audience === AudienceMessage.Salesperson && isRequestForFinancing && (
                            <Text align="left" level="p" paddingTop={0}>
                                <Trans
                                    i18nKey="emails:configurator.submitOrder.introSalesperson.requestForFinancing"
                                    values={{
                                        variantName,
                                        companyName,
                                        submissionType,
                                        applicationId,
                                        customerName,
                                    }}
                                >
                                    {customerName} has requested for financing: {applicationId}
                                </Trans>
                            </Text>
                        )}
                    </MjmlColumn>
                </MjmlSection>
            </MjmlWrapper>

            <DetailSection>
                <MjmlSection paddingBottom="32px" paddingTop={0}>
                    <MjmlGroup>
                        <MjmlColumn>
                            <HeaderText>
                                {t('emails:configurator.submitOrder.orderSummary', {
                                    variantName,
                                })}
                            </HeaderText>
                        </MjmlColumn>
                    </MjmlGroup>
                </MjmlSection>
                {sections?.map((section, index) => (
                    <Fragment key={section.sectionTitle}>
                        <ConfiguratorBlocksSections company={company} section={section} />
                        {index < sections.length - 1 ? <SectionSpacer /> : null}
                    </Fragment>
                ))}

                <SectionSpacer />

                <PriceSummary
                    application={application}
                    applicationModule={applicationModule}
                    bank={bank}
                    company={company}
                    dealer={dealer}
                    link={
                        audience === AudienceMessage.Salesperson
                            ? applicationStagesLink[ApplicationStage.Reservation]
                            : undefined
                    }
                    promoCode={promoCode}
                    variant={variant}
                />

                {sendBookingSection && (
                    <>
                        <SectionSpacer />
                        <BookingInformation
                            appointmentDate={application.appointmentStage?.bookingTimeSlot?.slot}
                            appointmentId={application.appointmentStage?.identifier}
                            link={
                                audience === AudienceMessage.Salesperson
                                    ? applicationStagesLink[ApplicationStage.Appointment]
                                    : undefined
                            }
                            timeZone={company.timeZone}
                            visitAppointmentDate={application.visitAppointmentStage?.bookingTimeSlot?.slot}
                            visitAppointmentId={application.visitAppointmentStage?.identifier}
                            visitLink={
                                audience === AudienceMessage.Salesperson
                                    ? applicationStagesLink[ApplicationStage.VisitAppointment]
                                    : undefined
                            }
                        />
                    </>
                )}

                {sendPaymentSection && (
                    <>
                        <SectionSpacer />
                        <PaymentInformation
                            application={application}
                            company={company}
                            customerEmail={customerEmail}
                            customerFullName={customerName}
                            deposit={journey.deposit}
                        />
                    </>
                )}

                {sendFinancingSection && (
                    <>
                        <SectionSpacer />
                        <FinanceApplicationSummary
                            application={application}
                            applicationModule={applicationModule}
                            bank={bank}
                            company={company}
                            dealer={dealer}
                            financeProduct={financeProduct}
                            link={
                                audience === AudienceMessage.Salesperson
                                    ? applicationStagesLink[ApplicationStage.Financing]
                                    : undefined
                            }
                            showBank={isBankViewable}
                        />
                    </>
                )}

                {sendInsuranceSection && (
                    <>
                        <SectionSpacer />
                        <InsuranceApplicationSummary
                            application={application}
                            applicationModule={applicationModule}
                            bank={bank}
                            company={company}
                            customer={applicant}
                            customerModule={customerModule}
                            dealer={dealer}
                            insuranceProduct={insuranceProduct}
                            insurer={insurer}
                            link={
                                audience === AudienceMessage.Salesperson
                                    ? applicationStagesLink[ApplicationStage.Insurance]
                                    : undefined
                            }
                        />
                    </>
                )}
            </DetailSection>

            <DealerInformationSection
                contactUsText="emails:configurator.general.contactUs"
                dealerEmailContext={dealerEmailContext}
                showContactUs={audience === AudienceMessage.Customer}
            />
        </EDMLayout>
    );
};

export default SubmitConfiguratorApplication;
