import { MjmlColumn, MjmlSection, MjmlText } from '@faire/mjml-react';
import { useTranslation } from 'react-i18next';
import dayjs from '../../dayjs.extend';
import {
    Application,
    ApplicationJourney,
    Company,
    Customer,
    LocalVariant,
    User,
    ApplicationStage,
    LocalCustomerManagementModule,
    getKYCPresetsForCustomerModule,
    Lead,
} from '../database';
import { getCustomerName } from '../database/helpers/customers';
import type { EmailContext } from '../database/helpers/settings';
import { getApplicationIdentifier } from '../utils/application';
import GeneralEDMLayout from './components/GeneralEDMLayout';
import useFormats from './utils/useFormats';

type CustomerDepositConfirmationProps = {
    emailContext: EmailContext;
    application: Application;
    lead: Lead;
    variant: LocalVariant;
    journey: ApplicationJourney;
    customer: Customer;
    assignee: User;
    company: Company;
    customerModule: LocalCustomerManagementModule;
};

export const PaymentSection = ({
    application,
    journey,
    variant,
    company,
    lead,
}: Pick<CustomerDepositConfirmationProps, 'application' | 'journey' | 'variant' | 'company' | 'lead'>) => {
    const { t } = useTranslation(['emails', 'common']);

    if (!journey.deposit) {
        return null;
    }

    const { deposit } = journey;

    const { formatAmountWithCurrency } = useFormats(
        company?.currency || '',
        company?.roundings?.amount?.decimals || 0,
        company?.roundings?.percentage?.decimals || 0
    );

    const identifier = getApplicationIdentifier(application, lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
    ]);

    return (
        <>
            <p style={{ fontWeight: 'bold' }}>{t('emails:customerDepositConfirmation.body.paymentSection.title')}</p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:customerDepositConfirmation.body.paymentSection.applicationId', {
                    identifier,
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:customerDepositConfirmation.body.paymentSection.paymentMethod', {
                    paymentMethod: deposit.paymentMethod,
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:customerDepositConfirmation.body.paymentSection.paymentDate', {
                    paymentDate: dayjs(deposit.completedAt)
                        .tz(company.timeZone)
                        .format(t('common:formats.dateTimeFormat')),
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:customerDepositConfirmation.body.paymentSection.amountPaid', {
                    paymentAmount: formatAmountWithCurrency(deposit.amount),
                })}
            </p>
            <p style={{ margin: '5px 0' }}>
                {t('emails:customerDepositConfirmation.body.paymentSection.selectedVehicle', {
                    vehicle: variant.name.defaultValue,
                })}
            </p>
        </>
    );
};

const CustomerDepositConfirmation = ({
    emailContext,
    customer,
    assignee,
    customerModule,
    ...props
}: CustomerDepositConfirmationProps) => {
    const { t } = useTranslation(['common', 'emails']);

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, props.company, kycPresets);

    return (
        <GeneralEDMLayout emailContext={emailContext}>
            <MjmlSection>
                <MjmlColumn>
                    <MjmlText align="left">
                        <p>
                            {t('emails:customerDepositConfirmation.greeting', {
                                customerName,
                            })}
                        </p>
                        <p>{t('emails:customerDepositConfirmation.body.introduction')}</p>
                        <PaymentSection {...props} />
                        <p>
                            {t('emails:customerDepositConfirmation.body.contact', {
                                assignee: {
                                    name: assignee.displayName,
                                    phone: `+${assignee.mobile.prefix} ${assignee.mobile.value}`,
                                    email: assignee.email,
                                },
                            })}
                        </p>
                    </MjmlText>
                </MjmlColumn>
            </MjmlSection>
        </GeneralEDMLayout>
    );
};

export default CustomerDepositConfirmation;
