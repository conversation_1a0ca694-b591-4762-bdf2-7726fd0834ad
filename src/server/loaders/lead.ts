import DataLoader from 'dataloader';
import { ObjectId } from 'mongodb';
import { Lead } from '../database/documents/Lead';
import getDatabaseContext from '../database/getDatabaseContext';
import { buildOneToOneLoader } from './helpers';

export type LeadLoaders = {
    leadById: DataLoader<ObjectId, Lead>;
    leadBySuiteId: DataLoader<ObjectId, Lead>;
};

const createLeadLoaders = (): LeadLoaders => {
    const leadById = buildOneToOneLoader<Lead>(keys =>
        getDatabaseContext().then(({ collections }) => collections.leads.find({ _id: { $in: keys } }).toArray())
    );

    const leadBySuiteId = buildOneToOneLoader<Lead>(
        keys =>
            getDatabaseContext().then(({ collections }) =>
                collections.leads.find({ '_versioning.suiteId': { $in: keys }, '_versioning.isLatest': true }).toArray()
            ),
        ({ _versioning }) => _versioning.suiteId.toHexString()
    );

    return {
        leadById,
        leadBySuiteId,
    };
};

export default createLeadLoaders;
