import { isNil, xor } from 'lodash/fp';
import {
    ApplicationKind,
    ApplicationStage,
    ConfiguratorApplication,
    ConfiguratorModule,
    LocalCustomerManagementModule,
    LocalVariant,
    ModuleType,
    getKYCPresetsForCustomerModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import { AudienceMessage } from '../../emails';
import SubmitConfiguratorApplication, {
    SubmitConfiguratorApplicationProps,
} from '../../emails/SubmitConfiguratorApplication';
import createLoaders from '../../loaders';
import { checkIsBankViewable } from '../../queues/implementations/shared';
import getApplicationStagesLink from '../../queues/implementations/shared/getApplicationStagesLink';
import { getApplicationStage } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getConfiguratorEmailContext from '../../utils/getConfiguratorEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';
import { makeHtml } from './utils';

const getData = async (): Promise<SubmitConfiguratorApplicationProps> => {
    const { i18n } = await createI18nInstance();
    await i18n.loadNamespaces(['common', 'emails', 'calculators']);

    const { collections } = await getDatabaseContext();

    const loaders = createLoaders();

    const audience: AudienceMessage = AudienceMessage.Salesperson;
    const applications = await collections.applications
        .find({
            kind: ApplicationKind.Configurator,
            isDraft: false,
            '_versioning.isLatest': true,
        })
        .sort({ _id: -1 })
        .limit(1)
        .toArray();

    const application = applications?.[0] as ConfiguratorApplication;
    if (application.kind !== ApplicationKind.Configurator) {
        throw new Error('Application kind is not Configurator Application');
    }

    const configuratorModule = (await loaders.moduleById.load(application.moduleId)) as ConfiguratorModule;
    if (configuratorModule._type !== ModuleType.ConfiguratorModule) {
        throw new Error('Module type is not Configurator Module');
    }

    // load supporting documents
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [
        variant,
        applicant,
        company,
        bank,
        financeProduct,
        journey,
        dealer,
        variantConfigurator,
        previousApplication,
        insuranceProduct,
        lead,
    ] = await Promise.all([
        loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>,
        loaders.customerById.load(application.applicantId),
        loaders.companyById.load(configuratorModule.companyId),
        loaders.bankById.load(application.bankId),
        loaders.financeProductById.load(application.financing?.financeProductId),
        loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId),
        loaders.dealerById.load(application.dealerId),
        loaders.loadVariantConfiguratorById.load(application.configuratorId),
        loaders.previousApplicationBySuiteId.load(application._versioning.suiteId) ?? null,
        !isNil(application.insurancing) && !isNil(application.insurancing.insuranceProductId)
            ? loaders.insuranceProductById.load(application.insurancing.insuranceProductId)
            : null,
        loaders.leadById.load(application.leadId),
    ]);

    const customerModule = (await loaders.moduleById.load(applicant.moduleId)) as LocalCustomerManagementModule;

    // load mail context
    const [emailContext, dealerEmailContext, configuratorEmailContext, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getDealerEmailContext(dealer),
        getConfiguratorEmailContext({
            colorSectionTitle: 'emails:configurator.general.colorSection',
            packageSectionTitle: 'emails:configurator.general.packageSection',
            application,
            variantConfigurator,
            configuratorModule,
        }),
        getEdmEmailFooterContext({ company }),
    ]);

    const { t } = i18n;

    const stage = getApplicationStage(application, lead, [
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Lead,
    ]);

    const currentStages = xor(previousApplication?.stages ?? [], application.stages);

    const submissionType = t(`emails:component.applicationStage.${stage.stage}`);

    let subjectKey: string;
    switch (audience) {
        case AudienceMessage.Salesperson:
            subjectKey = 'emails:configurator.submitOrder.subjectSalesperson';
            break;

        // case AudienceMessage.Customer:
        //     subjectKey = configuratorEmailContext.emailContents.submitOrder.subject.defaultValue;
        //     break;
    }

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const subject = t(subjectKey, {
        companyName: emailContext.companyName,
        variantName: variant.name.defaultValue,
        submissionType,
        applicationId: stage.value?.identifier,
        customerName: customerFullName,
    });

    const applicationStagesLink = await getApplicationStagesLink(application, lead);

    return {
        audience: AudienceMessage.Salesperson,
        submissionType,
        emailContext,
        dealerEmailContext,
        configuratorEmailContext,
        subject,
        application,
        variant,
        applicant,
        bank,
        financeProduct,
        journey,
        edmEmailFooterContext,
        dealer,
        applicationModule: configuratorModule,
        applicationId: application.financingStage?.identifier,
        isBankViewable: await checkIsBankViewable(variant._versioning.suiteId, application.moduleId),
        applicationStagesLink,
        customerModule,
        currentStages,
        insuranceProduct,
    };
};

const serveConfiguratorSubmitOrder = makeHtml(SubmitConfiguratorApplication, getData);
export default serveConfiguratorSubmitOrder;
