import { isEmpty } from 'lodash/fp';
import { getUrlForUpload } from '../../core/storage';
import {
    ApplicationKind,
    DealerTranslatedStringSetting,
    LocalCustomerManagementModule,
    LocalVariant,
    MobilityApplication,
    ModuleType,
    TranslatedString,
    UploadedFileWithPreview,
    getKYCPresetsForCustomerModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerEmail, getCustomerFullNameWithTitle, getCustomerName } from '../../database/helpers/customers';
import MobilityEmail, { MobilityEmailProps } from '../../emails/MobilityEmail';
import createLoaders from '../../loaders';
import { getApplicationDataFromApplication } from '../../queues';
import getMobilityEmailActionLinks from '../../queues/implementations/shared/getMobilityEmailActionLinks';
import getMobilityEmailContent, {
    MobilityEmailPath,
} from '../../queues/implementations/shared/getMobilityEmailContent';
import { MobilityRecipient, MobilityAsset } from '../../schema/resolvers/enums';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';
import { makeHtml } from './utils';

const getRentalDisclaimer = (
    moduleRentalDisclaimer: DealerTranslatedStringSetting,
    dealerId: string
): TranslatedString | null => {
    const dealerRentalDisclaimer = moduleRentalDisclaimer.overrides.find(
        override => override.dealerId.toString() === dealerId
    );

    if (isEmpty(dealerRentalDisclaimer?.value.defaultValue)) {
        if (isEmpty(moduleRentalDisclaimer.defaultValue?.defaultValue)) {
            return null;
        }

        return moduleRentalDisclaimer.defaultValue;
    }

    return dealerRentalDisclaimer.value;
};

const getData = async (): Promise<MobilityEmailProps> => {
    const message = {
        mobilityRecipient: MobilityRecipient.Customers,
        mobilityAsset: MobilityAsset.BookingConfirmation,
    };

    const { mobilityRecipient, mobilityAsset } = message;

    const { i18n } = await createI18nInstance();
    await i18n.loadNamespaces(['common', 'emails', 'calculators']);
    const { t } = i18n;

    const { collections } = await getDatabaseContext();

    const loaders = createLoaders();

    const applications = await collections.applications
        .find({
            kind: ApplicationKind.Mobility,
            isDraft: false,
            '_versioning.isLatest': true,
        })
        .sort({ _id: -1 })
        .limit(1)
        .toArray();

    const application = applications?.[0] as MobilityApplication;

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    if (applicationModule._type !== ModuleType.MobilityModule) {
        throw new Error('ModuleType not support');
    }

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    if (!journey) {
        throw new Error('Application Journey not found');
    }
    const [vehicleId, customerId] = getApplicationDataFromApplication(application);

    const [variant, applicant, company, dealer, assignee, promoCode, giftVoucher] = await Promise.all([
        loaders.vehicleById.load(vehicleId) as Promise<LocalVariant>,
        loaders.customerById.load(customerId),
        loaders.companyById.load(applicationModule.companyId),
        loaders.dealerById.load(application.dealerId),
        application.mobilityStage.assigneeId ? loaders.userById.load(application.mobilityStage.assigneeId) : null,
        application.promoCodeId ? loaders.promoCodeById.load(application.promoCodeId) : null,
        application.giftVoucherSuiteId ? loaders.giftVoucherBySuiteId.load(application.giftVoucherSuiteId) : null,
    ]);

    const [emailContext, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getEdmEmailFooterContext({ company }),
    ]);

    const customerModule = (await loaders.moduleById.load(applicant.moduleId)) as LocalCustomerManagementModule;

    const model = await loaders.loadModelById.load(variant.modelId);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerName = getCustomerName(t, applicant, company, kycPresets);
    const customerEmail = getCustomerEmail(t, applicant);
    const dealerName = dealer.displayName;
    const dealerEmailContext = await getDealerEmailContext(dealer);

    const getEmailContent = getMobilityEmailContent(application, applicationModule);

    const introImageFile = getEmailContent<UploadedFileWithPreview | null>(
        `${mobilityRecipient}.${mobilityAsset}` as MobilityEmailPath,
        'introImage'
    );

    const introImageUrl = await getUrlForUpload(introImageFile || variant.images?.[0]);

    const { amendLink, cancelLink } = await getMobilityEmailActionLinks(applicationModule._id, application);

    const operatorName = assignee?.displayName ?? dealerName;

    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const recipientName = mobilityRecipient === MobilityRecipient.Operators ? operatorName : customerName;

    const rentalDisclaimer = getRentalDisclaimer(applicationModule.rentalDisclaimer, application.dealerId.toString());

    return {
        emailContext,
        mobilityRecipient,
        mobilityAsset,
        dealer,

        customerName: recipientName.trim() !== '' ? recipientName : customerName,
        customerFullName,
        customerEmail,
        application,

        variant,
        modelName: model.name,
        edmEmailFooterContext,
        introImageUrl,
        applicant,
        introTitle: getEmailContent(`${mobilityRecipient}.${mobilityAsset}` as MobilityEmailPath, 'introTitle'),
        contentText: getEmailContent(`${mobilityRecipient}.${mobilityAsset}` as MobilityEmailPath, 'contentText'),
        payment: journey.deposit,
        amendLink,
        cancelLink,
        dealerEmailContext,
        rentalDisclaimer,
        promoCode,
        giftVoucher,
    };
};

const serveMobilityEmail = makeHtml(MobilityEmail, getData);
export default serveMobilityEmail;
