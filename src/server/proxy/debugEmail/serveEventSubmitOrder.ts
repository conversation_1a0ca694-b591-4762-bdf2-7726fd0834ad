import {
    ApplicationKind,
    ApplicationStage,
    EventApplication,
    EventApplicationModule,
    LocalCustomerManagementModule,
    LocalVariant,
    ModuleType,
    getKYCPresetsForCustomerModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import { AudienceMessage } from '../../emails';
import SubmitEventApplication, { SubmitEventApplicationProps } from '../../emails/SubmitEventApplication';
import createLoaders from '../../loaders';
import { getApplicationStage } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';
import { getEventEmailContextSalesperson } from '../../utils/getEventEmailContext';
import { makeHtml } from './utils';

const getData = async (): Promise<SubmitEventApplicationProps> => {
    const { i18n } = await createI18nInstance();
    await i18n.loadNamespaces(['common', 'emails', 'calculators']);

    const { collections } = await getDatabaseContext();

    const loaders = createLoaders();

    const audience: AudienceMessage = AudienceMessage.Salesperson;

    const [application] = (await collections.applications
        .find({
            kind: ApplicationKind.Event,
            isDraft: false,
            dealerId: { $ne: null },
            '_versioning.isLatest': true,
        })
        .sort({ _id: -1 })
        .limit(1)
        .toArray()) as EventApplication[];

    if (application?.kind !== ApplicationKind.Event) {
        throw new Error('Application kind is not Configurator Application');
    }

    const lead = await loaders.leadById.load(application.leadId);

    const eventModule = (await loaders.moduleById.load(application.moduleId)) as EventApplicationModule;
    if (eventModule?._type !== ModuleType.EventApplicationModule) {
        throw new Error('Module type is not Configurator Module');
    }

    // load supporting documents
    const stage = getApplicationStage(application, lead, [
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Lead,
    ]);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [variant, applicant, company, journey, dealer, assignee, event, guarantor] = await Promise.all([
        application.vehicleId ? (loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>) : null,
        loaders.customerById.load(application.applicantId),
        loaders.companyById.load(eventModule.companyId),
        loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId),
        loaders.dealerById.load(application.dealerId),
        stage?.value?.assigneeId ? loaders.userById.load(stage.value.assigneeId) : null,
        loaders.eventById.load(application.eventId),
        application.guarantorId && application.guarantorId.length
            ? loaders.customerById.load(application.guarantorId[0])
            : Promise.resolve(null),
    ]);

    const customerModule = (await loaders.moduleById.load(applicant.moduleId)) as LocalCustomerManagementModule;

    const checkModel = variant?.modelId ? await loaders.loadModelById.load(variant.modelId) : null;
    const model = checkModel?.parentModelId ? await loaders.loadModelById.load(checkModel.parentModelId) : checkModel;

    // load mail context
    const [emailContext, dealerEmailContext, eventEmailContextSalesperson, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getDealerEmailContext(dealer),
        getEventEmailContextSalesperson(variant, eventModule, application),
        getEdmEmailFooterContext({ company }),
    ]);

    const { t } = i18n;
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);

    const submissionType = t(`emails:component.applicationStage.${stage.stage}`);

    let subjectKey: string;
    switch (audience) {
        case AudienceMessage.Salesperson:
            subjectKey = 'emails:event.submitOrder.subjectSalesperson';
            break;

        // case AudienceMessage.Customer:
        //     subjectKey = eventEmailContext.emailContents.submitOrder.subject.defaultValue;
        //     break;
    }

    const subject = t(subjectKey, {
        companyName: emailContext.companyName,
        variantName: variant?.name.defaultValue,
        submissionType,
        applicationId: stage.value?.identifier,
        customerName: customerFullName,
    });

    return {
        audience,
        submissionType,
        emailContext,
        dealerEmailContext,
        eventEmailContext: eventEmailContextSalesperson,
        subject,
        application,
        variant,
        applicant,
        journey,
        edmEmailFooterContext,
        guarantor,
        model,
        applicationId: application.financingStage?.identifier,
        stages: [ApplicationStage.Financing],
        dealer,
        event,
        customerModule,
    };
};

const serveEventSubmitOrder = makeHtml(SubmitEventApplication, getData);
export default serveEventSubmitOrder;
