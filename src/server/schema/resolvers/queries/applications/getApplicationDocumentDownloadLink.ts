import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../../../core/config';
import { DownloadApplicationDocumentLink, ExternalLinkKind, PasswordConfiguration } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { isApplicationWithDocuments } from '../../../../database/helpers/applications';
import { ApplicationPolicyAction } from '../../../../permissions';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getApplicationDocumentDownloadLink'] = async (
    root,
    { applicationId, documentId, stage },
    { getTranslations, getPermissionController, getUser }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissions = await getPermissionController();
    const filter = await permissions.applications.getFilterQueryForAction(ApplicationPolicyAction.View);

    const application = await collections.applications.findOne({
        $and: [
            {
                _id: applicationId,
                'documents._id': documentId,
                '_versioning.isLatest': true,
            },
            filter,
        ],
    });

    // no link for not found application
    if (isNil(application) || !isApplicationWithDocuments(application)) {
        return null;
    }

    const document = application.documents.find(({ _id }) => _id.equals(documentId));

    if (!document) {
        return null;
    }

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    const company = applicationModule?.companyId
        ? await collections.companies.findOne({ _id: applicationModule.companyId })
        : null;

    // Bring back encryption for document
    // But output as zip file
    // See: https://appvantage.atlassian.net/browse/AN-1981
    const encrypt = company.passwordConfiguration !== PasswordConfiguration.Off;

    // password is only required when encrypt is true
    const password = encrypt ? nanoid() : null;
    const link: DownloadApplicationDocumentLink = {
        _id: new ObjectId(),
        _kind: ExternalLinkKind.DownloadApplicationDocument,
        secret: nanoid(),
        data: {
            userId: user._id,
            applicationId: application._id,
            applicationSuiteId: application._versioning.suiteId,
            documentId,
            encrypt,
            password,
            stage,
        },
        expiresAt: dayjs().add(5, 'minute').toDate(),
        deleteOnFetch: true,
    };
    await collections.externalLinks.insertOne(link);

    return {
        link: urljoin(config.applicationEndpoint, 'api', 'downloads', 'applications', 'documents', link.secret),
        ...(encrypt && {
            encryption: {
                enabled: true,
                password,
            },
        }),
    };
};

export default requiresLoggedUser(query);
