import {
    ConditionContext,
    ModuleType,
    getKYCFieldsFromPresets,
    getKYCPresetsForCustomerModuleId,
    isMatchingConditions,
    hasCapQualificationCondition,
} from '../../../../database';
import { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['prefetchKYCFieldsForQualify'] = async (
    root,
    { applicationModuleId },
    { loaders }
) => {
    const applicationModule = await loaders.moduleById.load(applicationModuleId);

    switch (applicationModule._type) {
        case ModuleType.ConfiguratorModule:
        case ModuleType.EventApplicationModule:
        case ModuleType.FinderApplicationPrivateModule:
        case ModuleType.FinderApplicationPublicModule:
        case ModuleType.LaunchPadModule:
        case ModuleType.StandardApplicationModule: {
            const customerModule = await loaders.customerModuleById.load(applicationModule.customerModuleId);
            const initialPresets = await getKYCPresetsForCustomerModuleId(applicationModule.customerModuleId);

            const conditionContext: ConditionContext = {
                customerKind: 'local',
                applicationModuleId: applicationModule._id,
                isCapQualification: true,
            };

            const sortedKycFields = customerModule.kycFields.sort((a, b) => a.order - b.order);

            const presets = initialPresets.filter(
                preset =>
                    hasCapQualificationCondition(preset.conditions) &&
                    isMatchingConditions(preset.conditions, conditionContext)
            );

            return getKYCFieldsFromPresets(sortedKycFields, presets);
        }

        default: {
            throw new Error('Invalid application module');
        }
    }
};

export default query;
