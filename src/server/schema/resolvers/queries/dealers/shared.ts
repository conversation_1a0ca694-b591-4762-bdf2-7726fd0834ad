import { type Document, type Filter, type Sort, ObjectId } from 'mongodb';
import { type Dealer, type User } from '../../../../database/documents';
import type { Loaders } from '../../../../loaders';
import { getSortingValue } from '../../../../utils/pagination';
import {
    DealerSortingField,
    type GraphQLDealerFilteringRule,
    type GraphQLDealerSortingRule,
    type Maybe,
} from '../../definitions';

/**
 * Gets dealer IDs accessible to a user based on their user groups, with optional company filtering
 *
 * @param user - The user for whom to retrieve dealer IDs
 * @param filter - Optional filtering rules, can specify a company ID to filter user groups
 * @param loaders - DataLoader instances for efficient data fetching
 * @returns Promise resolving to an array of dealer IDs the user has access to
 */
export const getDealerIdsByUserGroup = async (user: User, filter: { companyId?: ObjectId }, loaders: Loaders) =>
    loaders.userGroupsByUserId
        .load(user._id)
        .then(groups =>
            filter?.companyId
                ? groups.filter(userGroup => userGroup.companyId.toHexString() === filter.companyId.toHexString())
                : groups
        )
        .then(groups => groups.flatMap(group => group.dealerIds));

export const getFilter = (rule?: Maybe<GraphQLDealerFilteringRule>): Filter<Dealer> => {
    const filter: Filter<Dealer> = { isDeleted: false };

    if (!rule) {
        return filter;
    }

    if (rule.id) {
        filter._id = rule.id;
    }

    if (rule.ids) {
        filter._id = {
            $in: rule.ids,
        };
    }

    if (rule.companyId) {
        filter.companyId = rule.companyId;
    }

    if (rule.displayName) {
        filter.displayName = new RegExp(rule.displayName, 'i');
    }

    if (rule.limitFeatures?.length > 0) {
        filter.limitFeature = {
            $in: rule.limitFeatures,
        };
    }

    return filter;
};

export const getSort = (rule?: Maybe<GraphQLDealerSortingRule>): { query: Sort; pipelines: Document[] } => {
    // always sort by ID for consistency
    const query: Sort = { _id: 1 };

    if (!rule) {
        return { query, pipelines: [] };
    }

    switch (rule.field) {
        case DealerSortingField.DisplayName:
            return {
                query: { displayName: getSortingValue(rule.order), ...query },
                pipelines: [],
            };

        case DealerSortingField.UserCount: {
            return {
                query: { userCount: getSortingValue(rule.order), ...query },
                pipelines: [
                    // look up user groups which contain dealer ID
                    {
                        $lookup: {
                            from: 'userGroups',
                            localField: '_id',
                            foreignField: 'dealerIds',
                            as: 'userGroup_docs',
                        },
                    },
                    // reduce and combine the users from user groups
                    {
                        $addFields: {
                            userArrayFromGroups: {
                                $reduce: {
                                    input: '$userGroup_docs.userIds',
                                    initialValue: [],
                                    in: { $setUnion: ['$$value', '$$this'] },
                                },
                            },
                        },
                    },
                    // get size of user array to get total number of users
                    {
                        $addFields: {
                            userCount: {
                                $size: '$userArrayFromGroups',
                            },
                        },
                    },
                ],
            };
        }
        default:
            throw new Error('Sorting field not supported');
    }
};
