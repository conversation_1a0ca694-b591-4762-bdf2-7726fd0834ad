import { Document } from 'mongodb';
import { Dealer } from '../../../../database/documents/Dealer';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import getGeometry from '../../../../integrations/mapbox';
import { InvalidPermission } from '../../../errors';
import { GraphQLQueryResolvers } from '../../definitions';

type DealerWithDist = Dealer & {
    calculatedDistance?: number;
};

const query: GraphQLQueryResolvers['getNearbyDealers'] = async (
    root,
    { filter },
    { getPermissionController, getUser, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const pipelines: Document[] = [];

    const company = await collections.companies.findOne({ _id: filter.companyId });

    if (company.addressAutofill && filter.searchText) {
        const geometry = await getGeometry(filter.searchText, company.countryCode);
        // return dealers with distance, maximum of 5 dealers within a 5000-meter radius
        if (geometry) {
            const { coordinates } = geometry;
            pipelines.push(
                {
                    $geoNear: {
                        near: {
                            type: 'Point',
                            coordinates,
                        },
                        spherical: true,
                        maxDistance: 5000,
                        distanceField: 'dist.calculated',
                        includeLocs: 'dist.location',
                        distanceMultiplier: 0.001,
                        key: 'location',
                    },
                },
                { $limit: 5 },
                {
                    $match: {
                        isActive: true,
                        companyId: filter.companyId,
                    },
                }
            );

            const dealers = await collections.dealers.aggregate(pipelines).toArray();

            const dealersWithDist = dealers.map(dealer => ({
                ...dealer,
                calculatedDistance: dealer.dist?.calculated,
            }));

            return dealersWithDist as DealerWithDist[];
        }

        return [];
    }

    if (!filter?.productionOnly) {
        const user = await getUser(true);

        if (!user) {
            throw new InvalidPermission();
        }

        // filter by assigned dealer if
        if (!permissionController.hasRootPermission()) {
            const dealerIds = await loaders.userGroupsByUserId
                .load(user._id)
                .then(groups =>
                    filter?.companyId
                        ? groups.filter(userGoup => userGoup.companyId.toHexString() === filter.companyId.toHexString())
                        : groups
                )
                .then(groups => groups.flatMap(group => group.dealerIds));

            if (dealerIds?.length) {
                pipelines.push({
                    $match: {
                        _id: { $in: dealerIds },
                    },
                });
            } else {
                // user does not have any user group & dealer asssigned
                return [];
            }
        }
    } else {
        // limit to active only
        pipelines.push({ $match: { isActive: true } });
    }

    pipelines.push({
        $match: {
            companyId: filter.companyId,
        },
    });

    const dealers = await collections.dealers.aggregate(pipelines).toArray();

    return dealers as DealerWithDist[];
};

export default query;
