import type { Document } from 'mongodb';
import { ReservationStockStatus, type Dealer } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { InvalidPermission } from '../../../errors';
import { type GraphQLQueryResolvers } from '../../definitions';
import { getDealerIdsByUserGroup, getFilter, getSort } from './shared';

const query: GraphQLQueryResolvers['listDealerOptions'] = async (
    root,
    { filter, sort },
    { getPermissionController, getUser, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const sorting = getSort(sort);

    const pipelines: Document[] = [{ $match: getFilter(filter) }];

    if (!filter?.productionOnly) {
        const user = await getUser(true);

        if (!user) {
            throw new InvalidPermission();
        }

        if (!permissionController.hasRootPermission()) {
            const dealerIdsByUserGroup = await getDealerIdsByUserGroup(user, filter, loaders);

            if (!dealerIdsByUserGroup?.length) {
                return [];
            }

            pipelines.push({
                $match: {
                    _id: { $in: dealerIdsByUserGroup },
                },
            });
        }
    } else {
        // limit to active only
        pipelines.push({ $match: { isActive: true } });
    }

    if (filter?.checkConfiguratorStock) {
        pipelines.push(
            {
                $lookup: {
                    from: 'inventories',
                    localField: '_id',
                    foreignField: 'dealerId',
                    pipeline: [
                        {
                            $match: {
                                'stocks.reservationStatus': ReservationStockStatus.Available,
                                isActive: true,
                                isDeleted: false,
                            },
                        },
                    ],
                    as: 'inventories',
                },
            },
            {
                $match: {
                    inventories: {
                        $gt: {
                            $size: 0,
                        },
                    },
                },
            }
        );
    }

    pipelines.push(...sorting.pipelines, { $sort: sorting.query });

    return collections.dealers.aggregate<Dealer>(pipelines).toArray();
};

export default query;
