import type { Document } from 'mongodb';
import { ReservationStockStatus } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { paginateAggregation } from '../../../../utils/pagination';
import { InvalidPermission } from '../../../errors';
import { type GraphQLQueryResolvers } from '../../definitions';
import { getDealerIdsByUserGroup, getFilter, getSort } from './shared';

const query: GraphQLQueryResolvers['listDealers'] = async (
    root,
    { filter, pagination, sort },
    { getPermissionController, getUser, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const sorting = getSort(sort);

    const pipelines: Document[] = [{ $match: getFilter(filter) }];

    if (!filter?.productionOnly) {
        const user = await getUser(true);

        if (!user) {
            throw new InvalidPermission();
        }

        if (!permissionController.hasRootPermission()) {
            const dealerIdsByUserGroup = await getDealerIdsByUserGroup(user, filter, loaders);

            if (!dealerIdsByUserGroup?.length) {
                return { items: [], count: 0 };
            }

            pipelines.push({
                $match: {
                    _id: { $in: dealerIdsByUserGroup },
                    // non-superadmin users can only view subscribed dealers
                    limitFeature: false,
                },
            });
        }
    } else {
        // limit to active only
        pipelines.push({ $match: { isActive: true } });
    }

    if (filter?.checkConfiguratorStock) {
        pipelines.push(
            {
                $lookup: {
                    from: 'inventories',
                    localField: '_id',
                    foreignField: 'dealerId',
                    pipeline: [
                        {
                            $match: {
                                'stocks.reservationStatus': ReservationStockStatus.Available,
                                isActive: true,
                                isDeleted: false,
                            },
                        },
                    ],
                    as: 'inventories',
                },
            },
            {
                $match: {
                    inventories: {
                        $gt: {
                            $size: 0,
                        },
                    },
                },
            }
        );
    }

    pipelines.push(...sorting.pipelines, { $sort: sorting.query });

    return paginateAggregation(collections.dealers, pipelines, pagination);
};

export default query;
