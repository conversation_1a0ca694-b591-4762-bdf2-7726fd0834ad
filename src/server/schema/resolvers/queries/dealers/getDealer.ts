import getDatabaseContext from '../../../../database/getDatabaseContext';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getDealer'] = async (root, { id }, { getPermissionController }) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    if (permissionController.hasRootPermission()) {
        return collections.dealers.findOne({ _id: id });
    }

    return collections.dealers.findOne({
        _id: id,
        // non-superadmin users can only view subscribed dealers
        limitFeature: false,
    });
};

export default requiresLoggedUser(query);
