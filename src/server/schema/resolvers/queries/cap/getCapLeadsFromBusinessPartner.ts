import { capitalize } from 'lodash/fp';
import { AuditTrailKind } from '../../../../database/documents/AuditTrail';
import getDealerDetails from '../../../../integrations/cap/getDealerDetails';
import getLead from '../../../../integrations/cap/lead/getLead';
import ppnAuth from '../../../../integrations/cap/ppnAuth';
import {
    createCapSubmissionAuditTrails,
    dateFromCapDateFormat,
    primaryInterestMapping,
    withRetryApiCall,
} from '../../../../integrations/cap/utils';
import { getCampaignIdFromCap } from '../../../../integrations/cap/utils/resolveCustomerDataFromCap';
import { LeadPolicyAction } from '../../../../permissions';
import { ApplicationPolicyAction } from '../../../../permissions/types/applications';
import { ModulePolicyAction } from '../../../../permissions/types/modules';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLQueryResolvers, ModuleType } from '../../definitions';

const query: GraphQLQueryResolvers['getCapLeadsFromBusinessPartner'] = async (
    root,
    { applicationModuleId, eventId, leadId, capModuleId, dealerId, partnerGuid, pagination },
    { loaders, getTranslations, getPermissionController }
) => {
    const {
        applications: applicationPermission,
        modules: modulePermission,
        leads: leadPermission,
    } = await getPermissionController();
    const { t } = await getTranslations(['auditTrails']);

    const applicationModule = await loaders.moduleById.load(applicationModuleId);

    if (!applicationModule) {
        throw new InvalidInput({ module: `Module of this application is invalid.` });
    }

    if (applicationModule._type === ModuleType.EventApplicationModule) {
        if (!eventId) {
            throw new InvalidInput({ event: `Invalid Event ID` });
        }

        const eventDetails = await loaders.eventById.load(eventId);
        if (!eventDetails.isCapEnabled) {
            throw new InvalidInput({ event: `This event does not enable C@P integrated` });
        }
    } else if (
        applicationModule._type === ModuleType.ConfiguratorModule ||
        applicationModule._type === ModuleType.FinderApplicationPrivateModule ||
        applicationModule._type === ModuleType.FinderApplicationPublicModule ||
        applicationModule._type === ModuleType.LaunchPadModule ||
        applicationModule._type === ModuleType.StandardApplicationModule
    ) {
        if (!applicationModule.capModuleId) {
            throw new InvalidInput({ module: `This module does not have C@P module integrated` });
        }
    } else {
        throw new InvalidInput({ module: `Module of this application is invalid.` });
    }

    if (
        !modulePermission.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication) &&
        !applicationPermission.hasPolicyForAction(ApplicationPolicyAction.Update) &&
        !leadPermission.hasPolicyForAction(LeadPolicyAction.UpdateContact)
    ) {
        throw new InvalidPermission();
    }

    const capSetting = await loaders.capSettingByModuleId.load(capModuleId);

    const lead = leadId ? await loaders.leadById.load(leadId) : null;

    const authentication = await ppnAuth(capSetting);
    if (authentication.error) {
        const errorMessage = t('auditTrails:application.defaultCapError.authFailed');

        if (lead) {
            await createCapSubmissionAuditTrails({
                lead,
                capActionAuditTrail: AuditTrailKind.CapBPSearchFailed,
                success: false,
                errorMessage,
            });
        }
        throw new Error(errorMessage);
    }

    const dealerData = await loaders.dealerById.load(dealerId);

    const dealerGuid = dealerData?.integrationDetails?.dealerCode
        ? await withRetryApiCall(() =>
              getDealerDetails({
                  ...capSetting,
                  auth: authentication.access_token,
                  dealerCode: dealerData.integrationDetails.dealerCode,
              })
          ).then(dealerDetails => (dealerDetails?.d?.results?.length ? dealerDetails.d.results[0].dealerGuid : ''))
        : '';

    const leadDetailData = await getLead({
        ...capSetting,
        t,
        captureTrails: true,
        auth: authentication.access_token,
        query: {
            dealerGuid,
            partnerGuid,
            page: Math.floor(pagination.offset / pagination.limit) + 1,
            size: pagination.limit,
        },
    });

    if (leadDetailData.error) {
        throw new Error('Failed to fetching the leads');
    }

    if (leadDetailData?.d?.results?.length) {
        const {
            d: { results, __count: count },
        } = leadDetailData;

        const mappedLeads = await Promise.all(
            results.map(async result => {
                const interestVehicleModel = primaryInterestMapping.find(vehicle => vehicle.modelId === result.modelId);
                const dateValue = dateFromCapDateFormat(result.changeDateTime);

                return {
                    leadGuid: result.leadGuid,
                    leadId: result.leadId,
                    date: dateValue ? dateValue.toDate() : null,
                    interestVehicle: interestVehicleModel ? capitalize(interestVehicleModel.model) : result.modelId,
                    campaignId: await getCampaignIdFromCap(
                        t,
                        lead,
                        capSetting,
                        authentication.access_token,
                        result.campaignGuid
                    ),
                };
            })
        );

        return {
            count: parseInt(count, 10),
            items: mappedLeads,
        };
    }

    return { count: 0, items: [] };
};

export default requiresLoggedUser(query);
