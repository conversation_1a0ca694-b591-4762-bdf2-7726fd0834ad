import { flatten, isEmpty, uniqBy } from 'lodash/fp';
import { AuditTrailKind } from '../../../../database/documents/AuditTrail';
import { getLocalCustomerAggregatedFields } from '../../../../database/helpers/customers/shared';
import { ppnAuth, searchBusinessPartner } from '../../../../integrations/cap';
import { BusinessPartnerResData } from '../../../../integrations/cap/businessPartner/types';
import { getCustomerCompetitorVehicleDetails } from '../../../../integrations/cap/customerCompetitorVehicle';
import { createCapSubmissionAuditTrails, getCustomerCurrentVehicleFromCap } from '../../../../integrations/cap/utils';
// eslint-disable-next-line max-len
import formatBpCompetitorVehicleReturnValue from '../../../../integrations/cap/utils/formatBpCompetitorVehicleReturnValue';
import formatBusinessPartnerReturnValue from '../../../../integrations/cap/utils/formatBusinessPartnerReturnValue';
import { getCustomerExistingHobbies } from '../../../../integrations/cap/utils/getExistingHobbies';
import { GenericGetResult } from '../../../../integrations/cap/utils/types';
import { LeadPolicyAction } from '../../../../permissions';
import { ModulePolicyAction } from '../../../../permissions/types/modules';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLCapBusinessPartnerData, GraphQLQueryResolvers, ModuleType } from '../../definitions';

/* 
    Note : If the query has leadId, meaning the request coming from lead details
*/

/* 
    Some functions is commented and will reverted to using thee old functions since there're some edge cases from C@P
    where the enhanced function for getting BP in one query with pagination is getting error from C@P.

    DO NOT REMOVE THESE COMMENTED FUNCTIONS
*/

const query: GraphQLQueryResolvers['getCapBusinessPartners'] = async (
    root,
    { pagination, applicationModuleId, eventId, capModuleId, leadId, dealerId, email, phone },
    { loaders, getTranslations, getPermissionController }
) => {
    const { leads: leadPermission, modules: modulePermission } = await getPermissionController();
    const { t } = await getTranslations(['auditTrails']);

    if (!leadId && !email && !phone) {
        throw new InvalidInput({ ref: 'Reference value is invalid' });
    }

    const applicationModule = await loaders.moduleById.load(applicationModuleId);

    if (!applicationModule) {
        throw new InvalidInput({ module: `Module of this application is invalid.` });
    }

    const company = await loaders.companyById.load(applicationModule.companyId);
    if (!company) {
        throw new InvalidInput({ module: `Company of this application is invalid.` });
    }

    // User access check
    if (
        !(leadId && leadPermission.hasPolicyForAction(LeadPolicyAction.UpdateContact)) &&
        !modulePermission.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication)
    ) {
        throw new InvalidPermission();
    }

    if (applicationModule._type === ModuleType.EventApplicationModule) {
        if (!eventId) {
            throw new InvalidInput({ event: `Invalid Event ID` });
        }

        const eventDetails = await loaders.eventById.load(eventId);
        if (!eventDetails.isCapEnabled) {
            throw new InvalidInput({ event: `This event does not enable C@P integrated` });
        }
    } else if (
        applicationModule._type === ModuleType.ConfiguratorModule ||
        applicationModule._type === ModuleType.FinderApplicationPrivateModule ||
        applicationModule._type === ModuleType.FinderApplicationPublicModule ||
        applicationModule._type === ModuleType.LaunchPadModule ||
        applicationModule._type === ModuleType.StandardApplicationModule
    ) {
        if (!applicationModule.capModuleId) {
            throw new InvalidInput({ module: `This module does not have C@P module integrated` });
        }
    } else {
        throw new InvalidInput({ module: `Module of this application is invalid.` });
    }

    const capSetting = await loaders.capSettingByModuleId.load(capModuleId);

    const lead = leadId ? await loaders.leadById.load(leadId) : null;

    const authentication = await ppnAuth(capSetting);
    if (authentication.error) {
        const errorMessage = t('auditTrails:application.defaultCapError.authFailed');
        if (lead) {
            await createCapSubmissionAuditTrails({
                lead,
                capActionAuditTrail: AuditTrailKind.CapBPSearchFailed,
                success: false,
                errorMessage,
            });
        }
        throw new Error(errorMessage);
    }

    const getCustomerData = async () => {
        if (lead) {
            const [customerData, dealerData] = await Promise.all([
                loaders.customerById.load(lead.customerId),
                loaders.dealerById.load(lead.dealerId),
            ]);
            const customerFields = getLocalCustomerAggregatedFields(customerData);

            return {
                email: customerFields.email ?? '',
                mobileNumber: customerFields.phone?.value ?? '',
                dealer: dealerData.integrationDetails?.dealerCode ?? '',
            };
        }

        const dealerData = await loaders.dealerById.load(dealerId);

        return {
            email: email ?? '',
            mobileNumber: phone ?? '',
            dealer: dealerData.integrationDetails?.dealerCode,
        };
    };

    const customerData = await getCustomerData();

    if (isEmpty(customerData.dealer)) {
        return { count: 0, items: [] };
    }

    // Start of old function
    const businessPartnerDetailData: GenericGetResult<BusinessPartnerResData>[] = await Promise.all(
        [
            !isEmpty(customerData.email)
                ? searchBusinessPartner({
                      ...capSetting,
                      lead,
                      t,
                      auth: authentication.access_token,
                      query: { email: customerData.email, dealer: customerData.dealer, size: 50 },
                  })
                : null,
            !isEmpty(customerData.mobileNumber)
                ? searchBusinessPartner({
                      ...capSetting,
                      lead,
                      t,
                      auth: authentication.access_token,
                      query: { mobileNumber: customerData.mobileNumber, dealer: customerData.dealer, size: 50 },
                  })
                : null,
        ].filter(Boolean)
    );

    const combinedBusinessPartnerData: {
        data?: GraphQLCapBusinessPartnerData;
        error?: string;
    }[] = flatten(
        await Promise.all(
            businessPartnerDetailData
                .map(async bpDetail => {
                    if (bpDetail?.error) {
                        return {
                            data: null,
                            error: bpDetail.error,
                        };
                    }

                    if (!bpDetail?.d?.results?.length) {
                        return null;
                    }

                    const {
                        d: { results },
                    } = bpDetail;

                    const resultPromises = results.map(async result => ({
                        data: await formatBusinessPartnerReturnValue(result, company, loaders),
                        error: null,
                    }));

                    return Promise.all(resultPromises);
                })
                .filter(Boolean)
        )
    ).filter(Boolean);

    // If all of the query is error
    if (combinedBusinessPartnerData.length && combinedBusinessPartnerData.every(bpData => !isEmpty(bpData?.error))) {
        const uniqError = uniqBy('error', combinedBusinessPartnerData)[0].error;
        await createCapSubmissionAuditTrails({
            lead,
            capActionAuditTrail: AuditTrailKind.CapBPSearchFailed,
            success: false,
            errorMessage: uniqError,
        });
    }

    const combinedBpData = combinedBusinessPartnerData.map(bp => bp.data).filter(Boolean);

    const [matchByPhoneAndEmail, matchByPhone, matchByEmail] = [
        combinedBpData.filter(
            bp =>
                bp.email.toLowerCase() === customerData.email.toLowerCase() &&
                bp.phone.value === customerData.mobileNumber
        ),
        combinedBpData.filter(bp => bp.phone.value === customerData.mobileNumber),
        combinedBpData.filter(bp => bp.email.toLowerCase() === customerData.email.toLowerCase()),
    ];

    const uniqueBusinessPartners = uniqBy('businessPartnerGuid', [
        ...matchByPhoneAndEmail,
        ...matchByPhone,
        ...matchByEmail,
    ]);

    const businessPartnerIds = uniqueBusinessPartners.map(bp => bp.businessPartnerId);
    // End of old function

    // DO NOT REMOVE THIS
    /* 
    const businessPartnerDetailData: GenericGetResult<BusinessPartnerResData> = await searchBusinessPartner({
        ...capSetting,
        t,
        auth: authentication.access_token,
        query: {
            page: Math.floor(pagination.offset / pagination.limit) + 1,
            size: pagination.limit,
            email: customerData.email,
            mobileNumber: customerData.mobileNumber,
            dealer: customerData.dealer,
        },
    });

    if (businessPartnerDetailData?.error) {
        await createCapSubmissionAuditTrails({
            lead,
            capActionAuditTrail: AuditTrailKind.CapBPSearchFailed,
            success: false,
            errorMessage: businessPartnerDetailData.error,
        });
        throw new Error(businessPartnerDetailData.error);
    }

    if (!businessPartnerDetailData?.d?.results?.length) {
        return { count: 0, items: [] };
    }

    const {
        d: { results, __count: count },
    } = businessPartnerDetailData;

    const mappedBusinessPartner = await Promise.all(
        results.map(async result => ({
            businessPartnerGuid: result.BusinessPartnerGuid,
            businessPartnerId: result.BusinessPartnerId,
            fullName: result.NameFirst || result.NameLast ? [result.NameFirst, result.NameLast].join(' ') : '',
            firstName: result.NameFirst ?? '',
            lastName: result.NameLast,
            lastNameFront: result.NameLast,
            firstNameJapan: result.NameMiddle,
            lastNameJapan: result.NameLastOther,
            dateOfBirth: !isEmpty(result.DateOfBirth) ? dateFromCapDateFormat(result.DateOfBirth).toDate() : undefined,
            email: result.EMailAddress,
            phone: {
                prefix: result.BpData_To_BpComm.results.length
                    ? getPhonePrefix(result.BpData_To_BpComm.results[0].Country)
                    : undefined,
                value: result.MobileNumber ?? '',
            },
            telephone: {
                prefix: result.BpData_To_BpComm.results.length
                    ? getPhonePrefix(result.BpData_To_BpComm.results[0].Country)
                    : undefined,
                value: result.TelephoneNumber ?? '',
            },
            title: result.Title ? getTitleByCapCode(result.Title) : undefined,
            maritalStatus: result.BpData_To_BpDemographics?.results?.length
                ? getMaritalByCapCode(result.BpData_To_BpDemographics.results[0].MaritalStatus)
                : undefined,
            gender: result.BpData_To_BpDemographics?.results?.length
                ? getGenderByCapCode(result.BpData_To_BpDemographics.results[0].Sex)
                : undefined,
            companyName: result.CompanyName1,
            businessTitle: result.BusinessTitle,
            companyPhoeticName: getCompanyPhoeticName(result.CompanyName2),
            occupation: getOccupation(result.CompanyName2),
            ...(result.BpData_To_BpAddress?.results?.length
                ? getCustomerAdressFromCap(result.BpData_To_BpAddress.results[0])
                : {}),
            ...(await getResponsibleSalesPersonAndDealerFromCap(result, loaders)),
            isActive: !result.Xdele && result.DealerArchivingFlag !== 'X',
        }))
    );

    const businessPartnerIds = mappedBusinessPartner.map(bp => bp.businessPartnerId);
    */

    const customerCompetitorVehicleResultData = await getCustomerCompetitorVehicleDetails({
        ...capSetting,
        auth: authentication.access_token,
        customerIds: businessPartnerIds,
    });

    // This is the start of old function
    const competitorVehicleFromCustomerData =
        !customerCompetitorVehicleResultData?.error && customerCompetitorVehicleResultData?.d?.results?.length
            ? customerCompetitorVehicleResultData.d.results
            : [];

    const competitorVehicleFromBpData = uniqBy(
        'businessPartnerId',
        flatten(
            businessPartnerDetailData
                .map(bpDetail => {
                    if (bpDetail.error || !bpDetail.d?.results.length) {
                        return null;
                    }

                    const {
                        d: { results },
                    } = bpDetail;

                    return results
                        .map((result: BusinessPartnerResData) => {
                            if (!businessPartnerIds.includes(result.BusinessPartnerId)) {
                                return null;
                            }

                            return {
                                businessPartnerId: result.BusinessPartnerId,
                                competitorVehicle: result.BpData_To_BpCompetitorVehicle?.results?.length
                                    ? result.BpData_To_BpCompetitorVehicle.results[0]
                                    : null,
                            };
                        })
                        .filter(Boolean);
                })
                .filter(Boolean)
        )
    );
    // End of old function

    // DO NOT REMOVE THIS
    /*
    const competitorVehicleFromCustomerData =
        !customerCompetitorVehicleResultData?.error && customerCompetitorVehicleResultData?.d?.results?.length
            ? customerCompetitorVehicleResultData.d.results
            : [];

    const competitorVehicleFromBpData = results.map((result: BusinessPartnerResData) => {
        if (!businessPartnerIds.includes(result.BusinessPartnerId)) {
            return null;
        }

        return {
            businessPartnerId: result.BusinessPartnerId,
            competitorVehicle: result.BpData_To_BpCompetitorVehicle?.results?.length
                ? result.BpData_To_BpCompetitorVehicle.results[0]
                : null,
        };
    });
    */

    const combinedCompetitorVehicle = await Promise.all(
        businessPartnerIds.map(async businessPartnerId => {
            const [vehicleFromCustomer, vehicleFromBp] = [
                competitorVehicleFromCustomerData.find(vehicle => vehicle.customerId === businessPartnerId),
                competitorVehicleFromBpData.find(vehicle => vehicle.businessPartnerId === businessPartnerId),
            ];

            const capCustomerCurrentVehicle = formatBpCompetitorVehicleReturnValue(
                vehicleFromBp?.competitorVehicle,
                vehicleFromCustomer
            );

            const currentVehicle =
                competitorVehicleFromCustomerData?.length || competitorVehicleFromBpData
                    ? await getCustomerCurrentVehicleFromCap(capCustomerCurrentVehicle)
                    : undefined;

            return vehicleFromCustomer || vehicleFromBp?.competitorVehicle
                ? { businessPartnerId, currentVehicle }
                : { businessPartnerId, currentVehicle: null };
        })
    );

    const uniqueBusinessPartnersWithHobby = await Promise.all(
        uniqueBusinessPartners.map(async bpData => {
            const { existingHobbiesMap } = await getCustomerExistingHobbies({
                customerGuid: bpData.businessPartnerGuid,
                companyCode: company?.countryCode,
                t,
                lead,
                ...capSetting,
                auth: authentication.access_token,
            });

            const hobbyKeys = Array.from(existingHobbiesMap.values())
                ?.filter(hobby => hobby.isValid)
                .map(hobby => hobby.hobbyKey);

            const currentVehicle = combinedCompetitorVehicle.find(
                competitor => competitor.businessPartnerId === bpData.businessPartnerId
            );

            return {
                ...bpData,
                hobby: hobbyKeys,
                ...(currentVehicle?.currentVehicle ? { currentVehicle: currentVehicle.currentVehicle } : {}),
            };
        })
    );

    // Start of Adjusted return to following old query result
    return {
        count: uniqueBusinessPartnersWithHobby.length,
        items: uniqueBusinessPartnersWithHobby,
    };
    // End of Adjusted return to following old query result

    // DO NOT REMOVE THIS
    /*
    return {
        count: parseInt(count, 10),
        // Inactive BP will be placed on latest rows
        items: mappedBusinessPartner
            .sort((a, b) => Number(b.isActive) - Number(a.isActive))
            .map(bpData => {
                const currentVehicle = combinedCompetitorVehicle.find(
                    competitor => competitor.businessPartnerId === bpData.businessPartnerId
                );

            return {
                ...bpData,
                ...(currentVehicle?.currentVehicle ? { currentVehicle: currentVehicle.currentVehicle } : {}),
            };
        }),
    }; */
};

export default requiresLoggedUser(query);
