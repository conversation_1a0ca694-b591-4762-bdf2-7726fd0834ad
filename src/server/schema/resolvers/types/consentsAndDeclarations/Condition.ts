import { ConditionType, GraphQLConditionResolvers } from '../../definitions';

const ConditionGraphQL: GraphQLConditionResolvers = {
    __resolveType: parent => {
        switch (parent.type) {
            case ConditionType.Or:
            case ConditionType.And:
                return 'LogicCondition';

            case ConditionType.IsApplicationModule:
                return 'ApplicationModuleCondition';

            case ConditionType.IsBank:
                return 'BankCondition';

            case ConditionType.IsDealer:
                return 'DealerCondition';

            case ConditionType.IsInsurer:
                return 'InsurerCondition';

            case ConditionType.isLocation:
                return 'LocationCondition';

            case ConditionType.IsGiftVoucher:
                return 'GiftVoucherCondition';

            case ConditionType.SalesOfferAgreements:
                return 'SalesOfferAgreementsCondition';

            case ConditionType.IsApplicant:
            case ConditionType.IsGuarantor:
            case ConditionType.IsCorporate:
            case ConditionType.IsApplyingForFinancing:
            case ConditionType.IsApplyingForInsurance:
            case ConditionType.IsTradeIn:
            case ConditionType.IsTestDrive:
            case ConditionType.IsShowroomVisit:
            case ConditionType.WithMyinfo:
            case ConditionType.IsTestDriveProcess:
            case ConditionType.WithoutMyinfo:
            case ConditionType.ForCapQualification:
                return 'ContextualCondition';

            default:
                throw new Error('Condition type is not supported');
        }
    },
};

export default ConditionGraphQL;
