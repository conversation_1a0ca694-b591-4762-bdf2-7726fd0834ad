import { uniqWith } from 'lodash/fp';
import * as permissionKind from '../../../../shared/permissions';
import { TranslatedString } from '../../../database/documents';
import { ModuleType } from '../../../database/documents/modules';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { DealerPolicyAction } from '../../../permissions';
import { createPermissionsResolver } from '../../../utils/permissionResolvers';
import { BankKind, GraphQLDealerResolvers } from '../definitions';

const resolver: GraphQLDealerResolvers = {
    id: root => root._id,
    company: (root, args, { loaders }) => loaders.companyById.load(root.companyId),
    versioning: root => root._versioning,
    permissions: (root, args, context) =>
        createPermissionsResolver(context, async () => {
            const { dealers: controller } = await context.getPermissionController();

            return [
                [controller.mayOperateOn(root, DealerPolicyAction.Update), permissionKind.updateDealer],
                [
                    controller.mayOperateOn(root, DealerPolicyAction.UpdateIntegration),
                    permissionKind.updateDealerIntegration,
                ],
                [controller.mayOperateOn(root, DealerPolicyAction.Delete), permissionKind.deleteDealer],
            ];
        }),
    userIds: async (root, args, { loaders }) => {
        const userGroups = await loaders.userGroupsByDealerId.load(root._id);

        const userIds = userGroups.flatMap(userGroup => userGroup.userIds);

        return uniqWith((a, b) => a.equals(b), userIds);
    },
    disclaimers: async (root, args, { loaders }) => {
        const { collections } = await getDatabaseContext();
        const companyLookup = [
            {
                $lookup: {
                    from: 'modules',
                    localField: 'moduleId',
                    foreignField: '_id',
                    as: 'module',
                },
            },
            {
                $unwind: {
                    path: '$module',
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: 'companies',
                    localField: 'module.companyId',
                    foreignField: '_id',
                    as: 'company',
                },
            },
            {
                $unwind: {
                    path: '$company',
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $match: {
                    'company._id': root.companyId,
                    isDeleted: false,
                },
            },
        ];

        /* 
            Related ticket : AN-1565
            Current Condition :
            Financing & Insurance disclaimer should able to return multiple value like the price disclaimer, 
            but currently it's returning only the 1st value of the array.
            *still need to check PM/PO whether with current condition already sufficient or need to be update.

            If need to be update then,
            TO DO:
            1. Check and update function below so Financing & Insurance disclaimer able to be returned as multiple value
            2. Check with related functions/components in the front-end (on DealerDisclaimers component and maybe other 
            related component/function) whether it's already good or need to have some adjustment regarding this changes

         */

        const financingDisclaimer = await collections.banks
            .aggregate([{ $match: { kind: BankKind.System } }, ...companyLookup])
            .map(bank => ({
                bankId: bank._id,
                bankName: bank.displayName,
                financingDisclaimer: (bank.financingDisclaimer?.overrides.filter(disclaimer =>
                    disclaimer.dealerId.equals(root._id)
                )[0]?.value[0] as TranslatedString) || { defaultValue: '', overrides: [] },
            }))
            .toArray();

        const insuranceDisclaimer = await collections.insurers
            .aggregate(companyLookup)
            .map(insurer => ({
                insurerId: insurer._id,
                insurerName: insurer.displayName,
                insuranceDisclaimer: (insurer.insuranceDisclaimer?.overrides.filter(disclaimer =>
                    disclaimer.dealerId.equals(root._id)
                )[0]?.value[0] as TranslatedString) || { defaultValue: '', overrides: [] },
            }))
            .toArray();

        return {
            insuranceDisclaimer,
            financingDisclaimer,
        };
    },
    modulesInDealer: async (root, args, { loaders, getPermissionController }) => {
        const { collections } = await getDatabaseContext();
        const permissionController = await getPermissionController();
        /**
         *
         * if the user does not have the Dealer view permission
         * it should not able to view Modules in dealer fragments
         */
        if (!permissionController.dealers.mayOperateOn(root, DealerPolicyAction.View)) {
            return [];
        }

        const moduleInDealerAvailable = [
            ModuleType.AppointmentModule,
            ModuleType.VisitAppointmentModule,
            ModuleType.ConfiguratorModule,
            ModuleType.StandardApplicationModule,
            ModuleType.FinderApplicationPrivateModule,
            ModuleType.FinderApplicationPublicModule,
            ModuleType.EventApplicationModule,
            ModuleType.MobilityModule,
            ModuleType.GiftVoucherModule,
            ModuleType.LaunchPadModule,
            ModuleType.SalesOfferModule,
        ];

        const moduleInDealer = await Promise.all(
            moduleInDealerAvailable.map(async moduleInDealerType => {
                const modules = await collections.modules
                    .find({ _type: moduleInDealerType, companyId: root.companyId })
                    .toArray();

                return { moduleType: moduleInDealerType, count: modules.length, items: modules };
            })
        );

        return moduleInDealer;
    },
    // TODO(VF-1205): this field should be mandatory once the migration is in place
    location: root =>
        root.location
            ? {
                  longitude: root.location?.coordinates[0],
                  latitude: root.location?.coordinates[1],
              }
            : null,
};

export default resolver;
