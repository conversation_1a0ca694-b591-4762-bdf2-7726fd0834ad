import { isEmpty } from 'lodash/fp';
import { LocalCustomerManagementModule, getKYCFieldsFromPresets } from '../../../../database';
import { getCustomerEmail, getMaskedCustomerInfo } from '../../../../database/helpers/customers';
import { GraphQLCorporateCustomerResolvers, MaskDirection, ModuleType } from '../../definitions';
import { getApplications, getFullName, getLatestCustomer } from './LocalCustomer';

const CustomerGraphQL: GraphQLCorporateCustomerResolvers = {
    id: root => root._id,
    module: (root, args, { loaders }) => loaders.moduleById.load(root.moduleId),
    versioning: root => root._versioning,
    fullName: async (root, args, { loaders, getTranslations }) => getFullName(root, loaders, getTranslations),
    latestFullName: async (root, args, { loaders, getTranslations }) => {
        const customer = await getLatestCustomer(root._versioning.suiteId);

        return getFullName(customer, loaders, getTranslations);
    },
    email: async (root, args, { getTranslations }) => {
        const { t } = await getTranslations();

        return getCustomerEmail(t, root);
    },
    kind: root => root._kind,
    fields: async (root, args, { loaders }) => {
        // get the application module
        const applicationModule = await loaders.moduleById.load(root.moduleId);

        // get company
        const company = await loaders.companyById.load(applicationModule.companyId);
        const maskSetting = company?.mask;

        if (maskSetting.direction !== MaskDirection.None) {
            const customerDataFields = root.fields.map(field =>
                getMaskedCustomerInfo(field, company.mask.count, company.mask.direction)
            );

            return customerDataFields;
        }

        return root.fields;
    },

    // Latest application
    latestApplication: async (root, args, { loaders, getPermissionController }) => {
        const applications = await getApplications(root._versioning.suiteId, loaders, getPermissionController, true);

        return applications[0] ?? null;
    },
    isMaskingCustomerData: async (root, args, { loaders }) => {
        // get the application module
        const applicationModule = await loaders.moduleById.load(root.moduleId);

        // get company
        const company = await loaders.companyById.load(applicationModule.companyId);
        const maskSetting = company?.mask;

        return maskSetting.direction !== MaskDirection.None;
    },
    kycPresets: async (root, args, { loaders }) => {
        if (isEmpty(root.kycPresetIds)) {
            return [];
        }
        const customerModule = await loaders.moduleById.load(root.moduleId);
        if (customerModule._type !== ModuleType.LocalCustomerManagement) {
            throw new Error('Local Customer Management Modules required');
        }

        const { kycPresets } = customerModule as LocalCustomerManagementModule;

        const matchingKycPresets = kycPresets.filter(kycPreset =>
            root.kycPresetIds.some(rootKycPresetId => kycPreset._id.equals(rootKycPresetId))
        );

        return getKYCFieldsFromPresets(
            customerModule.kycFields.sort((a, b) => a.order - b.order),
            matchingKycPresets
        );
    },
    applications: async (root, args, { loaders, getPermissionController }) =>
        getApplications(root._versioning.suiteId, loaders, getPermissionController),
};

export default CustomerGraphQL;
