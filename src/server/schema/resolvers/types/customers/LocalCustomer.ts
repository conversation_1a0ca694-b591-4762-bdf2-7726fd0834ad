import { isEmpty } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { GetTranslations } from '../../../../core/translations';
import {
    type Customer,
    type LocalCustomerManagementModule,
    getKYCFieldsFromPresets,
    isCapAvailableApplication,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getMaskedCustomerInfo,
    getCustomerEmail,
    getConditionTypeFromCustomerkind,
    getCustomerFullName,
} from '../../../../database/helpers/customers';
import type { Loaders } from '../../../../loaders';
import { ApplicationPolicyAction } from '../../../../permissions/types/applications';
import applyDataMasking from '../../../../utils/maskInfo';
import type { PermissionControllerGetter } from '../../../permissions';
import { ApplicationStage, type GraphQLLocalCustomerResolvers, MaskDirection, ModuleType } from '../../definitions';

export const getApplications = async (
    suiteId: ObjectId,
    loaders: Loaders,
    getPermissionController: PermissionControllerGetter,
    includeAllStagesFromHierarchy?: boolean
) => {
    const { collections } = await getDatabaseContext();
    const { applications: applicationPermission } = await getPermissionController();
    const permissionFilter = await applicationPermission.getFilterQueryForAction(
        ApplicationPolicyAction.View,
        includeAllStagesFromHierarchy
            ? [
                  ApplicationStage.Financing,
                  ApplicationStage.Insurance,
                  ApplicationStage.Appointment,
                  ApplicationStage.Reservation,
                  ApplicationStage.Mobility,
              ]
            : null
    );

    const customers = await loaders.customerBySuiteId.load(suiteId);

    const applicationArray = await collections.applications
        .find({
            $and: [
                { isDraft: false },
                { '_versioning.isLatest': true },
                { applicantId: { $in: customers.map(customer => customer._id) } },
                permissionFilter,
            ],
        })
        .sort({ '_versioning.updatedAt': -1 })
        .toArray();

    return applicationArray;
};

const getLeads = async (suiteId: ObjectId, loaders: Loaders, getPermissionController: PermissionControllerGetter) => {
    const { collections } = await getDatabaseContext();

    const customers = await loaders.customerBySuiteId.load(suiteId);
    const applications = await getApplications(suiteId, loaders, getPermissionController);

    const leads = await collections.leads
        .find({
            '_versioning.isLatest': true,
            $or: [
                { customerId: { $in: customers.map(customer => customer._id) } },
                {
                    _id: {
                        $in: applications.filter(isCapAvailableApplication).map(application => application.leadId),
                    },
                },
            ],
        })
        .sort({ '_versioning.updatedAt': -1 })
        .toArray();

    return leads;
};

export const getLatestCustomer = async suiteId => {
    const { collections } = await getDatabaseContext();
    const customer = await collections.customers.findOne({
        '_versioning.suiteId': suiteId,
        '_versioning.isLatest': true,
    });

    return customer;
};

export const getFullName = async (root: Customer, loaders: Loaders, getTranslations: GetTranslations) => {
    const { t } = await getTranslations(['common']);
    // get the application module
    const module = await loaders.moduleById.load(root.moduleId);

    if (module._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('module not found');
    }

    const { kycPresets } = module;

    // get company
    const company = await loaders.companyById.load(module.companyId);
    const maskSetting = company?.mask;

    const filteredKycPresets = kycPresets.filter(
        kyc =>
            kyc.conditions.length > 0 &&
            kyc.conditions.some(condition => condition.type === getConditionTypeFromCustomerkind(root))
    );

    const customerFullName = getCustomerFullName(t, root, company, filteredKycPresets);

    if (maskSetting.direction !== MaskDirection.None) {
        return applyDataMasking(customerFullName, company.mask.count, company.mask.direction);
    }

    return customerFullName;
};

const CustomerGraphQL: GraphQLLocalCustomerResolvers = {
    id: root => root._id,
    module: (root, args, { loaders }) => loaders.moduleById.load(root.moduleId),
    versioning: root => root._versioning,
    fullName: async (root, args, { loaders, getTranslations }) => getFullName(root, loaders, getTranslations),
    latestFullName: async (root, args, { loaders, getTranslations }) => {
        const customer = await getLatestCustomer(root._versioning.suiteId);

        return getFullName(customer, loaders, getTranslations);
    },
    email: async (root, args, { getTranslations }) => {
        const { t } = await getTranslations();

        return getCustomerEmail(t, root);
    },
    kind: root => root._kind,
    fields: async (root, args, { loaders }) => {
        // get the application module
        const applicationModule = await loaders.moduleById.load(root.moduleId);
        // get company
        const company = await loaders.companyById.load(applicationModule.companyId);
        const maskSetting = company?.mask;

        if (maskSetting.direction !== MaskDirection.None) {
            const customerDataFields = root.fields.map(field =>
                getMaskedCustomerInfo(field, company.mask.count, company.mask.direction)
            );

            return customerDataFields;
        }

        return root.fields;
    },

    // Latest application
    latestApplication: async (root, args, { loaders, getPermissionController }) => {
        const applications = await getApplications(root._versioning.suiteId, loaders, getPermissionController);

        return applications[0] ?? null;
    },
    latestLead: async (root, args, { loaders, getPermissionController }) => {
        const leads = await getLeads(root._versioning.suiteId, loaders, getPermissionController);

        return leads?.[0] ?? null;
    },
    isMaskingCustomerData: async (root, args, { loaders }) => {
        // get the application module
        const applicationModule = await loaders.moduleById.load(root.moduleId);

        // get company
        const company = await loaders.companyById.load(applicationModule.companyId);
        const maskSetting = company?.mask;

        return maskSetting.direction !== MaskDirection.None;
    },
    kycPresets: async (root, args, { loaders }) => {
        if (isEmpty(root.kycPresetIds)) {
            return [];
        }
        const customerModule = await loaders.moduleById.load(root.moduleId);
        if (customerModule._type !== ModuleType.LocalCustomerManagement) {
            throw new Error('Local Customer Management Modules required');
        }

        const { kycPresets } = customerModule as LocalCustomerManagementModule;

        const matchingKycPresets = kycPresets.filter(kycPreset =>
            root.kycPresetIds.some(rootKycPresetId => kycPreset._id.equals(rootKycPresetId))
        );

        return getKYCFieldsFromPresets(customerModule.kycFields, matchingKycPresets);
    },
    applications: async (root, args, { loaders, getPermissionController }) =>
        getApplications(root._versioning.suiteId, loaders, getPermissionController, true),
    businessPartnerIds: async (root, args, { loaders, getPermissionController }) => {
        const leads = await getLeads(root._versioning.suiteId, loaders, getPermissionController);

        const businessPartnerIds = [...new Set(leads.map(lead => lead.capValues?.businessPartnerId).filter(Boolean))];

        return businessPartnerIds;
    },
};

export default CustomerGraphQL;
