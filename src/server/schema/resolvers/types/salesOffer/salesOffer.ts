import { getKycFieldsFromSalesOffer, SalesOfferApplication, VehicleSalesOffer } from '../../../../database';
import extractVehicleData from '../../../../integrations/porscheVehicleData/extractVehicleData';
import { GraphQLSalesOfferResolvers, SalesOfferAgreementKind, SalesOfferFeatureKind } from '../../definitions';
import { getSalesOfferAgreements } from '../applications/shared';

const extractedVehicleDataSchema = async (root: VehicleSalesOffer['resultAPI']) => extractVehicleData(root);

const resolver: GraphQLSalesOfferResolvers = {
    id: root => root._id,
    vehicle: async (root, args, { loaders }) => ({
        ...root.vehicle,
        vehicle: loaders.vehicleById.load(root.vehicle.vehicleId),
        porscheVehicleData: await extractedVehicleDataSchema(root.vehicle.resultAPI),
    }),
    versioning: root => root._versioning,
    module: async (root, args, { loaders }) => root.moduleId && loaders.moduleById.load(root.moduleId),
    latestFinancingApplication: async (root, args, { loaders }) => {
        if (!root.latestFinancingApplicationSuiteId) {
            return null;
        }

        return (await loaders.applicationBySuiteId.load(
            root.latestFinancingApplicationSuiteId
        )) as SalesOfferApplication;
    },
    latestInsuranceApplication: async (root, args, { loaders }) => {
        if (!root.latestInsuranceApplicationSuiteId) {
            return null;
        }

        return (await loaders.applicationBySuiteId.load(
            root.latestInsuranceApplicationSuiteId
        )) as SalesOfferApplication;
    },
    latestReservationApplication: async (root, args, { loaders }) => {
        if (!root.latestReservationApplicationSuiteId) {
            return null;
        }

        return (await loaders.applicationBySuiteId.load(
            root.latestReservationApplicationSuiteId
        )) as SalesOfferApplication;
    },
    kycPresets: async (root, args, { loaders }) => {
        const finance = await getKycFieldsFromSalesOffer(root, SalesOfferFeatureKind.Finance);
        const insurance = await getKycFieldsFromSalesOffer(root, SalesOfferFeatureKind.Insurance);
        const salesOffer = await getKycFieldsFromSalesOffer(
            root,
            SalesOfferFeatureKind.VSA,
            SalesOfferAgreementKind.VSA
        );
        const coe = await getKycFieldsFromSalesOffer(
            root,
            SalesOfferFeatureKind.MainDetails,
            SalesOfferAgreementKind.COE
        );
        const specification = await getKycFieldsFromSalesOffer(
            root,
            SalesOfferFeatureKind.Vehicle,
            SalesOfferAgreementKind.Specification
        );

        return {
            finance,
            insurance,
            salesOffer,
            coe,
            specification,
        };
    },
    consents: async (root, args, { loaders }) => {
        const finance = await getSalesOfferAgreements(root, SalesOfferFeatureKind.Finance, 'local');
        const insurance = await getSalesOfferAgreements(root, SalesOfferFeatureKind.Insurance, 'local');
        const salesOffer = await getSalesOfferAgreements(
            root,
            SalesOfferFeatureKind.VSA,
            'local',
            SalesOfferAgreementKind.VSA
        );
        const coe = await getSalesOfferAgreements(
            root,
            SalesOfferFeatureKind.MainDetails,
            'local',
            SalesOfferAgreementKind.COE
        );
        const specification = await getSalesOfferAgreements(
            root,
            SalesOfferFeatureKind.Vehicle,
            'local',
            SalesOfferAgreementKind.Specification
        );

        return {
            finance,
            insurance,
            salesOffer,
            coe,
            specification,
        };
    },
};

export default resolver;
