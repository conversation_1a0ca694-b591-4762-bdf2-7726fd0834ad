import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import urljoin from 'url-join';
import { ApplicationKind, EndpointType } from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { isValidStatusToAmendOrCancel } from '../../../database/helpers/applications';
import { generateJourneyToken } from '../../../journeys';
import { GraphQLMobilityApplicationCancellationLinkResolvers, ModuleType } from '../definitions';

const resolver: GraphQLMobilityApplicationCancellationLinkResolvers = {
    token: async ({ data: { applicationSuiteId, userId } }, args, { loaders }) => {
        const application = await loaders.applicationBySuiteId.load(applicationSuiteId);
        const applicationModule = application?.moduleId ? await loaders.moduleById.load(application.moduleId) : null;
        const lead = await loaders.leadById.load(application.leadId);

        if (
            application?.kind !== ApplicationKind.Mobility ||
            applicationModule._type !== ModuleType.MobilityModule ||
            !isValidStatusToAmendOrCancel(application.mobilityStage?.status)
        ) {
            return '';
        }

        const { mobilityBookingDetails } = application;
        const { amendmentCutOff } = applicationModule;

        if (dayjs().add(amendmentCutOff, 'days').isAfter(mobilityBookingDetails.period.start)) {
            return 'expired';
        }

        const company = await loaders.companyById.load(applicationModule.companyId);

        return generateJourneyToken(lead, application, userId ? 'draft' : 'remote-applicant', company.sessionTimeout, {
            _id: userId,
        });
    },
    endpoint: async (root, args, { loaders }) => {
        const router = await loaders.routerById.load(root.data.routerId);

        return router.endpoints.find(endpoint => endpoint._id.equals(root.data.endpointId));
    },
    router: (root, args, { loaders }) => loaders.routerById.load(root.data.routerId),
    path: async ({ data: { applicationSuiteId } }, args, { loaders }) => {
        const application = await loaders.applicationBySuiteId.load(applicationSuiteId);
        const applicationModule = application?.moduleId ? await loaders.moduleById.load(application.moduleId) : null;

        if (
            application?.kind !== ApplicationKind.Mobility ||
            applicationModule._type !== ModuleType.MobilityModule ||
            !isValidStatusToAmendOrCancel(application.mobilityStage?.status)
        ) {
            return '';
        }

        const {
            routerId,
            endpointId,
            mobilityBookingDetails: { period, inventoryStockId },
        } = application;
        const { amendmentCutOff } = applicationModule;

        if (dayjs().add(amendmentCutOff, 'days').isAfter(period.start)) {
            return '';
        }

        const { collections } = await getDatabaseContext();

        const router = routerId
            ? await loaders.routerById.load(routerId)
            : await collections.routers.findOne({
                  'endpoints._type': EndpointType.MobilityApplicationEntrypoint,
                  mobilityApplicationModuleId: applicationModule._id,
              });

        const endpoint = router?.endpoints?.find(({ _id, _type }) =>
            isNil(endpointId) ? _type === EndpointType.MobilityApplicationEntrypoint : _id.equals(endpointId)
        );

        if (endpoint?._type !== EndpointType.MobilityApplicationEntrypoint) {
            return '';
        }

        return urljoin(endpoint.pathname, 'details', inventoryStockId.toHexString());
    },
    application: async ({ data: { applicationSuiteId } }, args, { loaders }) =>
        loaders.applicationBySuiteId.load(applicationSuiteId),
};

export default resolver;
