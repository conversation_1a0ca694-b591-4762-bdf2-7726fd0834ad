import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import urljoin from 'url-join';
import { EndpointType } from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { isValidStatusToAmendOrCancel } from '../../../database/helpers/applications';
import { generateJourneyToken } from '../../../journeys';
import { ApplicationKind, GraphQLMobilityApplicationAmendmentLinkResolvers, ModuleType } from '../definitions';

const resolver: GraphQLMobilityApplicationAmendmentLinkResolvers = {
    token: async ({ data: { applicationSuiteId, userId } }, args, { loaders }) => {
        const application = await loaders.applicationBySuiteId.load(applicationSuiteId);
        const applicationModule = application?.moduleId ? await loaders.moduleById.load(application.moduleId) : null;

        if (
            application?.kind !== ApplicationKind.Mobility ||
            applicationModule?._type !== ModuleType.MobilityModule ||
            !application.mobilityStage?.status ||
            !isValidStatusToAmendOrCancel(application.mobilityStage?.status)
        ) {
            return '';
        }

        const { mobilityBookingDetails } = application;
        const { amendmentCutOff } = applicationModule;

        if (dayjs().add(amendmentCutOff, 'days').isAfter(mobilityBookingDetails.period.start)) {
            return 'expired';
        }

        const company = await loaders.companyById.load(applicationModule.companyId);

        const lead = await loaders.leadById.load(application.leadId);

        return generateJourneyToken(
            lead,
            application,
            userId ? 'draft' : 'remote-applicant',
            company.sessionTimeout,
            userId ? { _id: userId } : null
        );
    },
    path: async ({ data: { applicationSuiteId } }, args, { loaders }) => {
        const application = await loaders.applicationBySuiteId.load(applicationSuiteId);
        const applicationModule = application?.moduleId ? await loaders.moduleById.load(application.moduleId) : null;

        if (
            application?.kind !== ApplicationKind.Mobility ||
            applicationModule?._type !== ModuleType.MobilityModule ||
            !application.mobilityStage?.status ||
            !isValidStatusToAmendOrCancel(application.mobilityStage?.status)
        ) {
            return '';
        }

        const {
            routerId,
            endpointId,
            mobilityBookingDetails: { period, inventoryStockId },
        } = application;
        const { amendmentCutOff } = applicationModule;

        if (dayjs().add(amendmentCutOff, 'days').isAfter(period.start)) {
            return '';
        }

        const { collections } = await getDatabaseContext();

        const router = routerId
            ? await loaders.routerById.load(routerId)
            : await collections.routers.findOne({
                  'endpoints._type': EndpointType.MobilityApplicationEntrypoint,
                  mobilityApplicationModuleId: applicationModule._id,
              });

        const endpoint = router?.endpoints?.find(({ _id, _type }) =>
            isNil(endpointId) ? _type === EndpointType.MobilityApplicationEntrypoint : _id.equals(endpointId)
        );

        if (endpoint?._type !== EndpointType.MobilityApplicationEntrypoint) {
            return '';
        }

        return urljoin(endpoint.pathname, 'details', inventoryStockId.toHexString());
    },
    application: async ({ data: { applicationSuiteId } }, args, { loaders }) =>
        loaders.applicationBySuiteId.load(applicationSuiteId),
};

export default resolver;
