import { GraphQLSendSalesOfferLinkResolvers } from '../definitions';

const SendSalesOfferLinkGraphQL: GraphQLSendSalesOfferLinkResolvers = {
    linkId: root => root._id,
    router: (root, args, { loaders }) => loaders.routerById.load(root.data.routerId),
    secret: root => root.secret,
    endpoint: async (root, args, { loaders }) => {
        const router = await loaders.routerById.load(root.data.routerId);

        return router.endpoints.find(endpoint => endpoint._id.equals(root.data.endpointId));
    },
    featureKinds: root => root.data.featureKinds,
};

export default SendSalesOfferLinkGraphQL;
