import { ExternalLinkKind } from '../../../database';
import { GraphQLExternalLinkResolvers } from '../definitions';

const ExternalLinkGraphQL: GraphQLExternalLinkResolvers = {
    __resolveType: parent => {
        switch (parent._kind) {
            case ExternalLinkKind.ResetPassword:
                return 'ResetPasswordLink';

            case ExternalLinkKind.CreateNewUser:
                return 'CreateNewUserLink';

            case ExternalLinkKind.ConfiguratorApplication:
                return 'ConfiguratorApplicationLink';

            case ExternalLinkKind.MyInfoCallback:
                return 'MyInfoCallbackLink';

            case ExternalLinkKind.NamirialSigning:
                return 'NamirialSigningLink';

            case ExternalLinkKind.AdyenRedirection:
                return 'AdyenRedirectionLink';

            case ExternalLinkKind.VerifyEmailUpdate:
                return 'VerifyEmailUpdateLink';

            case ExternalLinkKind.ProceedWithCustomer:
                return 'ProceedWithCustomerLink';

            case ExternalLinkKind.PorschePaymentRedirection:
                return 'PorschePaymentRedirectionLink';

            case ExternalLinkKind.MobilityApplicationCancellation:
                return 'MobilityApplicationCancellationLink';

            case ExternalLinkKind.MobilityApplicationAmendment:
                return 'MobilityApplicationAmendmentLink';

            case ExternalLinkKind.FiservPaymentRedirection:
                return 'FiservPaymentRedirectionLink';

            case ExternalLinkKind.PayGatePaymentRedirection:
                return 'PayGatePaymentRedirectionLink';

            case ExternalLinkKind.TtbPaymentRedirection:
                return 'TtbPaymentRedirectionLink';

            case ExternalLinkKind.StandardApplication:
                return 'StandardApplicationLink';

            case ExternalLinkKind.EventApplication:
                return 'EventApplicationLink';

            case ExternalLinkKind.FinderApplication:
                return 'FinderApplicationLink';

            case ExternalLinkKind.CTSFinderRedirection:
                return 'CTSFinderRedirectionLink';

            case ExternalLinkKind.TestDriveProcessRedirection:
                return 'TestDriveProcessRedirectionLink';

            case ExternalLinkKind.GiftVoucherTtbPaymentRedirection:
                return 'GiftVoucherTtbPaymentRedirectionLink';

            case ExternalLinkKind.GiftVoucherPorschePaymentRedirection:
                return 'GiftVoucherPorschePaymentRedirectionLink';

            case ExternalLinkKind.GiftVoucherPayGatePaymentRedirection:
                return 'GiftVoucherPayGatePaymentRedirectionLink';

            case ExternalLinkKind.GiftVoucherFiservPaymentRedirection:
                return 'GiftVoucherFiservPaymentRedirectionLink';

            case ExternalLinkKind.GiftVoucherAdyenRedirection:
                return 'GiftVoucherAdyenRedirectionLink';

            case ExternalLinkKind.ApplyNewRedirection:
                return 'ApplyNewRedirectionLink';

            case ExternalLinkKind.PorscheIdCallback:
                return 'PorscheIdCallbackLink';

            case ExternalLinkKind.SendSalesOffer:
                return 'SendSalesOfferLink';

            default:
                throw new Error('External link kind not supported');
        }
    },
};

export default ExternalLinkGraphQL;
