import * as permissionKind from '../../../../../shared/permissions';
import { Bank, Insurer, VehicleDataWithPorscheCodeIntegrationSetting } from '../../../../database';
import { ModulePolicyAction } from '../../../../permissions';
import ensureManyFromLoaders from '../../../../utils/ensureManyFromLoaders';
import { createPermissionsResolver } from '../../../../utils/permissionResolvers';
import { GraphQLSalesOfferModuleResolvers } from '../../definitions';

const resolver: GraphQLSalesOfferModuleResolvers = {
    id: root => root._id,
    company: (root, args, { loaders }) => loaders.companyById.load(root.companyId),
    bankModule: (root, args, { loaders }) => (root.bankModuleId ? loaders.moduleById.load(root.bankModuleId) : null),
    insuranceModule: (root, args, { loaders }) =>
        root.insuranceModuleId ? loaders.moduleById.load(root.insuranceModuleId) : null,
    vehicleModule: (root, args, { loaders }) => loaders.moduleById.load(root.vehicleModuleId),
    permissions: (root, args, context) =>
        createPermissionsResolver(context, async () => {
            const { modules: controller } = await context.getPermissionController();

            return [
                [controller.mayOperateOn(root, ModulePolicyAction.CreateApplication), permissionKind.createApplication],
                [controller.mayOperateOn(root, ModulePolicyAction.Update), permissionKind.updateModule],
                [controller.mayOperateOn(root, ModulePolicyAction.Delete), permissionKind.deleteModule],
            ];
        }),
    versioning: root => root._versioning,
    insurers: async (root, args, { loaders }) =>
        loaders.insurerById.loadMany(root.insurerIds).then(ensureManyFromLoaders<Insurer>),
    banks: async (root, args, { loaders }) => loaders.bankById.loadMany(root.bankIds).then(ensureManyFromLoaders<Bank>),
    vehicleDataWithPorscheCodeIntegrationSetting: async (root, args, { loaders }) =>
        loaders.settingById.load(
            root.vehicleDataWithPorscheCodeIntegrationSettingId
        ) as Promise<VehicleDataWithPorscheCodeIntegrationSetting | null>,
    marketType: root => root.market,
    market: root => root.market.type,
};

export default resolver;
