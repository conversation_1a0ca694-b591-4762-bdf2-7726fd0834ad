import * as permissionKind from '../../../../../shared/permissions';
import { getNamirialSetting } from '../../../../database/helpers/settings';
import { ModulePolicyAction } from '../../../../permissions';
import { createPermissionsResolver } from '../../../../utils/permissionResolvers';
import { GraphQLNamirialSigningModuleResolvers } from '../../definitions';

const NamirialSigningModuleGraphQL: GraphQLNamirialSigningModuleResolvers = {
    id: root => root._id,
    company: (root, args, { loaders }) => loaders.companyById.load(root.companyId),
    permissions: (root, args, context) =>
        createPermissionsResolver(context, async () => {
            const { modules: controller } = await context.getPermissionController();

            return [
                [controller.mayOperateOn(root, ModulePolicyAction.Update), permissionKind.updateModule],
                [controller.mayOperateOn(root, ModulePolicyAction.Delete), permissionKind.deleteModule],
            ];
        }),
    namirialSetting: root => getNamirialSetting(root._id),
    versioning: root => root._versioning,
};

export default NamirialSigningModuleGraphQL;
