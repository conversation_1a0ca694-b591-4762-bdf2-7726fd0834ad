import type { Collections } from '../../../../database/collections';
import { type Company, EmailProvider, SmsProvider } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CompanyPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import scheduleApplicationDataPurgeJob from '../../../../queues/implementations/shared/scheduleApplicationDataPurgeJob';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLCompanySettings, GraphQLMutationResolvers } from '../../definitions';
import { computeEmailSettingDocument, computeSmsSettingsDocument, validateMarketCode } from './createCompany';

const updateEmailSettingDocument = async (
    company: Company,
    companySettings: Omit<GraphQLCompanySettings, 'edmEmailFooter' | 'dataMask'>
) => {
    if (!company) {
        return null;
    }

    const { emailSettings } = companySettings;

    // check if there is a change in setting
    if (company.emailSettings.provider !== emailSettings.provider) {
        // delete previous setting
        if (company.emailSettings.provider === EmailProvider.Smtp) {
            const { collections } = await getDatabaseContext();
            await collections.settings.deleteOne({ _id: company.emailSettings.settingId });
        }
    }

    const companyEmailSettings = await computeEmailSettingDocument(company._id, companySettings);

    return {
        ...company.emailSettings,
        ...companyEmailSettings,
    };
};

const updateSmsSettingDocument = async (company: Company, companySettings: GraphQLCompanySettings) => {
    if (!company) {
        return null;
    }

    const { smsSettings } = companySettings;

    // check if there is a change in setting & the previous setting is twilio
    if (company.smsSettings.provider !== smsSettings.provider && company.smsSettings.provider === SmsProvider.Twilio) {
        // delete the previous setting
        const { collections } = await getDatabaseContext();
        await collections.settings.deleteOne({ _id: company.smsSettings.settingId });
    }

    const companySmsSettings = await computeSmsSettingsDocument(company._id, companySettings);

    return {
        ...company.smsSettings,
        ...companySmsSettings,
    };
};

const updateDealerLimitFeature = async (
    company: Company,
    companySettings: GraphQLCompanySettings,
    collections: Collections
) => {
    if (
        companySettings.allowLimitDealerFeature ||
        companySettings.allowLimitDealerFeature === company.allowLimitDealerFeature
    ) {
        return;
    }

    // If allowLimitDealerFeature is disabled, reset `dealer.limitFeature`
    await collections.dealers.updateMany(
        {
            companyId: company._id,
            limitFeature: true,
        },
        {
            $set: { limitFeature: false },
        }
    );
};

const mutation: GraphQLMutationResolvers['updateCompany'] = async (
    root,
    { id, settings },
    { getPermissionController, getUser, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const company = await collections.companies.findOne({ _id: id, isDeleted: false });
    const user = await getUser();
    const versioning = getSimpleVersioningByUserForUpdate(user._id);
    const { t } = await getTranslations(['errors']);

    validateMarketCode(settings, t);

    const { mfaSettings, edmEmailFooter, dataMask, ...others } = settings;

    const emailSettings = await updateEmailSettingDocument(company, others);
    const smsSettings = await updateSmsSettingDocument(company, settings);

    const update = await collections.companies.findOneAndUpdate(
        {
            $and: [
                { _id: id, isDeleted: false },
                permissionController.companies.getFilterQueryForAction(CompanyPolicyAction.Update),
            ],
        },
        {
            $set: {
                ...others,
                emailSettings,
                smsSettings,
                ...(mfaSettings &&
                    company?.mfaSettings?.type !== mfaSettings.type && {
                        mfaSettings: { ...mfaSettings, enabledOn: new Date() },
                    }),
                'edmEmailFooter.connectText': edmEmailFooter.connectText,
                'edmEmailFooter.copyRight': edmEmailFooter.copyRight,
                'edmEmailFooter.disclaimerText': edmEmailFooter.disclaimerText,
                'edmEmailFooter.legalNoticeUrl': edmEmailFooter.legalNoticeUrl,
                'edmEmailFooter.privacyPolicyUrl': edmEmailFooter.privacyPolicyUrl,
                ...versioning,
                mask: dataMask,
            },
            ...(!mfaSettings && company?.mfaSettings && { $unset: { mfaSettings: true } }),
        },
        { returnDocument: 'after' }
    );

    if (update) {
        await mainQueue.add({ type: 'upsertPermissions', target: 'company', companyId: id });

        if (update.isDataPurgeEnabled) {
            await scheduleApplicationDataPurgeJob(update._id, update.timeZone, { check: true });
        }

        // If it's different, trigger update customer management module order
        // This only applied to AN-2479. When AN-2417 implemented, this can be removed
        if (company.countryCode !== update.countryCode) {
            await mainQueue.add({
                type: 'updateCustomerManagementModuleOrder',
                companyId: update._id,
            });
        }

        updateDealerLimitFeature(company, settings, collections);
    }

    return update;
};

export default requiresLoggedUser(mutation);
