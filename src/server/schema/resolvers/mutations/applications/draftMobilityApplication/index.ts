import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { isEmpty, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationDraftedAuditTrail,
    ApplicationJourney,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    CustomerKind,
    InventoryKind,
    LocalCustomer,
    MobilityApplication,
    MobilityInventory,
    MobilityModule,
    MobilitySnapshot,
    MobilityStockInventory,
    Lead,
    LeadStatus,
    LeadDraftedAuditTrail,
    StockInventory,
    StockInventoryKind,
    VehicleKind,
} from '../../../../../database';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { convertLocalCustomerGraphQLFields } from '../../../../../database/helpers/customers';
import { executeMobilityJourney } from '../../../../../journeys/mobilityJourney';
import { Loaders } from '../../../../../loaders';
import { getApplicationLogStages } from '../../../../../utils/application';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../../errors';
import { buildRateLimiterMiddleware } from '../../../../middlewares';
import {
    GraphQLMutationDraftMobilityApplicationArgs,
    GraphQLMutationResolvers,
    MobilityKind,
    ModuleType,
} from '../../../definitions';
import { validateEndpoint } from '../helpers';
import { getLocationFromInput, validateBookingPeriod } from './shared';

const validateMobilityDetails = async (
    t: TFunction,
    loaders: Loaders,
    mobilityDetails: GraphQLMutationDraftMobilityApplicationArgs['mobilityDetails']
): Promise<Pick<MobilityApplication, 'mobilitySnapshots'>> => {
    const errors: string[] = [];

    const snapshots = await Promise.all(
        mobilityDetails.map<Promise<MobilitySnapshot | null>>(async ({ kind, id, addon }) => {
            const mobility = await loaders.mobilityById.load(id);
            if (isNil(mobility) || mobility._kind !== kind) {
                errors.push(t('errors:mobilityApplication.mobilityNotFound'));

                return null;
            }

            if (kind === MobilityKind.Addon && isEmpty(addon)) {
                errors.push(t('errors:mobilityApplication.mobilityAddonRequired'));

                return null;
            }

            switch (mobility._kind) {
                case MobilityKind.Addon: {
                    return {
                        _type: MobilityKind.Addon,
                        mobilityId: mobility._id,
                        title: mobility.title,
                        option: addon,
                    };
                }

                case MobilityKind.AdditionalInfo: {
                    return {
                        _type: MobilityKind.AdditionalInfo,
                        mobilityId: mobility._id,
                        title: mobility.title,
                        detail: mobility.detail,
                    };
                }

                default:
                    throw new Error('invalid mobility kind');
            }
        })
    );

    if (errors.length > 0) {
        throw new InvalidInput({ $root: errors.join('\n') });
    }

    return { mobilitySnapshots: snapshots.filter(Boolean) };
};

const validateVehicleBookingDetails = async (
    t: TFunction,
    mobilityModule: MobilityModule,
    mobilityBookingDetails: GraphQLMutationDraftMobilityApplicationArgs['mobilityBookingDetails']
): Promise<
    Pick<MobilityApplication, 'vehicleId' | 'dealerId' | 'mobilityBookingDetails'> & Pick<MobilityInventory, 'stocks'>
> => {
    const { inventoryStockId, location: locationInput } = mobilityBookingDetails;
    const { collections } = await getDatabaseContext();

    const inventory = (await collections.inventories.findOne({
        _kind: InventoryKind.MobilityInventory,
        moduleId: mobilityModule._id,
        isDeleted: false,
        'stocks._kind': StockInventoryKind.MobilityStock,
        'stocks._id': inventoryStockId,
    })) as MobilityInventory;

    const vehicle =
        inventory._kind === InventoryKind.MobilityInventory
            ? await collections.vehicles.findOne({
                  _kind: VehicleKind.LocalVariant,
                  isDeleted: false,
                  '_versioning.isLatest': true,
                  '_versioning.suiteId': inventory.variantSuiteId,
              })
            : null;

    // period check would happen on the flow
    if (isNil(inventory) || vehicle?._kind !== VehicleKind.LocalVariant) {
        throw new InvalidInput({ $root: t('errors:mobilityApplication.inventoryNotFound') });
    }

    // Check if booking code is viewable
    const bookingCode = (() => {
        const foundItem = mobilityModule.bookingCode.overrides.find(item => item.dealerId.equals(inventory.dealerId));

        if (!foundItem && mobilityModule.bookingCode.viewable) {
            if (isNil(mobilityBookingDetails.bookingCode)) {
                throw new InvalidInput({ $root: t('errors:mobilityApplication.bookingCodeRequired') });
            }

            if (mobilityModule.bookingCode.defaultValue !== mobilityBookingDetails.bookingCode) {
                throw new InvalidInput({ $root: t('errors:mobilityApplication.bookingCodeInvalid') });
            }

            return mobilityBookingDetails.bookingCode;
        }

        if (foundItem?.viewable) {
            if (isNil(mobilityBookingDetails.bookingCode)) {
                throw new InvalidInput({ $root: t('errors:mobilityApplication.bookingCodeRequired') });
            }

            if (foundItem.value !== mobilityBookingDetails.bookingCode) {
                throw new InvalidInput({ $root: t('errors:mobilityApplication.bookingCodeInvalid') });
            }

            return mobilityBookingDetails.bookingCode;
        }

        return undefined;
    })();

    const stock: StockInventory =
        inventory?._kind === InventoryKind.MobilityInventory
            ? inventory.stocks.find(
                  ({ _kind, _id }) =>
                      _kind === StockInventoryKind.MobilityStock && _id.equals(mobilityBookingDetails.inventoryStockId)
              )
            : null;

    const location = getLocationFromInput(locationInput);

    return {
        vehicleId: vehicle._id,
        dealerId: inventory.dealerId,
        mobilityBookingDetails: {
            ...mobilityBookingDetails,
            inventoryStockPrice: (stock as MobilityStockInventory).price || vehicle.vehiclePrice,
            // I'm put the vehicle.vehiclePrice as alternative value in case the stock price is still empty
            bookingCode,
            location,
        },
        stocks: inventory.stocks,
    };
};

const resolver: GraphQLMutationResolvers['draftMobilityApplication'] = async (
    root,
    { moduleId, customer: customerInputs, mobilityDetails, mobilityBookingDetails, endpointId, languageId },
    { getUser, getTranslations, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);

    if (dayjs(mobilityBookingDetails.period.end).isSameOrBefore(dayjs(mobilityBookingDetails.period.start), 'minute')) {
        throw new InvalidInput({ $root: t('errors:mobilityApplication.periodInvalid') });
    }

    // get the module
    const applicationModule = await collections.modules.findOne({ _id: moduleId });

    if (!applicationModule || applicationModule._type !== ModuleType.MobilityModule) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a mobility application module' });
    }
    const { stocks, ...vehicleBookingDetails } = await validateVehicleBookingDetails(
        t,
        applicationModule,
        mobilityBookingDetails
    );

    const bookablePeriod = await validateBookingPeriod(
        t,
        collections,
        vehicleBookingDetails.vehicleId,
        applicationModule.durationBeforeNextBooking,
        mobilityBookingDetails.period,
        applicationModule.unavailableTimeRange,
        applicationModule.unavailableDayOfWeek,
        stocks
    );

    if (!bookablePeriod) {
        throw new Error('BookingPeriod is invalid');
    }

    const mobilitySnapshots = await validateMobilityDetails(t, loaders, mobilityDetails);

    const origins = endpointId ? await validateEndpoint(endpointId, applicationModule) : null;

    // determine authoring
    const user = await getUser(true);
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    const customerModule = await collections.modules.findOne({ _id: applicationModule.customerModuleId });
    // get/create the applicant (customer object)
    const applicant = await (async () => {
        if (customerModule._type === ModuleType.LocalCustomerManagement) {
            if (customerInputs.existingLocalCustomer) {
                if (authoring.kind === AuthorKind.Customer) {
                    // only authenticated user may draft with existing customers
                    throw new InvalidPermission();
                }

                // get the customer
                return collections.customers.findOne({
                    _id: customerInputs.existingLocalCustomer,
                    moduleId: customerModule._id,
                    // todo apply permissions to limit available customers
                    '_versioning.isLatest': true,
                });
            }

            if (customerInputs.newLocalCustomer) {
                const newCustomerId = new ObjectId();

                if (authoring.kind === AuthorKind.Customer) {
                    // update the authoring object to match the newly created customer ID
                    authoring.customerId = newCustomerId;
                }

                // create a new customer
                const customer: LocalCustomer = {
                    _id: newCustomerId,
                    _kind: CustomerKind.Local,
                    moduleId: customerModule._id,
                    kycPresetIds: [],
                    fields: convertLocalCustomerGraphQLFields(customerInputs.newLocalCustomer.fields),
                    isDeleted: false,
                    _versioning: getAdvancedVersioningForCreation(authoring),
                };

                // insert the customer in the database
                await collections.customers.insertOne(customer);

                return customer;
            }
        }

        return null;
    })();

    // initialize the application
    const assigneeId = (() => {
        if (mobilityBookingDetails.location.isHomeDelivery) {
            return applicationModule.homeDelivery.assigneeId;
        }

        const locationId = mobilityBookingDetails.location.pickup?.id;
        if (locationId) {
            const { locations } = applicationModule;

            return locations.find(location => location._id.equals(locationId))?.assigneeId;
        }

        return null;
    })();

    const lead: Lead = {
        _id: new ObjectId(),
        kind: ApplicationKind.Mobility,
        status: LeadStatus.Drafted,
        isLead: false,
        moduleId: applicationModule._id,
        vehicleId: vehicleBookingDetails.vehicleId,
        customerId: applicant._id,
        dealerId: vehicleBookingDetails.dealerId,
        isDraft: true,
        identifier: '',
        tradeInVehicle: [],
        documents: [],
        _versioning: getAdvancedVersioningForCreation(authoring),
        ...origins,
        languageId,
    };

    const application: MobilityApplication = {
        _id: new ObjectId(),
        stages: [ApplicationStage.Mobility],
        mobilityStage: {
            status: ApplicationStatus.Drafted,
            identifier: '',
            assigneeId,
        },
        moduleId: applicationModule._id,
        isDraft: true,
        remarks: '',
        _versioning: getAdvancedVersioningForCreation(authoring),

        kind: ApplicationKind.Mobility,
        // TODO merge with dealerID,
        dealerId: vehicleBookingDetails.dealerId,
        scenarios: applicationModule.scenarios,
        applicantId: applicant._id,
        documents: [],
        ...mobilitySnapshots,
        ...vehicleBookingDetails,
        ...origins,

        // all emails should set as false since the booking is not confirmed yet.
        bookingEmail: {
            bookingCheckIn: false,
            bookingComplete: false,
            bookingReminder: false,
            bookingConfirmation: false,
        },
        guarantorId: [],
        languageId,

        leadId: lead._id,
    };

    await collections.leads.insertOne(lead);
    await collections.applications.insertOne(application);

    // initialize the journey
    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: application._versioning.createdAt,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
    };
    await collections.applicationJourneys.insertOne(applicationJourney);

    // create a log for it
    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: applicant._versioning.createdAt,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    const leadTrail: LeadDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadDrafted,
        _date: applicant._versioning.createdAt,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    await collections.auditTrails.insertMany([trail, leadTrail]);

    return executeMobilityJourney({
        application,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });
};

export default buildRateLimiterMiddleware({ operation: 'draftMobilityApplication' })(resolver);
