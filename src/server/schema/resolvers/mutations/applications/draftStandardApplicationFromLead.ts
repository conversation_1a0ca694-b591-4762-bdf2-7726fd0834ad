import { ObjectId } from 'mongodb';
import {
    ApplicationDraftedAuditTrail,
    ApplicationJourney,
    ApplicationKind,
    ApplicationStatus,
    ApplicationStage,
    AuditTrailKind,
    AuthorKind,
    ModuleType,
    StandardApplication,
    VehicleKind,
    LeadDraftedAuditTrail,
    Lead,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    retrieveFeatureProps,
    getApplicationInitialStage,
    allowAddInsuranceStage,
    getInsuranceApplicationSetup,
} from '../../../../database/helpers/applications';
import { getVehiclePrice } from '../../../../database/helpers/vehicles';
import { executeLegacyJourney } from '../../../../journeys/legacyJourney';
import { ModulePolicyAction } from '../../../../permissions';
import { getApplicationLogStages } from '../../../../utils/application';
import {
    validateApplicationFinancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import { getDealershipPaymentSetting } from '../../../../utils/getDealershipPaymentSetting';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, getApplyForInsurance, validateEndpoint } from './helpers';

const resolver: GraphQLMutationResolvers['draftStandardApplicationFromLead'] = async (
    root,
    {
        moduleId,
        vehicle: vehicleInputs,
        configuration: configurationInput,
        tradeInVehicle,
        financing: financingInputs,
        insurancing: insurancingInputs,
        endpointId,
        dealerId,
        promoCodeId,
        languageId,
        leadId,
    },
    { getUser, getTranslations, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);
    const permissionController = await getPermissionController();

    // get the current user which might come in handy
    const user = await getUser(true);
    const loggedInUser = !!user;

    // get the module
    const applicationModule = await collections.modules.findOne({ _id: moduleId });

    // ensure the user may create on this application
    if (!permissionController.modules.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication)) {
        throw new InvalidPermission();
    }

    if (!applicationModule || applicationModule._type !== ModuleType.StandardApplicationModule) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a standard application module' });
    }

    // Get company details of application
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    // get the customer & vehicle modules
    const vehicleModule = await collections.modules.findOne({ _id: applicationModule.vehicleModuleId });

    const withFinancing = getApplyForFinancing(configurationInput, applicationModule);

    // validate the financing settings
    const { financeProduct, financing, isAffinBank } = await validateApplicationFinancing(
        applicationModule,
        financingInputs,
        t,
        dealerId,
        null,
        withFinancing
    );

    // get router/endpoint when provided
    const origins = endpointId ? await validateEndpoint(endpointId, applicationModule) : null;

    // build up versioning
    // customer ID will be defined later on if the kind is Customer
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    // get/create the vehicle
    const vehicle = await (async () => {
        if (vehicleModule._type === ModuleType.SimpleVehicleManagement) {
            if (vehicleInputs.existingSimpleLocalVehicleId) {
                return collections.vehicles.findOne({
                    _id: vehicleInputs.existingSimpleLocalVehicleId,
                    _kind: VehicleKind.LocalVariant,
                    moduleId: vehicleModule._id,
                    isActive: true,
                    isDeleted: false,
                    '_versioning.isLatest': true,
                });
            }
        }

        return null;
    })();

    if (!vehicle) {
        // something was wrong with the vehicle inputs
        throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
    }

    const promoCode = promoCodeId
        ? await getValidPromoCode(promoCodeId, moduleId, dealerId, vehicle._versioning.suiteId, t)
        : null;

    // Validation for AN-2652 : To validating incoming financing values
    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        // Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice: getVehiclePrice(vehicle),
            financing,
            includeDealerOptionsForFinancing: applicationModule.includeDealerOptionsForFinancing,
            promoCode,
            withFinancing,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    const existingLead = await collections.leads.findOne({ _id: leadId, '_versioning.isLatest': true });

    if (!existingLead) {
        throw new InvalidInput({ leadId: 'Lead not found' });
    }

    // Check if new version needed due to different vehicle
    let lead: Lead = existingLead;
    if (!existingLead.vehicleId.equals(vehicle._id)) {
        const newLead: Lead = {
            ...existingLead,
            _id: new ObjectId(),
            vehicleId: vehicle._id,
            _versioning: getAdvancedVersioningForCreation(authoring, existingLead._versioning.suiteId),
        };

        await collections.leads.updateOne({ _id: existingLead._id }, { $set: { '_versioning.isLatest': false } });
        await collections.leads.insertOne(newLead);

        lead = newLead;
    }

    const applicant = await collections.customers.findOne({
        _id: lead.customerId,
    });

    if (!applicant) {
        throw new InvalidInput({ customerId: 'Customer not found' });
    }

    const dealer = await collections.dealers.findOne({ _id: dealerId });
    const paymentSetting = await getDealershipPaymentSetting(dealer._id, applicationModule, { collections });

    // we first need to create the customer document
    const stage = getApplicationInitialStage({
        dealer,
        paymentSetting,
        scenarios: applicationModule.scenarios,
        withFinancing,
        isFinancingOptional: applicationModule.isFinancingOptional,
        withInsurance: configurationInput.withInsurance,
        isInsuranceOptional: applicationModule.isInsuranceOptional,
        testDrive: configurationInput.testDrive,
    });

    // Check if there is insurance
    const withInsurance = getApplyForInsurance(configurationInput, applicationModule);

    // Validate insurance inputs first before drafting any application
    const { insurancing } = await getInsuranceApplicationSetup({
        applicationModule,
        insurancingInputs,
        t,
        withInsurance,
    });

    const assigneeId = user?._id;
    const needInsuranceStage = allowAddInsuranceStage(
        stage,
        applicationModule.scenarios,
        configurationInput.withInsurance,
        applicationModule.isInsuranceOptional
    );

    const configuration = {
        ...configurationInput,
        withFinancing,
        withInsurance,
        isAffinAutoFinanceCentreRequired: withFinancing && isAffinBank,
    };

    const application: StandardApplication = {
        _id: new ObjectId(),
        kind: ApplicationKind.Standard,
        moduleId: applicationModule._id,
        scenarios: applicationModule.scenarios,
        stages: [stage, needInsuranceStage && ApplicationStage.Insurance].filter(Boolean),
        ...getApplicationInitialStageDetails(
            stage,
            ApplicationStatus.Drafted,
            assigneeId,
            await retrieveFeatureProps(applicationModule._id)
        ),
        ...(needInsuranceStage &&
            getApplicationInitialStageDetails(ApplicationStage.Insurance, ApplicationStatus.Drafted, assigneeId, null)),

        // the application should be treated as a draft if there's no remote link sent
        isDraft: true,

        // define relationships
        bankId: financeProduct?.bankId,
        applicantId: applicant._id,
        vehicleId: vehicle._id,
        dealerId,

        // push financing and application configurations
        financing,

        // insurancing too, if it's enabled
        insurancing,

        configuration,
        tradeInVehicle,
        // start with empty remarks and no document
        remarks: '',
        documents: [],
        // spread origins
        ...origins,

        // provide initial versioning
        _versioning: getAdvancedVersioningForCreation(authoring),
        promoCodeId: promoCode?._id,
        withCustomerDevice: false,
        referenceApplicationSuiteIds: [],
        languageId,
        guarantorId: [],
        useMyinfo: {
            customer: false,
            guarantor: false,
        },

        leadId: lead._id,
    };

    // build up application journey
    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: applicant._versioning.createdAt,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
    };

    // insert the documents in database
    await collections.applications.insertOne(application);
    await collections.applicationJourneys.insertOne(applicationJourney);

    // generate the trail
    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: applicant._versioning.createdAt,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    const leadTrail: LeadDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadDrafted,
        _date: applicant._versioning.createdAt,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    // register the trail
    await collections.auditTrails.insertMany([trail, leadTrail]);

    // finally execute the journey
    return executeLegacyJourney({
        application,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });
};

export default buildRateLimiterMiddleware({ operation: 'draftStandardApplicationFromLead' })(resolver);
