import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../../../../core/config';
import {
    ApplicationJourney,
    ApplicationKind,
    ApplicationProceedWithCustomerDeviceAuditTrail,
    AuditTrailKind,
    AuthorKind,
    ConfiguratorApplicationLink,
    ConfiguratorKind,
    Customer,
    EventApplicationLink,
    ExternalLinkKind,
    FinderApplicationLink,
    ProceedWithCustomerLink,
    StandardApplicationLink,
} from '../../../../../database';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { getCustomerEmail } from '../../../../../database/helpers/customers';
import { ExecuteJourneyData } from '../../../../../journeys/legacyJourney/executeJourney';
import { Loaders } from '../../../../../loaders';
import { mainQueue } from '../../../../../queues';
import { getApplicationLogStages } from '../../../../../utils/application';
import createI18nInstance from '../../../../../utils/createI18nInstance';
import { Authoring, getAuthorFromAuthoring } from '../../../../../utils/versioning';

const sendEmail = async (
    latestJourney: ApplicationJourney,
    result: ExecuteJourneyData,
    isApplyingForFinancingOrInsurance: boolean,
    withRedirectionLink: boolean,
    newCustomer: Customer,
    userId: ObjectId,
    loaders: Loaders
) => {
    const { collections } = await getDatabaseContext();

    const { i18n } = await createI18nInstance(result.application?.languageId?.toHexString());
    await i18n.loadNamespaces('common');
    const { t } = i18n;

    if (
        !latestJourney.isReceived &&
        result.application.kind !== ApplicationKind.Mobility &&
        result.application.kind !== ApplicationKind.Launchpad &&
        result.application.kind !== ApplicationKind.SalesOffer &&
        (result.application.withCustomerDevice || withRedirectionLink)
    ) {
        if (result.application.withCustomerDevice) {
            // delete previous links
            await collections.externalLinks.deleteMany({
                _kind: ExternalLinkKind.ProceedWithCustomer,
                'data.applicationSuiteId': result.application._versioning.suiteId,
            });
        }

        const externalLink = await generateExternalLink(result.application);

        await collections.externalLinks.insertOne(externalLink);

        if (!isApplyingForFinancingOrInsurance && result.application.withCustomerDevice) {
            await mainQueue.add({
                type: 'sendProceedWithCustomerEmail',
                linkId: externalLink._id,
                customerEmail: getCustomerEmail(t, newCustomer),
            });

            // create audit trail
            const authoring: Authoring = { kind: AuthorKind.User, userId };

            const trail: ApplicationProceedWithCustomerDeviceAuditTrail = {
                _id: new ObjectId(),
                _kind: AuditTrailKind.ApplicationProceedWithCustomerDevice,
                _date: new Date(),
                applicationId: result.application._id,
                applicationSuiteId: result.application._versioning.suiteId,
                stages: getApplicationLogStages(
                    result.application,
                    AuditTrailKind.ApplicationProceedWithCustomerDevice
                ),
                author: getAuthorFromAuthoring(authoring),
            };

            await collections.auditTrails.insertOne(trail);

            // redirection happen on customer's device
            // no `redirectionLink` hence redirection would not happen in details page
            return result;
        }

        const router = await loaders.routerById.load(result.application.routerId);

        const resumeUrl = urljoin(
            `${config.protocol}://${router.hostname}`,
            router.pathname,
            `l/${externalLink.secret}`
        );

        return { ...result, redirectionLink: resumeUrl };
    }

    return result;
};

export const generateExternalLink = async (application: ExecuteJourneyData['application']) => {
    const params = {
        _id: new ObjectId(),
        secret: nanoid(),
        expiresAt: dayjs().add(3, 'days').toDate(),
        data: {
            applicationId: application._id,
            routerId: application.routerId,
            endpointId: application.endpointId,
            ...(application.kind !== ApplicationKind.Mobility &&
                application.kind !== ApplicationKind.Launchpad &&
                application.kind !== ApplicationKind.SalesOffer &&
                application.withCustomerDevice && { applicationSuiteId: application._versioning.suiteId }),
        },
        deleteOnFetch: false,
    };

    switch (application.kind) {
        case ApplicationKind.Standard: {
            if (application.withCustomerDevice) {
                return {
                    ...params,
                    _kind: ExternalLinkKind.ProceedWithCustomer,
                } as ProceedWithCustomerLink;
            }

            return {
                ...params,
                _kind: ExternalLinkKind.StandardApplication,
            } as StandardApplicationLink;
        }

        case ApplicationKind.Event: {
            if (application.withCustomerDevice) {
                return {
                    ...params,
                    _kind: ExternalLinkKind.ProceedWithCustomer,
                } as ProceedWithCustomerLink;
            }

            return {
                ...params,
                _kind: ExternalLinkKind.EventApplication,
            } as EventApplicationLink;
        }

        case ApplicationKind.Finder: {
            if (application.withCustomerDevice) {
                return {
                    ...params,
                    _kind: ExternalLinkKind.ProceedWithCustomer,
                } as ProceedWithCustomerLink;
            }

            return {
                ...params,
                _kind: ExternalLinkKind.FinderApplication,
            } as FinderApplicationLink;
        }

        case ApplicationKind.Configurator: {
            const { collections } = await getDatabaseContext();
            const variantConfigurator = await collections.configurators.findOne({
                _id: application.configuratorId,
                _kind: ConfiguratorKind.Variant,
            });

            if (!variantConfigurator || variantConfigurator._kind !== ConfiguratorKind.Variant) {
                throw new Error('Invalid Variant Configurator');
            }

            if (application.withCustomerDevice) {
                return {
                    ...params,
                    _kind: ExternalLinkKind.ProceedWithCustomer,
                    data: {
                        ...params.data,
                        variantConfiguratorId: variantConfigurator._id,
                        modelConfiguratorId: variantConfigurator.modelConfiguratorId,
                    },
                } as ProceedWithCustomerLink;
            }

            return {
                ...params,
                _kind: ExternalLinkKind.ConfiguratorApplication,
                data: {
                    ...params.data,
                    applicationSuiteId: application._versioning.suiteId,
                    variantConfiguratorId: variantConfigurator._id,
                    modelConfiguratorId: variantConfigurator.modelConfiguratorId,
                },
            } as ConfiguratorApplicationLink;
        }

        default:
            throw new Error('Application kind not supported');
    }
};

export default sendEmail;
