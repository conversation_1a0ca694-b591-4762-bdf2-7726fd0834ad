import { difference, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationApplyForFinanceCreation,
    ApplicationApplyForInsuranceCreationAuditTrail,
    ApplicationJourney,
    ApplicationJourneyAgreements,
    ApplicationKind,
    AuthorKind,
    getKYCPresetsForApplication,
} from '../../../../../database';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { isApplicationWithInsurance } from '../../../../../database/helpers/applications';
import getInitialAgreementsForApplication from '../../../../../database/helpers/getInitialAgreementsForApplication';
import { createNewLeadVersion } from '../../../../../database/helpers/leads';
import { ApplicantKYCStep } from '../../../../../journeys/common';
import { hasPaymentScenario } from '../../../../../journeys/common/helpers';
import { executeJourney } from '../../../../../journeys/legacyJourney';
import { ApplicationPolicyAction } from '../../../../../permissions';
import { getApplicationLogStages } from '../../../../../utils/application';
import { getAdvancedVersioningByUserForCreation, getAuthorFromAuthoring } from '../../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../../errors';
import { requiresLoggedUser } from '../../../../middlewares';
import {
    ApplicationJourneySigningMode,
    ApplicationStage,
    AuditTrailKind,
    CustomerKind,
    GraphQLMutationResolvers,
} from '../../../definitions';
import amendmentAuditTrail from './amendmentAuditTrail';
import checkFinancingSubmissionApproval from './checkFinancingSubmissionApproval';
import reinitializeLatestStep from './reinitializeLatestStep';
import sendEmail from './sendEmail';
import getUpdateHandlers from './updateHandlers/getUpdateHandlers';
import { isValidApplicationModuleForSubmitChange } from './updateHandlers/shared';
import { AmendableApplication } from './updateHandlers/types';

const mutate: GraphQLMutationResolvers['submitChanges'] = async (
    root,
    { applicationId, updates, stage, withRedirectionLink, remarks, commentsToInsurer },
    { getUser, loaders, getPermissionController, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);
    const permissionController = await getPermissionController();

    const applicationFilter = await permissionController.applications.getFilterQueryForAction(
        ApplicationPolicyAction.Update,
        [stage]
    );

    const application = await collections.applications.findOne({ $and: [{ _id: applicationId }, applicationFilter] });

    if (!application || application.kind === ApplicationKind.SalesOffer) {
        throw new InvalidPermission();
    }

    // fetch the application journey
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });

    if (!journey || journey.isImmutable) {
        throw new InvalidInput({ $root: 'Invalid application journey' });
    }

    let lead = await collections.leads.findOne({ _id: application.leadId });

    if (!lead) {
        throw new InvalidInput({ $root: 'Invalid lead' });
    }

    const module = await loaders.moduleById.load(application.moduleId);

    if (!isValidApplicationModuleForSubmitChange(module)) {
        throw new InvalidInput({ $root: 'Invalid application module' });
    }

    // get the user from the GraphQl context
    const user = await getUser(true);

    const handlers = await getUpdateHandlers(updates, application, module, loaders, t, permissionController);

    const areUpdatesValid = await Promise.all(handlers.map(handler => handler.isValid(application))).then(checks =>
        checks.every(value => value === true)
    );

    if (!areUpdatesValid) {
        throw new InvalidInput({ $root: 'Invalid updates' });
    }

    // apply updates and get final application documents to insert
    const updatedBy = { kind: AuthorKind.User, id: user._id };

    const commentsToInsurerUpdate =
        commentsToInsurer && isApplicationWithInsurance(application) ? { commentsToInsurer } : null;

    const nextApplication = await handlers.reduce(
        (acc, handler) => acc.then(patchedApplication => handler.apply(patchedApplication, user)),
        Promise.resolve<AmendableApplication>({
            _id: new ObjectId(),
            ...application,
            _versioning: {
                ...application._versioning,
                updatedBy,
                updatedAt: new Date(),
            },
            ...(application.kind !== ApplicationKind.Mobility && { withCustomerDevice: false }),
            ...commentsToInsurerUpdate,
            remarks,
        })
    );

    // enforce KYC to be provided/confirmed again when moving to financing
    const isApplyingForFinancing =
        application.kind !== ApplicationKind.Event &&
        application.kind !== ApplicationKind.Mobility &&
        application.kind !== ApplicationKind.Launchpad &&
        !application.configuration.withFinancing &&
        nextApplication.kind !== ApplicationKind.Event &&
        nextApplication.kind !== ApplicationKind.Mobility &&
        nextApplication.configuration.withFinancing;

    const isApplyingForInsurance =
        application.kind !== ApplicationKind.Event &&
        application.kind !== ApplicationKind.Mobility &&
        application.kind !== ApplicationKind.Launchpad &&
        !application.configuration.withInsurance &&
        nextApplication.kind !== ApplicationKind.Event &&
        nextApplication.kind !== ApplicationKind.Mobility &&
        nextApplication.configuration.withInsurance;

    if (isApplyingForFinancing || isApplyingForInsurance) {
        const newVersioning = getAdvancedVersioningByUserForCreation(user._id);

        nextApplication._versioning = newVersioning;

        // Make it invisible from application listing
        nextApplication.isDraft = true;
    } else {
        // update previous application document
        await collections.applications.findOneAndUpdate(
            { _id: applicationId },
            { $set: { '_versioning.isLatest': false } }
        );
    }

    // the newer application id is not updated in the reducing method
    nextApplication._id = new ObjectId();

    // insert new lead document
    // only update lead if customer changes
    if (!nextApplication.applicantId.equals(application.applicantId)) {
        const latestLead = await collections.leads.findOne({
            '_versioning.suiteId': lead._versioning.suiteId,
            '_versioning.isLatest': true,
        });
        const newLead = {
            ...latestLead,
            _id: new ObjectId(),
            customerId: nextApplication.applicantId,
            _versioning: {
                ...latestLead._versioning,
                updatedBy,
                updatedAt: new Date(),
            },
        };

        await createNewLeadVersion(newLead);

        lead = newLead;
        nextApplication.leadId = newLead._id;
    }

    // insert new application document
    await collections.applications.insertOne(nextApplication);

    // reset the received flag
    journey.isReceived = false;

    // set the resubmission stage
    journey.resubmission = {
        // temporarily filter out the visit appointment stage until we do the resubmission for it
        stage: stage !== ApplicationStage.VisitAppointment && stage !== ApplicationStage.TradeIn && stage,
        author: updatedBy,
    };

    await amendmentAuditTrail(application, nextApplication, { collections, user, stage, loaders });

    // fetch the latest LocalCustomer
    const newCustomer = await collections.customers.findOne({ _id: nextApplication.applicantId });

    const isResubmit = !isNil(journey.submission);

    // fetch the latest bank setting if exist from financing stage
    const needFinancingApproval = await checkFinancingSubmissionApproval(nextApplication, isResubmit);

    // set it back to false so if we need signing it will be enforced
    // Doesn't mean that when applying for insurance
    if (isResubmit && !isApplyingForInsurance && journey.applicantSigning?.completed) {
        journey.applicantSigning.completed = false;

        if (journey.applicantSigning.kind === ApplicationJourneySigningMode.Namirial) {
            journey.applicantSigning.envelopeId = null;
            journey.applicantSigning.redirectionUrl = null;
        }
    }

    // check for missing KYCs
    if (journey.applicantKYC) {
        const customerModule = await loaders.customerModuleById.load(module.customerModuleId);
        const kycPresets = await getKYCPresetsForApplication(application, 'local');
        if (
            !(await ApplicantKYCStep.validateInputFields(
                customerModule.kycFields.sort((a, b) => a.order - b.order),
                kycPresets,
                newCustomer,
                nextApplication
            ))
        ) {
            journey.applicantKYC.completed = false;
        }
    }

    if (isApplyingForFinancing || isApplyingForInsurance) {
        const allAgreementConsentHexStringIds = journey.applicantAgreements.agreements.map(item =>
            item.consentId.toHexString()
        );

        // Get checked consent ids
        const checkedConsentIds = journey.applicantAgreements.agreements
            .filter(item => item.isAgreed)
            .map(item => item.consentId);

        // Generate new agreements
        const newApplicantAgreements = await getInitialAgreementsForApplication(nextApplication, 'local');
        const newApplicantAgreementHexStringIds = newApplicantAgreements.map(item => item.consentId.toHexString());

        // Compare in hex string format
        const hasNewAgreements = difference(newApplicantAgreementHexStringIds, allAgreementConsentHexStringIds);
        if (hasNewAgreements && journey.applicantKYC) {
            journey.applicantKYC.completed = false;
        }

        // Build up new agreements, check it if previously was checked
        const newJourneyAgreements = newApplicantAgreements.map(agreement => ({
            ...agreement,
            isAgreed: checkedConsentIds.some(item => item.equals(agreement.consentId)),
        }));

        const newJourneyApplicantAgreements: ApplicationJourneyAgreements = {
            moduleId: journey.applicantAgreements.moduleId,
            agreements: newJourneyAgreements,
        };

        const nextCustomer = await collections.customers.findOne({ _id: nextApplication.applicantId });

        // build up application journey
        const nextApplicationJourney: ApplicationJourney = {
            _id: new ObjectId(),
            applicationSuiteId: nextApplication._versioning.suiteId,
            updatedAt: nextApplication._versioning.createdAt,
            applicantKYC: journey.applicantKYC,
            isReceived: false,
            isImmutable: false,
            isCorporateCustomer: nextCustomer._kind === CustomerKind.Corporate,

            // Copy necessary data from previous application journey
            deposit: {
                ...journey.deposit,
                skipped: hasPaymentScenario(application.scenarios),
            },
            applicantSigning: journey.applicantSigning,
            applicantAppointment: journey.applicantAppointment,
            applicantAgreements: newJourneyApplicantAgreements,
        };

        if (isApplyingForFinancing) {
            nextApplicationJourney.applyingForFinance = {
                referenceSuiteId: application._versioning.suiteId,
                completed: false,
            };
        }

        if (isApplyingForInsurance) {
            nextApplicationJourney.applyingForInsurance = {
                referenceSuiteId: application._versioning.suiteId,
                completed: false,
            };
        }

        await collections.applicationJourneys.insertOne(nextApplicationJourney);

        if (nextApplicationJourney.applyingForFinance) {
            // update audit trail for the conversion finance
            const applyFinanceTrail: ApplicationApplyForFinanceCreation = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.ApplicationApplyForFinanceCreation,
                applicationId: nextApplication._id,
                applicationSuiteId: nextApplication._versioning.suiteId,
                stages: getApplicationLogStages(nextApplication, AuditTrailKind.ApplicationApplyForFinanceCreation),
                fromStage: stage,
            };
            await collections.auditTrails.insertOne(applyFinanceTrail);
        }

        if (nextApplicationJourney.applyingForInsurance) {
            // update audit trail for the conversion insurance
            const applyForInsuranceTrail: ApplicationApplyForInsuranceCreationAuditTrail = {
                _id: new ObjectId(),
                _date: new Date(),
                _kind: AuditTrailKind.ApplicationApplyForInsuranceCreation,
                applicationId: nextApplication._id,
                applicationSuiteId: nextApplication._versioning.suiteId,
                stages: getApplicationLogStages(nextApplication, AuditTrailKind.ApplicationApplyForInsuranceCreation),
                author: getAuthorFromAuthoring({ kind: AuthorKind.User, userId: user._id, date: new Date() }),
                fromStage: stage,
            };
            await collections.auditTrails.insertOne(applyForInsuranceTrail);
        }

        // identifiers would be assigned in drafting step
        const result = await executeJourney({
            application: nextApplication,
            lead,
            identifier: 'drafting',
            payload: null,
            user,
            origin: 'system',
            skipCompletedSteps: true,
            resubmitStage: stage,
        });

        return sendEmail(
            nextApplicationJourney,
            result,
            isApplyingForFinancing || isApplyingForInsurance,
            withRedirectionLink || needFinancingApproval,
            newCustomer,
            user._id,
            loaders
        );
    }

    // update journey in database
    await collections.applicationJourneys.findOneAndUpdate(
        { applicationSuiteId: journey.applicationSuiteId },
        { $set: journey },
        { returnDocument: 'after' }
    );

    const result = await executeJourney({
        application: nextApplication,
        lead,
        identifier: 'drafting',
        payload: null,
        user,
        origin: 'system',
        skipCompletedSteps: true,
        resubmitStage: stage,
    });

    const latestJourney = await collections.applicationJourneys.findOne({
        applicationSuiteId: journey.applicationSuiteId,
    });

    if (!withRedirectionLink && !needFinancingApproval) {
        await reinitializeLatestStep(result.application, latestJourney, user, 'system', stage);
    }

    return sendEmail(
        latestJourney,
        result,
        isApplyingForFinancing,
        withRedirectionLink || needFinancingApproval,
        newCustomer,
        user._id,
        loaders
    );
};

export default requiresLoggedUser(mutate);
