import { TFunction } from 'i18next';
import { ObjectId } from 'mongodb';
import { Application, LocalCustomerField, MaskDirection, Module } from '../../../../../../database';
import {
    computeCustomerChanges,
    getMaskedCustomerInfo,
    convertLocalCustomerGraphQLFields,
    mergeLocalCustomerFields,
} from '../../../../../../database/helpers/customers';
import { Loaders } from '../../../../../../loaders';
import { ApplicationPolicyAction, PermissionController } from '../../../../../../permissions';
import { ApplicationKind, GraphQLApplicationUpdate, GraphQLLocalCustomerFieldSettings } from '../../../../definitions';
import AppointmentDetailsUpdate from './AppointmentDetailsUpdate';
import ConfigurationUpdate from './ConfigurationUpdate';
import FinancingUpdate from './FinancingUpdate';
import InsuranceUpdate from './InsuranceUpdate';
import LocalApplicantUpdate from './LocalApplicantUpdate';
import MobilityBookingAmendment from './MobilityBookingAmendment';
import OtherVehicleInformationUpdate from './OtherVehicleInformationUpdate';
import QuotationDetailsUpdate from './QuotationDetailsUpdate';
import VisitAppointmentDetailsUpdate from './VisitAppointmentDetailsUpdate';
import { UpdateHandler } from './types';

export const calculateCustomerChanges = async (
    customerFields: GraphQLLocalCustomerFieldSettings[],
    companyId: ObjectId,
    loaders: Loaders,
    applicantId: ObjectId
): Promise<LocalCustomerField[]> => {
    const newFields = convertLocalCustomerGraphQLFields(customerFields);
    const [customer, company] = await Promise.all([
        loaders.customerById.load(applicantId),
        loaders.companyById.load(companyId),
    ]);
    const mergedFields = mergeLocalCustomerFields(customer.fields, newFields);
    const maskSetting = company?.mask;

    if (maskSetting.direction !== MaskDirection.None) {
        const customerDataFields = customer.fields.map(field =>
            getMaskedCustomerInfo(field, company.mask.count, company.mask.direction)
        );

        return computeCustomerChanges(customerDataFields, mergedFields, customer);
    }

    return newFields;
};

const getUpdateHandlers = (
    updates: GraphQLApplicationUpdate[],
    application: Application,
    module: Module,
    loaders: Loaders,
    t: TFunction,
    permissionController: PermissionController
) =>
    Promise.all(
        updates.map(async update => {
            let handler: UpdateHandler | null = null;
            // allow user to recalculate
            const hasRecalculatePermission = await permissionController.applications.mayOperateOn(
                application,
                ApplicationPolicyAction.RecalculateView
            );

            // Update financing happened when recalculate, or applying for financing
            if (update.updateFinancing && (hasRecalculatePermission || update.configuration?.withFinancing)) {
                handler = new FinancingUpdate(update.updateFinancing, loaders, t);
            } else if (update.updateInsurance) {
                handler = new InsuranceUpdate(update.updateInsurance, loaders, t);
            } else if (update.updateLocalCustomer) {
                // we first calculate customer changes
                const computedCustomerFields = await calculateCustomerChanges(
                    update.updateLocalCustomer,
                    module.companyId,
                    loaders,
                    application.applicantId
                );

                handler = new LocalApplicantUpdate(computedCustomerFields, loaders);
            } else if (update.configuration) {
                handler = new ConfigurationUpdate(update.configuration, loaders);
            } else if (update.otherVehicleInformation) {
                handler = new OtherVehicleInformationUpdate(update.otherVehicleInformation, loaders);
            } else if (update.updateMobilityBooking && application.kind === ApplicationKind.Mobility) {
                handler = new MobilityBookingAmendment(
                    update.updateMobilityBooking.location,
                    update.updateMobilityBooking.period,
                    update.updateMobilityBooking.userTimezone,
                    loaders,
                    t,
                    application
                );
            } else if (update.updateAppointmentDetails) {
                handler = new AppointmentDetailsUpdate(update.updateAppointmentDetails, loaders);
            } else if (update.updateVisitAppointmentDetails) {
                handler = new VisitAppointmentDetailsUpdate(update.updateVisitAppointmentDetails, loaders);
            } else if (update.updateQuotationDetails) {
                handler = new QuotationDetailsUpdate(update.updateQuotationDetails, loaders);
            }
            if (handler && (await handler.isEmpty(application))) {
                return null;
            }

            return handler;
        })
    ).then(items => items.filter(item => item !== null));

export default getUpdateHandlers;
