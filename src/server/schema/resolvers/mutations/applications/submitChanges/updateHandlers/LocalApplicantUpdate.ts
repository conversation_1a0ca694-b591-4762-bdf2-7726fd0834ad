import isEqual from 'fast-deep-equal';
import { ObjectId } from 'mongodb';
import { Customer, LocalCustomerField } from '../../../../../../database';
import { createNewCustomerVersion, mergeLocalCustomerFields } from '../../../../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../../../../database/helpers/customers/shared';
import { Loaders } from '../../../../../../loaders';
import { UpdateHandler } from './types';

class LocalApplicantUpdate implements UpdateHandler {
    private payload: LocalCustomerField[];

    private customer: Customer | null;

    private loaders: Loaders;

    private mergedFields: LocalCustomerField[] | null;

    constructor(update: LocalCustomerField[], loaders: Loaders) {
        this.payload = update;
        this.customer = null;
        this.loaders = loaders;
        this.mergedFields = null;
    }

    async isEmpty(application) {
        const customer = await this.loaders.customerById.load(application.applicantId);
        // const newFields = convertLocalCustomerGraphQLFields(this.payload);
        const mergedFields = mergeLocalCustomerFields(customer.fields, this.payload);

        // persist merged fields and customer document
        this.customer = customer;
        this.mergedFields = mergedFields;

        return isEqual(getLocalCustomerAggregatedFields(customer), mergedFields);
    }

    async isValid(application) {
        return true;
    }

    async apply(application) {
        if (!this.customer || !this.mergedFields) {
            throw new Error('Unexpected error when applying update');
        }

        // prepare new customer document
        const newCustomer: Customer = {
            ...this.customer,
            _id: new ObjectId(),
            fields: this.mergedFields,
            _versioning: {
                ...this.customer._versioning,
                isLatest: true,
            },
        };

        // insert it in database
        await createNewCustomerVersion(newCustomer);

        // apply the update on application document
        return { ...application, applicantId: newCustomer._id };
    }
}

export default LocalApplicantUpdate;
