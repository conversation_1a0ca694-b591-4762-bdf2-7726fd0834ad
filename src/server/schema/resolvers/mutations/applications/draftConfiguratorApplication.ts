import dayjs from 'dayjs';
import { isEmpty, isNil, pick } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import {
    ApplicationComboOption,
    ApplicationConfiguratorBlock,
    ApplicationDraftedAuditTrail,
    ApplicationJourney,
    ApplicationKind,
    ApplicationOptionSettings,
    ApplicationStage,
    AuditTrailKind,
    AuthorKind,
    BlockType,
    ColorAndTrimSettings,
    ComboOptionCore,
    ComboType,
    ConfiguratorApplication,
    ConfiguratorApplicationLink,
    ConfiguratorKind,
    CustomerKind,
    ExternalLinkKind,
    LocalCustomer,
    LocalCustomerFieldKey,
    Option,
    OptionKind,
    PackageSettings,
    Vehicle,
    VehicleKind,
    ApplicationStatus,
    FinancingPreferenceValue,
    Collections,
    Lead,
    LeadStatus,
    LeadDraftedAuditTrail,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    retrieveFeatureProps,
    getApplicationInitialStage,
    getInsuranceApplicationSetup,
} from '../../../../database/helpers/applications';
import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import { getVehiclePrice } from '../../../../database/helpers/vehicles';
import { executeConfiguratorJourney } from '../../../../journeys/configuratorJourney';
import { mainQueue } from '../../../../queues';
import { getApplicationLogStages } from '../../../../utils/application';
import {
    validateApplicationFinancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import { getDealershipPaymentSetting } from '../../../../utils/getDealershipPaymentSetting';
import getDealershipSettingId from '../../../../utils/getDealershipSettingId';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import {
    GraphQLConfiguratorBlockPayload,
    GraphQLMutationResolvers,
    ModuleType,
    ReservationStockStatus,
    StockInventoryKind,
} from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, getApplyForInsurance, validateEndpoint } from './helpers';

export const configuratorStockValidation = async (
    configuratorBlocksInput: ApplicationConfiguratorBlock[],
    dealerId: ObjectId,
    configuratorId: ObjectId,
    collections: Collections
) => {
    const filter = getConfiguratorInventoryFilter(configuratorBlocksInput);
    const variantStock = await collections.inventories.findOne({
        ...filter,
        dealerId,
        configuratorId,
        isDeleted: false,
        isActive: true,
    });

    if (!variantStock) {
        throw new InvalidInput({
            $emptyInventory: { message: 'No Inventory for this Variant' },
        });
    }

    // find any availability of stock
    const availableStock = variantStock.stocks.find(
        stock =>
            stock._kind === StockInventoryKind.ConfiguratorStock &&
            ((stock.reservationExpiryDate && dayjs(stock.reservationExpiryDate).isAfter(dayjs())) ||
                (isEmpty(stock.applicationId) && stock.reservationStatus === ReservationStockStatus.Available))
    );

    if (!availableStock) {
        throw new InvalidInput({
            $emptyInventory: { message: 'No available stock for this variant' },
        });
    }
};

export const getConfiguratorInventoryFilter = (configuratorBlocks: ApplicationConfiguratorBlock[]) =>
    !isNil(configuratorBlocks.find(block => block._type === BlockType.Package))
        ? {
              'colorSetting._id': (
                  configuratorBlocks.find(block => block._type === BlockType.Color).value as Omit<
                      ColorAndTrimSettings,
                      'image'
                  >
              )._id,
              'trimSetting._id': (
                  configuratorBlocks.find(block => block._type === BlockType.Trim).value as Omit<
                      ColorAndTrimSettings,
                      'image'
                  >
              )._id,
              'packageSetting._id': (
                  configuratorBlocks.find(block => block._type === BlockType.Package).value as Omit<
                      PackageSettings,
                      'sectionImage'
                  >
              )._id,
          }
        : {
              'colorSetting._id': (
                  configuratorBlocks.find(block => block._type === BlockType.Color).value as Omit<
                      ColorAndTrimSettings,
                      'image'
                  >
              )._id,
              'trimSetting._id': (
                  configuratorBlocks.find(block => block._type === BlockType.Trim).value as Omit<
                      ColorAndTrimSettings,
                      'image'
                  >
              )._id,
          };
type ReducedBlocks = {
    colors: ColorAndTrimSettings[];
    trims: ColorAndTrimSettings[];
    packages: PackageSettings[];
    options: ApplicationOptionSettings[];
};

export const getConfiguratorBlocks = async (
    configuratorId: ObjectId,
    configuratorBlocks: GraphQLConfiguratorBlockPayload[]
): Promise<ApplicationConfiguratorBlock[]> => {
    const { collections } = await getDatabaseContext();

    // return empty if there are no blocks selected
    if (configuratorBlocks.length === 0) {
        return [];
    }

    const variantConfigurator = await collections.configurators.findOne({ _id: configuratorId });

    // validate variant configurator
    if (!variantConfigurator || variantConfigurator._kind !== ConfiguratorKind.Variant) {
        throw new InvalidInput({ eventId: 'Configurator Variant is not valid' });
    }

    // map block options
    const blocks = variantConfigurator.blocks.reduce(
        (previous: ReducedBlocks, current) => {
            switch (current._type) {
                case BlockType.Color:
                    return { ...previous, colors: [...current.colorSettings, ...previous.colors] };

                case BlockType.Options:
                    return { ...previous, options: [...current.optionSettings.options, ...previous.options] };

                case BlockType.Package:
                    return { ...previous, packages: [...current.packageSettings, ...previous.packages] };

                case BlockType.Trim:
                    return { ...previous, trims: [...current.trimSettings, ...previous.trims] };

                default:
                    return current;
            }
        },
        {
            colors: [],
            options: [],
            packages: [],
            trims: [],
        }
    );

    // build application blocks
    return configuratorBlocks.map(applicationConfiguratorBlock => {
        const configuratorBlock = variantConfigurator.blocks.find(({ _id }) =>
            applicationConfiguratorBlock.blockId.equals(_id)
        );
        switch (configuratorBlock._type) {
            case BlockType.Color: {
                const [id] = applicationConfiguratorBlock.ids;

                const color = blocks.colors.find(({ _id }) => _id.equals(id));

                if (!color) {
                    throw new Error('Block color does not exist');
                }

                const { image, ...value } = color;

                return { _type: BlockType.Color, value, blockId: configuratorBlock._id };
            }

            case BlockType.Options: {
                switch (configuratorBlock.optionSettings._kind) {
                    case OptionKind.Dropdown: {
                        const options = applicationConfiguratorBlock.ids.map(id =>
                            (configuratorBlock.optionSettings.options as Option[]).find(({ _id }) => _id.equals(id))
                        );

                        return {
                            _type: BlockType.Options,
                            value: {
                                _kind: OptionKind.Dropdown,
                                options,
                            },
                            blockId: configuratorBlock._id,
                        };
                    }

                    case OptionKind.MultiSelect: {
                        const options = configuratorBlock.optionSettings.options.filter(({ _id }) =>
                            applicationConfiguratorBlock.ids.some(id => id.equals(_id))
                        );

                        return {
                            _type: BlockType.Options,
                            value: {
                                _kind: OptionKind.MultiSelect,
                                options,
                            },
                            blockId: configuratorBlock._id,
                        };
                    }

                    case OptionKind.SingleSelect: {
                        const options = applicationConfiguratorBlock.ids.map(id =>
                            (configuratorBlock.optionSettings.options as Option[]).find(({ _id }) => _id.equals(id))
                        );

                        return {
                            _type: BlockType.Options,
                            value: {
                                _kind: OptionKind.SingleSelect,
                                options,
                            },
                            blockId: configuratorBlock._id,
                        };
                    }

                    case OptionKind.Combo: {
                        const options: ApplicationComboOption[] = applicationConfiguratorBlock.combo
                            .map(({ id, value }) => {
                                const option = (
                                    configuratorBlock.optionSettings.options as ComboOptionCore<ComboType>[]
                                ).find(({ _id }) => _id.equals(id));

                                if (option) {
                                    return { value, ...pick(['_id', 'label', '_comboType'], option) };
                                }

                                return null;
                            })
                            .filter(Boolean);

                        return {
                            _type: BlockType.Options,
                            value: {
                                _kind: OptionKind.Combo,
                                price: configuratorBlock.optionSettings.price,
                                options,
                            },
                            blockId: configuratorBlock._id,
                        };
                    }

                    default:
                        throw new Error('Option kind not supported');
                }
            }

            case BlockType.Package: {
                const [id] = applicationConfiguratorBlock.ids;
                const blockPackage = blocks.packages.find(({ _id }) => _id.equals(id));

                if (!blockPackage) {
                    throw new Error('Block package does not exist');
                }

                const { sectionImage, ...value } = blockPackage;

                return { _type: BlockType.Package, value, blockId: configuratorBlock._id };
            }

            case BlockType.Trim: {
                const [id] = applicationConfiguratorBlock.ids;
                const trim = blocks.trims.find(({ _id }) => _id.equals(id));

                if (!trim) {
                    throw new Error('Block trim does not exist');
                }

                const { image, ...value } = trim;

                return { _type: BlockType.Trim, value, blockId: configuratorBlock._id };
            }

            default:
                throw new Error('Block type not supported');
        }
    });
};

const resolver: GraphQLMutationResolvers['draftConfiguratorApplication'] = async (
    root,
    {
        moduleId,
        customer: customerInputs,
        vehicle: vehicleInputs,
        configuratorId, // variant configurator id
        configuratorBlocks,
        configuration,
        tradeInVehicle,
        endpointId,
        financing: financingInputs,
        insurancing: insurancingInputs,
        dealerId,
        isDraft = false,
        promoCodeId,
        languageId,
    },
    { getUser, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);

    // get the module
    const applicationModule = await collections.modules.findOne({ _id: moduleId });

    if (!applicationModule || applicationModule._type !== ModuleType.ConfiguratorModule) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a configurator application module' });
    }

    // Get company details of application
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    // get the customer & vehicle modules
    const customerModule = await collections.modules.findOne({ _id: applicationModule.customerModuleId });
    const vehicleModule = await collections.modules.findOne({ _id: applicationModule.vehicleModuleId });

    const withFinancing = getApplyForFinancing(configuration, applicationModule);

    // validate the financing settings
    const { financeProduct, financing, isAffinBank } = await validateApplicationFinancing(
        applicationModule,
        financingInputs,
        t,
        dealerId,
        null,
        withFinancing || configuration.requestForFinancing
    );

    const variantConfigurator = await collections.configurators.findOne({
        _id: configuratorId,
        _kind: ConfiguratorKind.Variant,
    });

    // validate variant configurator
    if (!variantConfigurator || variantConfigurator._kind !== ConfiguratorKind.Variant) {
        throw new InvalidInput({ eventId: 'Configurator Variant is not valid' });
    }

    // validate model configurator
    const modelConfigurator = await collections.configurators.findOne({ _id: variantConfigurator.modelConfiguratorId });

    // for some reason the model does not exist or it's a different kind
    if (!modelConfigurator || modelConfigurator._kind !== ConfiguratorKind.Model) {
        throw new InvalidInput({ eventId: 'Configurator Variant is not valid' });
    }

    // then we check the module
    if (!modelConfigurator.moduleId.equals(moduleId)) {
        throw new InvalidInput({ eventId: 'Configurator Variant is not valid' });
    }

    // get router/endpoint when provided
    const origins = endpointId ? await validateEndpoint(endpointId, applicationModule) : null;

    // get the current user which might come in handy
    const user = await getUser(true);
    const loggedInUser = !!user;

    // build up versioning
    // customer ID will be defined later on if the kind is Customer
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    // get/create the vehicle

    let vehicle: Vehicle | null = null;
    if (vehicleModule._type === ModuleType.SimpleVehicleManagement || vehicleInputs.existingSimpleLocalVehicleId) {
        vehicle = await collections.vehicles.findOne({
            _id: vehicleInputs.existingSimpleLocalVehicleId,
            _kind: VehicleKind.LocalVariant,
            moduleId: vehicleModule._id,
            isActive: true,
            isDeleted: false,
            '_versioning.isLatest': true,
        });
    }

    if (!vehicle) {
        // something was wrong with the vehicle inputs
        throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
    }

    const promoCode = promoCodeId
        ? await getValidPromoCode(promoCodeId, applicationModule._id, dealerId, vehicle._versioning.suiteId, t)
        : null;

    // Validation for AN-2652 : To validating incoming financing values
    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        /// Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        const vehiclePrice = variantConfigurator.vehiclePrice || getVehiclePrice(vehicle);

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice,
            financing,
            promoCode,
            withFinancing: withFinancing || configuration.requestForFinancing,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    // get/create the applicant (customer object)
    const applicant = await (async () => {
        if (customerModule._type === ModuleType.LocalCustomerManagement) {
            if (customerInputs.existingLocalCustomer) {
                if (authoring.kind === AuthorKind.Customer) {
                    // only authenticated user may draft with existing customers
                    throw new InvalidPermission();
                }

                // get the customer
                return collections.customers.findOne({
                    _id: customerInputs.existingLocalCustomer,
                    moduleId: customerModule._id,
                    // todo apply permissions to limit available customers
                    '_versioning.isLatest': true,
                });
            }

            if (customerInputs.newLocalCustomer) {
                const newCustomerId = new ObjectId();

                if (authoring.kind === AuthorKind.Customer) {
                    // update the authoring object to match the newly created customer ID
                    authoring.customerId = newCustomerId;
                }

                // create a new customer
                const customer: LocalCustomer = {
                    _id: newCustomerId,
                    _kind: CustomerKind.Local,
                    moduleId: customerModule._id,
                    kycPresetIds: [],
                    fields: convertLocalCustomerGraphQLFields(customerInputs.newLocalCustomer.fields),
                    isDeleted: false,
                    _versioning: getAdvancedVersioningForCreation(authoring),
                };

                // insert the customer in the database
                await collections.customers.insertOne(customer);

                return customer;
            }
        }

        return null;
    })();

    if (!applicant) {
        // something was wrong with the customer inputs
        throw new InvalidInput({ customer: 'Invalid customer inputs' });
    }

    const dealer = await collections.dealers.findOne({ _id: dealerId });
    const paymentSetting = await getDealershipPaymentSetting(dealer._id, applicationModule, { collections });

    // we first need to create the customer document
    const stage = getApplicationInitialStage({
        dealer,
        paymentSetting,
        scenarios: applicationModule.scenarios,
        // With financing of request for financing, should be categorized as finance stage
        withFinancing: withFinancing || configuration.requestForFinancing,
        isFinancingOptional: applicationModule.financingPreference !== FinancingPreferenceValue.Mandatory,
        withInsurance: configuration.withInsurance,
        isInsuranceOptional: applicationModule.isInsuranceOptional,
        testDrive: configuration.testDrive,
    });

    // Check if there is insurance
    const withInsurance = getApplyForInsurance(configuration, applicationModule);

    // Validate insurance inputs first before drafting any application
    const { insurancing } = await getInsuranceApplicationSetup({
        applicationModule,
        insurancingInputs,
        t,
        withInsurance,
    });

    const allowAddInsuranceStage = stage !== ApplicationStage.Insurance && withInsurance;
    const assigneeId = getDealershipSettingId(dealerId, applicationModule.assignee);

    const applicationConfiguratorBlocks = await getConfiguratorBlocks(configuratorId, configuratorBlocks);

    const lead: Lead = {
        _id: new ObjectId(),
        kind: ApplicationKind.Configurator,
        status: LeadStatus.Drafted,
        isLead: false,
        moduleId: applicationModule._id,
        vehicleId: vehicle._id,
        customerId: applicant._id,
        dealerId: dealer._id,
        isDraft: true,
        identifier: '',
        tradeInVehicle,
        documents: [],
        _versioning: getAdvancedVersioningForCreation(authoring),
        configuratorBlocks: applicationConfiguratorBlocks,
        configuratorId,
        ...origins,
        languageId,
    };

    const application: ConfiguratorApplication = {
        _id: new ObjectId(),
        kind: ApplicationKind.Configurator,
        moduleId: applicationModule._id,
        scenarios: applicationModule.scenarios,
        stages: [stage, allowAddInsuranceStage && ApplicationStage.Insurance].filter(Boolean),
        ...getApplicationInitialStageDetails(
            stage,
            ApplicationStatus.Drafted,
            assigneeId,
            await retrieveFeatureProps(applicationModule._id)
        ),
        ...(allowAddInsuranceStage &&
            getApplicationInitialStageDetails(
                ApplicationStage.Insurance,
                ApplicationStatus.Drafted,
                assigneeId,
                await retrieveFeatureProps(applicationModule._id)
            )),

        // the application should be treated as a draft if there's no remote link sent
        isDraft: true,

        // define relationships
        bankId: financeProduct?.bankId,
        applicantId: applicant._id,
        vehicleId: vehicle._id,
        configuratorId,
        dealerId,

        // start with empty remarks and no document
        remarks: '',
        documents: [],

        // spread origins
        ...origins,
        guarantorId: [],

        // provide initial versioning
        _versioning: getAdvancedVersioningForCreation(authoring),
        configuration: {
            ...configuration,
            withFinancing,
            isAffinAutoFinanceCentreRequired: isAffinBank && (withFinancing || configuration.requestForFinancing),
        },
        tradeInVehicle,
        configuratorBlocks: applicationConfiguratorBlocks,

        financing,
        insurancing,

        promoCodeId: promoCode?._id,
        withCustomerDevice: false,
        referenceApplicationSuiteIds: [],
        languageId,
        useMyinfo: {
            customer: false,
            guarantor: false,
        },

        leadId: lead._id,
    };

    // build up application journey
    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: applicant._versioning.createdAt,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
    };

    // insert the documents in database
    await collections.leads.insertOne(lead);
    const configuratorApplication = await collections.applications.insertOne(application);
    await collections.applicationJourneys.insertOne(applicationJourney);

    // generate the trail
    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: applicant._versioning.createdAt,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    const leadTrail: LeadDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadDrafted,
        _date: applicant._versioning.createdAt,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    // register the trail
    await collections.auditTrails.insertMany([trail, leadTrail]);

    if (isDraft) {
        const customerContent = customerInputs.newLocalCustomer.fields.find(
            customer => customer.key === LocalCustomerFieldKey.Email
        );
        if (!customerContent) {
            throw new Error('Customer Email is not added');
        }

        const linkDocument: ConfiguratorApplicationLink = {
            _id: new ObjectId(),
            _kind: ExternalLinkKind.ConfiguratorApplication,
            secret: nanoid(),
            data: {
                applicationId: configuratorApplication.insertedId,
                applicationSuiteId: application._versioning.suiteId,
                routerId: origins.routerId,
                endpointId: origins.endpointId,
                modelConfiguratorId: variantConfigurator.modelConfiguratorId,
                variantConfiguratorId: variantConfigurator._id,
            },
            expiresAt: dayjs().add(7, 'days').toDate(),
            deleteOnFetch: false,
        };

        const link = await collections.externalLinks.insertOne(linkDocument);

        await mainQueue.add({
            type: 'saveOrderConfiguratorApplication',
            linkId: link.insertedId,
            customerEmail: customerContent.stringValue,
        });
    }

    // finally execute the journey
    return executeConfiguratorJourney({
        application,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });
};

export default buildRateLimiterMiddleware({ operation: 'draftConfiguratorApplication' })(resolver);
