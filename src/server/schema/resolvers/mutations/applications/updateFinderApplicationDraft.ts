import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AppointmentModule,
    Collections,
    FinancingPreferenceValue,
    FinderApplication,
    ModuleType,
    SettingId,
    TimeSlotEnum,
    VehicleKind,
    FinderApplicationPrivateModule,
    FinderApplicationPublicModule,
    FinderVehicleManagementModule,
    FinderVehicle,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    retrieveFeatureProps,
    getApplicationInitialStage,
    getInsuranceApplicationSetup,
} from '../../../../database/helpers/applications';
import { getVehiclePrice } from '../../../../database/helpers/vehicles';
import { consumeJourneyToken, generateJourneyToken } from '../../../../journeys';
import { ModulePolicyAction } from '../../../../permissions';
import {
    validateApplicationFinancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import { getDealershipPaymentSetting } from '../../../../utils/getDealershipPaymentSetting';
import getDealershipSettingId from '../../../../utils/getDealershipSettingId';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { ApplicationScenario, GraphQLMutationResolvers } from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, getApplyForInsurance } from './helpers';

type FinderModule = FinderApplicationPrivateModule | FinderApplicationPublicModule;

const getFinderModuleOrFail = async (moduleId: ObjectId, collections: Collections) => {
    const module = await collections.modules.findOne<FinderModule>({
        _id: moduleId,
    });

    if (
        !module ||
        (module._type !== ModuleType.FinderApplicationPublicModule &&
            module._type !== ModuleType.FinderApplicationPrivateModule)
    ) {
        throw new InvalidInput({ moduleId: 'Module is not a finder application module' });
    }

    return module;
};

const getOneApplicationOrFail = async (applicationId: ObjectId, collections: Collections) => {
    const application = await collections.applications.findOne<FinderApplication>({
        _id: applicationId,
        kind: ApplicationKind.Finder,
        '_versioning.isLatest': true,
    });

    if (!application || application.kind !== ApplicationKind.Finder) {
        throw new InvalidInput('Invalid token');
    }

    return application;
};

const getLeadOrFail = async (leadId: ObjectId, collections: Collections) => {
    const lead = await collections.leads.findOne({
        _id: leadId,
    });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Invalid lead inputs' });
    }

    return lead;
};

const getVehicleOrFail = async (vehicleId: ObjectId, moduleId: ObjectId, collections: Collections) => {
    const vehicle = await collections.vehicles.findOne<FinderVehicle>({
        _id: vehicleId,
        _kind: VehicleKind.FinderVehicle,
        moduleId,
        isDeleted: false,
        '_versioning.isLatest': true,
    });

    if (!vehicle) {
        throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
    }

    return vehicle;
};

const resolver: GraphQLMutationResolvers['updateFinderApplicationDraft'] = async (
    root,
    {
        financing: financingInputs,
        insurancing: insurancingInputs,
        configuration,
        promoCodeId,
        tradeInVehicle,
        languageId,
        token,
        isTestDriveDrafting,
        dealerId,
    },
    { getUser, getTranslations, loaders, getPermissionController }
) => {
    const { applicationId, origin, leadId } = await consumeJourneyToken(token);
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['errors']);

    const application = await getOneApplicationOrFail(applicationId, collections);
    const applicationModule = await getFinderModuleOrFail(application.moduleId, collections);
    const lead = await getLeadOrFail(leadId, collections);

    const user = await getUser(true);
    const loggedInUser = !!user;

    // Check private or public application module, to have user
    if (applicationModule._type === ModuleType.FinderApplicationPrivateModule && !user) {
        // Make sure for private, and permission
        if (
            !user ||
            !permissionController.modules.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication)
        ) {
            throw new InvalidPermission();
        }
    }

    const appointmentModule = !isNil(applicationModule?.appointmentModuleId)
        ? ((await loaders.moduleById.load(applicationModule?.appointmentModuleId)) as AppointmentModule)
        : null;

    if (
        !appointmentModule &&
        isTestDriveDrafting &&
        applicationModule.scenarios.includes(ApplicationScenario.Appointment)
    ) {
        // The path is for test drive. But appointment is not there
        // So return the invalid input error
        throw new InvalidInput({ $root: 'Invalid application setup for test drive' });
    }

    const isTestDriveOnly =
        isTestDriveDrafting &&
        applicationModule.scenarios.includes(ApplicationScenario.Appointment) &&
        !isNil(appointmentModule);

    const [vehicleModule, company, promoCode] = await Promise.all([
        collections.modules.findOne<FinderVehicleManagementModule>({ _id: applicationModule.vehicleModuleId }),
        collections.companies.findOne({ _id: applicationModule.companyId }),
        promoCodeId
            ? getValidPromoCode(promoCodeId, applicationModule._id, dealerId, applicationModule.vehicleModuleId, t)
            : null,
    ]);

    const vehicle = await getVehicleOrFail(application.vehicleId, vehicleModule._id, collections);
    const withFinancing = getApplyForFinancing(configuration, applicationModule);

    const { financeProduct, financing, isAffinBank } = await validateApplicationFinancing(
        applicationModule,
        financingInputs,
        t,
        dealerId,
        null,
        withFinancing || configuration.requestForFinancing
    );

    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        // Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice: getVehiclePrice(vehicle),
            financing,
            includeDealerOptionsForFinancing:
                applicationModule._type === ModuleType.FinderApplicationPrivateModule
                    ? applicationModule.includeDealerOptionsForFinancing
                    : undefined,
            promoCode,
            withFinancing,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    const foundSetting = await collections.settings.findOne({
        settingId: SettingId.FinderVehicleManagementSetting,
        moduleId: vehicle.moduleId,
    });

    const isLTAEnabled = foundSetting.settingId === 'finderVehicleManagementSetting' && foundSetting.allowLTA;

    const canApplyForFinancing = (function () {
        if (vehicle._kind !== VehicleKind.FinderVehicle) {
            throw new Error('Vehicle Kind is not Finder Vehicle');
        }

        return vehicle.listing.vehicle.condition.value === 'new' || (isLTAEnabled && !!vehicle.lta) || !isLTAEnabled;
    })();

    const dealer = await collections.dealers.findOne({ _id: dealerId });
    const paymentSetting = await getDealershipPaymentSetting(dealer._id, applicationModule, { collections });

    // this flag is to tell whether the user is coming from finder public test drive journey
    const stage = isTestDriveOnly
        ? ApplicationStage.Appointment
        : getApplicationInitialStage({
              dealer,
              paymentSetting,
              scenarios: applicationModule.scenarios,
              // With financing of request for financing, should be categorized as finance stage
              withFinancing: (canApplyForFinancing && configuration.withFinancing) || configuration.requestForFinancing,
              isFinancingOptional:
                  !canApplyForFinancing || applicationModule.financingPreference !== FinancingPreferenceValue.Mandatory,
              withInsurance: configuration.withInsurance,
              isInsuranceOptional: applicationModule.isInsuranceOptional,
              testDrive: configuration.testDrive,
          });

    // Check if there is insurance
    const withInsurance = !isTestDriveOnly ? getApplyForInsurance(configuration, applicationModule) : false;

    // Validate insurance inputs first before drafting any application
    const { insurancing } = await getInsuranceApplicationSetup({
        applicationModule,
        insurancingInputs,
        t,
        withInsurance,
    });

    const allowAddInsuranceStage = stage !== ApplicationStage.Insurance && withInsurance;
    const assigneeId =
        (applicationModule._type === ModuleType.FinderApplicationPublicModule
            ? getDealershipSettingId(dealerId, applicationModule.assignee)
            : null) ?? user?._id;

    const updateBody: Partial<FinderApplication> = {
        scenarios: isTestDriveOnly ? [ApplicationScenario.Appointment] : applicationModule.scenarios,
        stages: [stage, allowAddInsuranceStage && ApplicationStage.Insurance].filter(Boolean),
        ...(isTestDriveOnly
            ? {
                  appointmentStage: {
                      identifier: '',
                      status: ApplicationStatus.Drafted,
                      appointmentModuleId: applicationModule.appointmentModuleId,
                      bookingTimeSlot: {
                          _type: TimeSlotEnum.Appointment,
                          bookingLimit: 0,
                          slot: null,
                      },
                      assigneeId,
                  },
              }
            : getApplicationInitialStageDetails(
                  stage,
                  ApplicationStatus.Drafted,
                  assigneeId,
                  await retrieveFeatureProps(applicationModule._id)
              )),
        ...(allowAddInsuranceStage &&
            getApplicationInitialStageDetails(
                ApplicationStage.Insurance,
                ApplicationStatus.Drafted,
                assigneeId,
                await retrieveFeatureProps(applicationModule._id)
            )),
        // the application should be treated as a draft if there's no remote link sent
        isDraft: true,
        vehicleId: vehicle._id,
        bankId: financeProduct?.bankId,

        dealerId,
        promoCodeId: promoCode?._id,

        configuration: {
            ...configuration,
            // add check if we can apply for financing depending on finder vehicle
            withFinancing: canApplyForFinancing && configuration.withFinancing,
            isAffinAutoFinanceCentreRequired:
                isAffinBank && (configuration.withFinancing || configuration.requestForFinancing),
        },
        tradeInVehicle,
        financing,
        insurancing,
        languageId,
    };

    const updatedApplication = await collections.applications.findOneAndUpdate(
        { _id: applicationId },
        {
            $set: updateBody,
        },
        { returnDocument: 'after' }
    );

    return {
        application: updatedApplication,
        token: generateJourneyToken(lead, updatedApplication, origin, company.sessionTimeout, user),
        lead,
    };
};

export default resolver;
