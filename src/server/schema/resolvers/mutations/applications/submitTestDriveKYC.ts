import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import { executeJourneyFromGraphQL } from '../../../../journeys';
import { TestDriveKYCStepPayload } from '../../../../journeys/common/TestDriveKYCStep';
import { CustomerKind, GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['submitTestDriveKYC'] = async (root, { token, fields }, context) =>
    executeJourneyFromGraphQL<TestDriveKYCStepPayload>({
        token,
        identifier: 'test-drive-kyc',
        payload: {
            fields: convertLocalCustomerGraphQLFields(fields),
            customerKind: CustomerKind.Local,
        },
        context,
    });

export default resolver;
