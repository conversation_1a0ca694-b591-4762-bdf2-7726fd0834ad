import { isNil, xor } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import urljoin from 'url-join';
import config from '../../../../core/config';
import {
    Application,
    ApplicationJourney,
    ApplicationKind,
    ApplicationModule,
    ApplicationProceedWithCustomerDeviceAuditTrail,
    AuditTrailKind,
    AuthorKind,
    ExternalLinkKind,
    Module,
    ModuleType,
    User,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getCustomerEmail } from '../../../../database/helpers/customers';
import updateAgreementsForApplication from '../../../../database/helpers/updateAgreementsForApplication';
import { getPreviousApplicationStages } from '../../../../database/queries/application';
import { JourneyTokenPayload } from '../../../../journeys';
import makeJourney, { canMakeJourney } from '../../../../journeys/makeJourney';
import { ApplicationPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import { getApplicationLogStages } from '../../../../utils/application';
import { Authoring, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { ApplicationStage, GraphQLMutationResolvers } from '../../definitions';
import { generateExternalLink } from './submitChanges/sendEmail';
import skipDeposit from './submitChanges/skipDeposit';

const isValidApplicationModuleForContinueApplication = (module: Module): module is ApplicationModule =>
    [
        ModuleType.StandardApplicationModule,
        ModuleType.EventApplicationModule,
        ModuleType.ConfiguratorModule,
        ModuleType.FinderApplicationPublicModule,
        ModuleType.FinderApplicationPrivateModule,
    ].includes(module._type);

const getLink = async (routerId, secret, loaders) => {
    const router = await loaders.routerById.load(routerId);
    const link = urljoin(`${config.protocol}://${router.hostname}`, router.pathname, `l/${secret}`);

    return link;
};

const mayContinueApplication = (
    application,
    module,
    journey: ApplicationJourney,
    stage: ApplicationStage,
    continueRequestFinancing: boolean
) => {
    if (!journey.isReceived) {
        return true;
    }

    if (journey.isReceived && continueRequestFinancing) {
        return true;
    }

    // apply new, then continue application
    if (module._kind === ModuleType.EventApplicationModule) {
        return false;
    }

    if (
        stage === ApplicationStage.Financing &&
        !isNil(application?.financingStage?.isDraft) &&
        (!application?.financingStage?.isDraft ||
            (!isNil(journey.applyNewSubmission?.financingStage?.isReceived) &&
                !journey.applyNewSubmission?.financingStage?.isReceived))
    ) {
        return true;
    }

    if (
        stage === ApplicationStage.Insurance &&
        !isNil(application?.insuranceStage?.isDraft) &&
        (!application?.insuranceStage?.isDraft ||
            (!isNil(journey.applyNewSubmission?.insuranceStage?.isReceived) &&
                !journey.applyNewSubmission?.insuranceStage?.isReceived))
    ) {
        return true;
    }

    if (
        stage === ApplicationStage.Appointment &&
        !isNil(application?.appointmentStage) &&
        !isNil(journey.applyNewSubmission?.appointmentStage?.isReceived) &&
        !journey.applyNewSubmission?.appointmentStage?.isReceived
    ) {
        return true;
    }

    return false;
};

export const reinitializeLatestStep = async (
    application: Application,
    journey: ApplicationJourney,
    user: User | null,
    origin: JourneyTokenPayload['origin']
) => {
    const previousStages = await getPreviousApplicationStages(application._versioning.suiteId);

    if (!canMakeJourney(application, journey, previousStages) || application.kind === ApplicationKind.Mobility) {
        return null;
    }

    const { controller, context } = await makeJourney(
        application,
        journey,
        user,
        origin,
        undefined,
        false,
        xor(previousStages ?? [], application.stages)
    );

    const step = controller?.search(item => !item.isFinalized);

    if (!step || !step.mayBeExecuted) {
        // it's too early or too late to execute it
        throw new InvalidPermission();
    }

    const isReinitializationNeeded = (() => {
        // last step is payment, re-initialize the step
        if (step.identifier.includes('payment')) {
            return true;
        }

        if (step.identifier.includes('signing')) {
            return true;
        }

        return false;
    })();

    if (isReinitializationNeeded) {
        await step.initialize();
    }

    return context;
};

const mutate: GraphQLMutationResolvers['continueApplication'] = async (
    root,
    { applicationId, stage, isProceedWithCustomerDevice, isSkipDeposit, continueRequestFinancing },
    { loaders, getPermissionController, getUser, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['common']);
    const permissionController = await getPermissionController();

    const applicationFilter = await permissionController.applications.getFilterQueryForAction(
        ApplicationPolicyAction.Update,
        [stage]
    );

    const application = await collections.applications.findOne({
        $and: [{ _id: applicationId, '_versioning.isLatest': true }, applicationFilter],
    });

    if (
        !application ||
        application.kind === ApplicationKind.Mobility ||
        application.kind === ApplicationKind.Launchpad ||
        application.kind === ApplicationKind.SalesOffer
    ) {
        throw new InvalidPermission();
    }

    const module = await loaders.moduleById.load(application.moduleId);

    if (!isValidApplicationModuleForContinueApplication(module)) {
        throw new InvalidPermission();
    }

    // only Configurator and Finder can continue requesting financing
    if (
        continueRequestFinancing &&
        application.kind !== ApplicationKind.Configurator &&
        application.kind !== ApplicationKind.Finder
    ) {
        throw new InvalidPermission();
    }

    const user = await getUser(true);

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });

    const canContinueApplication = mayContinueApplication(
        application,
        module,
        journey,
        stage,
        continueRequestFinancing
    );
    if (!canContinueApplication) {
        throw new InvalidPermission();
    }

    // only finder and configurator journey has request financing flow
    if (continueRequestFinancing) {
        // For request for financing
        // the process same with "needFinancingApproval" but we need to change "withFinancing" to true
        // As the submission process already done from previous journey
        // It's not done inside configuration update, because the stage already "finance" to begin with
        const value = await collections.applications.findOneAndUpdate(
            { _id: applicationId },
            {
                $set: {
                    'configuration.withFinancing': true,
                },
            },
            { returnDocument: 'after' }
        );

        // check for missing agreements
        if (journey.applicantAgreements) {
            const customer = await collections.customers.findOne({ _id: application.applicantId });
            const newAgreements = await updateAgreementsForApplication(value, customer._kind, loaders);
            journey.applicantAgreements.agreements = newAgreements;
        }

        // continueRequestFinancing special scenario, reset isReceived status
        // For continueRequestFinancing, application is already finished, so no need to reset
        await collections.applicationJourneys.findOneAndUpdate(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    isReceived: false,
                    'applicantKYC.completed': false,
                    'applicantAgreements.agreements': journey.applicantAgreements.agreements,
                },
            }
        );
    }

    application.withCustomerDevice = isProceedWithCustomerDevice;
    await collections.applications.findOneAndUpdate(
        { _id: applicationId },
        {
            $set: {
                withCustomerDevice: isProceedWithCustomerDevice,
            },
        }
    );

    if (isSkipDeposit) {
        await skipDeposit({
            application,
            applicationModule: module,
            journey,
            user,
        });
    }

    if (!isProceedWithCustomerDevice) {
        // init payment or signing
        const latestJourney = await collections.applicationJourneys.findOne({
            applicationSuiteId: application._versioning.suiteId,
        });

        await reinitializeLatestStep(application, latestJourney, user, 'draft');
    }

    if (isProceedWithCustomerDevice) {
        // delete previous links
        await collections.externalLinks.deleteMany({
            _kind: ExternalLinkKind.ProceedWithCustomer,
            'data.applicationSuiteId': application._versioning.suiteId,
        });
    }

    const externalLink = await generateExternalLink(application);

    await collections.externalLinks.insertOne(externalLink);

    const previousStages = await getPreviousApplicationStages(application._versioning.suiteId);

    const currentStages = xor(previousStages ?? [], application.stages);

    if (isProceedWithCustomerDevice) {
        const customer = await collections.customers.findOne({ _id: application.applicantId });
        await mainQueue.add({
            type: 'sendProceedWithCustomerEmail',
            linkId: externalLink._id,
            customerEmail: getCustomerEmail(t, customer),
        });

        // create audit trail
        const authoring: Authoring = { kind: AuthorKind.User, userId: user._id };

        const trail: ApplicationProceedWithCustomerDeviceAuditTrail = {
            _id: new ObjectId(),
            _kind: AuditTrailKind.ApplicationProceedWithCustomerDevice,
            _date: new Date(),
            applicationId,
            applicationSuiteId: application._versioning.suiteId,
            stages: getApplicationLogStages(
                application,
                AuditTrailKind.ApplicationProceedWithCustomerDevice,
                !isNil(journey.applyNewSubmission) && currentStages.length ? currentStages : null
            ),
            author: getAuthorFromAuthoring(authoring),
        };

        await collections.auditTrails.insertOne(trail);
    }

    return getLink(application.routerId, externalLink.secret, loaders);
};

export default requiresLoggedUser(mutate);
