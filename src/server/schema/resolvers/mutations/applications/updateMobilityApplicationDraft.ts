import { TFunction } from 'i18next';
import { isEmpty, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { GetTranslations } from '../../../../core/translations';
import {
    ApplicationKind,
    InventoryKind,
    MobilityApplication,
    MobilityInventory,
    MobilityStockInventory,
    StockInventory,
    MobilitySnapshot,
    StockInventoryKind,
    PromoTypeEnum,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { isValidStatusToAmendOrCancel } from '../../../../database/helpers/applications';
import { consumeJourneyToken, generateJourneyToken } from '../../../../journeys';
import getMobilityTotalAmount from '../../../../journeys/helper/getMobilityTotalAmount';
import { Loaders } from '../../../../loaders';
import { InvalidInput } from '../../../errors';
import {
    GraphQLMutationDraftMobilityApplicationArgs,
    GraphQLMutationResolvers,
    MobilityKind,
    ModuleType,
    VehicleKind,
} from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getLocationFromInput, validateBookingPeriod } from './draftMobilityApplication/shared';
import { getPromoCodeAmount } from './updateMobilityDepositAmount';

const validateVehicleBookingDetails = async (
    t: TFunction,
    mobilityModuleId: ObjectId,
    mobilityBookingDetails: GraphQLMutationDraftMobilityApplicationArgs['mobilityBookingDetails']
): Promise<
    Pick<MobilityApplication, 'vehicleId' | 'dealerId' | 'mobilityBookingDetails'> & { stocks: StockInventory[] }
> => {
    const { collections } = await getDatabaseContext();

    const { location: locationInput } = mobilityBookingDetails;

    const inventory = (await collections.inventories.findOne({
        _kind: InventoryKind.MobilityInventory,
        moduleId: mobilityModuleId,
        isDeleted: false,
        'stocks._kind': StockInventoryKind.MobilityStock,
        'stocks._id': mobilityBookingDetails.inventoryStockId,
    })) as MobilityInventory;

    const vehicle =
        inventory._kind === InventoryKind.MobilityInventory
            ? await collections.vehicles.findOne({
                  _kind: VehicleKind.LocalVariant,
                  isDeleted: false,
                  '_versioning.isLatest': true,
                  '_versioning.suiteId': inventory.variantSuiteId,
              })
            : null;

    // period check would happen on the flow
    if (isNil(inventory) || vehicle?._kind !== VehicleKind.LocalVariant) {
        throw new InvalidInput({ $root: t('errors:mobilityApplication.inventoryNotFound') });
    }

    const stock: StockInventory =
        inventory?._kind === InventoryKind.MobilityInventory
            ? inventory.stocks.find(
                  ({ _kind, _id }) =>
                      _kind === StockInventoryKind.MobilityStock && _id.equals(mobilityBookingDetails.inventoryStockId)
              )
            : null;

    const location = getLocationFromInput(locationInput);

    return {
        vehicleId: vehicle._id,
        dealerId: inventory.dealerId,
        mobilityBookingDetails: {
            ...mobilityBookingDetails,
            inventoryStockPrice: (stock as MobilityStockInventory).price || vehicle.vehiclePrice,
            // I'm put the vehicle.vehiclePrice as alternative value in case the stock price is still empty
            location,
        },
        stocks: inventory.stocks,
    };
};

const validateMobilityDetails = async (
    t: TFunction,
    loaders: Loaders,
    mobilityDetails: GraphQLMutationDraftMobilityApplicationArgs['mobilityDetails']
): Promise<Pick<MobilityApplication, 'mobilitySnapshots'>> => {
    const errors: string[] = [];

    const snapshots = await Promise.all(
        mobilityDetails.map<Promise<MobilitySnapshot | null>>(async ({ kind, id, addon }) => {
            const mobility = await loaders.mobilityById.load(id);
            if (isNil(mobility) || mobility._kind !== kind) {
                errors.push(t('errors:mobilityApplication.mobilityNotFound'));

                return null;
            }

            if (kind === MobilityKind.Addon && isEmpty(addon)) {
                errors.push(t('errors:mobilityApplication.mobilityAddonRequired'));

                return null;
            }

            switch (mobility._kind) {
                case MobilityKind.Addon: {
                    return {
                        _type: MobilityKind.Addon,
                        mobilityId: mobility._id,
                        title: mobility.title,
                        option: addon,
                    };
                }

                case MobilityKind.AdditionalInfo: {
                    return {
                        _type: MobilityKind.AdditionalInfo,
                        mobilityId: mobility._id,
                        title: mobility.title,
                        detail: mobility.detail,
                    };
                }

                default:
                    throw new Error('invalid mobility kind');
            }
        })
    );

    if (errors.length > 0) {
        throw new InvalidInput({ $root: errors.join('\n') });
    }

    return { mobilitySnapshots: snapshots.filter(Boolean) };
};

const updatePromoCode = async (
    application: MobilityApplication,
    mobilityBookingDetails: MobilityApplication['mobilityBookingDetails'],
    getTranslations: GetTranslations
) => {
    if (!application.promoCodeId) {
        // no existing promo code
        // do nothing
        return null;
    }

    if (!application.mobilityBookingDetails.inventoryStockId.equals(mobilityBookingDetails.inventoryStockId)) {
        // if there is promo code but stock change
        // reset promo code
        return {
            promoCodeAmount: undefined,
            promoCodeId: undefined,
        };
    }

    const { collections } = await getDatabaseContext();
    const vehicle = await collections.vehicles.findOne({ _id: application.vehicleId });
    const { t } = await getTranslations(['errors']);

    // promo code remains the same, same stock, but can be different rental day
    const promoCode = application.promoCodeId
        ? await getValidPromoCode(
              application.promoCodeId,
              application.moduleId,
              application.dealerId,
              vehicle._versioning.suiteId,
              t
          )
        : null;

    if (!promoCode) {
        return {
            promoCodeAmount: undefined,
            promoCodeId: undefined,
        };
    }
    const depositAmount = getMobilityTotalAmount({ ...application, mobilityBookingDetails });

    const promoCodeAmount =
        promoCode && promoCode.promoType._type === PromoTypeEnum.Discount
            ? getPromoCodeAmount(promoCode, depositAmount)
            : 0;

    return { promoCodeAmount };
};

const resolver: GraphQLMutationResolvers['updateMobilityApplicationDraft'] = async (
    root,
    { token, mobilityBookingDetails, mobilityDetails },
    { getUser, getTranslations, loaders }
) => {
    const { applicationId, origin, leadId } = await consumeJourneyToken(token);

    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);

    const application = await collections.applications.findOne({
        _id: applicationId,
        kind: ApplicationKind.Mobility,
        isDraft: true,
        '_versioning.isLatest': true,
    });

    if (application?.kind !== ApplicationKind.Mobility) {
        throw new Error('invalid token');
    }

    if (!isValidStatusToAmendOrCancel(application.mobilityStage?.status)) {
        throw new Error(`Application with status ${application.mobilityStage?.status} cannot be amended`);
    }

    const lead = await collections.leads.findOne({ _id: leadId });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Lead not found' });
    }

    // get the module
    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });

    if (!applicationModule || applicationModule._type !== ModuleType.MobilityModule) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a mobility application module' });
    }

    const { stocks, ...vehicleBookingDetails } = await validateVehicleBookingDetails(
        t,
        application.moduleId,
        mobilityBookingDetails
    );

    // check for period
    const bookablePeriod = await validateBookingPeriod(
        t,
        collections,
        vehicleBookingDetails.vehicleId,
        applicationModule.durationBeforeNextBooking,
        mobilityBookingDetails.period,
        applicationModule.unavailableTimeRange,
        applicationModule.unavailableDayOfWeek,
        stocks,
        application._versioning.suiteId, // application suite id
        null, // user timezone
        true // isDraft?
    );

    if (!bookablePeriod) {
        throw new Error('BookingPeriod is invalid');
    }

    const mobilitySnapshots = await validateMobilityDetails(t, loaders, mobilityDetails);

    const updatedApplication = await collections.applications.findOneAndUpdate(
        { _id: application._id },
        {
            $set: {
                ...mobilitySnapshots,
                mobilityBookingDetails: vehicleBookingDetails.mobilityBookingDetails,
                vehicleId: vehicleBookingDetails.vehicleId,
                dealerId: vehicleBookingDetails.dealerId,
                ...(await updatePromoCode(application, vehicleBookingDetails.mobilityBookingDetails, getTranslations)),
            },
        }
    );

    const user = await getUser(true);
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    return {
        application: updatedApplication,
        token: generateJourneyToken(lead, updatedApplication, origin, company.sessionTimeout, user),
        lead,
    };
};

export default resolver;
