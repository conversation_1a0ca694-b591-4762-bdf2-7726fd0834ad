import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationDraftedAuditTrail,
    ApplicationJourney,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AppointmentModule,
    AuditTrailKind,
    AuthorKind,
    CustomerKind,
    FinancingPreferenceValue,
    FinderApplication,
    LocalCustomer,
    ModuleType,
    SettingId,
    TimeSlotEnum,
    VehicleKind,
    Lead,
    LeadStatus,
    LeadDraftedAuditTrail,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    retrieveFeatureProps,
    getApplicationInitialStage,
    getInsuranceApplicationSetup,
} from '../../../../database/helpers/applications';
import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import { getVehiclePrice } from '../../../../database/helpers/vehicles';
import executeFinderJourney from '../../../../journeys/finderJourney/executeFinderJourney';
import { ModulePolicyAction } from '../../../../permissions';
import { getApplicationLogStages } from '../../../../utils/application';
import {
    validateApplicationFinancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import { getDealershipPaymentSetting } from '../../../../utils/getDealershipPaymentSetting';
import getDealershipSettingId from '../../../../utils/getDealershipSettingId';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import { ApplicationScenario, FinderVehicleStatus, GraphQLMutationResolvers } from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, getApplyForInsurance, validateEndpoint } from './helpers';
import { retrieveTestDriveRedirectionRouterAndEndpoint } from './startTestDrive';

const resolver: GraphQLMutationResolvers['draftFinderApplication'] = async (
    root,
    {
        moduleId,
        customer: customerInputs,
        vehicle: vehicleInputs,
        financing: financingInputs,
        insurancing: insurancingInputs,
        configuration,
        promoCodeId,
        tradeInVehicle,
        endpointId,
        dealerId,
        languageId,
        isTestDriveDrafting = false,
    },
    { getUser, getTranslations, loaders, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['errors']);

    // get the module
    const applicationModule = await collections.modules.findOne({ _id: moduleId });

    if (
        !applicationModule ||
        (applicationModule._type !== ModuleType.FinderApplicationPublicModule &&
            applicationModule._type !== ModuleType.FinderApplicationPrivateModule)
    ) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a finder application module' });
    }

    // get the current user which might come in handy
    const user = await getUser(true);
    const loggedInUser = !!user;

    // Check private or public application module, to have user
    if (applicationModule._type === ModuleType.FinderApplicationPrivateModule) {
        // Make sure for private, and permission
        if (
            !user ||
            !permissionController.modules.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication)
        ) {
            throw new InvalidPermission();
        }
    }

    const appointmentModule = !isNil(applicationModule?.appointmentModuleId)
        ? ((await loaders.moduleById.load(applicationModule?.appointmentModuleId)) as AppointmentModule)
        : null;

    if (
        !appointmentModule &&
        isTestDriveDrafting &&
        applicationModule.scenarios.includes(ApplicationScenario.Appointment)
    ) {
        // The path is for test drive. But appointment is not there
        // So return the invalid input error
        throw new InvalidInput({ $root: 'Invalid application setup for test drive' });
    }

    const isTestDriveOnly =
        isTestDriveDrafting &&
        applicationModule.scenarios.includes(ApplicationScenario.Appointment) &&
        !isNil(appointmentModule);

    // Get company details of application
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    // get the customer & vehicle modules
    const customerModule = await collections.modules.findOne({ _id: applicationModule.customerModuleId });
    const vehicleModule = await collections.modules.findOne({ _id: applicationModule.vehicleModuleId });
    const withFinancing = getApplyForFinancing(configuration, applicationModule);

    // validate the financing settings
    const { financeProduct, financing, isAffinBank } = await validateApplicationFinancing(
        applicationModule,
        financingInputs,
        t,
        dealerId,
        null,
        withFinancing || configuration.requestForFinancing
    );

    // get router/endpoint when provided
    const origins = endpointId ? await validateEndpoint(endpointId, applicationModule) : null;

    // build up versioning
    // customer ID will be defined later on if the kind is Customer
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    // get/create the vehicle
    const vehicle = await (async () => {
        if (vehicleModule._type === ModuleType.FinderVehicleManagement) {
            if (vehicleInputs.existingSimpleLocalVehicleId) {
                return collections.vehicles.findOne({
                    _id: vehicleInputs.existingSimpleLocalVehicleId,
                    _kind: VehicleKind.FinderVehicle,
                    moduleId: vehicleModule._id,
                    isDeleted: false,
                    '_versioning.isLatest': true,
                });
            }
        }

        return null;
    })();

    if (vehicle._kind === VehicleKind.FinderVehicle && vehicle.status !== FinderVehicleStatus.Available) {
        throw new Error('Finder Vehicle is not available');
    }

    if (!vehicle) {
        // something was wrong with the vehicle inputs
        throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
    }

    const promoCode = promoCodeId
        ? await getValidPromoCode(promoCodeId, applicationModule._id, dealerId, vehicle._versioning.suiteId, t)
        : null;

    // Validation for AN-2652 : To validating incoming financing values
    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        // Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice: getVehiclePrice(vehicle),
            financing,
            includeDealerOptionsForFinancing:
                applicationModule._type === ModuleType.FinderApplicationPrivateModule
                    ? applicationModule.includeDealerOptionsForFinancing
                    : undefined,
            promoCode,
            withFinancing,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    // get/create the applicant (customer object)
    const applicant = await (async () => {
        if (customerModule._type === ModuleType.LocalCustomerManagement) {
            if (customerInputs.existingLocalCustomer) {
                if (authoring.kind === AuthorKind.Customer) {
                    // only authenticated user may draft with existing customers
                    throw new InvalidPermission();
                }

                // get the customer
                return collections.customers.findOne({
                    _id: customerInputs.existingLocalCustomer,
                    moduleId: customerModule._id,
                    // todo apply permissions to limit available customers
                    '_versioning.isLatest': true,
                });
            }

            if (customerInputs.newLocalCustomer) {
                const newCustomerId = new ObjectId();

                if (authoring.kind === AuthorKind.Customer) {
                    // update the authoring object to match the newly created customer ID
                    authoring.customerId = newCustomerId;
                }

                // create a new customer
                const customer: LocalCustomer = {
                    _id: newCustomerId,
                    _kind: CustomerKind.Local,
                    moduleId: customerModule._id,
                    kycPresetIds: [],
                    fields: convertLocalCustomerGraphQLFields(customerInputs.newLocalCustomer.fields),
                    isDeleted: false,
                    _versioning: getAdvancedVersioningForCreation(authoring),
                };

                // insert the customer in the database
                await collections.customers.insertOne(customer);

                return customer;
            }
        }

        return null;
    })();

    if (!applicant) {
        // something was wrong with the customer inputs
        throw new InvalidInput({ customer: 'Invalid customer inputs' });
    }

    const foundSetting = await collections.settings.findOne({
        settingId: SettingId.FinderVehicleManagementSetting,
        moduleId: vehicle.moduleId,
    });

    const isLTAEnabled = foundSetting.settingId === 'finderVehicleManagementSetting' && foundSetting.allowLTA;

    const canApplyForFinancing = (function () {
        if (vehicle._kind !== VehicleKind.FinderVehicle) {
            throw new Error('Vehicle Kind is not Finder Vehicle');
        }

        return vehicle.listing.vehicle.condition.value === 'new' || (isLTAEnabled && !!vehicle.lta) || !isLTAEnabled;
    })();

    const dealer = await collections.dealers.findOne({ _id: dealerId });
    const paymentSetting = await getDealershipPaymentSetting(dealer._id, applicationModule, { collections });

    // this flag is to tell whether the user is coming from finder public test drive journey
    const stage = isTestDriveOnly
        ? ApplicationStage.Appointment
        : getApplicationInitialStage({
              dealer,
              paymentSetting,
              scenarios: applicationModule.scenarios,
              // With financing of request for financing, should be categorized as finance stage
              withFinancing: (canApplyForFinancing && configuration.withFinancing) || configuration.requestForFinancing,
              isFinancingOptional:
                  !canApplyForFinancing || applicationModule.financingPreference !== FinancingPreferenceValue.Mandatory,
              withInsurance: configuration.withInsurance,
              isInsuranceOptional: applicationModule.isInsuranceOptional,
              testDrive: configuration.testDrive,
          });

    // Check if there is insurance
    const withInsurance = !isTestDriveOnly ? getApplyForInsurance(configuration, applicationModule) : false;

    // Validate insurance inputs first before drafting any application
    const { insurancing } = await getInsuranceApplicationSetup({
        applicationModule,
        insurancingInputs,
        t,
        withInsurance,
    });

    const allowAddInsuranceStage = stage !== ApplicationStage.Insurance && withInsurance;
    const assigneeId =
        (applicationModule._type === ModuleType.FinderApplicationPublicModule
            ? getDealershipSettingId(dealerId, applicationModule.assignee)
            : null) ?? user?._id;

    const lead: Lead = {
        _id: new ObjectId(),
        kind: ApplicationKind.Finder,
        status: LeadStatus.Drafted,
        isLead: false,
        moduleId: applicationModule._id,
        vehicleId: vehicle._id,
        customerId: applicant._id,
        dealerId: dealer._id,
        isDraft: true,
        identifier: '',
        tradeInVehicle,
        documents: [],
        _versioning: getAdvancedVersioningForCreation(authoring),
        ...origins,
        languageId,
    };

    const application: FinderApplication = {
        _id: new ObjectId(),
        kind: ApplicationKind.Finder,
        moduleId: applicationModule._id,
        scenarios: isTestDriveOnly ? [ApplicationScenario.Appointment] : applicationModule.scenarios,
        stages: [stage, allowAddInsuranceStage && ApplicationStage.Insurance].filter(Boolean),
        ...(isTestDriveOnly
            ? {
                  appointmentStage: {
                      identifier: '',
                      status: ApplicationStatus.Drafted,
                      appointmentModuleId: applicationModule.appointmentModuleId,
                      bookingTimeSlot: {
                          _type: TimeSlotEnum.Appointment,
                          bookingLimit: 0,
                          slot: null,
                      },
                      assigneeId,
                  },
              }
            : getApplicationInitialStageDetails(
                  stage,
                  ApplicationStatus.Drafted,
                  assigneeId,
                  await retrieveFeatureProps(applicationModule._id)
              )),
        ...(allowAddInsuranceStage &&
            getApplicationInitialStageDetails(
                ApplicationStage.Insurance,
                ApplicationStatus.Drafted,
                assigneeId,
                await retrieveFeatureProps(applicationModule._id)
            )),
        // the application should be treated as a draft if there's no remote link sent
        isDraft: true,

        // define relationships
        applicantId: applicant._id,
        vehicleId: vehicle._id,
        bankId: financeProduct?.bankId,

        dealerId,
        promoCodeId: promoCode?._id,

        // start with empty remarks and no document
        remarks: '',
        documents: [],
        guarantorId: [],

        // spread origins
        ...origins,

        // provide initial versioning
        _versioning: getAdvancedVersioningForCreation(authoring),
        configuration: {
            ...configuration,
            // add check if we can apply for financing depending on finder vehicle
            withFinancing: canApplyForFinancing && configuration.withFinancing,
            isAffinAutoFinanceCentreRequired:
                isAffinBank && (configuration.withFinancing || configuration.requestForFinancing),
        },
        tradeInVehicle,
        financing,
        insurancing,
        referenceApplicationSuiteIds: [],
        languageId,

        withCustomerDevice: false,
        useMyinfo: {
            customer: false,
            guarantor: false,
        },

        leadId: lead._id,
    };

    const router = origins.routerId ? await loaders.routerById.load(origins.routerId) : null;

    // build up application journey
    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: applicant._versioning.createdAt,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
        testDriveRedirectUrl: await retrieveTestDriveRedirectionRouterAndEndpoint(application, router),
    };
    if (
        application.configuration.testDrive &&
        appointmentModule &&
        appointmentModule._type === ModuleType.AppointmentModule &&
        appointmentModule.hasTestDriveProcess
    ) {
        applicationJourney.isTestDriveProcessStarted = false;
        applicationJourney.testDriveSetting = {
            hasTestDriveSigning: appointmentModule.hasTestDriveSigning ?? false,
            signingModuleId: appointmentModule.signingModuleId,
        };
    }

    // insert the documents in database
    await collections.leads.insertOne(lead);
    await collections.applications.insertOne(application);
    await collections.applicationJourneys.insertOne(applicationJourney);

    // generate the trail
    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: applicant._versioning.createdAt,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    const leadTrail: LeadDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadDrafted,
        _date: applicant._versioning.createdAt,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    // register the trail
    await collections.auditTrails.insertMany([trail, leadTrail]);

    // finally execute the journey
    return executeFinderJourney({
        application,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });
};

export default buildRateLimiterMiddleware({ operation: 'draftFinderApplication' })(resolver);
