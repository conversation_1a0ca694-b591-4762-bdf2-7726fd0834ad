import { ApplicationKind, AuthorKind } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateTemporaryApplicationDocuments } from '../../../../database/helpers/applications';
import { generateJourneyToken } from '../../../../journeys';
import { consumeJourneyToken } from '../../../../journeys/generateJourneyToken';
import { Authoring, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { GraphQLMutationResolvers, ModuleType } from '../../definitions';
import { isValidTradeInVehicle } from './helpers';

const resolver: GraphQLMutationResolvers['updateStandardApplicationJourney'] = async (
    root,
    { token, configuration, tradeInVehicle, customerKind, saveDraft, financing },
    { getUser, loaders }
) => {
    const { collections } = await getDatabaseContext();

    // first read the token
    const { userId, applicationId, origin, leadId } = await consumeJourneyToken(token);

    // get the application
    const application = await collections.applications.findOne({ _id: applicationId, '_versioning.isLatest': true });

    if (!application) {
        // application is outdated, this token is not valid anymore
        throw new InvalidPermission();
    }

    if (application.kind !== ApplicationKind.Standard) {
        throw new InvalidInput({ $root: 'Application is not a standard application' });
    }

    const lead = await collections.leads.findOne({ _id: leadId });

    if (!lead) {
        // should not happen
        throw new InvalidInput({ leadId: 'Lead not found' });
    }

    // get the user from the GraphQl context
    const user = await getUser(true);

    if (userId && (!user || !user._id.equals(userId))) {
        // the user change, we may not proceed with it
        throw new InvalidPermission();
    }
    // get the application module
    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });

    if (!applicationModule || applicationModule._type !== ModuleType.StandardApplicationModule) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a standard application module' });
    }

    if (application.configuration?.isAffinAutoFinanceCentreRequired && !financing?.affinAutoFinanceCentre) {
        throw new InvalidInput({ affinAutoFinanceCentre: 'Affin Auto Finance Centre is mandatory' });
    }

    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id }
        : { kind: AuthorKind.Customer, customerId: application.applicantId };

    // update and cleanup for the temporarily uploaded application documents
    const documents = await updateTemporaryApplicationDocuments(application);

    if (!saveDraft) {
        const isValid = await isValidTradeInVehicle({ ...application, tradeInVehicle }, customerKind);
        if (!isValid) {
            throw new InvalidInput({ tradeInVehicle: 'Trade In Vehicle is mandatory' });
        }
    }

    const value = await collections.applications.findOneAndUpdate(
        { _id: applicationId },
        {
            $set: {
                documents,
                tradeInVehicle,
                configuration,
                '_versioning.updatedBy': getAuthorFromAuthoring(authoring),
                '_versioning.updatedAt': new Date(),
                ...(financing && { 'financing.affinAutoFinanceCentre': financing.affinAutoFinanceCentre }),
            },
        },
        { returnDocument: 'after' }
    );

    if (!value) {
        // should not happen
        throw new Error('Unexpected error');
    }

    // TODO: Can be refactor later on to make the function below more reusable
    if (application.leadId) {
        // Do cleanup also for lead's documents
        const lead = await loaders.leadById.load(application.leadId);

        await collections.leads.updateOne(
            { _id: lead._id, '_versioning.isLatest': true },
            { $set: { documents: lead.documents.map(document => ({ ...document, isTemporary: false })) } }
        );
    }

    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    const newToken = generateJourneyToken(lead, value, origin, company.sessionTimeout, user);

    return { token: newToken, application: value, lead };
};

export default resolver;
