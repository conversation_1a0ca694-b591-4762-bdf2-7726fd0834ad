import { isEmpty } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { ExternalLinkKind } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import createLoaders from '../../../../loaders';
import { consumeOTP } from '../../../../utils';
import { InvalidInput } from '../../../errors';
import { GraphQLMutationResolvers } from '../../definitions';
import { getToken } from './generateSalesOfferJourneyToken';

const resolver: GraphQLMutationResolvers['validateSalesOfferRemoteJourneyPasscode'] = async (
    root,
    { secret, code },
    { getTranslations, getUser }
) => {
    const { t } = await getTranslations(['errors']);
    const { collections } = await getDatabaseContext();

    const link = await collections.externalLinks.findOne({ secret });

    if (!link || link._kind !== ExternalLinkKind.SendSalesOffer) {
        throw new Error('Invalid Link.');
    }

    if (isEmpty(code)) {
        throw new InvalidInput({ $root: t('errors:emptyIdentity') });
    }

    const loaders = createLoaders();

    const reference = `${link.data.salesOfferId.toHexString()}_${link.data.featureKinds.join('_')}`;
    const salesOfferData = await consumeOTP<ObjectId>(code, reference, 'remoteJourney');
    if (!salesOfferData) {
        throw new InvalidInput({ $root: t('errors:invalidOTP') });
    }

    // get the user from the GraphQl context
    const user = await getUser(true);

    return getToken(link, user, collections, loaders);
};

export default resolver;
