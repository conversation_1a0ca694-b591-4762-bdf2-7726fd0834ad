import { ObjectId } from 'mongodb';
import { AuthorKind } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { isApplicationWithInsurance } from '../../../../database/helpers/applications';
import { createNewLeadVersion } from '../../../../database/helpers/leads';
import { ApplicationPolicyAction } from '../../../../permissions';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import amendmentAuditTrail from './submitChanges/amendmentAuditTrail';
import getUpdateHandlers from './submitChanges/updateHandlers/getUpdateHandlers';
import { isValidApplicationModuleForSubmitChange } from './submitChanges/updateHandlers/shared';
import { AmendableApplication } from './submitChanges/updateHandlers/types';

const mutate: GraphQLMutationResolvers['updateApplication'] = async (
    root,
    { applicationId, updates, stage, remarks, commentsToInsurer },
    { getUser, loaders, getPermissionController, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);
    const permissionController = await getPermissionController();

    const applicationFilter = await permissionController.applications.getFilterQueryForAction(
        ApplicationPolicyAction.Update,
        [stage]
    );

    const application = await collections.applications.findOne({
        $and: [{ _id: applicationId, '_versioning.isLatest': true }, applicationFilter],
    });

    if (!application) {
        throw new InvalidPermission();
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    if (!lead) {
        throw new InvalidInput({ $root: 'Invalid lead' });
    }

    // fetch the application journey
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });

    if (!journey || journey.isImmutable) {
        throw new InvalidPermission();
    }

    const module = await loaders.moduleById.load(application.moduleId);

    if (!isValidApplicationModuleForSubmitChange(module)) {
        throw new InvalidPermission();
    }

    // get the user from the GraphQl context
    const user = await getUser(true);

    const handlers = await getUpdateHandlers(updates, application, module, loaders, t, permissionController);

    const areUpdatesValid = await Promise.all(handlers.map(handler => handler.isValid(application))).then(checks =>
        checks.every(value => value === true)
    );

    if (!areUpdatesValid) {
        throw new InvalidInput({ $root: 'Invalid updates' });
    }

    const commentsToInsurerUpdate =
        commentsToInsurer && isApplicationWithInsurance(application) ? { commentsToInsurer } : null;

    // apply updates and get final application documents to insert
    const nextApplication = await handlers.reduce(
        (acc, handler) => acc.then(patchedApplication => handler.apply(patchedApplication, user)),
        Promise.resolve<AmendableApplication>({
            _id: new ObjectId(),
            ...application,
            _versioning: {
                ...application._versioning,
                updatedBy: { kind: AuthorKind.User, id: user._id },
                updatedAt: new Date(),
            },
            ...commentsToInsurerUpdate,
            remarks,
        })
    );

    // update previous application document
    await collections.applications.findOneAndUpdate(
        { _id: applicationId },
        { $set: { '_versioning.isLatest': false } }
    );

    nextApplication._id = new ObjectId();
    // insert new application document
    await collections.applications.insertOne(nextApplication);

    // insert new lead document
    // only update lead if customer changes
    if (!nextApplication.applicantId.equals(application.applicantId)) {
        const latestLead = await collections.leads.findOne({ '_versioning.suiteId': lead._versioning.suiteId });
        const newLead = {
            ...latestLead,
            _id: new ObjectId(),
            customerId: nextApplication.applicantId,
            _versioning: {
                ...latestLead._versioning,
                updatedBy: { kind: AuthorKind.User, id: user._id },
                updatedAt: new Date(),
            },
        };

        await createNewLeadVersion(newLead);
    }

    await amendmentAuditTrail(application, nextApplication, { collections, user, stage, loaders });

    return nextApplication;
};

export default requiresLoggedUser(mutate);
