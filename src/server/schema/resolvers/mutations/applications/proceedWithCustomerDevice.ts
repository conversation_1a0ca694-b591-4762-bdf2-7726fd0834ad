import Dayjs from 'dayjs';
import { isEmpty, isString, uniqWith } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import {
    ApplicationKind,
    ExternalLinkKind,
    ProceedWithCustomerLink,
    ModuleType,
    ApplicationProceedWithCustomerDeviceAuditTrail,
    AuthorKind,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getApplicationIdentifierFromApplicationModule } from '../../../../database/helpers/applications/shared';
import {
    getLocalCustomerFieldValueByKey,
    convertLocalCustomerGraphQLFields,
} from '../../../../database/helpers/customers';
import { consumeJourneyToken } from '../../../../journeys/generateJourneyToken';
import { mainQueue } from '../../../../queues';
import { getApplicationLogStages, getApplicationStages } from '../../../../utils/application';
import { Authoring, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import { AuditTrailKind, GraphQLMutationResolvers, LocalCustomerFieldKey } from '../../definitions';

const mutation: GraphQLMutationResolvers['proceedWithCustomerDevice'] = async (
    root,
    { token, fields },
    { loaders }
) => {
    const { collections } = await getDatabaseContext();

    let customerFields = convertLocalCustomerGraphQLFields(fields);

    const email = getLocalCustomerFieldValueByKey(customerFields, LocalCustomerFieldKey.Email);

    if (!email || !isString(email) || isEmpty(email)) {
        throw new Error('Customer Email is not added');
    }
    const { applicationId, userId } = await consumeJourneyToken(token);

    // get the module
    const application = await collections.applications.findOne({ _id: new ObjectId(applicationId) });

    if (!application) {
        throw new InvalidInput({ applicationId: 'Application not found' });
    }

    if (
        !(
            application.kind === ApplicationKind.Standard ||
            application.kind === ApplicationKind.Event ||
            application.kind === ApplicationKind.Configurator ||
            application.kind === ApplicationKind.Finder
        )
    ) {
        throw new Error('Application Kind not supported');
    }

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });

    if (
        !applicationModule ||
        (applicationModule._type !== ModuleType.StandardApplicationModule &&
            applicationModule._type !== ModuleType.EventApplicationModule &&
            applicationModule._type !== ModuleType.FinderApplicationPrivateModule &&
            applicationModule._type !== ModuleType.ConfiguratorModule)
    ) {
        throw new Error('Application Module not found');
    }

    const identifierUpdates = {};
    const stages = getApplicationStages(application);
    await Promise.all(
        stages.map(async ({ stage, value, path }) => {
            if (!value?.identifier) {
                identifierUpdates[`${path}.identifier`] = await getApplicationIdentifierFromApplicationModule(
                    stage,
                    applicationModule
                );
            }
        })
    );
    await collections.applications.findOneAndUpdate(
        { _id: application._id },
        {
            $set: {
                ...identifierUpdates,
                withCustomerDevice: true, // set remote link to true
                isDraft: false,
            },
        }
    );

    // prepare the link
    const externalLink = generateExternalLink(
        application.kind,
        application._id,
        application._versioning.suiteId,
        application.routerId,
        application.endpointId
    );

    const customer = await collections.customers.findOne({ _id: application.applicantId });

    // IF application is coming from launchpad, then need to keep the existing value from launchpad
    if (application.kind === ApplicationKind.Standard || application.kind === ApplicationKind.Finder) {
        const leadOfApplication = await loaders.leadById.load(application.leadId);
        if (leadOfApplication.kind === ApplicationKind.Launchpad) {
            const applicantDataFromLead = await loaders.customerById.load(leadOfApplication.customerId);
            const mergedKycPresetIdsWithLeadApplicant = uniqWith(
                (a, b) => a.equals(b),
                [...customer.kycPresetIds, ...applicantDataFromLead.kycPresetIds]
            );

            customer.kycPresetIds = mergedKycPresetIdsWithLeadApplicant;

            const mergedFieldsWithLeadApplicant = uniqWith(
                (a, b) => a.key === b.key,
                [...customerFields, ...applicantDataFromLead.fields]
            );

            customerFields = mergedFieldsWithLeadApplicant;
        }
    }

    // update the customer in the database
    await collections.customers.findOneAndUpdate({ _id: customer._id }, { $set: { fields: customerFields } });

    // create the link in database
    await collections.externalLinks.insertOne(externalLink);

    // create audit trail
    const authoring: Authoring = { kind: AuthorKind.User, userId };

    const trail: ApplicationProceedWithCustomerDeviceAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationProceedWithCustomerDevice,
        _date: application._versioning.createdAt,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationProceedWithCustomerDevice),
        author: getAuthorFromAuthoring(authoring),
    };

    await collections.auditTrails.insertOne(trail);
    // trigger a task to send the email
    await mainQueue.add({
        type: 'sendProceedWithCustomerEmail',
        linkId: externalLink._id,
        customerEmail: email,
    });

    return true;
};

const generateExternalLink = (
    kind: ApplicationKind,
    applicationId: ObjectId,
    applicationSuiteId: ObjectId,
    routerId: ObjectId,
    endpointId: ObjectId
): ProceedWithCustomerLink => ({
    _id: new ObjectId(),
    _kind: ExternalLinkKind.ProceedWithCustomer,
    secret: nanoid(),
    data: {
        applicationId,
        applicationSuiteId,
        routerId,
        endpointId,
    },
    expiresAt: Dayjs().add(3, 'days').toDate(),
    deleteOnFetch: false,
});

export default buildRateLimiterMiddleware({ operation: 'proceedWithCustomerDevice' })(mutation);
