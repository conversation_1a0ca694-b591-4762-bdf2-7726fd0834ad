import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { ObjectId } from 'mongodb';
import {
    ApplicationKind,
    ApplicationStage,
    AuditTrailKind,
    AuthorKind,
    InventoryKind,
    MobilityApplication,
    StockInventoryKind,
    getKYCPresetsForApplication,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { isValidStatusToAmendOrCancel } from '../../../../database/helpers/applications';
import getInitialAgreementsForApplication from '../../../../database/helpers/getInitialAgreementsForApplication';
import { consumeJourneyToken, generateJourneyToken } from '../../../../journeys';
import { ApplicantAgreementsStep, ApplicantKYCStep } from '../../../../journeys/common';
import { getAgreementDocument } from '../../../../journeys/helper';
import { executeJourney } from '../../../../journeys/legacyJourney';
import { attachAgreementDocument, sendMobilityApplicationEmails } from '../../../../queues';
import { InvalidInput } from '../../../errors';
import {
    ConsentsAndDeclarationsPurpose,
    GraphQLMutationResolvers,
    GraphQLPeriodPayload,
    ModuleType,
} from '../../definitions';
import { getLocationFromInput } from './draftMobilityApplication/shared';
import { removeOldReservation } from './updateMobilityApplication';

const validatePeriod = async (t: TFunction, application: MobilityApplication, period: GraphQLPeriodPayload) => {
    const {
        mobilityBookingDetails: { period: original, inventoryStockId },
    } = application;

    const { collections } = await getDatabaseContext();

    const inventory = await collections.inventories.findOne({
        _kind: InventoryKind.MobilityInventory,
        'stocks._kind': StockInventoryKind.MobilityStock,
        'stocks._id': inventoryStockId,
    });

    const stock =
        inventory?._kind === InventoryKind.MobilityInventory
            ? inventory.stocks.find(
                  ({ _kind, _id }) => _kind === StockInventoryKind.MobilityStock && _id.equals(inventoryStockId)
              )
            : null;

    if (stock?._kind !== StockInventoryKind.MobilityStock) {
        throw new InvalidInput({ $root: t('errors:mobilityApplication.inventoryNotFound') });
    }

    const otherReservedApplicationSuiteIds = stock.reservations
        .filter(({ applicationId }) => !applicationId.equals(application._versioning.suiteId))
        .map(({ applicationId }) => applicationId);

    if (otherReservedApplicationSuiteIds.length > 0) {
        const conflicts = await collections.applications.countDocuments({
            kind: ApplicationKind.Mobility,
            '_versioning.isLatest': true,
            '_versioning.suiteId': { $in: otherReservedApplicationSuiteIds },
            'mobilityBookingDetails.period.end': { $gte: period.start },
            'mobilityBookingDetails.period.start': { $lte: period.end },
        });

        if (conflicts > 0) {
            throw new InvalidInput({
                $root: t('errors:mobilityApplication.periodNotAvailable'),
            });
        }
    }
};

const validateBookingPeriod = (
    period: GraphQLPeriodPayload,
    mobilityBookingDetails: MobilityApplication['mobilityBookingDetails']
) => {
    const minimumBookingDays = dayjs(mobilityBookingDetails.period.end).diff(
        dayjs(mobilityBookingDetails.period.start),
        'day'
    );

    return Math.abs(dayjs(period.start).diff(dayjs(period.end), 'day')) >= minimumBookingDays;
};

const resolver: GraphQLMutationResolvers['amendMobilityApplication'] = async (
    root,
    { token, period, location: locationInput },
    { getUser, getTranslations, loaders }
) => {
    const { applicationId, origin } = await consumeJourneyToken(token);

    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);

    const application = await collections.applications.findOne({
        _id: applicationId,
        kind: ApplicationKind.Mobility,
        '_versioning.isLatest': true,
    });

    if (application?.kind !== ApplicationKind.Mobility) {
        throw new Error('invalid token');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    if (!lead) {
        throw new Error('Lead not found');
    }

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    if (!applicationModule || applicationModule._type !== ModuleType.MobilityModule) {
        throw new Error('ModuleType not support for amend');
    }

    if (!isValidStatusToAmendOrCancel(application.mobilityStage?.status)) {
        throw new Error(`Application with status ${application.mobilityStage?.status} cannot be amended`);
    }

    // check for period
    await validatePeriod(t, application, period);

    const validityAmendmentDate = validateBookingPeriod(period, application.mobilityBookingDetails);

    if (!validityAmendmentDate) {
        throw new Error('unable to book less than previous booking date');
    }

    // create a new version
    const user = await getUser(true);

    const location = getLocationFromInput(locationInput);

    const updatedBy = user
        ? { kind: AuthorKind.User, id: user._id }
        : { kind: AuthorKind.Customer, id: application.applicantId };

    const nextApplication: MobilityApplication = {
        ...application,
        mobilityBookingDetails: {
            ...application.mobilityBookingDetails,
            period,
            location,
        },
        _id: new ObjectId(),
        isDraft: false,
        _versioning: {
            ...application._versioning,
            updatedBy,
            updatedAt: new Date(),
        },
    };

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });

    journey.resubmission = {
        stage: ApplicationStage.Mobility,
        previousLocationId: application.mobilityBookingDetails.location._id,
        previousOperatorId: application.mobilityStage.assigneeId,
        previousApplicationId: application._id,
        author: updatedBy,
    };

    // Check kyc
    if (journey.applicantKYC) {
        const newKycPresets = await getKYCPresetsForApplication(nextApplication, 'local');
        const customerModule = await loaders.customerModuleById.load(applicationModule.customerModuleId);
        const newCustomer = await collections.customers.findOne({ _id: nextApplication.applicantId });
        if (
            !(await ApplicantKYCStep.validateInputFields(
                customerModule.kycFields.sort((a, b) => a.order - b.order),
                newKycPresets,
                newCustomer,
                nextApplication
            ))
        ) {
            journey.applicantKYC.completed = false;
        }
    }

    // Check agreement
    if (journey.applicantAgreements) {
        const newApplicantAgreements = await getInitialAgreementsForApplication(nextApplication, 'local');

        // Get checked consent ids
        const checkedConsentIds = journey.applicantAgreements.agreements
            .filter(item => item.isAgreed)
            .map(item => item.consentId);

        // Build up new agreements, check it if previously was checked
        const newJourneyAgreements = newApplicantAgreements.map(agreement => ({
            ...agreement,
            isAgreed: checkedConsentIds.some(item => item.equals(agreement.consentId)),
        }));

        journey.applicantAgreements.agreements = newJourneyAgreements;

        if (journey.applicantKYC) {
            journey.applicantKYC.completed =
                journey.applicantKYC.completed &&
                ApplicantAgreementsStep.checkAgreementsCompletion(journey.applicantAgreements, [
                    ConsentsAndDeclarationsPurpose.KYC,
                ]);
        }
    }

    // before we add the add the latest amended reservation we need to remove the existing reservation
    await removeOldReservation(application);

    // add the amended reservation
    await collections.inventories.findOneAndUpdate(
        { 'stocks._id': application.mobilityBookingDetails.inventoryStockId },
        {
            $push: {
                'stocks.$[stock].reservations': {
                    applicationId: application._versioning.suiteId,
                    reservationExpiryDate: period.end,
                },
            },
        },
        { arrayFilters: [{ 'stock._id': application.mobilityBookingDetails.inventoryStockId }] }
    );

    await collections.applications.updateOne({ _id: application._id }, { $set: { '_versioning.isLatest': false } });

    // Update also the one coming from 'updateMobilityApplication'
    await collections.applications.updateMany(
        {
            kind: ApplicationKind.Mobility,
            '_versioning.suiteId': application._versioning.suiteId,
            '_versioning.isLatest': true,
        },
        { $set: { '_versioning.isLatest': false } }
    );

    const isLocationSame = location._id.equals(application.mobilityBookingDetails.location._id);
    const newAssigneeId = (() => {
        // If location is still the same, use the same assignee
        if (isLocationSame) {
            return application.mobilityStage.assigneeId;
        }

        if (locationInput.isHomeDelivery) {
            return applicationModule.homeDelivery.assigneeId;
        }

        const locationId = locationInput.pickup?.id;
        if (locationId) {
            const { locations } = applicationModule;

            return locations.find(location => location._id.equals(locationId))?.assigneeId;
        }

        return null;
    })();

    nextApplication.mobilityStage.assigneeId = newAssigneeId;
    await collections.applications.insertOne(nextApplication);

    // check if it's needed to fill the details first
    if (!journey.applicantKYC.completed) {
        journey.isReceived = false;

        await collections.applicationJourneys.findOneAndUpdate(
            { applicationSuiteId: journey.applicationSuiteId },
            { $set: journey },
            { returnDocument: 'after' }
        );

        const result = await executeJourney({
            application: nextApplication,
            lead,
            identifier: 'drafting',
            payload: null,
            user,
            origin: 'system',
            skipCompletedSteps: true,
            resubmitStage: ApplicationStage.Mobility,
        });

        return result;
    }

    // generate pdf
    const agreementPdf = getAgreementDocument(nextApplication);

    if (!agreementPdf) {
        // must attach it
        await attachAgreementDocument(nextApplication, lead);
    }

    // store the updated journey for resubmission part
    const updatedJourney = await collections.applicationJourneys.findOneAndUpdate(
        { _id: journey._id },
        { $set: { resubmission: journey.resubmission } },
        { returnDocument: 'after' }
    );

    // Add amend audit trail
    // Since it's not inside journey
    await collections.auditTrails.insertOne({
        _id: new ObjectId(),
        _kind: AuditTrailKind.BookingAmended,
        _date: new Date(),
        applicationId: nextApplication._id,
        applicationSuiteId: nextApplication._versioning.suiteId,
        stages: [ApplicationStage.Mobility],
    });

    await sendMobilityApplicationEmails(nextApplication, updatedJourney);

    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    return {
        application: nextApplication,
        lead,
        token: generateJourneyToken(lead, nextApplication, origin, company.sessionTimeout, user),
    };
};

export default resolver;
