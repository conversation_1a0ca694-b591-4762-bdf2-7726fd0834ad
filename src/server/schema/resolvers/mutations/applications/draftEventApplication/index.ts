import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationCampaignValues,
    ApplicationDraftedAuditTrail,
    ApplicationJourney,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    CustomerKind,
    Event,
    EventApplication,
    LocalCustomer,
    LocalVariant,
    VehicleKind,
    LeadStatus,
    LeadDraftedAuditTrail,
    EventLead,
} from '../../../../../database';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    retrieveFeatureProps,
    getApplicationInitialStage,
} from '../../../../../database/helpers/applications';
import { convertLocalCustomerGraphQLFields } from '../../../../../database/helpers/customers';
import { hasPaymentScenario, hasVisitAppointmentScenario } from '../../../../../journeys/common/helpers';
import { executeEventJourney } from '../../../../../journeys/eventJourney';
import { ModulePolicyAction } from '../../../../../permissions';
import { getApplicationLogStages } from '../../../../../utils/application';
import { getDealershipPaymentSetting } from '../../../../../utils/getDealershipPaymentSetting';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../../errors';
import { buildRateLimiterMiddleware } from '../../../../middlewares';
import { GraphQLApplicationCampaignValuesInput, GraphQLMutationResolvers, ModuleType } from '../../../definitions';
import { retrieveUTMParameterQueryParameter } from '../../events/updateEvent';
import { isValidTradeInVehicle, validateEndpoint } from '../helpers';
import { getEventAssigneeId } from './shared';

const retrieveAllStages = async (
    stages: ApplicationStage[],
    applicationModuleId: ObjectId,
    assigneeId: ObjectId,
    event: Event
) => {
    const appointmentModuleStageProps = await retrieveFeatureProps(applicationModuleId, event);

    const details = stages.map(stage =>
        getApplicationInitialStageDetails(stage, ApplicationStatus.Drafted, assigneeId, appointmentModuleStageProps)
    );

    return Object.assign({}, ...details);
};

const getCampaignValues = (
    campaignValuesInput: GraphQLApplicationCampaignValuesInput,
    eventDetails: Event
): ApplicationCampaignValues => {
    const { utmParametersSettings, enableDynamicUtmTracking } = eventDetails;
    const { defaultValue, overrides = [] } = utmParametersSettings;

    if (enableDynamicUtmTracking) {
        const target = overrides.find(setting => {
            const parameters = retrieveUTMParameterQueryParameter(setting);

            return (
                parameters?.utm_medium === campaignValuesInput?.utmMedium &&
                parameters?.utm_campaign === campaignValuesInput?.utmCampaign &&
                parameters?.utm_source === campaignValuesInput?.utmSource
            );
        });

        if (target) {
            const url = new URL(target.utmUrl);
            const utmUrl = url.searchParams;

            return {
                capCampaignId: target.capCampaignId,
                capLeadSource: target.capLeadSource,
                capLeadOrigin: target.capLeadOrigin,
                utmCampaign: utmUrl.get('utm_campaign'),
                utmMedium: utmUrl.get('utm_medium'),
                utmSource: utmUrl.get('utm_source'),
                utmUrl: target.utmUrl,
            };
        }
    }

    return {
        ...campaignValuesInput,
        capCampaignId: defaultValue?.capCampaignId,
        capLeadSource: defaultValue?.capLeadSource,
        capLeadOrigin: defaultValue?.capLeadOrigin,
    };
};

const resolver: GraphQLMutationResolvers['draftEventApplication'] = async (
    root,
    {
        moduleId,
        customer: customerInputs,
        vehicle: vehicleInputs,
        eventId,
        configuration,
        tradeInVehicle,
        endpointId,
        dealerId,
        languageId,
        customerKind,
        customizedFields,
        remarks,
        withMyinfo,
        withPorscheId,
        capValues,
        campaignValues: campaignValuesInput,
        originSalesConsultantId,
    },
    { getUser, getPermissionController, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    // get the module
    const applicationModule = await collections.modules.findOne({ _id: moduleId });

    if (!applicationModule || applicationModule._type !== ModuleType.EventApplicationModule) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a event application module' });
    }

    // get the current user which might come in handy
    const user = await getUser(true);

    const event = await collections.events.findOne({ _id: eventId });
    if (!event || !event.moduleId.equals(moduleId)) {
        throw new InvalidInput({ eventId: 'Event is not valid' });
    }

    // For private access
    if (event.privateAccess) {
        // Check if user is there and permission for creating an application from event is there
        if (
            !user ||
            !permissionController.modules.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication)
        ) {
            throw new InvalidPermission();
        }
    }

    // get the customer
    const customerModule = await collections.modules.findOne({ _id: applicationModule.customerModuleId });

    // get router/endpoint when provided
    const origins = endpointId ? await validateEndpoint(endpointId, applicationModule) : null;

    // build up versioning
    // customer ID will be defined later on if the kind is Customer
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    let vehicle: LocalVariant;
    if (vehicleInputs.existingSimpleLocalVehicleId) {
        // get the vehicle modules
        const vehicleModule = await collections.modules.findOne({ _id: applicationModule.vehicleModuleId });

        vehicle =
            vehicleModule._type === ModuleType.SimpleVehicleManagement
                ? ((await collections.vehicles.findOne({
                      _id: vehicleInputs.existingSimpleLocalVehicleId,
                      _kind: VehicleKind.LocalVariant,
                      moduleId: vehicleModule._id,
                      isActive: true,
                      isDeleted: false,
                      '_versioning.isLatest': true,
                  })) as LocalVariant)
                : null;

        if (!vehicle) {
            // something was wrong with the vehicle inputs
            throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
        }
    }

    // get/create the applicant (customer object)
    const applicant = await (async () => {
        if (customerModule._type === ModuleType.LocalCustomerManagement) {
            if (customerInputs.existingLocalCustomer) {
                if (authoring.kind === AuthorKind.Customer) {
                    // only authenticated user may draft with existing customers
                    throw new InvalidPermission();
                }

                // get the customer
                return collections.customers.findOne({
                    _id: customerInputs.existingLocalCustomer,
                    moduleId: customerModule._id,
                    // todo apply permissions to limit available customers
                    '_versioning.isLatest': true,
                });
            }

            if (customerInputs.newLocalCustomer) {
                const newCustomerId = new ObjectId();

                if (authoring.kind === AuthorKind.Customer) {
                    // update the authoring object to match the newly created customer ID
                    authoring.customerId = newCustomerId;
                }

                // create a new customer
                const customer: LocalCustomer = {
                    _id: newCustomerId,
                    _kind: CustomerKind.Local,
                    moduleId: customerModule._id,
                    kycPresetIds: [],
                    fields: convertLocalCustomerGraphQLFields(customerInputs.newLocalCustomer.fields),
                    isDeleted: false,
                    _versioning: getAdvancedVersioningForCreation(authoring),
                };

                // insert the customer in the database
                await collections.customers.insertOne(customer);

                return customer;
            }
        }

        return null;
    })();

    if (!applicant) {
        // something was wrong with the customer inputs
        throw new InvalidInput({ customer: 'Invalid customer inputs' });
    }

    const dealer = await collections.dealers.findOne({ _id: dealerId });
    const paymentSetting = await getDealershipPaymentSetting(dealer._id, event, { collections });

    const stage = getApplicationInitialStage({
        dealer,
        paymentSetting,
        scenarios: event.scenarios,
        withFinancing: false,
        isFinancingOptional: false,
        withInsurance: false,
        isInsuranceOptional: false,
        testDrive: configuration.testDrive,
        visitAppointment: configuration.visitAppointment,
    });

    const assigneeId = await getEventAssigneeId({
        loaders,
        event,
        dealerId,
        user,
        salesPersonAlias: capValues?.salesPersonId,
        originSalesConsultantId,
    });

    const hasPayment = hasPaymentScenario(event.scenarios) && !isNil(paymentSetting);

    const hasVisitAppointment = hasVisitAppointmentScenario(event.scenarios) && configuration.visitAppointment;

    const concatenatedStages = Array.from(
        new Set(
            [
                stage,
                hasPayment && ApplicationStage.Reservation,
                hasVisitAppointment && ApplicationStage.VisitAppointment,
            ].filter(Boolean)
        )
    );

    const stageDetails = await retrieveAllStages(concatenatedStages, applicationModule._id, assigneeId, event);

    const campaignValues = getCampaignValues(campaignValuesInput, event);

    const lead: EventLead = {
        _id: new ObjectId(),
        kind: ApplicationKind.Event,
        status: LeadStatus.Drafted,
        customizedFields,
        isLead: false,
        moduleId: applicationModule._id,
        vehicleId: vehicle?._id,
        customerId: applicant._id,
        dealerId: dealer._id,
        isDraft: true,
        eventId,
        identifier: '',
        tradeInVehicle,
        campaignValues,
        documents: [],
        _versioning: getAdvancedVersioningForCreation(authoring),
        ...origins,
        languageId,
        capValues,
        originSalesConsultantId,
    };

    const application: EventApplication = {
        _id: new ObjectId(),
        kind: ApplicationKind.Event,
        moduleId: applicationModule._id,
        scenarios: event.scenarios,
        stages: concatenatedStages,
        ...stageDetails,

        // the application should be treated as a draft if there's no remote link sent
        isDraft: true,

        // define relationships
        applicantId: applicant._id,
        vehicleId: vehicle?._id,
        eventId,

        dealerId,

        // Save the remarks
        remarks: remarks ?? '',

        // start no document
        documents: [],

        // spread origins
        ...origins,
        guarantorId: [],

        // provide initial versioning
        _versioning: getAdvancedVersioningForCreation(authoring),
        configuration,
        tradeInVehicle,
        withCustomerDevice: false,
        referenceApplicationSuiteIds: [],
        languageId,
        useMyinfo: {
            customer: false,
            guarantor: false,
        },
        customizedFields,
        campaignValues,

        leadId: lead._id,
    };

    const isTradeInVehicleValid =
        withMyinfo || withPorscheId ? true : await isValidTradeInVehicle(application, customerKind);

    if (!isTradeInVehicleValid) {
        throw new InvalidInput({ tradeInVehicle: 'Trade In Vehicle is mandatory' });
    }

    // build up application journey
    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: applicant._versioning.createdAt,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
        originSalesConsultantId,
    };

    const appointmentModule = applicationModule.appointmentModuleId
        ? await collections.modules.findOne({
              _id: applicationModule.appointmentModuleId,
              _type: ModuleType.AppointmentModule,
          })
        : null;

    if (
        application.configuration.testDrive &&
        appointmentModule &&
        appointmentModule._type === ModuleType.AppointmentModule &&
        appointmentModule.hasTestDriveProcess
    ) {
        applicationJourney.isTestDriveProcessStarted = false;
        applicationJourney.testDriveSetting = {
            hasTestDriveSigning: appointmentModule.hasTestDriveSigning,
            signingModuleId: appointmentModule.signingModuleId,
        };
    }

    // insert the documents in database
    await collections.leads.insertOne(lead);
    await collections.applications.insertOne(application);
    await collections.applicationJourneys.insertOne(applicationJourney);

    // generate the trail
    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: applicant._versioning.createdAt,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    const leadTrail: LeadDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadDrafted,
        _date: applicant._versioning.createdAt,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    // register the trail
    await collections.auditTrails.insertMany([trail, leadTrail]);

    // finally execute the journey
    return executeEventJourney({
        application,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });
};

export default buildRateLimiterMiddleware({ operation: 'draftEventApplication' })(resolver);
