import { head } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationCustomerAgreedOnCnDAuditTrail,
    ApplicationJourney,
    ApplicationKind,
    ApplicationSharedAuditTrail,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    CustomerKind,
    Lead,
    LeadSharedAuditTrail,
    LeadStatus,
    LocalCustomer,
    ModuleType,
    StandardApplication,
    VehicleKind,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getApplicationIdentifierFromApplicationModule } from '../../../../database/helpers/applications/shared';
import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import getInitialAgreementsForApplication from '../../../../database/helpers/getInitialAgreementsForApplication';
import { getVehiclePrice } from '../../../../database/helpers/vehicles';
import { mainQueue } from '../../../../queues';
import { getApplicationLogStages } from '../../../../utils/application';
import {
    validateApplicationFinancing,
    validateApplicationInsurancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, validateEndpoint } from './helpers';

const resolver: GraphQLMutationResolvers['shareStandardApplication'] = async (
    root,
    {
        moduleId,
        customer: customerInputs,
        vehicle: vehicleInputs,
        configuration,
        financing: financingInputs,
        insurancing: insurancingInputs,
        dealerId,
        promoCodeId,
        endpointId,
        agreedConsents,
        languageId,
    },
    { getUser, getTranslations, ip }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);
    const now = new Date();

    if (vehicleInputs.length !== financingInputs.length) {
        throw new InvalidInput({ $root: 'Invalid financing and/or vehicle input' });
    }

    // get the module
    const applicationModule = await collections.modules.findOne({ _id: moduleId });

    if (!applicationModule || applicationModule._type !== ModuleType.StandardApplicationModule) {
        throw new InvalidInput({ moduleId: 'Module is not a standard application module' });
    }

    /* prepare customer info */
    const customerModule = await collections.modules.findOne({ _id: applicationModule.customerModuleId });

    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        // something was wrong with the customer inputs
        throw new InvalidInput({ customer: 'Invalid customer module' });
    }

    const user = await getUser(true);
    const loggedInUser = !!user;

    // build up versioning
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    // build customer document
    // get/create the applicant (customer object)
    const applicant = await (async () => {
        if (customerModule._type === ModuleType.LocalCustomerManagement) {
            if (customerInputs.existingLocalCustomer) {
                throw new InvalidInput({ customer: 'Customer already existed' });
            }

            if (customerInputs.newLocalCustomer) {
                const newCustomerId = new ObjectId();
                if (authoring.kind === AuthorKind.Customer) {
                    // update the authoring object to match the newly created customer ID
                    authoring.customerId = newCustomerId;
                }

                // create a new customer
                const customer: LocalCustomer = {
                    _id: newCustomerId,
                    _kind: CustomerKind.Local,
                    moduleId: customerModule._id,
                    kycPresetIds: [],
                    fields: convertLocalCustomerGraphQLFields(customerInputs.newLocalCustomer.fields),
                    isDeleted: false,
                    _versioning: getAdvancedVersioningForCreation(authoring),
                };

                // insert the customer in the database
                await collections.customers.insertOne(customer);

                return customer;
            }
        }

        return null;
    })();

    if (!applicant) {
        // something was wrong with the customer inputs
        throw new InvalidInput({ customer: 'Invalid customer inputs' });
    }

    const isComparison = vehicleInputs.length > 1;

    // Get company details of application
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    /* prepare vehicle info */
    const vehicleModule = await collections.modules.findOne({ _id: applicationModule.vehicleModuleId });

    if (vehicleModule._type !== ModuleType.SimpleVehicleManagement) {
        throw new InvalidInput({ vehicle: 'Invalid vehicle module' });
    }

    // get/create the vehicle
    const vehicle = await (async () => {
        if (vehicleModule._type === ModuleType.SimpleVehicleManagement) {
            if (vehicleInputs[0].existingSimpleLocalVehicleId) {
                return collections.vehicles.findOne({
                    _id: vehicleInputs[0].existingSimpleLocalVehicleId,
                    _kind: VehicleKind.LocalVariant,
                    moduleId: vehicleModule._id,
                    isActive: true,
                    isDeleted: false,
                    '_versioning.isLatest': true,
                });
            }
        }

        return null;
    })();

    if (!vehicle) {
        // something was wrong with the vehicle inputs
        throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
    }

    const promoCode = promoCodeId
        ? await getValidPromoCode(promoCodeId, moduleId, dealerId, vehicle._versioning.suiteId, t)
        : null;

    const withFinancing = getApplyForFinancing(configuration, applicationModule);

    // validate the financing settings
    const applicationFinancings = await Promise.all(
        financingInputs.map(financingInput =>
            validateApplicationFinancing(
                applicationModule,
                financingInput,
                t,
                undefined,
                null,
                applicationModule.showFinanceCalculator
            )
        )
    );

    const { financing, financeProduct } = head(applicationFinancings);

    const origins = endpointId ? await validateEndpoint(endpointId, applicationModule) : null;

    // Validation for AN-2652 : To validating incoming financing values
    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        // Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice: getVehiclePrice(vehicle),
            financing,
            includeDealerOptionsForFinancing: applicationModule.includeDealerOptionsForFinancing,
            withFinancing,
            promoCode,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    const applicationInsuranceProducts = await Promise.all(
        insurancingInputs.map(insuranceProductInput =>
            validateApplicationInsurancing(applicationModule, insuranceProductInput, t, undefined)
        )
    );

    const insuranceProducts = applicationInsuranceProducts.flatMap(
        applicationInsurance => applicationInsurance.insuranceProduct
    );
    const { insurancing } = head(applicationInsuranceProducts);

    const lead: Lead = {
        _id: new ObjectId(),
        kind: ApplicationKind.Standard,
        status: LeadStatus.Shared,
        isLead: false,
        moduleId: applicationModule._id,
        vehicleId: vehicle._id,
        customerId: applicant._id,
        isDraft: false,
        dealerId,
        identifier: await getApplicationIdentifierFromApplicationModule(ApplicationStage.Lead, applicationModule),
        assigneeId: user?._id ?? null,
        tradeInVehicle: [],
        documents: [],
        _versioning: getAdvancedVersioningForCreation(authoring),
    };

    // create new application
    const application: StandardApplication = {
        _id: new ObjectId(),
        kind: ApplicationKind.Standard,
        moduleId: applicationModule._id,
        scenarios: applicationModule.scenarios,
        stages: [ApplicationStage.Lead],

        // the application should be treated as a draft if there's no remote link sent
        isDraft: false,

        // define relationships
        bankId: financeProduct?.bankId,
        applicantId: applicant._id,
        vehicleId: vehicle._id,
        dealerId,

        // push financing and application configurations
        financing,
        insurancing,
        // share pdf actions use configuration.tradeIn to display trade in field in ShareApplicationPdf
        configuration: {
            withFinancing: configuration.withFinancing,
            tradeIn: configuration.tradeIn,
            testDrive: false,
            withInsurance: configuration.withInsurance,
        },
        tradeInVehicle: [],

        // start with empty remarks and no document
        remarks: '',
        documents: [],

        ...origins,
        // provide initial versioning
        _versioning: getAdvancedVersioningForCreation(authoring),
        withCustomerDevice: false,
        referenceApplicationSuiteIds: [],
        languageId,
        useMyinfo: {
            customer: false,
            guarantor: false,
        },
        promoCodeId,
        leadId: lead._id,
    };

    const initialAgreements = await getInitialAgreementsForApplication(application, 'local');

    const newAgreements: ObjectId[] = [];

    const agreements = initialAgreements.map(item => {
        if (item.isAgreed) {
            // we cannot remove the agreement as it's already agreed
            return item;
        }

        const isAgreed = agreedConsents.some(agreement => agreement.id.equals(item.consentId));

        if (isAgreed) {
            newAgreements.push(item.consentId);
        }

        if (item.platformsAgreed) {
            // fetch the corresponding agreed consents
            const marketingAgreement = agreedConsents.find(agreement => agreement.id.equals(item.consentId));

            return {
                ...item,
                isAgreed,
                date: now,
                platformsAgreed: marketingAgreement?.platformsAgreed || item.platformsAgreed,
            };
        }

        return { ...item, isAgreed, date: now };
    });

    if (newAgreements.length) {
        const { collections } = await getDatabaseContext();
        const stages = getApplicationLogStages(application, AuditTrailKind.ApplicationCustomerAgreedOnCnD);
        await collections.auditTrails.insertMany(
            newAgreements.map(
                (item): ApplicationCustomerAgreedOnCnDAuditTrail => ({
                    _id: new ObjectId(),
                    _date: now,
                    _kind: AuditTrailKind.ApplicationCustomerAgreedOnCnD,
                    applicationId: application._id,
                    applicationSuiteId: application._versioning.suiteId,
                    stages,
                    customerKind: 'applicant',
                    consentId: item,
                    author: user
                        ? { kind: AuthorKind.User, id: user._id }
                        : { kind: AuthorKind.Customer, id: application.applicantId },
                    ip,
                })
            )
        );
    }

    await collections.leads.insertOne(lead);
    await collections.applications.insertOne(application);

    const trail: ApplicationSharedAuditTrail = {
        _id: new ObjectId(),
        _date: applicant._versioning.createdAt,
        _kind: AuditTrailKind.ApplicationShared,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationShared),
        author: getAuthorFromAuthoring(authoring),
    };

    const leadTrail: LeadSharedAuditTrail = {
        _id: new ObjectId(),
        _date: applicant._versioning.createdAt,
        _kind: AuditTrailKind.LeadShared,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        applicantAgreements: {
            moduleId: applicationModule.agreementsModuleId,
            agreements,
        },
        updatedAt: applicant._versioning.createdAt,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
    };

    await collections.applicationJourneys.insertOne(applicationJourney);

    await collections.auditTrails.insertMany([trail, leadTrail]);

    if (!isComparison) {
        await mainQueue.add({ type: 'onShareApplication', applicationId: application._id, userId: user?._id });
    } else {
        const vehicleIds = vehicleInputs.map(vehicle => vehicle.existingSimpleLocalVehicleId);

        await mainQueue.add({
            type: 'onComparisonShareApplication',
            applicationId: application._id,
            userId: user?._id,
            vehicleIds,
            financings: applicationFinancings.map(({ financing }) => financing),
            insuranceProducts,
        });
    }

    return application;
};

export default buildRateLimiterMiddleware({ operation: 'shareStandardApplication' })(resolver);
