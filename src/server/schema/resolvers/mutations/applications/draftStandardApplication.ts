import { ObjectId } from 'mongodb';
import {
    ApplicationDraftedAuditTrail,
    ApplicationJourney,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    CustomerKind,
    Lead,
    LeadDraftedAuditTrail,
    LeadStatus,
    LocalCustomer,
    ModuleType,
    StandardApplication,
    VehicleKind,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    retrieveFeatureProps,
    getApplicationInitialStage,
    allowAddInsuranceStage,
    getInsuranceApplicationSetup,
} from '../../../../database/helpers/applications';
import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import { getVehiclePrice } from '../../../../database/helpers/vehicles';
import { executeLegacyJourney } from '../../../../journeys/legacyJourney';
import { ModulePolicyAction } from '../../../../permissions';
import { getApplicationLogStages } from '../../../../utils/application';
import {
    validateApplicationFinancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import { getDealershipPaymentSetting } from '../../../../utils/getDealershipPaymentSetting';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, getApplyForInsurance, validateEndpoint } from './helpers';

const resolver: GraphQLMutationResolvers['draftStandardApplication'] = async (
    root,
    {
        moduleId,
        customer: customerInputs,
        vehicle: vehicleInputs,
        configuration: configurationInput,
        tradeInVehicle,
        financing: financingInputs,
        insurancing: insurancingInputs,
        endpointId,
        dealerId,
        promoCodeId,
        languageId,
    },
    { getUser, getTranslations, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);
    const permissionController = await getPermissionController();

    // get the current user which might come in handy
    const user = await getUser(true);
    const loggedInUser = !!user;

    // get the module
    const applicationModule = await collections.modules.findOne({ _id: moduleId });

    // ensure the user may create on this application
    if (!permissionController.modules.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication)) {
        throw new InvalidPermission();
    }

    if (!applicationModule || applicationModule._type !== ModuleType.StandardApplicationModule) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a standard application module' });
    }

    // Get company details of application
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    // get the customer & vehicle modules
    const customerModule = await collections.modules.findOne({ _id: applicationModule.customerModuleId });
    const vehicleModule = await collections.modules.findOne({ _id: applicationModule.vehicleModuleId });

    const withFinancing = getApplyForFinancing(configurationInput, applicationModule);

    // validate the financing settings
    const { financeProduct, financing, isAffinBank } = await validateApplicationFinancing(
        applicationModule,
        financingInputs,
        t,
        dealerId,
        null,
        withFinancing
    );

    // get router/endpoint when provided
    const origins = endpointId ? await validateEndpoint(endpointId, applicationModule) : null;

    // build up versioning
    // customer ID will be defined later on if the kind is Customer
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    // get/create the vehicle
    const vehicle = await (async () => {
        if (vehicleModule._type === ModuleType.SimpleVehicleManagement) {
            if (vehicleInputs.existingSimpleLocalVehicleId) {
                return collections.vehicles.findOne({
                    _id: vehicleInputs.existingSimpleLocalVehicleId,
                    _kind: VehicleKind.LocalVariant,
                    moduleId: vehicleModule._id,
                    isActive: true,
                    isDeleted: false,
                    '_versioning.isLatest': true,
                });
            }
        }

        return null;
    })();

    if (!vehicle) {
        // something was wrong with the vehicle inputs
        throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
    }

    const promoCode = promoCodeId
        ? await getValidPromoCode(promoCodeId, moduleId, dealerId, vehicle._versioning.suiteId, t)
        : null;

    // Validation for AN-2652 : To validating incoming financing values
    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        // Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice: getVehiclePrice(vehicle),
            financing,
            includeDealerOptionsForFinancing: applicationModule.includeDealerOptionsForFinancing,
            promoCode,
            withFinancing,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    // get/create the applicant (customer object)
    const applicant = await (async () => {
        if (customerModule._type === ModuleType.LocalCustomerManagement) {
            if (customerInputs.existingLocalCustomer) {
                if (authoring.kind === AuthorKind.Customer) {
                    // only authenticated user may draft with existing customers
                    throw new InvalidPermission();
                }

                // get the customer
                return collections.customers.findOne({
                    _id: customerInputs.existingLocalCustomer,
                    moduleId: customerModule._id,
                    // todo apply permissions to limit available customers
                    '_versioning.isLatest': true,
                });
            }

            if (customerInputs.newLocalCustomer) {
                const newCustomerId = new ObjectId();

                if (authoring.kind === AuthorKind.Customer) {
                    // update the authoring object to match the newly created customer ID
                    authoring.customerId = newCustomerId;
                }

                // create a new customer
                const customer: LocalCustomer = {
                    _id: newCustomerId,
                    _kind: CustomerKind.Local,
                    moduleId: customerModule._id,
                    kycPresetIds: [],
                    fields: convertLocalCustomerGraphQLFields(customerInputs.newLocalCustomer.fields),
                    isDeleted: false,
                    _versioning: getAdvancedVersioningForCreation(authoring),
                };

                // insert the customer in the database
                await collections.customers.insertOne(customer);

                return customer;
            }
        }

        return null;
    })();

    if (!applicant) {
        // something was wrong with the customer inputs
        throw new InvalidInput({ customer: 'Invalid customer inputs' });
    }

    const dealer = await collections.dealers.findOne({ _id: dealerId });
    const paymentSetting = await getDealershipPaymentSetting(dealer._id, applicationModule, { collections });

    // we first need to create the customer document
    const stage = getApplicationInitialStage({
        dealer,
        paymentSetting,
        scenarios: applicationModule.scenarios,
        withFinancing,
        isFinancingOptional: applicationModule.isFinancingOptional,
        withInsurance: configurationInput.withInsurance,
        isInsuranceOptional: applicationModule.isInsuranceOptional,
        testDrive: configurationInput.testDrive,
    });

    // Check if there is insurance
    const withInsurance = getApplyForInsurance(configurationInput, applicationModule);

    // Validate insurance inputs first before drafting any application
    const { insurancing } = await getInsuranceApplicationSetup({
        applicationModule,
        insurancingInputs,
        t,
        withInsurance,
    });

    const assigneeId = user?._id;
    const needInsuranceStage = allowAddInsuranceStage(
        stage,
        applicationModule.scenarios,
        configurationInput.withInsurance,
        applicationModule.isInsuranceOptional
    );

    const lead: Lead = {
        _id: new ObjectId(),
        kind: ApplicationKind.Standard,
        status: LeadStatus.Drafted,
        isLead: false,
        moduleId: applicationModule._id,
        vehicleId: vehicle._id,
        customerId: applicant._id,
        dealerId: dealer._id,
        isDraft: true,
        identifier: '',
        tradeInVehicle,
        documents: [],
        _versioning: getAdvancedVersioningForCreation(authoring),
        ...origins,
        languageId,
    };

    await collections.leads.insertOne(lead);

    const configuration = {
        ...configurationInput,
        withFinancing,
        withInsurance,
        isAffinAutoFinanceCentreRequired: withFinancing && isAffinBank,
    };

    const application: StandardApplication = {
        _id: new ObjectId(),
        kind: ApplicationKind.Standard,
        moduleId: applicationModule._id,
        scenarios: applicationModule.scenarios,
        stages: [stage, needInsuranceStage && ApplicationStage.Insurance].filter(Boolean),
        ...getApplicationInitialStageDetails(
            stage,
            ApplicationStatus.Drafted,
            assigneeId,
            await retrieveFeatureProps(applicationModule._id)
        ),
        ...(needInsuranceStage &&
            getApplicationInitialStageDetails(ApplicationStage.Insurance, ApplicationStatus.Drafted, assigneeId, null)),

        // the application should be treated as a draft if there's no remote link sent
        isDraft: true,

        // define relationships
        bankId: financeProduct?.bankId,
        applicantId: applicant._id,
        vehicleId: vehicle._id,
        dealerId,

        // push financing and application configurations
        financing,

        // insurancing too, if it's enabled
        insurancing,

        configuration,
        tradeInVehicle,
        // start with empty remarks and no document
        remarks: '',
        documents: [],
        // spread origins
        ...origins,

        // provide initial versioning
        _versioning: getAdvancedVersioningForCreation(authoring),
        promoCodeId: promoCode?._id,
        withCustomerDevice: false,
        referenceApplicationSuiteIds: [],
        languageId,
        guarantorId: [],
        useMyinfo: {
            customer: false,
            guarantor: false,
        },

        leadId: lead._id,
    };

    // build up application journey
    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: applicant._versioning.createdAt,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
    };

    // insert the documents in database
    await collections.applications.insertOne(application);
    await collections.applicationJourneys.insertOne(applicationJourney);

    // generate the trail
    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: applicant._versioning.createdAt,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    const leadTrail: LeadDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadDrafted,
        _date: applicant._versioning.createdAt,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    // register the trail
    await collections.auditTrails.insertMany([trail, leadTrail]);

    // finally execute the journey
    return executeLegacyJourney({
        application,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });
};

export default buildRateLimiterMiddleware({ operation: 'draftStandardApplication' })(resolver);
