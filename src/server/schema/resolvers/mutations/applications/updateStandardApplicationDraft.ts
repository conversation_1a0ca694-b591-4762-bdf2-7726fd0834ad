import { ObjectId } from 'mongodb';
import {
    ApplicationKind,
    Collections,
    StandardApplication,
    StandardApplicationModule,
    LocalCustomerManagementModule,
    ModuleType,
    SimpleVehicleManagementModule,
    VehicleKind,
    LocalVariant,
    ApplicationStatus,
    ApplicationStage,
    Lead,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    getApplicationInitialStage,
    allowAddInsuranceStage,
    getInsuranceApplicationSetup,
} from '../../../../database/helpers/applications';
import { getVehiclePrice } from '../../../../database/helpers/vehicles';
import { consumeJourneyToken, generateJourneyToken } from '../../../../journeys';
import { ModulePolicyAction } from '../../../../permissions';
import {
    validateApplicationFinancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import { getDealershipPaymentSetting } from '../../../../utils/getDealershipPaymentSetting';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, getApplyForInsurance } from './helpers';

const getOneApplicationOrFail = async (applicationId: ObjectId, collections: Collections) => {
    const application = await collections.applications.findOne<StandardApplication>({
        _id: applicationId,
        kind: ApplicationKind.Standard,
        '_versioning.isLatest': true,
    });

    if (!application || application.kind !== ApplicationKind.Standard) {
        throw new InvalidInput('Invalid token');
    }

    return application;
};

const getShowroomModuleOrFail = async (moduleId: ObjectId, collections: Collections) => {
    const module = await collections.modules.findOne<StandardApplicationModule>({
        _id: moduleId,
    });

    if (!module || module._type !== ModuleType.StandardApplicationModule) {
        throw new InvalidInput({ moduleId: 'Module is not a standard application module' });
    }

    return module;
};

const getLocalVariantOrFail = async (variantId: ObjectId, moduleId: ObjectId, collections: Collections) => {
    const localVariant = await collections.vehicles.findOne<LocalVariant>({
        _id: variantId,
        _kind: VehicleKind.LocalVariant,
        moduleId,
        isActive: true,
        isDeleted: false,
        '_versioning.isLatest': true,
    });

    if (!localVariant) {
        throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
    }

    return localVariant;
};

const resolver: GraphQLMutationResolvers['updateStandardApplicationDraft'] = async (
    root,
    { token, configuration, financing: financingInputs, insurancing: insurancingInputs, promoCodeId, languageId },
    { getUser, getTranslations, getPermissionController }
) => {
    const { applicationId, origin, leadId } = await consumeJourneyToken(token);
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);
    const permissionController = await getPermissionController();

    const application = await getOneApplicationOrFail(applicationId, collections);
    const module = await getShowroomModuleOrFail(application.moduleId, collections);

    const [user, company, vehicleModule, lead] = await Promise.all([
        getUser(true),
        collections.companies.findOne({ _id: module.companyId }),
        collections.modules.findOne<SimpleVehicleManagementModule>({ _id: module.vehicleModuleId }),
        collections.leads.findOne<Lead>({ _id: leadId }),
        collections.modules.findOne<LocalCustomerManagementModule>({ _id: module.customerModuleId }),
    ]);

    if (!permissionController.modules.mayOperateOn(module, ModulePolicyAction.CreateApplication)) {
        throw new InvalidPermission();
    }

    const withFinancing = getApplyForFinancing(configuration, module);

    // validate the financing settings
    const { financeProduct, financing, isAffinBank } = await validateApplicationFinancing(
        module,
        financingInputs,
        t,
        application.dealerId,
        null,
        withFinancing
    );

    const vehicle = await getLocalVariantOrFail(application.vehicleId, vehicleModule._id, collections);

    const promoCode = promoCodeId
        ? await getValidPromoCode(promoCodeId, module._id, application.dealerId, vehicle._versioning.suiteId, t)
        : null;

    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        // Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        const loggedInUser = !!user;

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice: getVehiclePrice(vehicle),
            financing,
            includeDealerOptionsForFinancing: module.includeDealerOptionsForFinancing,
            promoCode,
            withFinancing,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    const dealer = await collections.dealers.findOne({ _id: application.dealerId });
    const paymentSetting = await getDealershipPaymentSetting(dealer._id, module, { collections });

    // we first need to create the customer document
    const stage = getApplicationInitialStage({
        dealer,
        paymentSetting,
        scenarios: module.scenarios,
        withFinancing,
        isFinancingOptional: module.isFinancingOptional,
        withInsurance: configuration.withInsurance,
        isInsuranceOptional: module.isInsuranceOptional,
        testDrive: configuration.testDrive,
    });

    // Check if there is insurance
    const withInsurance = getApplyForInsurance(configuration, module);

    // Validate insurance inputs first before drafting any application
    const { insurancing } = await getInsuranceApplicationSetup({
        applicationModule: module,
        insurancingInputs,
        t,
        withInsurance,
    });

    const assigneeId = user?._id;
    const needInsuranceStage = allowAddInsuranceStage(
        stage,
        module.scenarios,
        configuration.withInsurance,
        module.isInsuranceOptional
    );

    const updateBody: Partial<StandardApplication> = {
        ...(needInsuranceStage &&
            getApplicationInitialStageDetails(ApplicationStage.Insurance, ApplicationStatus.Drafted, assigneeId, null)),
        bankId: financeProduct?.bankId,
        financing,
        insurancing,
        configuration: {
            ...configuration,
            withFinancing,
            withInsurance,
            isAffinAutoFinanceCentreRequired: withFinancing && isAffinBank,
        },
        promoCodeId: promoCode?._id,
        languageId,
    };

    // update the application
    const updatedApplication = await collections.applications.findOneAndUpdate(
        { _id: application._id },
        {
            $set: updateBody,
        }
    );

    return {
        application: updatedApplication,
        token: generateJourneyToken(lead, updatedApplication, origin, company.sessionTimeout, user),
        lead,
    };
};

export default buildRateLimiterMiddleware({ operation: 'updateStandardApplicationDraft' })(resolver);
