import dayjs from 'dayjs';
import sendSMS from '../../../../core/sms';
import { ExternalLinkKind, LocalCustomerFieldKey, LocalCustomerRandomizedPhoneField } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { createOTP, getOngoingOTP } from '../../../../utils';
import { ApplicationKind, GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['generateRemoteJourneyPasscode'] = async (
    root,
    { secret },
    { getTranslations, loaders }
) => {
    const { t } = await getTranslations(['emails']);
    const { collections } = await getDatabaseContext();

    const link = await collections.externalLinks.findOne({ secret });

    if (!link) {
        throw new Error('Invalid Link.');
    }

    // return existing if it's yet not expired
    switch (link._kind) {
        case ExternalLinkKind.ProceedWithCustomer: {
            const existing = await getOngoingOTP('remoteJourney', link.data.applicationId.toHexString());
            if (existing) {
                return { expiredAt: existing.expiresAt };
            }

            break;
        }

        case ExternalLinkKind.SendSalesOffer: {
            const reference = `${link.data.salesOfferId.toHexString()}_${link.data.featureKinds.join('_')}`;
            const existing = await getOngoingOTP('remoteJourney', reference);

            if (existing) {
                return { expiredAt: existing.expiresAt };
            }

            break;
        }

        default:
            throw new Error('Invalid Link.');
    }

    const { code, date, lifeTime, phone, companyId } = await (async () => {
        switch (link._kind) {
            case ExternalLinkKind.ProceedWithCustomer: {
                const application = await loaders.applicationById.load(link.data.applicationId);

                if (
                    !application ||
                    !application._versioning.isLatest ||
                    !(
                        application.kind === ApplicationKind.Standard ||
                        application.kind === ApplicationKind.Event ||
                        application.kind === ApplicationKind.Configurator ||
                        application.kind === ApplicationKind.Finder
                    )
                ) {
                    throw new Error('Application kind not supported');
                }

                const applicant = await loaders.customerById.load(application.applicantId);
                const phone = applicant?.fields.find(
                    ({ key }) => key === LocalCustomerFieldKey.Phone
                ) as LocalCustomerRandomizedPhoneField<LocalCustomerFieldKey.Phone>;

                // shouldn't happen to begin with
                if (!phone?.randomizedPhone) {
                    throw new Error('Application cannot be continued from remote');
                }

                const { code, date, lifeTime } = await createOTP(
                    application._id,
                    'remoteJourney',
                    10,
                    application._id.toHexString()
                );

                const applicationModule = await loaders.moduleById.load(application.moduleId);

                return { code, date, lifeTime, phone, companyId: applicationModule.companyId };
            }

            case ExternalLinkKind.SendSalesOffer: {
                const lead = await loaders.leadBySuiteId.load(link.data.leadSuiteId);
                const applicant = await loaders.customerById.load(lead.customerId);

                const phone = applicant?.fields.find(
                    ({ key }) => key === LocalCustomerFieldKey.Phone
                ) as LocalCustomerRandomizedPhoneField<LocalCustomerFieldKey.Phone>;

                // shouldn't happen to begin with
                if (!phone?.randomizedPhone) {
                    throw new Error('Application cannot be continued from remote');
                }

                const reference = `${link.data.salesOfferId.toHexString()}_${link.data.featureKinds.join('_')}`;
                const { code, date, lifeTime } = await createOTP(
                    {
                        salesOfferId: link.data.salesOfferId,
                        featureKinds: link.data.featureKinds,
                    },
                    'remoteJourney',
                    10,
                    reference
                );

                const launchpadModule = await loaders.moduleById.load(lead.moduleId);

                return { code, date, lifeTime, phone, companyId: launchpadModule.companyId };
            }

            default:
                throw new Error('Invalid Link.');
        }
    })();

    const company = await loaders.companyById.load(companyId);

    await sendSMS(
        company,
        phone.randomizedPhone,
        `${t('emails:otp.code', { code })} ${t('emails:otp.expiration', { expiration: lifeTime })}`
    );

    return { expiredAt: dayjs(date).add(lifeTime, 'minute').toDate() };
};

export default resolver;
