import dayjs from 'dayjs';
import type { TFunction } from 'i18next';
import { isEmpty } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    isCapAvailableApplication,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    ModuleType,
    ReminderKind,
    type Application,
    type ApplicationConfirmedBookingAuditTrail,
    type User,
    type Lead,
    type EndTestDriveReminder,
    ApplicationKind,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { ppnAuth, submitCap } from '../../../../integrations/cap';
import submitActivity from '../../../../integrations/cap/submissions/submitActivity';
import {
    createCapSubmissionAuditTrails,
    getActivityEndDate,
    resolveCapEligibility,
    storeCapGuidsToLead,
} from '../../../../integrations/cap/utils';
import getSupportingValuesFromLead from '../../../../integrations/cap/utils/getSupportingValuesFromLead';
import { checkCapIntegrationIsEnabled } from '../../../../integrations/cap/utils/isApplicationAbleToSubmitCapCheck';
import { ActivityKind, ActivityStatus } from '../../../../integrations/cap/utils/types';
import { hasAppointmentStage } from '../../../../journeys/common/helpers';
import type { Loaders } from '../../../../loaders';
import createLoaders from '../../../../loaders';
import { ApplicationPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import { getPathByStage } from '../../../../utils/application';
import { getAdvancedVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import type {
    GraphQLApplicationUpdateAppointmentDetails,
    GraphQLCapValuesInput,
    GraphQLMutationResolvers,
} from '../../definitions';
import mergeAndCreateNewCustomer from '../customer/utils/mergeAndCreateNewCustomer';

const invalidApplicationStatus = [
    ApplicationStatus.BookingConfirmed,
    ApplicationStatus.TestDriveStarted,
    ApplicationStatus.TestDriveCompleted,
    ApplicationStatus.Cancelled,
    ApplicationStatus.Completed,
];

type BasesParams = {
    application: Application;
    lead: Lead;
    capValues?: GraphQLCapValuesInput;
    t: TFunction;
    loaders: Loaders;
    stage: ApplicationStage;
};

type SubmitPlannedActivityParams = BasesParams & { moduleId: ObjectId };

const submitPlannedActivityToCap = async ({
    lead,
    application,
    moduleId,
    capValues,
    loaders,
    stage,
    t,
}: SubmitPlannedActivityParams) => {
    const isCapIntegrationEnabled = await checkCapIntegrationIsEnabled(lead, loaders);
    const { shouldSubmit, capModuleId } = await resolveCapEligibility(lead);

    if (!isCapIntegrationEnabled || !shouldSubmit || !isCapAvailableApplication(application)) {
        return;
    }

    let activityKind: ActivityKind;
    switch (stage) {
        case ApplicationStage.Appointment:
            activityKind = ActivityKind.TestDrive;
            break;

        case ApplicationStage.VisitAppointment:
            activityKind = ActivityKind.ShowroomVisit;
            break;

        default:
            activityKind = ActivityKind.Normal;
            break;
    }

    if (!lead.isLead) {
        const businessPartnerGuid = capValues?.businessPartnerGuid;
        const leadGuid = capValues?.leadGuid;

        submitCap({
            t,
            application,
            lead,
            capModuleId,
            existingCapValues: { businessPartnerGuid, leadGuid },
            activityKind,
            activityStatus: ActivityStatus.Planned,
            isQualified: true,
        });

        return;
    }

    const capSetting = await loaders.capSettingByModuleId.load(capModuleId);

    try {
        const authentication = await ppnAuth(capSetting);

        if (authentication.error) {
            throw new Error(t('auditTrails:application.defaultCapError.authFailed'));
        }

        const { dealerData, customerData, campaignGuid, salespersonGuid } = await getSupportingValuesFromLead(
            t,
            lead,
            authentication.access_token,
            capSetting
        );

        const { businessPartnerGuid, leadGuid } = lead.capValues ?? {};

        const module = await loaders.moduleById.load(lead.moduleId);
        const company = await loaders.companyById.load(module.companyId);

        const createdActivityDetails = await submitActivity({
            lead,
            application,
            t,
            capSetting,
            auth: authentication.access_token,
            dealerData,
            customerData,
            customerGuid: businessPartnerGuid,
            planEndDate: getActivityEndDate(company.countryCode, application),
            campaignGuid,
            leadGuid,
            salespersonGuid,
            activityKind,
            activityStatus: ActivityStatus.Planned,
        });

        if (createdActivityDetails) {
            let activityUpdateCapValues;
            switch (stage) {
                case ApplicationStage.Appointment:
                    activityUpdateCapValues = {
                        testDriveActivityGuid: createdActivityDetails.d.activityGuid,
                        testDriveId: createdActivityDetails.d.activityId,
                    };
                    break;
                case ApplicationStage.VisitAppointment:
                    activityUpdateCapValues = {
                        showroomVisitActivityGuid: createdActivityDetails.d.activityGuid,
                        showroomVisitId: createdActivityDetails.d.activityId,
                    };
                    break;
                default:
                    activityUpdateCapValues = {};
                    break;
            }
            await storeCapGuidsToLead(lead, activityUpdateCapValues);
        }
    } catch (error) {
        console.error({
            message: error?.message,
            data: {
                leadId: lead._id,
                moduleId,
                capSettingId: capSetting._id,
            },
        });

        await createCapSubmissionAuditTrails({
            lead,
            capActionAuditTrail: AuditTrailKind.CapActivitySubmitted,
            success: false,
            id: null,
        });
    }
};

const scheduleEndTestDriveReminderEmail = async (
    application: Application,
    loaders: Loaders = createLoaders()
): Promise<void> => {
    const journey = await loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId);

    // only applied to test drive applications without process
    // test drive application with process will be handled in `TestDriveStartedStep`
    if (!journey || journey.testDriveSetting) {
        return;
    }

    const { appointmentModuleId } = application.appointmentStage;

    const appointmentModule = await loaders.moduleById.load(appointmentModuleId);

    if (appointmentModule?._type !== ModuleType.AppointmentModule || !appointmentModule?.isReminderTimeEnabled) {
        return;
    }

    const { timeToSendReminder } = appointmentModule;

    const willSendAt = dayjs().add(timeToSendReminder, 'hour').toDate();

    const reminder: EndTestDriveReminder = {
        _id: new ObjectId(),
        _kind: ReminderKind.EndTestDriveReminder,
        applicationSuiteId: application._versioning.suiteId,
        willSendAt,
    };

    const { collections } = await getDatabaseContext();
    await collections.reminders.insertOne(reminder);
};

type ConfirmBookingParams = BasesParams & {
    stage: ApplicationStage;
    user: User;
    loaders?: Loaders;
    appointmentDetails?: GraphQLApplicationUpdateAppointmentDetails;
};

export const confirmBooking = async ({
    application,
    stage,
    user,
    appointmentDetails,
    loaders,
    ...rest
}: ConfirmBookingParams) => {
    const { collections } = await getDatabaseContext();
    const path = getPathByStage(stage);

    const updatedData = {
        [`${path}.status`]: ApplicationStatus.BookingConfirmed,
        ...(!isEmpty(appointmentDetails)
            ? {
                  [`${path}.bookingTimeSlot.slot`]: appointmentDetails.bookingTimeSlot,
              }
            : {}),
    };

    const { ok, value } = await collections.applications.findOneAndUpdate(
        { _id: application._id },
        {
            $set: {
                ...updatedData,
                ...getAdvancedVersioningByUserForUpdate(user._id),
            },
        },
        {
            returnDocument: 'after',
            includeResultMetadata: true,
        }
    );

    const result = ok === 1;

    // update activity log, when it's success
    if (result) {
        if (application.kind !== ApplicationKind.Mobility) {
            submitPlannedActivityToCap({
                application: value,
                moduleId: application.moduleId,
                stage,
                loaders,
                ...rest,
            });

            if (
                application.kind !== ApplicationKind.SalesOffer &&
                hasAppointmentStage(application.stages) &&
                application.configuration.testDrive
            ) {
                scheduleEndTestDriveReminderEmail(value, loaders);
            }
        }

        const trail: ApplicationConfirmedBookingAuditTrail = {
            _date: new Date(),
            _id: new ObjectId(),
            _kind: AuditTrailKind.ApplicationConfirmedBookingAuditTrail,
            applicationId: application._id,
            applicationSuiteId: application._versioning.suiteId,
            author: { kind: AuthorKind.User, id: user._id },
            stages: [stage],
        };

        await collections.auditTrails.insertOne(trail);

        switch (stage) {
            case ApplicationStage.Appointment: {
                await mainQueue.add({
                    type: 'sendAppointmentBookingConfirmation',
                    applicationId: application._id,
                });
                break;
            }

            case ApplicationStage.VisitAppointment: {
                await mainQueue.add({
                    type: 'sendVisitAppointmentBookingConfirmation',
                    applicationId: application._id,
                });
                break;
            }

            default:
                break;
        }
    }

    return result;
};

const resolver: GraphQLMutationResolvers['confirmBookingApplication'] = async (
    root,
    { applicationId, stage, capValues, appointmentDetails, updatedKyc, updatedTradeInVehicle },
    { getUser, getPermissionController, getTranslations, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['auditTrails']);

    const user = await getUser();
    const application = await collections.applications.findOne({ _id: applicationId, '_versioning.isLatest': true });
    if (!application) {
        throw new Error('Application ID invalid');
    }

    if (invalidApplicationStatus.some(status => status === application.appointmentStage?.status)) {
        throw new Error(`Application with status ${application.appointmentStage?.status} cannot be confirmed`);
    }

    if (!permissionController.applications.mayOperateOn(application, ApplicationPolicyAction.Update)) {
        throw new InvalidPermission();
    }

    const lead = await collections.leads.findOne({ _id: application.leadId, '_versioning.isLatest': true });
    if (!lead) {
        throw new Error('Lead ID invalid');
    }

    // Do update to customer with incoming updatedKyc & updatedTradeInVehicle
    let { customerId } = lead;

    if (updatedKyc) {
        customerId = await mergeAndCreateNewCustomer({
            collections,
            loaders,
            newCustomerFieldsSettings: updatedKyc,
            lead,
        });
    }

    const updatedApplication =
        updatedKyc || updatedTradeInVehicle
            ? await collections.applications.findOneAndUpdate(
                  { _id: application._id },
                  {
                      $set: {
                          applicantId: customerId,
                          ...(updatedTradeInVehicle ? { tradeInVehicle: updatedTradeInVehicle } : {}),
                      },
                  },
                  { returnDocument: 'after' }
              )
            : application;

    const updatedLead =
        updatedKyc || updatedTradeInVehicle
            ? await collections.leads.findOneAndUpdate(
                  { _id: lead._id },
                  {
                      $set: {
                          customerId,
                          ...(updatedTradeInVehicle ? { tradeInVehicle: updatedTradeInVehicle } : {}),
                      },
                  },
                  { returnDocument: 'after' }
              )
            : lead;

    return confirmBooking({
        application: updatedApplication,
        lead: updatedLead,
        stage,
        capValues,
        appointmentDetails,
        user,
        t,
        loaders,
    });
};

export default requiresLoggedUser(resolver);
