import { Collections } from '../../../../database';
import { ExternalLinkKind, SalesOfferFeatureKind, SendSalesOfferLink, User } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import createLoaders, { Loaders } from '../../../../loaders';
import { GraphQLMutationResolvers } from '../../definitions';
import generateSalesOfferJourneyToken, { Origin } from '../salesOffers/generateSalesOfferJourneyToken';

export const getToken = async (link: SendSalesOfferLink, user: User, collections: Collections, loaders: Loaders) => {
    const lead = await loaders.leadBySuiteId.load(link.data.leadSuiteId);

    const launchpadModule = await loaders.moduleById.load(lead.moduleId);
    const company = await collections.companies.findOne({ _id: launchpadModule.companyId });

    let origin: Origin = 'remote-signing';
    if (link.data.featureKinds.includes(SalesOfferFeatureKind.Deposit)) {
        const salesOffer = await collections.salesOffers.findOne({ _id: link.data.salesOfferId });
        const applicationJourney = await collections.applicationJourneys.findOne({
            applicationSuiteId: salesOffer.latestReservationApplicationSuiteId,
        });

        if (!applicationJourney?.isReceived || !applicationJourney?.deposit?.completed) {
            origin = 'remote-deposit';
        }
    }

    // generate a new token
    return generateSalesOfferJourneyToken(
        {
            userId: user?._id || null,
            leadSuiteId: link.data.leadSuiteId,
            salesOfferId: link.data.salesOfferId,
            featureKinds: link.data.featureKinds,
            origin,
        },
        company.sessionTimeout
    );
};

const resolver: GraphQLMutationResolvers['generateSalesOfferJourneyToken'] = async (root, { secret }, { getUser }) => {
    const { collections } = await getDatabaseContext();

    const link = await collections.externalLinks.findOne({ secret });

    if (!link || link._kind !== ExternalLinkKind.SendSalesOffer) {
        throw new Error('Invalid Link.');
    }

    // get the user from the GraphQl context
    const user = await getUser(true);

    const loaders = createLoaders();

    return getToken(link, user, collections, loaders);
};

export default resolver;
