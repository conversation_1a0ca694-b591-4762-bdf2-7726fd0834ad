import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import { executeJourneyFromGraphQL } from '../../../../journeys';
import { GuarantorKYCStepPayload } from '../../../../journeys/common/GuarantorKYCStep';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['submitGuarantorKYC'] = async (root, { token, fields, saveDraft }, context) =>
    executeJourneyFromGraphQL<GuarantorKYCStepPayload>({
        token,
        identifier: 'guarantor-kyc',
        payload: {
            fields: convertLocalCustomerGraphQLFields(fields),
            saveDraft,
        },
        context,
    });

export default resolver;
