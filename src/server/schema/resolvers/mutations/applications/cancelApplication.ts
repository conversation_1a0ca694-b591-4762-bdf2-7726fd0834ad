import { ApplicationKind, ApplicationStage, ApplicationStatus } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    isInvalidInsuranceApplicationStatusToCancel,
    isValidStatusToAmendOrCancel,
} from '../../../../database/helpers/applications';
import { consumeJourneyToken } from '../../../../journeys';
import { ApplicationPolicyAction } from '../../../../permissions';
import { ApplicationCancelSource, mainQueue } from '../../../../queues';
import { getApplicationStatus } from '../../../../utils/application';
import { InvalidPermission } from '../../../errors';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['cancelApplication'] = async (
    root,
    { applicationId, token, stage },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const id = token ? (await consumeJourneyToken(token)).applicationId : applicationId;

    const application = await collections.applications.findOne({
        _id: id,
        '_versioning.isLatest': true,
    });

    if (!application) {
        throw new Error('Application ID invalid');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    // for the mobility user should be optional due to customer can click cancel booking without login
    const user = await getUser(true);
    if (!user && !token) {
        throw new InvalidPermission();
    }

    const status = getApplicationStatus(application, lead, stage);

    // Since mobility can be cancelled by customer, so the permissions will only work on another stages
    if (stage !== ApplicationStage.Mobility) {
        const hasPermission = await permissionController.applications.mayOperateOn(
            application,
            ApplicationPolicyAction.Update
        );

        if (!hasPermission) {
            throw new InvalidPermission();
        }
    }

    switch (stage) {
        case ApplicationStage.Financing: {
            if (!isValidStatusToAmendOrCancel(status)) {
                throw new Error(`Application with status ${status} cannot be cancelled`);
            }

            // call the job
            if (token) {
                await mainQueue.add({
                    type: 'onApplicationCancelled',
                    source: ApplicationCancelSource.Customer,
                    applicationId: application._id,
                    applicantId: application.kind === ApplicationKind.Mobility && application.applicantId,
                });
            } else {
                await mainQueue.add({
                    type: 'onApplicationCancelled',
                    source: ApplicationCancelSource.User,
                    applicationId: application._id,
                    ...(applicationId && { userId: user._id }),
                    stage,
                });
            }

            break;
        }

        case ApplicationStage.Insurance: {
            if (isInvalidInsuranceApplicationStatusToCancel(status)) {
                throw new Error(`Application with status ${status} cannot be cancelled`);
            }

            // call the job
            await mainQueue.add({
                type: 'onInsuranceApplicationCancelled',
                userId: user._id,
                applicationId: application._id,
            });

            break;
        }

        case ApplicationStage.Mobility: {
            if (token) {
                await mainQueue.add({
                    type: 'onApplicationCancelled',
                    source: ApplicationCancelSource.Customer,
                    applicationId: application._id,
                    applicantId: application.kind === ApplicationKind.Mobility && application.applicantId,
                });
            } else {
                await mainQueue.add({
                    type: 'onApplicationCancelled',
                    source: ApplicationCancelSource.User,
                    applicationId: application._id,
                    ...(applicationId && { userId: user._id }),
                    stage,
                });
            }

            break;
        }

        case ApplicationStage.Reservation:
        case ApplicationStage.Appointment:
        case ApplicationStage.VisitAppointment: {
            if (!user) {
                // Cancelling lead or reservation only happened from stored details
                // via admin or inner admin route
                throw new InvalidPermission();
            }

            if (
                application.kind !== ApplicationKind.Event &&
                application.kind !== ApplicationKind.Finder &&
                application.kind !== ApplicationKind.Standard &&
                application.kind !== ApplicationKind.Configurator
            ) {
                throw new Error(`Application kind not supported to cancel for ${stage}`);
            }

            if (
                [
                    ApplicationStatus.Completed,
                    ApplicationStatus.TestDriveCompleted,
                    ApplicationStatus.Cancelled,
                ].includes(status as ApplicationStatus)
            ) {
                throw new Error(`Application with status ${status} cannot be cancelled`);
            }

            await mainQueue.add({
                type: 'onApplicationCancelled',
                source: ApplicationCancelSource.User,
                userId: user._id,
                applicationId: application._id,
                stage,
            });
        }
    }

    return true;
};

export default resolver;
