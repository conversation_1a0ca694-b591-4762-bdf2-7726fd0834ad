import dayjs from 'dayjs';
import isEqual from 'fast-deep-equal';
import { isNil, omit } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { ApplicationTradeInQuoted, AuditTrailKind, AuthorKind, LaunchpadApplication } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { mainQueue } from '../../../../queues';
import { getApplicationLogStages } from '../../../../utils/application';
import { Authoring, getAdvancedVersioningByUserForUpdate, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { Context } from '../../../context';
import { InvalidInput } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import {
    ApplicationStatus,
    GraphQLMutationResolvers,
    GraphQLTradeInVehiclePayload,
    ModuleType,
} from '../../definitions';

/**
 *
 * @param tradeInVehicle Trade In Vehicle Payload
 * @param applicationSuiteId ApplicationSuiteId
 * @param context BE Contexts
 * @returns
 */
export const updateTradeInLaunchpadApplication = async (
    tradeInVehicle: GraphQLTradeInVehiclePayload[],
    applicationSuiteId: ObjectId,
    context: Context
) => {
    const { loaders, getUser } = context;
    const { collections } = await getDatabaseContext();
    const existingApplication = (await collections.applications.findOne({
        '_versioning.suiteId': applicationSuiteId,
        '_versioning.isLatest': true,
    })) as LaunchpadApplication;

    if (!existingApplication) {
        throw new InvalidInput({ applicationSuiteId: 'Application not found' });
    }
    const module = await loaders.moduleById.load(existingApplication.moduleId);

    if (isNil(module) || module._type !== ModuleType.LaunchPadModule) {
        throw new Error('Module not found');
    }
    // get the current user which might come in handy
    const user = await getUser(true);

    // build up versioning
    // customer ID will be defined later on if the kind is Customer
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    const isQuoteUpdated =
        module.salesManager.equals(user._id) &&
        !isNil(tradeInVehicle[0].price) &&
        tradeInVehicle[0].price !== existingApplication.tradeInVehicle[0].price &&
        tradeInVehicle[0].price > 0;

    const newApplication: LaunchpadApplication = {
        ...existingApplication,
        tradeInStage: {
            ...existingApplication.tradeInStage,
            status:
                isQuoteUpdated || !isNil(existingApplication.tradeInVehicle[0].price)
                    ? ApplicationStatus.TradeInQuoted
                    : ApplicationStatus.TradeInPending,
            isQuoted:
                !isNil(existingApplication.tradeInStage.lastQuotedAt) ||
                !isNil(existingApplication.tradeInVehicle[0].price),
            lastQuotedAt: isQuoteUpdated ? dayjs().toDate() : existingApplication.tradeInStage.lastQuotedAt,
        },
        _id: new ObjectId(),
        tradeInVehicle,
        _versioning: {
            ...existingApplication._versioning,
            updatedBy: user
                ? { kind: AuthorKind.User, id: user._id }
                : { kind: AuthorKind.Customer, id: existingApplication.applicantId },
            updatedAt: new Date(),
        },
    };

    // setting isLatest to false
    await collections.applications.findOneAndUpdate(
        {
            _id: existingApplication._id,
        },
        {
            $set: {
                '_versioning.isLatest': false,
            },
        }
    );

    // insert the documents in database
    await collections.applications.insertOne(newApplication);

    // generate the trail

    const trail: ApplicationTradeInQuoted = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.TradeInQuoted,
        _date: newApplication._versioning.createdAt,
        applicationId: newApplication._id,
        applicationSuiteId: newApplication._versioning.suiteId,
        stages: getApplicationLogStages(newApplication, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    // register the trail
    if (isQuoteUpdated) {
        await collections.auditTrails.insertOne(trail);
    }

    // Price updated => send to SC
    if (isQuoteUpdated) {
        await mainQueue.add({ type: 'sendTradeInQuotedEmailToAssignee', applicationId: newApplication._id });
    }

    // Vehicle updated => send to SM
    if (!isEqual(omit('price', existingApplication.tradeInVehicle[0]), omit('price', tradeInVehicle[0]))) {
        await mainQueue.add({ type: 'sendTradeInVehicleChangedToSalesManager', applicationId: newApplication._id });
    }

    return newApplication;
};

const resolver: GraphQLMutationResolvers['updateLaunchpadApplicationTradeIn'] = async (
    root,
    { tradeInVehicle, applicationId },
    context
) => updateTradeInLaunchpadApplication(tradeInVehicle, applicationId, context);

export default buildRateLimiterMiddleware({ operation: 'updateLaunchpadApplication' })(resolver);
