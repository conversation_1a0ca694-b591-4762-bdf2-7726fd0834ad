import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import { executeJourneyFromGraphQL } from '../../../../journeys';
import { ApplicantKYCStepPayload } from '../../../../journeys/common';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['submitApplicantKYC'] = async (
    root,
    { token, fields, saveDraft = false, customerKind, capValues, customerCiamId },
    context
) =>
    executeJourneyFromGraphQL<ApplicantKYCStepPayload>({
        token,
        identifier: 'applicant-kyc',
        payload: {
            fields: convertLocalCustomerGraphQLFields(fields),
            saveDraft,
            customerKind,
            capValues,
            customerCiamId,
        },
        context,
    });

export default resolver;
