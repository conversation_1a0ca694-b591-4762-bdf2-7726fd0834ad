import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationKind,
    ApplicationStatus,
    ApplicationStage,
    AuditTrailKind,
    AuthorKind,
    ModuleType,
    VehicleKind,
    ApplicationScenario,
    SettingId,
    FinancingPreferenceValue,
    TimeSlotEnum,
    type ApplicationDraftedAuditTrail,
    type ApplicationJourney,
    type AppointmentModule,
    type FinderApplication,
    type Lead,
    type LeadDraftedAuditTrail,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    retrieveFeatureProps,
    getApplicationInitialStage,
    getInsuranceApplicationSetup,
} from '../../../../database/helpers/applications';
import { finderVehicleConditionMapper, getVehiclePrice } from '../../../../database/helpers/vehicles';
import { executeFinderJourney } from '../../../../journeys/finderJourney';
import { ModulePolicyAction } from '../../../../permissions/types';
import { getApplicationLogStages } from '../../../../utils/application';
import {
    validateApplicationFinancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import { getDealershipPaymentSetting } from '../../../../utils/getDealershipPaymentSetting';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import { getAdvancedVersioningForCreation, getAuthorFromAuthoring, type Authoring } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { FinderVehicleStatus, GraphQLMutationResolvers } from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, getApplyForInsurance, validateEndpoint } from './helpers';

const resolver: GraphQLMutationResolvers['draftFinderApplicationFromLead'] = async (
    root,
    {
        leadId,
        moduleId,
        vehicle: vehicleInputs,
        configuration: configurationInput,
        tradeInVehicle,
        financing: financingInputs,
        insurancing: insurancingInputs,
        endpointId,
        dealerId,
        promoCodeId,
        languageId,
        isTestDriveDrafting,
    },
    { loaders, getUser, getPermissionController, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);
    const permissionController = await getPermissionController();

    const user = await getUser(true);
    const loggedInUser = !!user;

    const applicationModule = await collections.modules.findOne({ _id: moduleId });

    if (!permissionController.modules.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication)) {
        throw new InvalidPermission();
    }

    if (!applicationModule || applicationModule._type !== ModuleType.FinderApplicationPrivateModule) {
        throw new InvalidInput({ moduleId: 'Module is not a finder private application module' });
    }

    if (!user || !permissionController.modules.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication)) {
        throw new InvalidPermission();
    }

    const existingLead = await collections.leads.findOne({ _id: leadId, '_versioning.isLatest': true });

    if (!existingLead) {
        throw new InvalidInput({ leadId: 'Lead not found' });
    }

    const [appointmentModule, company, vehicleModule, dealer] = await Promise.all([
        applicationModule?.appointmentModuleId
            ? (loaders.moduleById.load(applicationModule.appointmentModuleId) as Promise<AppointmentModule>)
            : null,
        collections.companies.findOne({ _id: applicationModule.companyId }),
        loaders.moduleById.load(applicationModule.vehicleModuleId),
        loaders.dealerById.load(dealerId),
    ]);

    if (
        !appointmentModule &&
        isTestDriveDrafting &&
        applicationModule.scenarios.includes(ApplicationScenario.Appointment)
    ) {
        // The path is for test drive. But appointment is not there
        // So return the invalid input error
        throw new InvalidInput({ $root: 'Invalid application setup for test drive' });
    }

    const isTestDriveOnly =
        isTestDriveDrafting &&
        applicationModule.scenarios.includes(ApplicationScenario.Appointment) &&
        !isNil(appointmentModule);

    const withFinancing = getApplyForFinancing(configurationInput, applicationModule);

    // validate the financing settings
    const { financeProduct, financing, isAffinBank } = await validateApplicationFinancing(
        applicationModule,
        financingInputs,
        t,
        dealerId,
        null,
        withFinancing || configurationInput.requestForFinancing
    );

    // get router/endpoint when provided
    const origins = endpointId ? await validateEndpoint(endpointId, applicationModule) : null;

    // build up versioning
    // customer ID will be defined later on if the kind is Customer
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    // get/create the vehicle
    const vehicle = await (async () => {
        if (vehicleModule._type === ModuleType.FinderVehicleManagement) {
            if (vehicleInputs.existingSimpleLocalVehicleId) {
                return collections.vehicles.findOne({
                    _id: vehicleInputs.existingSimpleLocalVehicleId,
                    _kind: VehicleKind.FinderVehicle,
                    moduleId: vehicleModule._id,
                    isDeleted: false,
                    '_versioning.isLatest': true,
                });
            }
        }

        return null;
    })();

    if (!vehicle || vehicle?._kind !== VehicleKind.FinderVehicle) {
        // something was wrong with the vehicle inputs
        throw new InvalidInput({ vehicle: 'Invalid vehicle inputs' });
    }

    if (vehicle._kind === VehicleKind.FinderVehicle && vehicle.status !== FinderVehicleStatus.Available) {
        throw new Error('Finder Vehicle is not available');
    }

    const promoCode = promoCodeId
        ? await getValidPromoCode(promoCodeId, applicationModule._id, dealerId, vehicle._versioning.suiteId, t)
        : null;

    // Validation for AN-2652 : To validating incoming financing values
    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        // Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice: getVehiclePrice(vehicle),
            financing,
            includeDealerOptionsForFinancing: applicationModule.includeDealerOptionsForFinancing,
            promoCode,
            withFinancing,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    // Check if new version needed due to different vehicle
    let lead: Lead = existingLead;
    if (!existingLead.vehicleId.equals(vehicle._id)) {
        const newLead: Lead = {
            ...existingLead,
            _id: new ObjectId(),
            vehicleId: vehicle._id,
            ...(existingLead.kind === ApplicationKind.Launchpad && {
                vehicleCondition: finderVehicleConditionMapper(vehicle),
            }),
            _versioning: getAdvancedVersioningForCreation(authoring, existingLead._versioning.suiteId),
        };

        await collections.leads.updateOne({ _id: existingLead._id }, { $set: { '_versioning.isLatest': false } });
        await collections.leads.insertOne(newLead);

        lead = newLead;
    }

    const applicant = await collections.customers.findOne({
        _id: lead.customerId,
    });

    if (!applicant) {
        throw new InvalidInput({ customerId: 'Customer not found' });
    }

    const foundSetting = await collections.settings.findOne({
        settingId: SettingId.FinderVehicleManagementSetting,
        moduleId: vehicle.moduleId,
    });

    const isLTAEnabled = foundSetting?.settingId === SettingId.FinderVehicleManagementSetting && foundSetting?.allowLTA;

    const canApplyForFinancing = (() => {
        if (vehicle._kind !== VehicleKind.FinderVehicle) {
            throw new Error('Vehicle Kind is not Finder Vehicle');
        }

        return vehicle.listing.vehicle.condition.value === 'new' || (isLTAEnabled && !!vehicle.lta) || !isLTAEnabled;
    })();

    const paymentSetting = await getDealershipPaymentSetting(dealer._id, applicationModule, { collections });

    const stage = isTestDriveOnly
        ? ApplicationStage.Appointment
        : getApplicationInitialStage({
              dealer,
              paymentSetting,
              scenarios: applicationModule.scenarios,
              // With financing of request for financing, should be categorized as finance stage
              withFinancing:
                  (canApplyForFinancing && configurationInput.withFinancing) || configurationInput.requestForFinancing,
              isFinancingOptional:
                  !canApplyForFinancing || applicationModule.financingPreference !== FinancingPreferenceValue.Mandatory,
              withInsurance: configurationInput.withInsurance,
              isInsuranceOptional: applicationModule.isInsuranceOptional,
              testDrive: configurationInput.testDrive,
          });

    // Check if there is insurance
    const withInsurance = !isTestDriveOnly ? getApplyForInsurance(configurationInput, applicationModule) : false;

    // Validate insurance inputs first before drafting any application
    const { insurancing } = await getInsuranceApplicationSetup({
        applicationModule,
        insurancingInputs,
        t,
        withInsurance,
    });

    const allowAddInsuranceStage = stage !== ApplicationStage.Insurance && withInsurance;
    const assigneeId = user?._id;

    const application: FinderApplication = {
        _id: new ObjectId(),
        kind: ApplicationKind.Finder,
        moduleId: applicationModule._id,
        scenarios: isTestDriveOnly ? [ApplicationScenario.Appointment] : applicationModule.scenarios,
        stages: [stage, allowAddInsuranceStage && ApplicationStage.Insurance].filter(Boolean),
        ...(isTestDriveOnly
            ? {
                  appointmentStage: {
                      identifier: '',
                      status: ApplicationStatus.Drafted,
                      appointmentModuleId: applicationModule.appointmentModuleId,
                      bookingTimeSlot: {
                          _type: TimeSlotEnum.Appointment,
                          bookingLimit: 0,
                          slot: null,
                      },
                      assigneeId,
                  },
              }
            : getApplicationInitialStageDetails(
                  stage,
                  ApplicationStatus.Drafted,
                  assigneeId,
                  await retrieveFeatureProps(applicationModule._id)
              )),
        ...(allowAddInsuranceStage &&
            getApplicationInitialStageDetails(
                ApplicationStage.Insurance,
                ApplicationStatus.Drafted,
                assigneeId,
                await retrieveFeatureProps(applicationModule._id)
            )),
        // the application should be treated as a draft if there's no remote link sent
        isDraft: true,

        // define relationships
        applicantId: applicant._id,
        vehicleId: vehicle._id,
        bankId: financeProduct?.bankId,

        dealerId,
        promoCodeId: promoCode?._id,

        // start with empty remarks and no document
        remarks: '',
        documents: [],
        guarantorId: [],

        // spread origins
        ...origins,

        // provide initial versioning
        _versioning: getAdvancedVersioningForCreation(authoring),
        configuration: {
            ...configurationInput,
            // add check if we can apply for financing depending on finder vehicle
            withFinancing: canApplyForFinancing && configurationInput.withFinancing,
            isAffinAutoFinanceCentreRequired:
                isAffinBank && (configurationInput.withFinancing || configurationInput.requestForFinancing),
        },
        tradeInVehicle,
        financing,
        insurancing,
        referenceApplicationSuiteIds: [],
        languageId,

        withCustomerDevice: false,
        useMyinfo: {
            customer: false,
            guarantor: false,
        },

        leadId: lead._id,
    };

    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: applicant._versioning.createdAt,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
    };

    await collections.applications.insertOne(application);
    await collections.applicationJourneys.insertOne(applicationJourney);

    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: applicant._versioning.createdAt,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    const leadTrail: LeadDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadDrafted,
        _date: applicant._versioning.createdAt,
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    await collections.auditTrails.insertMany([trail, leadTrail]);

    return executeFinderJourney({
        application,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });
};

export default requiresLoggedUser(resolver);
