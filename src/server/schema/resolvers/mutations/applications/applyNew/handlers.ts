/* eslint-disable max-len */
import { TFunction } from 'i18next';
import { isNil } from 'lodash/fp';
import { MatchKeysAndValues, ObjectId } from 'mongodb';
import { StandardApplicationConfiguration } from '../../../../../../app/api';
import {
    Application,
    ApplicationConfiguration,
    ApplicationDraftedAuditTrail,
    ApplicationFinancing,
    ApplicationInsurancing,
    ApplicationJourney,
    ApplicationStage,
    ApplicationStatus,
    AppointmentStage,
    AuditTrailKind,
    AuthorKind,
    Collections,
    ConfiguratorApplication,
    ConfiguratorApplicationConfiguration,
    ConfiguratorModule,
    FinancingStage,
    FinderApplication,
    FinderApplicationConfiguration,
    FinderApplicationModule,
    Guarantor,
    InsuranceStage,
    KYCPreset,
    LocalCustomerManagementModule,
    StandardApplication,
    StandardApplicationModule,
    getKYCPresetsForApplication,
} from '../../../../../database';
import { getInsuranceApplicationSetup } from '../../../../../database/helpers/applications';
import updateAgreementsForApplication from '../../../../../database/helpers/updateAgreementsForApplication';
import {
    ApplicantAgreementsStep,
    ApplicantKYCStep,
    GuarantorAgreementsStep,
    GuarantorKYCStep,
} from '../../../../../journeys/common';
import { validateApplicationFinancing } from '../../../../../utils/application/validations';
import { Authoring, getAuthorFromAuthoring } from '../../../../../utils/versioning';
import {
    ApplicationKind,
    ConsentsAndDeclarationsPurpose,
    CustomerKind,
    GraphQLApplicationFinanceSettings,
    GraphQLApplicationInsuranceSettings,
    GraphQLConfiguratorApplicationConfigurationPayload,
    GraphQLFinderApplicationConfigurationPayload,
    GraphQLStandardApplicationConfiguration,
} from '../../../definitions';
import { getApplyForFinancing, getApplyForInsurance } from '../helpers';

export const addDraftedAuditTrail = async ({ user, collections, application, financingInfo, insuranceInfo }) => {
    // build up versioning
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: application?.applicantId, date: new Date() };
    // generate the trail for apply new set up
    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: new Date(),
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: [financingInfo && financingInfo.stages[0], insuranceInfo && insuranceInfo.stages[0]].filter(Boolean),
        author: getAuthorFromAuthoring(authoring),
    };
    // register the trail
    await collections.auditTrails.insertOne(trail);
};

const upsertApplicationJourneyApplyNewSubmission = (
    type: 'financing' | 'insurance' | 'appointment',
    journey: ApplicationJourney,
    financingInfo: ApplyNewFinancingInfo,
    insuranceInfo: ApplyNewInsurancingInfo,
    configuration: ApplyNewApplicationConfiguration
) => {
    switch (type) {
        case 'appointment': {
            if (!isNil(journey?.applyNewSubmission?.appointmentStage)) {
                return journey.applyNewSubmission.appointmentStage;
            }

            return configuration.testDrive
                ? {
                      isReceived: false,
                  }
                : undefined;
        }

        case 'financing': {
            if (!isNil(journey?.applyNewSubmission?.financingStage)) {
                return journey.applyNewSubmission.financingStage;
            }

            return financingInfo
                ? {
                      isReceived: false,
                  }
                : undefined;
        }

        case 'insurance': {
            if (!isNil(journey?.applyNewSubmission?.insuranceStage)) {
                return journey.applyNewSubmission.insuranceStage;
            }

            return insuranceInfo
                ? {
                      isReceived: false,
                  }
                : undefined;
        }

        default:
            return undefined;
    }
};

export const updateApplyNewSubmissionForJourney = async ({
    collections,
    application,
    financingInfo,
    insuranceInfo,
    configuration,
    journey,
}) => {
    await collections.applicationJourneys.findOneAndUpdate(
        { applicationSuiteId: application._versioning.suiteId },
        {
            $set: {
                applyNewSubmission: {
                    financingStage: upsertApplicationJourneyApplyNewSubmission(
                        'financing',
                        journey,
                        financingInfo,
                        insuranceInfo,
                        configuration
                    ),
                    insuranceStage: upsertApplicationJourneyApplyNewSubmission(
                        'insurance',
                        journey,
                        financingInfo,
                        insuranceInfo,
                        configuration
                    ),
                    appointmentStage: upsertApplicationJourneyApplyNewSubmission(
                        'appointment',
                        journey,
                        financingInfo,
                        insuranceInfo,
                        configuration
                    ),
                },
            },
        }
    );
};

export const reInitKycJourney = async ({ collections, loaders, existingApplication, newApplication, customer }) => {
    // prepare journey for this apply new
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: existingApplication._versioning.suiteId,
    });

    // check for missing KYCs
    if (journey.applicantKYC && customer) {
        const previousKycPresets = await getKYCPresetsForApplication(
            {
                ...existingApplication,
                configuration: {
                    ...existingApplication.configuration,
                    withFinancing:
                        existingApplication.configuration.withFinancing &&
                        existingApplication.financingStage?.isDraft === true
                            ? false
                            : existingApplication.configuration.withFinancing,
                    withInsurance:
                        existingApplication.configuration.withInsurance &&
                        existingApplication.insuranceStage?.isDraft === true
                            ? false
                            : existingApplication.configuration.withInsurance,
                },
            },
            customer._kind
        );
        const customerModule: LocalCustomerManagementModule = await loaders.customerModuleById.load(customer.moduleId);
        const kycPresets = await getKYCPresetsForApplication(newApplication, customer._kind);
        const hasNewKyc = previousKycPresets.length !== kycPresets.length;
        if (
            !(await ApplicantKYCStep.validateInputFields(
                customerModule.kycFields.sort((a, b) => a.order - b.order),
                kycPresets,
                customer,
                newApplication
            )) ||
            hasNewKyc
        ) {
            journey.applicantKYC.completed = false;
        }
    }

    // check for missing agreements
    // TODO: validate agreements and remove if financing is still draft
    if (journey.applicantAgreements) {
        const newAgreements = await updateAgreementsForApplication(newApplication, customer._kind, loaders);
        const hasNewAgreement = journey.applicantAgreements.agreements.length !== newAgreements.length;

        if (journey.applicantKYC) {
            journey.applicantKYC.completed =
                journey.applicantKYC.completed &&
                ApplicantAgreementsStep.checkAgreementsCompletion(journey.applicantAgreements, [
                    ConsentsAndDeclarationsPurpose.KYC,
                ]) &&
                !hasNewAgreement;
        }

        journey.applicantAgreements.agreements = newAgreements;
    }

    // update applicantKYC info
    await collections.applicationJourneys.findOneAndUpdate(
        { applicationSuiteId: existingApplication._versioning.suiteId },
        {
            $set: {
                'applicantKYC.completed': journey.applicantKYC.completed,
                'applicantAgreements.agreements': journey.applicantAgreements.agreements,
            },
        }
    );
};

export const reInitGuarantorJourney = async ({
    collections,
    loaders,
    existingApplication,
    newApplication,
}): Promise<KYCPreset[]> => {
    // prepare journey for this apply new
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: existingApplication._versioning.suiteId,
    });

    /**
     *  // 1, firstly not created guarantor
        // 2, then apply new, has new guarantor fields, start with applicant kyc
        // 3, apply new, no new guarantor fields, end with applicant kyc

        // 1, then firstly created guarantor
        // 2, then apply new, has new guarantor fields, start with applicant kyc
        // 3, then apply new, no new guarantor field, end with applicant kyc
     */

    const guarantorKycPresets = await getKYCPresetsForApplication(newApplication, CustomerKind.Guarantor);
    // if there is guarantor kyc fields
    if (guarantorKycPresets.length) {
        // check for missing guarantor KYCs
        if (journey.hasGuarantorCustomer && existingApplication.guarantorId && journey.guarantorKYC) {
            const guarantor = await collections.customers.findOne({ _id: existingApplication.guarantorId[0] });
            const previousKycPresets = await getKYCPresetsForApplication(
                {
                    ...existingApplication,
                    configuration: {
                        ...existingApplication.configuration,
                        withFinancing:
                            existingApplication.configuration.withFinancing &&
                            existingApplication.financingStage?.isDraft === true
                                ? false
                                : existingApplication.configuration.withFinancing,
                        withInsurance:
                            existingApplication.configuration.withInsurance &&
                            existingApplication.insuranceStage?.isDraft === true
                                ? false
                                : existingApplication.configuration.withInsurance,
                    },
                },
                guarantor._kind
            );
            const customerModule = await loaders.customerModuleById.load(guarantor.moduleId);
            const hasNewKyc = previousKycPresets.length !== guarantorKycPresets.length;
            if (
                !(await GuarantorKYCStep.validateInputFields(
                    customerModule.kycFields.sort((a, b) => a.order - b.order),
                    guarantorKycPresets,
                    guarantor as Guarantor
                )) ||
                hasNewKyc
            ) {
                journey.guarantorKYC.completed = false;
                journey.hasGuarantorCustomer = false;
            }
        }

        // check for missing guarantor agreements
        if (journey.guarantorAgreements) {
            const newAgreements = await updateAgreementsForApplication(newApplication, CustomerKind.Guarantor, loaders);
            const hasNewAgreement = journey.guarantorAgreements.agreements.length !== newAgreements.length;

            if (journey.guarantorKYC) {
                journey.guarantorKYC.completed =
                    journey.guarantorKYC.completed &&
                    GuarantorAgreementsStep.checkAgreementsCompletion(journey.applicantAgreements, [
                        ConsentsAndDeclarationsPurpose.KYC,
                    ]) &&
                    !hasNewAgreement;
                if (!journey.guarantorKYC.completed) {
                    journey.hasGuarantorCustomer = false;
                }
            }

            journey.guarantorAgreements.agreements = newAgreements;
        }

        if (journey.guarantorKYC) {
            // update guarantor kyc info
            await collections.applicationJourneys.findOneAndUpdate(
                { applicationSuiteId: existingApplication._versioning.suiteId },
                {
                    $set: {
                        'guarantorKYC.completed': journey.guarantorKYC.completed,
                        'guarantorAgreements.agreements': journey.guarantorAgreements.agreements,
                        hasGuarantorCustomer: journey.hasGuarantorCustomer,
                    },
                }
            );
        }
    }

    return guarantorKycPresets;
};

export const reExecuteJourney = async (
    existingApplication,
    newApplication,
    runExecuteJourney,
    { collections, loaders, user, applicationModule, applyNewConfiguration }
) => {
    const customer = await collections.customers.findOne({ _id: existingApplication.applicantId });

    await reInitKycJourney({ collections, loaders, existingApplication, newApplication, customer });

    const guarantorKycPresets = await reInitGuarantorJourney({
        collections,
        loaders,
        existingApplication,
        newApplication,
    });

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: existingApplication._versioning.suiteId,
    });

    // if there are new customer kyc fields or new applicant agreements
    // run drafting journey step from kyc to start journey
    if (!journey.applicantKYC.completed) {
        // finally execute the journey
        return runExecuteJourney({
            application: newApplication,
            identifier: 'drafting',
            origin: 'draft',
            user,
            payload: null,
        });
    }

    // if this apply new process include appointment
    // and application module set up displayAppointmentDatepicker = true
    // we have to go through kyc step
    if (applyNewConfiguration.testDrive && applicationModule.displayAppointmentDatepicker) {
        await collections.applicationJourneys.findOneAndUpdate(
            { applicationSuiteId: newApplication._versioning.suiteId },
            {
                $set: {
                    'applicantKYC.completed': false,
                },
            }
        );

        return runExecuteJourney({
            application: newApplication,
            identifier: 'drafting',
            origin: 'draft',
            user,
            payload: null,
        });
    }

    // after reInitGuarantorJourney will have below 3 results:
    // 1, journey.guarantorKYC.completed -> false
    // 2, journey.guarantorKYC.completed -> true
    // 3, journey.guarantorKYC -> undefined

    // if do not have new guarantor kyc fields or do not have new guarantor agreements
    // end up with applicant-kyc
    if (guarantorKycPresets.length === 0 || journey.guarantorKYC?.completed) {
        // firstly set applicantKYC.completed as false
        // then run ApplicantKYCStep to set nessarary data and stage status
        await collections.applicationJourneys.findOneAndUpdate(
            { applicationSuiteId: newApplication._versioning.suiteId },
            {
                $set: {
                    'applicantKYC.completed': false,
                },
            }
        );

        return runExecuteJourney({
            application: newApplication,
            identifier: 'applicant-kyc',
            origin: 'draft',
            user,
            payload: {
                fields: [],
                saveDraft: false,
                customerKind: customer._kind,
            },
        });
    }

    // otherwise start up with applicant-kyc
    // firstly set applicantKYC.completed as false
    // then run DraftingStep from kyc to start journey
    await collections.applicationJourneys.findOneAndUpdate(
        { applicationSuiteId: newApplication._versioning.suiteId },
        {
            $set: {
                'applicantKYC.completed': false,
            },
        }
    );

    // start journey step from drafting
    return runExecuteJourney({
        application: newApplication,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: {
            fields: [],
            saveDraft: false,
            customerKind: customer._kind,
        },
    });
};

const applyNew = () => {};

export default applyNew;

export type ApplyNewFinancingInfo = {
    stages: ApplicationStage[];
    financingStage: FinancingStage;
    financing: ApplicationFinancing;
    bankId: ObjectId;
    configuration: ApplyNewApplicationConfiguration;
};

type ApplyNewApplicationConfiguration = {
    tradeIn: boolean;
    testDrive: boolean;
    // should we proceed with financing
    withFinancing: boolean;
    // should we proceed with insurance
    withInsurance: boolean;

    // standard application should set it as false
    requestForFinancing: boolean;

    isAffinAutoFinanceCentreRequired?: boolean;
};

export type ApplyNewInsurancingInfo = {
    stages: ApplicationStage[];
    insuranceStage: InsuranceStage;
    insurancing: ApplicationInsurancing;
    configuration: ApplyNewApplicationConfiguration;
};

export type ApplyNewApplicationFeatureInfo = {
    financingInfo: ApplyNewFinancingInfo;
    insuranceInfo: ApplyNewInsurancingInfo;
    journeyUpdate: FeatureStage;
    updatedExistedApplication: FinderApplication | StandardApplication | ConfiguratorApplication;
};

type FeatureStage = {
    financingStage: FinancingStage | null;
    insuranceStage: InsuranceStage | null;
    appointmentStage: AppointmentStage | null;
};

export const retrieveApplicationConfiguration = (
    applicationKind: ApplicationKind,
    configuration: ApplicationConfiguration
): ApplicationConfiguration => {
    switch (applicationKind) {
        case ApplicationKind.Standard:
            return { ...configuration, requestForFinancing: false };

        case ApplicationKind.Finder:
        case ApplicationKind.Configurator:
            return configuration;

        default:
            throw new Error('Apply new Application Configuration not supported');
    }
};

export const retrieveApplyNewApplicationFeatureInfo = async (
    configuration:
        | GraphQLFinderApplicationConfigurationPayload
        | GraphQLStandardApplicationConfiguration
        | GraphQLConfiguratorApplicationConfigurationPayload,
    existingApplication: FinderApplication | StandardApplication | ConfiguratorApplication,
    applicationModule: FinderApplicationModule | StandardApplicationModule | ConfiguratorModule,
    t: TFunction,
    assigneeId: ObjectId,
    financingInputs: GraphQLApplicationFinanceSettings,
    insurancingInputs: GraphQLApplicationInsuranceSettings,
    journey: ApplicationJourney
): Promise<ApplyNewApplicationFeatureInfo> => {
    /**
     * Initialize financing and insurancing configuration
     */
    // apply new for financing
    let financingInfo: {
        stages: ApplicationStage[];
        financingStage: FinancingStage;
        financing: ApplicationFinancing;
        bankId: ObjectId;
        configuration: ApplyNewApplicationConfiguration;
    } | null = null;

    // apply new for insurance
    let insuranceInfo: {
        stages: ApplicationStage[];
        insuranceStage: InsuranceStage;
        insurancing: ApplicationInsurancing;
        configuration: ApplyNewApplicationConfiguration;
    } | null = null;

    const journeyUpdate: FeatureStage = {
        financingStage: null,
        insuranceStage: null,
        appointmentStage: null,
    };

    const updatedExistedApplication = existingApplication;
    /**
     * retrieving financing and insurancing info based on the application configuration ( user selection in the CI )
     * if not 'Finder', 'Configurator' and 'Standard' Application attempt to retrieve,
     * :: return null for financing and insurancing
     *
     * @returns financing :: nullable Application Financing and insurancing :: nullable Application Insurancing
     */
    switch (updatedExistedApplication.kind) {
        case ApplicationKind.Finder:
        case ApplicationKind.Configurator: {
            const applicationConfiguration = configuration as
                | FinderApplicationConfiguration
                | ConfiguratorApplicationConfiguration;

            if (applicationConfiguration.withFinancing || applicationConfiguration.requestForFinancing) {
                // validate the financing settings
                const { financeProduct, financing } = await validateApplicationFinancing(
                    applicationModule,
                    financingInputs,
                    t,
                    updatedExistedApplication.dealerId,
                    null,
                    applicationConfiguration.withFinancing || applicationConfiguration.requestForFinancing
                );

                financingInfo = {
                    stages: [ApplicationStage.Financing],
                    financingStage: { identifier: '', status: ApplicationStatus.Drafted, assigneeId, isDraft: true },
                    financing,
                    // define relationships
                    bankId: financeProduct?.bankId,
                    configuration: {
                        ...updatedExistedApplication.configuration,
                        withFinancing: applicationConfiguration.withFinancing
                            ? true
                            : updatedExistedApplication.configuration.withFinancing,
                        requestForFinancing: applicationConfiguration.requestForFinancing
                            ? true
                            : updatedExistedApplication.configuration.requestForFinancing,
                    },
                };
            } else if (updatedExistedApplication?.financingStage?.isDraft) {
                /**
                 * delete financingStage when the user after apply new, user didn't continuing the journey and close/exit the journey
                 * :: delete the financingStage props so that the user able to apply new again for financing
                 * :: set journey.financingStage to be null too
                 */
                delete updatedExistedApplication.financingStage;
                updatedExistedApplication.stages = existingApplication.stages.filter(
                    i => i !== ApplicationStage.Financing
                );
                updatedExistedApplication.configuration.withFinancing = false;
                updatedExistedApplication.configuration.requestForFinancing = false;
                journeyUpdate.insuranceStage = null;
            }

            if (applicationConfiguration.withInsurance) {
                // Check if there is insurance
                const withInsurance = getApplyForInsurance(configuration, applicationModule);

                // Validate insurance inputs first before drafting any application
                const { insurancing } = await getInsuranceApplicationSetup({
                    applicationModule,
                    insurancingInputs,
                    t,
                    withInsurance,
                });

                insuranceInfo = {
                    stages: [ApplicationStage.Insurance],
                    insuranceStage: { identifier: '', status: ApplicationStatus.Drafted, assigneeId, isDraft: true },
                    insurancing,
                    configuration: { ...updatedExistedApplication.configuration, withInsurance },
                };
            } else if (updatedExistedApplication?.insuranceStage?.isDraft) {
                /**
                 * delete insuranceStage when the user after apply new, user didn't continuing the journey and close/exit the journey
                 * :: delete the insuranceStage props so that the user able to apply new again for insurance
                 * :: set journey.insuranceStage to be null too
                 */
                delete updatedExistedApplication.insuranceStage;
                updatedExistedApplication.stages = updatedExistedApplication.stages.filter(
                    i => i !== ApplicationStage.Insurance
                );
                updatedExistedApplication.configuration.withInsurance = false;
                journeyUpdate.insuranceStage = null;
            }

            return {
                financingInfo,
                insuranceInfo,
                journeyUpdate,
                updatedExistedApplication,
            };
        }

        case ApplicationKind.Standard: {
            if (configuration.withFinancing) {
                const withFinancing = getApplyForFinancing(configuration, applicationModule);

                // validate the financing settings
                const { financeProduct, financing } = await validateApplicationFinancing(
                    applicationModule,
                    financingInputs,
                    t,
                    updatedExistedApplication.dealerId,
                    null,
                    withFinancing
                );
                if (withFinancing) {
                    financingInfo = {
                        stages: [ApplicationStage.Financing],
                        financingStage: {
                            identifier: '',
                            status: ApplicationStatus.Drafted,
                            assigneeId,
                            isDraft: true,
                        },
                        financing,
                        // define relationships
                        bankId: financeProduct?.bankId,
                        configuration: {
                            ...updatedExistedApplication.configuration,
                            withFinancing,
                            requestForFinancing: false,
                        },
                    };
                }
            } else if (updatedExistedApplication?.financingStage?.isDraft) {
                /**
                 * delete financingStage when the user after apply new, user didn't continuing the journey and close/exit the journey
                 * :: delete the financingStage props so that the user able to apply new again for financing
                 * :: set journey.financingStage to be null too
                 */
                delete updatedExistedApplication.financingStage;
                updatedExistedApplication.stages = updatedExistedApplication.stages.filter(
                    i => i !== ApplicationStage.Financing
                );
                updatedExistedApplication.configuration.withFinancing = false;
                journeyUpdate.financingStage = null;
            }

            if (configuration.withInsurance) {
                // Check if there is insurance
                const withInsurance = getApplyForInsurance(configuration, applicationModule);

                // Validate insurance inputs first before drafting any application
                const { insurancing } = await getInsuranceApplicationSetup({
                    applicationModule,
                    insurancingInputs,
                    t,
                    withInsurance,
                });

                insuranceInfo = {
                    stages: [ApplicationStage.Insurance],
                    insuranceStage: { identifier: '', status: ApplicationStatus.Drafted, assigneeId, isDraft: true },
                    insurancing,
                    configuration: {
                        ...updatedExistedApplication.configuration,
                        withInsurance,
                        requestForFinancing: false,
                    },
                };
            } else if (updatedExistedApplication?.insuranceStage?.isDraft) {
                /**
                 * delete insuranceStage when the user after apply new, user didn't continuing the journey and close/exit the journey
                 * :: delete the insuranceStage props so that the user able to apply new again for insurance
                 * :: set journey.insuranceStage to be null too
                 */
                delete updatedExistedApplication.insuranceStage;
                updatedExistedApplication.stages = updatedExistedApplication.stages.filter(
                    i => i !== ApplicationStage.Insurance
                );
                updatedExistedApplication.configuration.withInsurance = false;
                journeyUpdate.insuranceStage = null;
            }

            return {
                financingInfo,
                insuranceInfo,
                journeyUpdate,
                updatedExistedApplication,
            };
        }

        default: {
            return {
                financingInfo: null,
                insuranceInfo: null,
                journeyUpdate: null,
                updatedExistedApplication,
            };
        }
    }
};

/**
 * resetting application journey applyNewSubmission when there is any incompleting stage
 * @param collections getdatabaseContext
 * @param suiteId existing application suite ID
 * @param journeyUpdate applicationJourney.applyNewSubmission to be updated
 * @param newApplication concatenate current stage application and previous application
 * @returns pushed latest versioning Application ( same suite ID )
 */
export const updateApplyNewApplicationAndJourneyDocument = async (
    collections: Collections,
    existingApplication: Application,
    journeyUpdate: MatchKeysAndValues<FinderApplication | ConfiguratorApplication | StandardApplication>,
    newApplication: Application
): Promise<Application> => {
    // reset the journey.applyNewSubmission incompleted journey
    await collections.applicationJourneys.findOneAndUpdate(
        { applicationSuiteId: existingApplication._versioning.suiteId },
        {
            $set: {
                applyNewSubmission: {
                    ...(!isNil(journeyUpdate.financingStage) && { financingStage: journeyUpdate.financingStage }),
                    ...(!isNil(journeyUpdate.insurancingStage) && {
                        financingStage: journeyUpdate.insurancingStage,
                    }),
                },
            },
        }
    );

    /**
     * we need to validate again the apply new comparison
     * when the previous apply new haven't complete / proceed for kyc step,
     * we should not using that as `latest` document / reference
     *
     * :: validate by removing incomplete apply new step
     * delete featureStage in the upcoming application document
     */

    const { insertedId } = await collections.applications.insertOne({
        ...newApplication,
        _id: new ObjectId(),
    });

    await collections.applications.findOneAndUpdate(
        { _id: existingApplication._id },
        { $set: { '_versioning.isLatest': false } }
    );

    const newVersioningApplication = await collections.applications.findOne({ _id: insertedId });

    if (!newVersioningApplication) {
        throw new Error('New Versioning Application not found');
    }

    return newVersioningApplication;
};

export const retrieveTestDriveSelected = (
    configuration:
        | ConfiguratorApplicationConfiguration
        | FinderApplicationConfiguration
        | StandardApplicationConfiguration,
    journey: ApplicationJourney,
    updatedExistedApplication: FinderApplication | ConfiguratorApplication | StandardApplication
) => {
    // when the user applying new `request for test drive`, we enforce it to be true ( by right always true)
    if (configuration.testDrive) {
        return true;
    }

    /**
     * defining whether the user complete the applying new submission for `request for test drive`
     * :: true means inherit previous configuration.testDrive
     * :: false means we should reset appointmentStage
     */
    if (
        !isNil(journey.applyNewSubmission?.appointmentStage) &&
        journey.applyNewSubmission?.appointmentStage.isReceived &&
        updatedExistedApplication.configuration.testDrive
    ) {
        return updatedExistedApplication.configuration.testDrive;
    }

    return false;
};
