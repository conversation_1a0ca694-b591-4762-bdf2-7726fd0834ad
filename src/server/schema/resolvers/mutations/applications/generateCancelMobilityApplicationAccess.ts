import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../../../core/config';
import {
    ApplicationKind,
    EndpointType,
    ExternalLinkKind,
    MobilityApplicationCancellationLink,
    ModuleType,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { isValidStatusToAmendOrCancel } from '../../../../database/helpers/applications';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['generateCancelMobilityApplicationAccess'] = async (
    root,
    { applicationId },
    { getUser }
) => {
    const { collections } = await getDatabaseContext();

    const user = await getUser();
    const application = await collections.applications.findOne({
        '_versioning.isLatest': true,
        _id: applicationId,
    });

    if (
        application?.kind !== ApplicationKind.Mobility ||
        !isValidStatusToAmendOrCancel(application.mobilityStage?.status)
    ) {
        return null;
    }

    const { moduleId, mobilityBookingDetails, routerId } = application;

    const applicationModule = await collections.modules.findOne({ _id: moduleId });
    if (applicationModule?._type !== ModuleType.MobilityModule) {
        throw new Error('invalid module for application');
    }

    const { amendmentCutOff } = applicationModule;
    if (dayjs().add(amendmentCutOff, 'days').isAfter(mobilityBookingDetails.period.start)) {
        return { url: 'expired' };
    }

    const router = routerId
        ? await collections.routers.findOne({ _id: routerId })
        : await collections.routers.findOne({
              'endpoints._type': EndpointType.MobilityApplicationEntrypoint,
              mobilityApplicationModuleId: applicationModule._id,
          });

    const link: MobilityApplicationCancellationLink = {
        _id: new ObjectId(),
        _kind: ExternalLinkKind.MobilityApplicationCancellation,
        secret: nanoid(),
        expiresAt: dayjs().add(5, 'minutes').toDate(),
        deleteOnFetch: true,
        data: {
            userId: user._id,
            applicationSuiteId: application._versioning.suiteId,
            endpointId: application.endpointId,
            routerId,
        },
    };

    await collections.externalLinks.insertOne(link);

    return { url: urljoin(`${config.protocol}://${router.hostname}`, router.pathname, `l/${link.secret}`) };
};

export default requiresLoggedUser(resolver);
