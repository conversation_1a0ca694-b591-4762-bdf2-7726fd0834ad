import { omit } from 'lodash/fp';
import {
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    FinderApplication,
    ModuleType,
    VehicleKind,
    FinancingPreferenceValue,
    FinderVehicleStatus,
    SettingId,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    getApplicationInitialStageDetails,
    retrieveFeatureProps,
    getApplicationInitialStage,
    getInsuranceApplicationSetup,
} from '../../../../database/helpers/applications';
import { getVehiclePrice } from '../../../../database/helpers/vehicles';
import { consumeJourneyToken, generateJourneyToken } from '../../../../journeys';
import {
    validateApplicationFinancing,
    validateFinanceCalculatorValue,
    validatePriceValues,
} from '../../../../utils/application/validations';
import { getDealershipPaymentSetting } from '../../../../utils/getDealershipPaymentSetting';
import getDealershipSettingId from '../../../../utils/getDealershipSettingId';
import isVehicleAssignedToFinanceProduct from '../../../../utils/isVehicleAssignedToFinanceProduct';
import {
    getAdvancedVersioningByCustomerForUpdate,
    getAdvancedVersioningByUserForUpdate,
} from '../../../../utils/versioning';
import { GraphQLMutationResolvers } from '../../definitions';
import { getValidPromoCode } from '../promoCode/helper';
import { getApplyForFinancing, getApplyForInsurance } from './helpers';

const resolver: GraphQLMutationResolvers['updateFinderApplication'] = async (
    root,
    {
        insurancing: insurancingInputs,
        financing: financingInputs,
        configuration,
        promoCodeId,
        tradeInVehicle,
        languageId,
        token,
    },
    { getUser, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['errors']);

    const { origin, userId, applicationId, leadId } = await consumeJourneyToken(token);

    const application = await collections.applications.findOne({ _id: applicationId });
    if (application.kind !== ApplicationKind.Finder) {
        throw new Error('Finder Application not found');
    }

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    if (applicationModule._type !== ModuleType.FinderApplicationPublicModule) {
        throw new Error('Finder Application Module not found');
    }

    const lead = await collections.leads.findOne({ _id: leadId });
    if (!lead) {
        throw new Error('Lead not found');
    }

    const withFinancing = getApplyForFinancing(configuration, applicationModule);

    // Get company details of application
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    const vehicle = await collections.vehicles.findOne({
        _kind: VehicleKind.FinderVehicle,
        _id: application.vehicleId,
    });

    if (!vehicle) {
        throw new Error('Finder vehicle not found');
    }

    // get the current user which might come in handy
    const user = await getUser(true);
    const loggedInUser = !!user;

    // build up versioning
    // customer ID will be defined later on if the kind is Customer

    const promoCode = promoCodeId
        ? await getValidPromoCode(
              promoCodeId,
              applicationModule._id,
              application.dealerId,
              vehicle._versioning.suiteId,
              t
          )
        : null;

    const foundSetting = await collections.settings.findOne({
        settingId: SettingId.FinderVehicleManagementSetting,
        moduleId: vehicle.moduleId,
    });

    const isLTAEnabled = foundSetting.settingId === 'finderVehicleManagementSetting' && foundSetting.allowLTA;

    const canApplyForFinancing = (function () {
        if (vehicle._kind !== VehicleKind.FinderVehicle) {
            throw new Error('Vehicle Kind is not Finder Vehicle');
        }

        return vehicle.listing.vehicle.condition.value === 'new' || (isLTAEnabled && !!vehicle.lta) || !isLTAEnabled;
    })();

    const dealer = await collections.dealers.findOne({ _id: application.dealerId });
    const paymentSetting = await getDealershipPaymentSetting(dealer._id, applicationModule, { collections });

    const stage = getApplicationInitialStage({
        dealer,
        paymentSetting,
        scenarios: applicationModule.scenarios,
        withFinancing: (canApplyForFinancing && withFinancing) || configuration.requestForFinancing,
        isFinancingOptional:
            !canApplyForFinancing || applicationModule.financingPreference !== FinancingPreferenceValue.Mandatory,
        withInsurance: configuration.withInsurance,
        isInsuranceOptional: applicationModule.isInsuranceOptional,
        testDrive: configuration.testDrive,
    });

    // validate the financing settings
    const { financeProduct, financing } = await validateApplicationFinancing(
        applicationModule,
        financingInputs,
        t,
        application.dealerId,
        null,
        withFinancing || configuration.requestForFinancing
    );
    // Check if there is insurance
    const withInsurance = getApplyForInsurance(configuration, applicationModule);

    // Validate insurance inputs first before drafting any application
    const { insurancing } = await getInsuranceApplicationSetup({
        applicationModule,
        insurancingInputs,
        t,
        withInsurance,
    });

    const allowAddInsuranceStage = stage !== ApplicationStage.Insurance && withInsurance;
    const assigneeId =
        (applicationModule._type === ModuleType.FinderApplicationPublicModule
            ? getDealershipSettingId(application.dealerId, applicationModule.assignee)
            : null) ?? user?._id;

    // Validation for AN-2652 : To validating incoming financing values
    if (financing && financeProduct) {
        // Validate if the selected vehicle is included on the financeProduct
        const isVehicleValidToFinanceProduct = isVehicleAssignedToFinanceProduct(financeProduct, vehicle);
        if (!isVehicleValidToFinanceProduct) {
            throw new Error('Error with selected vehicle');
        }

        // Validate the financing values using calculator
        const isFinancingValueValid = await validateFinanceCalculatorValue({
            financing,
            financeProduct,
            vehicle,
            company,
        });

        if (!isFinancingValueValid) {
            throw new Error('Error with financing value');
        }

        // Validate the values related with pricing
        const isPriceValuesValid = validatePriceValues({
            isFromPublicJourney: !loggedInUser,
            vehiclePrice: getVehiclePrice(vehicle),
            financing,
            promoCode,
            withFinancing,
        });

        if (!isPriceValuesValid) {
            throw new Error('Error with financing value');
        }
    }

    const updatedApplication: FinderApplication = {
        ...omit(['financing'], application),
        kind: ApplicationKind.Finder,
        moduleId: applicationModule._id,
        scenarios: applicationModule.scenarios,
        stages: [stage, allowAddInsuranceStage && ApplicationStage.Insurance].filter(Boolean),
        ...getApplicationInitialStageDetails(
            stage,
            ApplicationStatus.Drafted,
            assigneeId,
            await retrieveFeatureProps(applicationModule._id)
        ),
        ...(allowAddInsuranceStage &&
            getApplicationInitialStageDetails(
                ApplicationStage.Insurance,
                ApplicationStatus.Drafted,
                assigneeId,
                await retrieveFeatureProps(applicationModule._id)
            )),

        financing,
        bankId: financeProduct?.bankId,
        promoCodeId: promoCode?._id,
        ...configuration,
        tradeInVehicle,
        insurancing,
        languageId,

        // provide initial versioning
        _versioning: { ...application._versioning, ...getAdvancedVersioningByUserForUpdate(userId) },
    };

    if (application.stages.includes(ApplicationStage.Appointment) && application.stages.length === 1) {
        await collections.applications.findOneAndUpdate({ _id: applicationId }, { $set: { ...updatedApplication } });

        const company = await collections.companies.findOne({ _id: applicationModule.companyId });
        const newToken = await generateJourneyToken(lead, updatedApplication, origin, company.sessionTimeout);

        return { token: newToken, application: updatedApplication, lead };
    }

    await collections.vehicles.findOneAndUpdate(
        { _kind: VehicleKind.FinderVehicle, _id: vehicle._id },
        {
            $set: {
                status: FinderVehicleStatus.Reserved,
                ...getAdvancedVersioningByCustomerForUpdate(application.applicantId),
            },
        }
    );

    // update the documents in database
    await collections.applications.findOneAndUpdate({ _id: applicationId }, { $set: { ...updatedApplication } });

    const newToken = await generateJourneyToken(lead, updatedApplication, origin, company.sessionTimeout);

    return { token: newToken, application: updatedApplication, lead };
};

export default resolver;
