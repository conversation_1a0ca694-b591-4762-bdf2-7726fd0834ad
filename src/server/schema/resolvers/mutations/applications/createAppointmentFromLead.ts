import { ObjectId } from 'mongodb';
import {
    type ApplicationDraftedAuditTrail,
    type ApplicationJourney,
    AuditTrailKind,
    AuthorKind,
    CustomerKind,
    ModuleType,
} from '../../../../database';
import {
    ApplicationKind,
    ApplicationScenario,
    ApplicationStage,
    ApplicationStatus,
    type LaunchpadApplication,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getApplicationInitialStageDetails, retrieveFeatureProps } from '../../../../database/helpers/applications';
import { convertLocalCustomerGraphQLFields, mergeLocalCustomerFields } from '../../../../database/helpers/customers';
import { executeJourneyFromGraphQL } from '../../../../journeys';
import { ApplicantAgreementsStepPayload, ApplicantKYCStepPayload } from '../../../../journeys/common';
import { AppointmentPayload } from '../../../../journeys/common/AppointmentStep';
import { executeLaunchpadJourney } from '../../../../journeys/launchpadJourney';
import { LeadPolicyAction } from '../../../../permissions';
import { getApplicationLogStages } from '../../../../utils/application';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { GraphQLMutationResolvers } from '../../definitions';
import { confirmBooking } from './confirmBookingApplication';
import { validateEndpoint } from './helpers';

const resolver: GraphQLMutationResolvers['createAppointmentFromLead'] = async (
    root,
    { fields, leadId, tradeInVehicle, languageId, agreedConsents, endpointId, bookingTimeSlot, vehicleId },
    context
) => {
    const { getUser, getPermissionController, loaders, getTranslations } = context;
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['auditTrails']);

    const leadFilter = await permissionController.leads.getFilterQueryForAction(LeadPolicyAction.View);

    const user = await getUser();

    // check if lead id exists
    const lead = await collections.leads.findOne({
        $and: [{ _id: leadId, '_versioning.isLatest': true, isLead: true }, leadFilter],
    });

    if (!lead) {
        throw new InvalidPermission();
    }

    const applicationModule = await loaders.moduleById.load(lead.moduleId);

    if (!applicationModule || applicationModule._type !== ModuleType.LaunchPadModule) {
        // that should never happen, but we need to ensure we are not being tricked
        throw new InvalidInput({ moduleId: 'Module is not a launchpad application module' });
    }

    const customer = await loaders.customerById.load(lead.customerId);

    // create application from lead
    const stage = ApplicationStage.Appointment;
    const updatedEndpointId = endpointId || lead.endpointId;
    const origins = updatedEndpointId ? await validateEndpoint(updatedEndpointId, applicationModule) : null;

    const authoring: Authoring = { kind: AuthorKind.User, userId: user._id, date: new Date() };

    const application: LaunchpadApplication = {
        _id: new ObjectId(),
        kind: ApplicationKind.Launchpad,
        moduleId: lead.moduleId,
        scenarios: [ApplicationScenario.Appointment],
        stages: [stage],
        ...getApplicationInitialStageDetails(
            stage,
            ApplicationStatus.Drafted,
            user._id,
            await retrieveFeatureProps(applicationModule._id)
        ),

        // an appointment is already created here
        isDraft: false,

        vehicleId,
        applicantId: lead.customerId,
        dealerId: lead.dealerId,

        configuration: {
            // only enable test drive
            testDrive: true,

            // disable everything else
            visitAppointment: false,
            tradeIn: false,
            isAffinAutoFinanceCentreRequired: false,
            withFinancing: false,
            withInsurance: false,
        },

        tradeInVehicle,
        languageId,
        remarks: '',
        documents: [],

        // spread origins
        ...origins,

        _versioning: getAdvancedVersioningForCreation(authoring),

        referenceApplicationSuiteIds: [],
        leadId: lead._id,
    };

    // build up application journey
    const applicationJourney: ApplicationJourney = {
        _id: new ObjectId(),
        applicationSuiteId: application._versioning.suiteId,
        updatedAt: authoring.date,
        isReceived: false,
        isImmutable: false,
        isCorporateCustomer: false,
    };

    const appointmentModule = applicationModule.appointmentModuleId
        ? await collections.modules.findOne({
              _id: applicationModule.appointmentModuleId,
              _type: ModuleType.AppointmentModule,
          })
        : null;

    if (
        application.configuration.testDrive &&
        appointmentModule &&
        appointmentModule._type === ModuleType.AppointmentModule &&
        appointmentModule.hasTestDriveProcess
    ) {
        applicationJourney.isTestDriveProcessStarted = false;
        applicationJourney.testDriveSetting = {
            hasTestDriveSigning: appointmentModule.hasTestDriveSigning,
            signingModuleId: appointmentModule.signingModuleId,
        };
    }

    // insert the documents in database
    await collections.applications.insertOne(application);
    await collections.applicationJourneys.insertOne(applicationJourney);

    // generate the trail
    const trail: ApplicationDraftedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDrafted,
        _date: authoring.date,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDrafted),
        author: getAuthorFromAuthoring(authoring),
    };

    // register the trail
    await collections.auditTrails.insertOne(trail);

    // execute the journey
    const { token } = await executeLaunchpadJourney({
        application,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });

    const mergedFields = mergeLocalCustomerFields(customer.fields, convertLocalCustomerGraphQLFields(fields));
    // submit kyc
    await executeJourneyFromGraphQL<ApplicantKYCStepPayload>({
        token,
        identifier: 'test-drive-kyc',
        payload: {
            fields: mergedFields,
            saveDraft: false,
            customerKind: CustomerKind.Local,
        },
        context,
    });

    // submit consent
    await executeJourneyFromGraphQL<ApplicantAgreementsStepPayload>({
        token,
        identifier: 'test-drive-agreements',
        payload: { agreedConsents, ip: context.ip },
        context,
    });

    await executeJourneyFromGraphQL<AppointmentPayload>({
        token,
        identifier: 'appointment',
        payload: { bookingTimeSlot },
        context,
    });

    // application should be received at this point
    // need to confirm booking afterwards
    const isSuccess = await confirmBooking({
        application,
        lead,
        stage,
        user,
        t,
        loaders,
    });

    if (isSuccess) {
        return loaders.applicationBySuiteId.load(application._versioning.suiteId);
    }

    throw new Error('Appointment cannot be created');
};

export default resolver;
