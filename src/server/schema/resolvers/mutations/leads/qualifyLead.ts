import { ObjectId } from 'mongodb';
import { ApplicationKind, AuditTrailKind, AuthorKind, LeadStatus } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { submitCap } from '../../../../integrations/cap';
import { LeadPolicyAction } from '../../../../permissions';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers, ModuleType } from '../../definitions';
import mergeAndCreateNewCustomer from '../customer/utils/mergeAndCreateNewCustomer';

const resolver: GraphQLMutationResolvers['qualifyLead'] = async (
    root,
    { leadId, qualifyValues, updates },
    { loaders, getUser, getTranslations, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['auditTrails']);
    const permissionController = await getPermissionController();

    const lead = await collections.leads.findOne({
        _id: leadId,
        '_versioning.isLatest': true,
        status: { $ne: LeadStatus.Drafted },
        isDraft: false,
    });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Lead not found.' });
    }

    const hasPermission = permissionController.leads.mayOperateOn(lead, LeadPolicyAction.UpdateContact);

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    if (lead.status !== LeadStatus.PendingQualify && lead.status !== LeadStatus.Contacted) {
        throw new InvalidInput({ status: 'Lead with current status cannot be qualified.' });
    }

    const user = await getUser(false);

    const { vehicleId, vehicleCondition, campaignId, purchaseIntention, assigneeId } = qualifyValues;
    const moduleDetails = await loaders.moduleById.load(lead.moduleId);
    const currentCapValuesOnApplication = lead.capValues;

    let { customerId } = lead;
    if (updates) {
        customerId = await mergeAndCreateNewCustomer({
            collections,
            loaders,
            newCustomerFieldsSettings: updates,
            lead,
        });
    }

    const updatedLead = await collections.leads.findOneAndUpdate(
        { _id: leadId },
        {
            $set: {
                vehicleId,
                vehicleCondition,
                'campaignValues.capCampaignId': campaignId,
                purchaseIntention,
                status: LeadStatus.SubmittedToCap,
                assigneeId,
                customerId,
            },
        },
        { returnDocument: 'after' }
    );

    // add audit trail for lead qualification
    await collections.auditTrails.insertOne({
        _date: new Date(),
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadQualified,
        author: { kind: user ? AuthorKind.User : AuthorKind.System, id: user?._id },
        leadId: updatedLead._id,
        leadSuiteId: updatedLead._versioning.suiteId,
    });

    switch (moduleDetails._type) {
        case ModuleType.ConfiguratorModule:
        case ModuleType.FinderApplicationPrivateModule:
        case ModuleType.FinderApplicationPublicModule:
        case ModuleType.StandardApplicationModule:
        case ModuleType.LaunchPadModule: {
            if (moduleDetails.capModuleId) {
                submitCap({
                    t,
                    lead: updatedLead,
                    capModuleId: moduleDetails.capModuleId,
                    existingCapValues: {
                        businessPartnerGuid: currentCapValuesOnApplication?.businessPartnerGuid,
                        leadGuid: currentCapValuesOnApplication?.leadGuid,
                    },
                    isQualified: true,
                    user,
                    isLeadQualification: true,
                });
            }

            if (!moduleDetails.capModuleId) {
                await collections.leads.findOneAndUpdate(
                    { _id: leadId },
                    {
                        $set: {
                            status: LeadStatus.SubmittedToCap,
                            isLead: true,
                        },
                    }
                );
            }

            break;
        }

        case ModuleType.EventApplicationModule: {
            if (lead.kind !== ApplicationKind.Event || (lead.kind === ApplicationKind.Event && !lead.eventId)) {
                throw new InvalidInput({ eventId: 'Event not found.' });
            }

            const eventDetails = await loaders.eventById.load(lead.eventId);

            if (moduleDetails.capModuleId && eventDetails.isCapEnabled && eventDetails.capPrequalification) {
                submitCap({
                    t,
                    lead: updatedLead,
                    capModuleId: moduleDetails.capModuleId,
                    existingCapValues: {
                        businessPartnerGuid: currentCapValuesOnApplication?.businessPartnerGuid,
                        leadGuid: currentCapValuesOnApplication?.leadGuid,
                    },
                    isQualified: true,
                    user,
                    isLeadQualification: true,
                });
            }

            if (!moduleDetails.capModuleId || !eventDetails.isCapEnabled) {
                await collections.leads.findOneAndUpdate(
                    { _id: leadId },
                    {
                        $set: {
                            status: LeadStatus.SubmittedToCap,
                            isLead: true,
                        },
                    }
                );
            }

            break;
        }
    }

    // todo: confirm what message should be on the frontend when this long mutation is called
    return true;
};

export default requiresLoggedUser(resolver);
