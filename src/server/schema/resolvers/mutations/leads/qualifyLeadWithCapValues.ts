import { LeadStatus } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { submitCap } from '../../../../integrations/cap';
import { resolveCapEligibility } from '../../../../integrations/cap/utils';
import { checkCapIntegrationIsEnabled } from '../../../../integrations/cap/utils/isApplicationAbleToSubmitCapCheck';
import { LeadPolicyAction } from '../../../../permissions';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { ApplicationKind, GraphQLMutationResolvers } from '../../definitions';
import mergeAndCreateNewCustomer from '../customer/utils/mergeAndCreateNewCustomer';

const resolver: GraphQLMutationResolvers['qualifyLeadWithCapValues'] = async (
    root,
    { leadId, capValues, updatedKyc, updatedTradeInVehicle, selectedResponsibleSalesId },
    { loaders, getUser, getTranslations, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['auditTrails']);
    const permissionController = await getPermissionController();

    const lead = await collections.leads.findOne({ _id: leadId });

    if (!lead) {
        throw new InvalidInput({ applicationId: 'Lead not found.' });
    }

    const hasPermission = permissionController.leads.mayOperateOn(lead, LeadPolicyAction.UpdateContact);

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    if (lead.status !== LeadStatus.PendingQualify && lead.status !== LeadStatus.Contacted) {
        throw new Error(`Application with current status cannot be qualified.`);
    }

    const user = await getUser(false);

    let { customerId } = lead;
    if (updatedKyc) {
        customerId = await mergeAndCreateNewCustomer({
            collections,
            loaders,
            newCustomerFieldsSettings: updatedKyc,
            lead,
        });
    }

    // If user selecting existing BP that already have a responsible sales id, then we won't update the existing
    const updatedLead =
        selectedResponsibleSalesId || updatedKyc || updatedTradeInVehicle
            ? await collections.leads.findOneAndUpdate(
                  { _id: lead?._id },
                  {
                      $set: {
                          assigneeId: selectedResponsibleSalesId,
                          customerId,
                          ...(updatedTradeInVehicle ? { tradeInVehicle: updatedTradeInVehicle } : {}),
                      },
                  },
                  { returnDocument: 'after' }
              )
            : lead;

    const currentCapValuesOnLead = updatedLead.capValues;

    const { businessPartnerGuid, leadGuid } = capValues;

    const isCapIntegrationEnabled = await checkCapIntegrationIsEnabled(updatedLead, loaders);
    const { capModuleId, shouldSubmit } = await resolveCapEligibility(lead);

    switch (updatedLead.kind) {
        case ApplicationKind.Event:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
        case ApplicationKind.Standard: {
            if (isCapIntegrationEnabled && capModuleId && shouldSubmit) {
                submitCap({
                    t,
                    lead: updatedLead,
                    capModuleId,
                    existingCapValues: {
                        businessPartnerGuid: currentCapValuesOnLead?.businessPartnerGuid || businessPartnerGuid,
                        leadGuid: currentCapValuesOnLead?.leadGuid || leadGuid,
                    },
                    isQualified: true,
                    user,
                });
            }

            break;
        }
    }

    return true;
};

export default requiresLoggedUser(resolver);
