import { ObjectId } from 'mongodb';
import { AuditTrailKind, AuthorKind, LeadLostAuditTrail, LeadStatus } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { LeadPolicyAction } from '../../../../permissions';
import { getAdvancedVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['markLeadAsLost'] = async (
    root,
    { leadId },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const lead = await collections.leads.findOne({
        _id: leadId,
        '_versioning.isLatest': true,
        status: { $ne: LeadStatus.Drafted },
    });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Lead not found.' });
    }

    const hasPermission = permissionController.leads.mayOperateOn(lead, LeadPolicyAction.Update);

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    if (lead.status !== LeadStatus.SubmittedToCap) {
        throw new Error(`Lead with current status cannot be Marked  As Lost.`);
    }

    const user = await getUser(false);

    await collections.leads.updateOne(
        {
            _id: leadId,
            '_versioning.isLatest': true,
        },
        {
            $set: {
                status: LeadStatus.Lost,
                ...getAdvancedVersioningByUserForUpdate(user._id),
            },
        }
    );

    const auditTrail: LeadLostAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadLost,
        _date: new Date(),
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: { kind: AuthorKind.User, id: user?._id },
    };

    await collections.auditTrails.insertOne(auditTrail);

    return true;
};

export default requiresLoggedUser(resolver);
