import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { AuditTrailKind, AuthorKind, ModuleType } from '../../../../database/documents';
import { LeadFollowedUpAuditTrail } from '../../../../database/documents/AuditTrail/types/leads';
import { LeadStatus } from '../../../../database/documents/Lead';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import ppnAuth from '../../../../integrations/cap/ppnAuth';
import submitActivity from '../../../../integrations/cap/submissions/submitActivity';
import { createCapSubmissionAuditTrails } from '../../../../integrations/cap/utils/createCapSubmissionAuditTrails';
import getSupportingValuesFromLead from '../../../../integrations/cap/utils/getSupportingValuesFromLead';
import { LeadPolicyAction } from '../../../../permissions';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['createLeadFollowUp'] = async (
    root,
    { leadId, followUpValues },
    { getUser, getPermissionController, getTranslations, loaders }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['auditTrails']);

    const lead = await collections.leads.findOne({
        _id: leadId,
        '_versioning.isLatest': true,
        isDraft: false,
    });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Lead not found.' });
    }

    const hasPermission = permissionController.leads.mayOperateOn(lead, LeadPolicyAction.Update);

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    if (lead.status !== LeadStatus.SubmittedToCap) {
        throw new Error(`Lead with current status cannot be followed up.`);
    }

    const user = await getUser(false);

    const { capValues: existingCapValues, moduleId } = lead;
    const module = await loaders.moduleById.load(moduleId);

    if (module._type !== ModuleType.LaunchPadModule) {
        throw new Error('Only leads from LaunchPadModule can be followed up.');
    }

    const capSetting = await loaders.capSettingByModuleId.load(module.capModuleId);

    try {
        const authentication = await ppnAuth(capSetting);

        if (authentication.error) {
            throw new Error(t('auditTrails:application.defaultCapError.authFailed'));
        }

        const { dealerData, customerData, campaignGuid, salespersonGuid } = await getSupportingValuesFromLead(
            t,
            lead,
            authentication.access_token,
            capSetting
        );

        const { businessPartnerGuid, leadGuid } = existingCapValues;

        submitActivity({
            lead,
            t,
            capSetting,
            auth: authentication.access_token,
            dealerData,
            customerData,
            customerGuid: businessPartnerGuid,
            planEndDate: dayjs(followUpValues.scheduledDate),
            campaignGuid,
            leadGuid,
            salespersonGuid,
            description: followUpValues.remarks,
        });

        const auditTrail: LeadFollowedUpAuditTrail = {
            _id: new ObjectId(),
            _kind: AuditTrailKind.LeadFollowedUp,
            _date: new Date(),
            leadId: lead._id,
            leadSuiteId: lead._versioning.suiteId,
            remarks: followUpValues.remarks,
            scheduledDate: followUpValues.scheduledDate,
            author: { kind: AuthorKind.User, id: user?._id },
        };

        await collections.auditTrails.insertOne(auditTrail);
    } catch (error) {
        console.error({
            message: error?.message,
            data: {
                leadId: lead._id,
                moduleId,
                capSettingId: capSetting._id,
            },
        });

        await createCapSubmissionAuditTrails({
            lead,
            capActionAuditTrail: AuditTrailKind.CapActivitySubmitted,
            success: false,
            id: null,
        });

        return false;
    }

    return true;
};

export default requiresLoggedUser(resolver);
