import { ObjectId } from 'mongodb';
import { AuditTrailKind, AuthorKind, LeadContactedAuditTrail, LeadStatus } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { LeadPolicyAction } from '../../../../permissions';
import { getAdvancedVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['markAsContacted'] = async (
    root,
    { leadId },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const lead = await collections.leads.findOne({
        _id: leadId,
        '_versioning.isLatest': true,
        status: { $ne: LeadStatus.Drafted },
    });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Lead not found.' });
    }

    const hasPermission = permissionController.leads.mayOperateOn(lead, LeadPolicyAction.UpdateContact);

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    if (lead.status !== LeadStatus.PendingQualify) {
        throw new Error(`Lead with current status cannot be marked as contacted.`);
    }

    const user = await getUser(false);

    await collections.leads.updateOne(
        {
            _id: leadId,
            '_versioning.isLatest': true,
        },
        {
            $set: {
                status: LeadStatus.Contacted,
                ...getAdvancedVersioningByUserForUpdate(user._id),
            },
        }
    );

    const auditTrail: LeadContactedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.LeadContacted,
        _date: new Date(),
        leadId: lead._id,
        leadSuiteId: lead._versioning.suiteId,
        author: { kind: AuthorKind.User, id: user?._id },
    };

    await collections.auditTrails.insertOne(auditTrail);

    return true;
};

export default requiresLoggedUser(resolver);
