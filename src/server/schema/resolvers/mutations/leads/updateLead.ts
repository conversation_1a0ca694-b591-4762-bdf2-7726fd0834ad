import { ObjectId } from 'mongodb';
import { Customer, Lead, UserAuthor, LeadStatus } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { mergeLocalCustomerFields } from '../../../../database/helpers/customers';
import { LeadPolicyAction } from '../../../../permissions/types/leads';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { calculateCustomerChanges } from '../applications/submitChanges/updateHandlers/getUpdateHandlers';
import addLeadAmendmentAuditTrails from './utils/addLeadAmendmentAuditTrails';

// TODO: handle more update fields
const resolver: GraphQLMutationResolvers['updateLead'] = async (
    root,
    { leadId, input },
    { loaders, getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const user = await getUser();
    const permissionController = await getPermissionController();

    const versioning = getSimpleVersioningByUserForUpdate(user._id);

    const lead = await collections.leads.findOne({
        _id: leadId,
        '_versioning.isLatest': true,
        status: { $ne: LeadStatus.Drafted },
    });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Lead not found.' });
    }

    const hasPermission = lead.isLead
        ? permissionController.leads.mayOperateOn(lead, LeadPolicyAction.Update)
        : permissionController.leads.mayOperateOn(lead, LeadPolicyAction.UpdateContact);

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    if (!input.customer) {
        throw new InvalidInput({ customer: 'Customer data is required.' });
    }

    const module = await loaders.moduleById.load(lead.moduleId);
    const company = await loaders.companyById.load(module.companyId);
    const customer = await loaders.customerById.load(lead.customerId);

    const newCustomerFields = await calculateCustomerChanges(input.customer, company._id, loaders, lead.customerId);
    const mergedCustomerFields = mergeLocalCustomerFields(customer.fields, newCustomerFields);

    await collections.customers.updateMany(
        { '_versioning.suiteId': customer._versioning.suiteId },
        { $set: { '_versioning.isLatest': false } }
    );

    const newCustomer: Customer = {
        ...customer,
        _id: new ObjectId(),
        fields: mergedCustomerFields,
        _versioning: {
            ...customer._versioning,
            isLatest: true,
        },
    };

    await collections.customers.insertOne(newCustomer);

    const newLead: Lead = {
        ...lead,
        _id: new ObjectId(),
        customerId: newCustomer._id,
        _versioning: {
            ...lead._versioning,
            updatedAt: versioning['_versioning.updatedAt'],
            updatedBy: versioning['_versioning.updatedBy'] as UserAuthor,
            isLatest: true,
        },
    };

    // TODO: Need to check whether need to update the application using lead's suiteId since
    // currently leadId that stored on application is using lead id. Hence if the lead is updated, then
    // we can't refer to correct lead from application there's new lead record and the id has changed.

    await collections.leads.updateOne(
        { _id: leadId, '_versioning.isLatest': true },
        { $set: { '_versioning.isLatest': false } }
    );

    await collections.leads.insertOne(newLead);
    await addLeadAmendmentAuditTrails(lead, newLead, { collections, user, loaders });

    return newLead;
};

export default requiresLoggedUser(resolver);
