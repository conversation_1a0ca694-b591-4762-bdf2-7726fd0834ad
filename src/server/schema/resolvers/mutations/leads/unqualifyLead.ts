import { LeadStatus } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateCapSubmissionStatus } from '../../../../integrations/cap/utils/storeCapValuesToApplication';
import { LeadPolicyAction } from '../../../../permissions';
import { getAdvancedVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['unqualifyLead'] = async (
    root,
    { leadId },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const lead = await collections.leads.findOne({
        _id: leadId,
        '_versioning.isLatest': true,
        status: { $ne: LeadStatus.Drafted },
    });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Lead not found.' });
    }

    const hasPermission = permissionController.leads.mayOperateOn(lead, LeadPolicyAction.UpdateContact);

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    if (lead.status !== LeadStatus.PendingQualify && lead.status !== LeadStatus.Contacted) {
        throw new InvalidInput({ status: 'Lead with current status cannot be unqualified.' });
    }

    const user = await getUser(false);

    await collections.leads.updateOne(
        {
            _id: leadId,
            '_versioning.isLatest': true,
        },
        {
            $set: {
                status: LeadStatus.Unqualified,
                isLead: false,
                ...getAdvancedVersioningByUserForUpdate(user._id),
            },
        }
    );

    await updateCapSubmissionStatus({ lead, status: LeadStatus.Unqualified, user });

    return true;
};

export default requiresLoggedUser(resolver);
