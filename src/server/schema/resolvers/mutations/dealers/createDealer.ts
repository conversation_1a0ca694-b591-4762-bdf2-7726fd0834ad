import { ObjectId } from 'mongodb';
import { Dealer } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CompanyPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import transformLocationInputToGeoJSON from './shared';

const mutation: GraphQLMutationResolvers['createDealer'] = async (
    root,
    { companyId, dealerInput },
    { getUser, getPermissionController }
) => {
    const user = await getUser();
    const simpleVersioning = await getSimpleVersioningByUserForCreation(user._id);
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    if (!permissionController.companies.hasPolicyForAction(CompanyPolicyAction.CreateDealer)) {
        throw new InvalidPermission();
    }

    const company = await collections.companies.findOne({ _id: companyId });
    if (!company) {
        throw new InvalidInput({ companyId: 'invalid company ID' });
    }

    const hasRootPermission = permissionController.hasRootPermission();

    const limitFeature = hasRootPermission && company.allowLimitDealerFeature ? dealerInput.limitFeature : false;

    const { location, ...otherDealerInput } = dealerInput;

    const dealerInputWithSocialMedia = {
        ...otherDealerInput,
        contact: { ...otherDealerInput.contact, socialMedia: [] },
    };
    const document: Dealer = {
        ...dealerInputWithSocialMedia,
        _id: new ObjectId(),
        companyId,
        _versioning: simpleVersioning,
        isDeleted: false,
        limitFeature,
        location: transformLocationInputToGeoJSON(location),
    };

    await collections.dealers.insertOne(document);
    await mainQueue.add({ type: 'upsertPermissions', target: 'dealer', dealerId: document._id });

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createDealer' })(requiresLoggedUser(mutation));
