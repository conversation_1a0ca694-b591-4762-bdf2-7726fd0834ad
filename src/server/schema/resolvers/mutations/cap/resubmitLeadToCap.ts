import { CapAvailableLeadType, type Lead, LeadStatus } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { resubmitCap } from '../../../../integrations/cap';
import { resolveCapEligibility } from '../../../../integrations/cap/utils';
import { checkCapIntegrationIsEnabled } from '../../../../integrations/cap/utils/isApplicationAbleToSubmitCapCheck';
import { LeadPolicyAction } from '../../../../permissions';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { ApplicationKind, GraphQLMutationResolvers } from '../../definitions';
import mergeAndCreateNewCustomer from '../customer/utils/mergeAndCreateNewCustomer';

const resolver: GraphQLMutationResolvers['resubmitLeadToCap'] = async (
    root,
    { leadId, capValues, isTestDrive, updatedKyc, updatedTradeInVehicle },
    { loaders, getUser, getTranslations, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const { t } = await getTranslations(['auditTrails']);
    const permissionController = await getPermissionController();

    const lead = await collections.leads.findOne({ _id: leadId, '_versioning.isLatest': true });

    if (!lead) {
        throw new InvalidInput({ applicationId: 'Lead not found.' });
    }

    const hasPermission =
        (lead.status === LeadStatus.SubmissionFailed &&
            permissionController.leads.mayOperateOn(lead, LeadPolicyAction.UpdateContact)) ||
        (lead.status === LeadStatus.SubmittedWithError &&
            permissionController.leads.mayOperateOn(lead, LeadPolicyAction.Update));

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    if (!(lead.status === LeadStatus.SubmissionFailed || lead.status === LeadStatus.SubmittedWithError)) {
        throw new Error(`Application with current status cannot be resubmitted.`);
    }

    const user = await getUser(false);

    let { customerId } = lead;
    if (updatedKyc) {
        customerId = await mergeAndCreateNewCustomer({
            collections,
            loaders,
            newCustomerFieldsSettings: updatedKyc,
            lead,
        });
    }

    let latestLead = lead as Lead;

    if (capValues.businessPartnerGuid || capValues.leadGuid || updatedKyc || updatedTradeInVehicle) {
        const { capValues: existingCapValues } = lead;

        const updatedCapValues = {
            ...existingCapValues,
            businessPartnerGuid: capValues.businessPartnerGuid || existingCapValues.businessPartnerGuid,
            businessPartnerId: capValues.businessPartnerId || existingCapValues.businessPartnerId,
            leadGuid: capValues.leadGuid || existingCapValues.leadGuid,
            leadId: capValues.leadId || existingCapValues.leadId,
        };
        const value = await collections.leads.findOneAndUpdate(
            { _id: lead._id, '_versioning.isLatest': true },
            {
                $set: {
                    capValues: updatedCapValues,
                    ...(updatedKyc ? { customerId } : {}),
                    ...(updatedTradeInVehicle ? { tradeInVehicle: updatedTradeInVehicle } : {}),
                },
            },
            { returnDocument: 'after' }
        );

        latestLead = value as Lead;
    }

    if (
        [
            ApplicationKind.Event,
            ApplicationKind.Configurator,
            ApplicationKind.Finder,
            ApplicationKind.Standard,
            ApplicationKind.Launchpad,
        ].includes(lead.kind)
    ) {
        const isCapIntegrationEnabled = await checkCapIntegrationIsEnabled(latestLead, loaders);
        const { shouldSubmit, capModuleId } = await resolveCapEligibility(lead as CapAvailableLeadType);

        if (isCapIntegrationEnabled && shouldSubmit && capModuleId) {
            resubmitCap({
                t,
                lead: latestLead,
                isTestDrive,
                user,
            });
        }
    }

    return true;
};

export default requiresLoggedUser(resolver);
