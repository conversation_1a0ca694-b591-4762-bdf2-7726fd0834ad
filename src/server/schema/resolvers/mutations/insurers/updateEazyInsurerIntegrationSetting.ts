import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateEazyInsurerIntegrationSetting } from '../../../../database/helpers/settings';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers, InsurerIntegrationProvider } from '../../definitions';
import { performInsurerIntegrationActionValidations } from './shared';

const mutation: GraphQLMutationResolvers['updateEazyInsurerIntegrationSetting'] = async (
    root,
    { insurerId, settings },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();

    const permissionController = await getPermissionController();

    const insurer = await performInsurerIntegrationActionValidations(insurerId, permissionController);

    if (insurer.integration.provider !== InsurerIntegrationProvider.Eazy) {
        throw new Error('You cannot update the Eazy integration setting for this insurer');
    }
    await updateEazyInsurerIntegrationSetting(insurerId, settings);

    return collections.insurers.findOneAndUpdate(
        { _id: insurerId, isDeleted: false },
        { $set: { 'integration.provider': InsurerIntegrationProvider.Eazy } },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutation);
