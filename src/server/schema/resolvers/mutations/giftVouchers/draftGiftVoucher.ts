import { ObjectId } from 'bson';
import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import {
    AuditTrailKind,
    AuthorKind,
    Collections,
    CustomerKind,
    FinderVehicle,
    FinderVehicleManagementModule,
    GiftVoucher,
    GiftVoucherCreatedAuditTrail,
    GiftVoucherJourney,
    GiftVoucherStatus,
    LocalCustomer,
    LocalVariant,
    ModuleType,
    SimpleVehicleManagementModule,
    VehicleKind,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import executeGiftVoucherJourney from '../../../../journeys/giftVoucherJourney/executeGiftVoucherJourney';
import randomAlphaNumeric from '../../../../utils/randomAlphaNumeric';
import { Authoring, getAdvancedVersioningForCreation, getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware } from '../../../middlewares';
import {
    GraphQLApplicationVehicleDraft,
    GraphQLDraftGiftVoucherSettings,
    GraphQLMutationResolvers,
    InventoryKind,
    StockInventoryKind,
} from '../../definitions';
import { validateEndpoint } from '../applications/helpers';

const retrieveVehicle = async (
    collections: Collections,
    vehicleModule: SimpleVehicleManagementModule | FinderVehicleManagementModule,
    vehicleInputs: GraphQLApplicationVehicleDraft,
    setting: GraphQLDraftGiftVoucherSettings
): Promise<{ vehicle: LocalVariant | FinderVehicle; dealerId: ObjectId; stockPrice: number }> => {
    if (vehicleModule._type === ModuleType.SimpleVehicleManagement) {
        if (!isNil(vehicleInputs.existingSimpleLocalVehicleId)) {
            const vehicle = (await collections.vehicles.findOne({
                _id: vehicleInputs.existingSimpleLocalVehicleId,
                _kind: VehicleKind.LocalVariant,
                moduleId: vehicleModule._id,
                isDeleted: false,
                '_versioning.isLatest': true,
            })) as LocalVariant;

            return {
                vehicle,
                dealerId: setting.dealerId,
                stockPrice: vehicle.vehiclePrice,
            };
        }
        const inventory = await collections.inventories.findOne({ 'stocks._id': setting.stockId });
        if (inventory._kind !== InventoryKind.MobilityInventory) {
            throw new Error('InventoryKind not supported');
        }
        const stock = inventory.stocks.find(stock => stock._id.equals(setting.stockId));
        if (stock._kind !== StockInventoryKind.MobilityStock) {
            throw new Error('StockInventoryKind not support');
        }
        const vehicle = (await collections.vehicles.findOne({
            _kind: VehicleKind.LocalVariant,
            moduleId: vehicleModule._id,
            isDeleted: false,
            '_versioning.isLatest': true,
            '_versioning.suiteId': inventory.variantSuiteId,
        })) as LocalVariant;

        return {
            vehicle,
            dealerId: inventory.dealerId,
            stockPrice: stock.price,
        };
    }
    if (vehicleModule._type === ModuleType.FinderVehicleManagement) {
        if (!isNil(vehicleInputs.existingSimpleLocalVehicleId)) {
            const vehicle = (await collections.vehicles.findOne({
                _id: vehicleInputs.existingSimpleLocalVehicleId,
                _kind: VehicleKind.FinderVehicle,
                moduleId: vehicleModule._id,
                isDeleted: false,
                '_versioning.isLatest': true,
            })) as FinderVehicle;

            return {
                vehicle,
                dealerId: setting.dealerId,
                stockPrice: vehicle.listing.price.value,
            };
        }
        const inventory = await collections.inventories.findOne({ 'stocks._id': setting.stockId });
        if (inventory._kind !== InventoryKind.MobilityInventory) {
            throw new Error('InventoryKind not supported');
        }

        const stock = inventory.stocks.find(stock => stock._id.equals(setting.stockId));
        if (stock._kind !== StockInventoryKind.MobilityStock) {
            throw new Error('StockInventoryKind not support');
        }

        const vehicle = (await collections.vehicles.findOne({
            _kind: VehicleKind.FinderVehicle,
            moduleId: vehicleModule._id,
            isDeleted: false,
            '_versioning.isLatest': true,
            '_versioning.suiteId': inventory.variantSuiteId,
        })) as FinderVehicle;

        return {
            vehicle,
            dealerId: inventory.dealerId,
            stockPrice: stock.price,
        };
    }

    return {
        vehicle: null,
        dealerId: null,
        stockPrice: 0,
    };
};

const mutate: GraphQLMutationResolvers['draftGiftVoucher'] = async (root, { setting }, { loaders, getUser }) => {
    const { collections } = await getDatabaseContext();
    const { moduleId, mobilityModuleId, endpointId, vehicle: vehicleInputs, customer: customerInputs } = setting;
    // get the module
    const module = await collections.modules.findOne({ _id: moduleId });

    if (module._type !== ModuleType.GiftVoucherModule) {
        throw new InvalidInput({ 'setting.moduleId': 'Invalid Gift Voucher Module Id' });
    }

    const mobilityModule = await collections.modules.findOne({ _id: mobilityModuleId });

    if (mobilityModule._type !== ModuleType.MobilityModule) {
        throw new InvalidInput({ 'setting.mobilityModuleId': 'Invalid Mobility Module Id' });
    }

    if (!mobilityModule.giftVoucherModuleId.equals(module._id)) {
        throw new InvalidInput({ 'setting.moduleId': 'Gift Voucher Module Id is different in Mobility Module' });
    }

    const customerModule = await collections.modules.findOne({ _id: module.customerModuleId });
    const vehicleModule = await collections.modules.findOne({ _id: mobilityModule.vehicleModuleId });
    if (
        vehicleModule._type !== ModuleType.SimpleVehicleManagement &&
        vehicleModule._type !== ModuleType.FinderVehicleManagement
    ) {
        throw new Error('vehicle module not found');
    }
    // get router/endpoint when provided
    const origins = endpointId ? await validateEndpoint(endpointId, mobilityModule) : null;

    const user = await getUser(true);

    // build up versioning
    // customer ID will be defined later on if the kind is Customer
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id, date: new Date() }
        : { kind: AuthorKind.Customer, customerId: null, date: new Date() };

    // get/create the vehicle
    const { vehicle, dealerId, stockPrice } = await retrieveVehicle(collections, vehicleModule, vehicleInputs, setting);

    if (isNil(vehicle)) {
        // something was wrong with the vehicle inputs
        throw new InvalidInput({ 'setting.vehicle': 'Invalid vehicle inputs' });
    }

    // get/create the applicant (customer object)
    const applicant = await (async () => {
        if (customerModule._type === ModuleType.LocalCustomerManagement) {
            if (customerInputs.existingLocalCustomer) {
                if (authoring.kind === AuthorKind.Customer) {
                    // only authenticated user may draft with existing customers
                    throw new InvalidPermission();
                }

                // get the customer
                return collections.customers.findOne({
                    _id: customerInputs.existingLocalCustomer,
                    moduleId: customerModule._id,
                    // todo apply permissions to limit available customers
                    '_versioning.isLatest': true,
                });
            }

            if (customerInputs.newLocalCustomer) {
                const newCustomerId = new ObjectId();

                if (authoring.kind === AuthorKind.Customer) {
                    // update the authoring object to match the newly created customer ID
                    authoring.customerId = newCustomerId;
                }

                // create a new customer
                const customer: LocalCustomer = {
                    _id: newCustomerId,
                    _kind: CustomerKind.Local,
                    moduleId: customerModule._id,
                    kycPresetIds: [],
                    fields: convertLocalCustomerGraphQLFields(customerInputs.newLocalCustomer.fields),
                    isDeleted: false,
                    _versioning: getAdvancedVersioningForCreation(authoring),
                };

                // insert the customer in the database
                await collections.customers.insertOne(customer);

                return customer;
            }
        }

        return null;
    })();

    if (!applicant) {
        // something was wrong with the customer inputs
        throw new InvalidInput({ customer: 'Invalid customer inputs' });
    }

    const giftCode = randomAlphaNumeric(16)();

    const existingGiftVoucher = await collections.giftVouchers.findOne({ giftCode });

    /**
     * :: Ensuring unique gift code ::
     *
     * technically this impossible to happen with 2 ** 16 chances (0.0050686806224%)
     * if customAlpha generate unique value
     * but we try to block continue journey when happen
     */
    if (existingGiftVoucher) {
        throw new Error('same gift code generation found');
    }

    const giftVoucher: GiftVoucher = {
        _id: new ObjectId(),
        _versioning: getAdvancedVersioningForCreation(authoring),
        giftCode,
        status: GiftVoucherStatus.Unused,
        isDraft: true,
        purchaserId: applicant._id,
        dealerId,
        mobilityModuleId,

        value: stockPrice,
        moduleId: setting.moduleId,
        stockId: setting.stockId,

        languageId: setting.languageId,
        vehicleId: vehicle._id,
        // upload documents will be handled by `uploadApplicationDocument`
        documents: [],

        // spread origins
        ...origins,
        numberOfBookingReferenceDays: setting.numberOfBookingReferenceDays,

        purchasedDate: dayjs().toDate(),
    };

    await collections.giftVouchers.insertOne(giftVoucher);

    const giftVoucherJourney: GiftVoucherJourney = {
        _id: new ObjectId(),
        giftVoucherSuiteId: giftVoucher._versioning.suiteId,
        updatedAt: giftVoucher._versioning.createdAt,
        isReceived: false,
    };

    await collections.giftVoucherJourney.insertOne(giftVoucherJourney);

    // generate the trail
    const trail: GiftVoucherCreatedAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.GiftVoucherCreated,
        _date: applicant._versioning.createdAt,
        giftVoucherId: giftVoucher._id,
        giftVoucherSuiteId: giftVoucher._versioning.suiteId,
        author: getAuthorFromAuthoring(authoring),
    };

    // register the trail
    await collections.auditTrails.insertOne(trail);

    return executeGiftVoucherJourney({
        giftVoucher,
        identifier: 'drafting',
        origin: 'draft',
        user,
        payload: null,
    });
};

export default buildRateLimiterMiddleware({ operation: 'draftGiftVoucher' })(mutate);
