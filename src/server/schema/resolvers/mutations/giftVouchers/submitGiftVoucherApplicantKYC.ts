import { convertLocalCustomerGraphQLFields } from '../../../../database/helpers/customers';
import { ApplicantKYCStepPayload } from '../../../../journeys/common';
// eslint-disable-next-line max-len
import executeGiftVoucherJourneyFromGraphQL from '../../../../journeys/giftVoucherJourney/executeGiftVoucherJourneyFromGraphQL';
import { GraphQLMutationResolvers } from '../../definitions';

const resolver: GraphQLMutationResolvers['submitGiftVoucherApplicantKYC'] = async (
    root,
    { token, fields, saveDraft = false, customerKind },
    context
) =>
    executeGiftVoucherJourneyFromGraphQL<ApplicantKYCStepPayload>({
        token,
        identifier: 'applicant-kyc',
        payload: {
            fields: convertLocalCustomerGraphQLFields(fields),
            saveDraft,
            customerKind,
        },
        context,
    });

export default resolver;
