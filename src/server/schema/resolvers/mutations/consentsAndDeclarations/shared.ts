import type { ObjectId } from 'mongodb';
import { ModuleType } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { InvalidInput } from '../../../errors';

export const getConsentAndModuleFromId = async (id: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const consent = await collections.consentsAndDeclarations.findOne({ _id: id, isDeleted: false });

    if (!consent) {
        throw new InvalidInput({ consentId: 'Invalid Consent ID' });
    }

    const consentModule = await collections.modules.findOne({ _id: consent.moduleId });

    if (!consentModule || consentModule._type !== ModuleType.ConsentsAndDeclarations) {
        throw new InvalidInput({ moduleId: 'Invalid Module ID' });
    }

    return { consent, consentModule };
};

export const getConsentAndModuleFromSuiteId = async (suiteId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const consent = await collections.consentsAndDeclarations.findOne({
        '_versioning.suiteId': suiteId,
        '_versioning.isLatest': true,
        isDeleted: false,
    });

    if (!consent) {
        throw new InvalidInput({ consentId: 'Invalid Consent ID' });
    }

    const consentModule = await collections.modules.findOne({ _id: consent.moduleId });

    if (!consentModule || consentModule._type !== ModuleType.ConsentsAndDeclarations) {
        throw new InvalidInput({ moduleId: 'Invalid Module ID' });
    }

    return { consent, consentModule };
};
