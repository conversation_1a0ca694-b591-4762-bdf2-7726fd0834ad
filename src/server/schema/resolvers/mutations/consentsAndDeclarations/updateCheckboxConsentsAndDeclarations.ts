import { ObjectId } from 'mongodb';
import { CheckboxConsentsAndDeclarations, Condition } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { AgreementPolicyAction } from '../../../../permissions';
import {
    getAdvancedVersioningByUserForCreation,
    getSimpleVersioningByUserForUpdate,
} from '../../../../utils/versioning';
import { InvalidPermission, InvalidInput } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { ConsentsAndDeclarationsType, GraphQLMutationResolvers } from '../../definitions';
import { checkAllConditionSettings } from './checkConditionSettings';
import generateConditions from './generateConditions';
import { getConsentAndModuleFromSuiteId } from './shared';

const mutation: GraphQLMutationResolvers['updateCheckboxConsentsAndDeclarations'] = async (
    root,
    { suiteId, settings, conditionSettings },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const userData = await getUser();
    const versioning = getSimpleVersioningByUserForUpdate(userData._id);
    const permissionController = await getPermissionController();

    const { consent, consentModule } = await getConsentAndModuleFromSuiteId(suiteId);

    if (!permissionController.agreements.mayOperateOn(consent, AgreementPolicyAction.Update, consentModule)) {
        throw new InvalidPermission();
    }

    let conditions: Condition[] = [];
    if (conditionSettings.length > 0 && conditionSettings) {
        const isConditionSettingsValid = checkAllConditionSettings(conditionSettings);

        if (!isConditionSettingsValid) {
            throw new InvalidInput({ conditionSettings: 'Invalid Condition Settings' });
        } else {
            conditions = conditionSettings.map(setting => generateConditions(setting));
        }
    }

    const result = await collections.consentsAndDeclarations.findOneAndUpdate(
        { '_versioning.suiteId': suiteId, '_versioning.isLatest': true },
        { $set: { '_versioning.isLatest': false, ...versioning } },
        { returnDocument: 'after' }
    );

    if (!result) {
        return null;
    }
    const user = await getUser();

    const createVersioning = getAdvancedVersioningByUserForCreation(user._id, result._versioning.suiteId);

    const document: CheckboxConsentsAndDeclarations = {
        ...result,
        _id: new ObjectId(),
        _type: ConsentsAndDeclarationsType.Checkbox,
        ...settings,
        conditions,
        _versioning: createVersioning,
    };

    await collections.consentsAndDeclarations.insertOne(document);

    return document;
};

export default requiresLoggedUser(mutation);
