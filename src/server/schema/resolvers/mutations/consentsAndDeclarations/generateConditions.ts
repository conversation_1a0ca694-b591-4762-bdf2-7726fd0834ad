import { Condition } from '../../../../database/documents';
import { ConditionType, GraphQLConditionSettings } from '../../definitions';

const generateConditions = (conditionSettings: GraphQLConditionSettings): Condition => {
    const { type } = conditionSettings;

    switch (type) {
        case ConditionType.Or:
        case ConditionType.And: {
            const { logicConditionSettings } = conditionSettings;
            const children = logicConditionSettings.children.map(conditionSetting =>
                generateConditions(conditionSetting)
            );

            const condition: Condition = {
                type,
                children,
            };

            return condition;
        }

        case ConditionType.IsApplicationModule: {
            const { applicationModuleConditionSettings } = conditionSettings;
            const condition: Condition = {
                type,
                moduleId: applicationModuleConditionSettings.moduleId,
            };

            return condition;
        }

        case ConditionType.IsBank: {
            const { bankConditionSettings } = conditionSettings;
            const condition: Condition = {
                type,
                bankId: bankConditionSettings.bankId,
            };

            return condition;
        }

        case ConditionType.IsDealer: {
            const { dealerConditionsSettings } = conditionSettings;
            const condition: Condition = {
                type,
                dealerId: dealerConditionsSettings.dealerId,
            };

            return condition;
        }

        case ConditionType.IsInsurer: {
            const { insurerConditionSettings } = conditionSettings;
            const condition: Condition = {
                type,
                insurerId: insurerConditionSettings.insurerId,
            };

            return condition;
        }

        case ConditionType.isLocation: {
            const { locationConditionSettings } = conditionSettings;
            const condition: Condition = {
                type,
                locationId: locationConditionSettings.locationId,
                isHomeDelivery: locationConditionSettings.isHomeDelivery,
            };

            return condition;
        }

        case ConditionType.IsGiftVoucher: {
            const { giftVoucherConditionSettings } = conditionSettings;
            const condition: Condition = {
                type,
                moduleId: giftVoucherConditionSettings.moduleId,
            };

            return condition;
        }

        case ConditionType.SalesOfferAgreements: {
            const { salesOfferConditionSettings } = conditionSettings;
            const condition: Condition = {
                type,
                feature: salesOfferConditionSettings.feature,
            };

            return condition;
        }

        case ConditionType.IsApplicant:
        case ConditionType.IsGuarantor:
        case ConditionType.IsCorporate:
        case ConditionType.IsApplyingForFinancing:
        case ConditionType.IsApplyingForInsurance:
        case ConditionType.IsTestDrive:
        case ConditionType.IsShowroomVisit:
        case ConditionType.IsTradeIn:
        case ConditionType.WithMyinfo:
        case ConditionType.IsTestDriveProcess:
        case ConditionType.WithoutMyinfo:
        case ConditionType.ForCapQualification: {
            const condition: Condition = {
                type,
            };

            return condition;
        }
        default:
            throw new Error('Condition type is not supported');
    }
};

export default generateConditions;
