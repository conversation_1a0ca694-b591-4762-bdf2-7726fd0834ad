import getDatabaseContext from '../../../../database/getDatabaseContext';
import { AgreementPolicyAction } from '../../../../permissions';
import { getAdvancedVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getConsentAndModuleFromSuiteId } from './shared';

const mutation: GraphQLMutationResolvers['deleteConsentsAndDeclarations'] = async (
    root,
    { suiteId },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const user = await getUser();
    const permissionController = await getPermissionController();

    const { consent, consentModule } = await getConsentAndModuleFromSuiteId(suiteId);

    if (!permissionController.agreements.mayOperateOn(consent, AgreementPolicyAction.Delete, consentModule)) {
        throw new InvalidPermission();
    }

    const { modifiedCount } = await collections.consentsAndDeclarations.updateOne(
        { '_versioning.suiteId': suiteId, '_versioning.isLatest': true },
        { $set: { isDeleted: true, ...getAdvancedVersioningByUserForUpdate(user._id) } }
    );

    return modifiedCount > 0;
};

export default requiresLoggedUser(mutation);
