import { isEmpty } from 'lodash/fp';
import { ConditionType, GraphQLConditionSettings } from '../../definitions';

export const checkAllConditionSettings = (conditionSettings: GraphQLConditionSettings[]): boolean => {
    // check that all settings are valid
    const invalidConditionSetting = conditionSettings.some(setting => !checkConditionSetting(setting));

    if (invalidConditionSetting) {
        return false;
    }

    return true;
};

export const checkConditionSetting = (conditionSetting: GraphQLConditionSettings): boolean => {
    const { type } = conditionSetting;

    switch (type) {
        case ConditionType.Or:
        case ConditionType.And:
            return !isEmpty(conditionSetting.logicConditionSettings);

        case ConditionType.IsApplicationModule:
            return !isEmpty(conditionSetting.applicationModuleConditionSettings);

        case ConditionType.IsBank:
            return !isEmpty(conditionSetting.bankConditionSettings);

        case ConditionType.IsDealer:
            return !isEmpty(conditionSetting.dealerConditionsSettings);

        case ConditionType.IsInsurer:
            return !isEmpty(conditionSetting.insurerConditionSettings);

        case ConditionType.isLocation:
            return !isEmpty(conditionSetting.locationConditionSettings);

        case ConditionType.IsGiftVoucher:
            return !isEmpty(conditionSetting.giftVoucherConditionSettings);

        case ConditionType.SalesOfferAgreements:
            return !isEmpty(conditionSetting.salesOfferConditionSettings);

        case ConditionType.IsApplicant:
        case ConditionType.IsGuarantor:
        case ConditionType.IsCorporate:
        case ConditionType.IsApplyingForFinancing:
        case ConditionType.IsApplyingForInsurance:
        case ConditionType.IsTestDrive:
        case ConditionType.IsShowroomVisit:
        case ConditionType.IsTradeIn:
        case ConditionType.WithMyinfo:
        case ConditionType.WithoutMyinfo:
        case ConditionType.IsTestDriveProcess:
        case ConditionType.ForCapQualification:
            return true;

        default:
            return false;
    }
};
