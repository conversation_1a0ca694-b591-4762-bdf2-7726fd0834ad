import { ObjectId } from 'mongodb';
import { ModuleType, PorscheRetainModule } from '../../../../database/documents/modules';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updatePorscheRetainModuleIntegrationSetting } from '../../../../database/helpers/settings';
import { CompanyPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues/mainQueue';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const mutate: GraphQLMutationResolvers['createPorscheRetainModule'] = async (
    root,
    { companyId, settings },
    { getUser, getPermissionController }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    if (!permissionController.companies.hasPolicyForAction(CompanyPolicyAction.CreateModule)) {
        throw new InvalidPermission();
    }

    // check company ID validity
    const company = await collections.companies.findOne({ _id: companyId });

    if (!company) {
        throw new InvalidInput({ companyId: 'Invalid company ID' });
    }

    const companyHasPorscheRetainModule = await collections.modules.findOne({
        companyId,
        _type: ModuleType.PorscheRetainModule,
    });

    if (companyHasPorscheRetainModule) {
        throw new InvalidInput({ companyId: 'This module has exist for this company' });
    }

    const simpleVersioning = getSimpleVersioningByUserForCreation(user._id);

    const moduleId = new ObjectId();

    await updatePorscheRetainModuleIntegrationSetting(moduleId, settings, false);

    const document: PorscheRetainModule = {
        _id: moduleId,
        _type: ModuleType.PorscheRetainModule,
        companyId,
        displayName: settings.displayName,
        link: settings.link,
        launchPadModuleId: settings.launchPadModuleId,
        _versioning: simpleVersioning,
    };
    await collections.modules.insertOne(document);

    await mainQueue.add({ type: 'upsertPermissions', target: 'module', moduleId: document._id });

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createPorscheRetainModule' })(requiresLoggedUser(mutate));
