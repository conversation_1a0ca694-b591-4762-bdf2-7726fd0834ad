import { omit } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    DealershipSettingType,
    DealerTranslatedStringSetting,
    SalesOfferEmailContents,
    SalesOfferModule,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CompanyPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import getMarketTypeInput from '../../../../utils/getApplicationMarketInputs';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers, ModuleType } from '../../definitions';

const defaultEmptyTranslatedString: DealerTranslatedStringSetting = {
    defaultValue: { defaultValue: '', overrides: [] },
    overrides: [],
};

const defaultEmptyEmailContent: SalesOfferEmailContents = {
    contentText: defaultEmptyTranslatedString,
    subject: defaultEmptyTranslatedString,
    introTitle: defaultEmptyTranslatedString,
};

const mutation: GraphQLMutationResolvers['createSalesOfferModule'] = async (
    root,
    { companyId, settings },
    { getPermissionController, getUser }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    // check permissions
    if (!permissionController.companies.hasPolicyForAction(CompanyPolicyAction.CreateModule)) {
        throw new InvalidPermission();
    }

    // check company ID validity
    const company = await collections.companies.findOne({ _id: companyId });

    if (!company) {
        throw new InvalidInput({ companyId: 'Invalid company ID' });
    }

    const simpleVersioning = await getSimpleVersioningByUserForCreation(user._id);

    const market = getMarketTypeInput(settings.marketType);

    const document: SalesOfferModule = {
        ...omit(['paymentSetting', 'market', 'marketType'], settings),
        _id: new ObjectId(),
        _type: ModuleType.SalesOfferModule,
        companyId,
        displayName: settings.displayName,
        _versioning: simpleVersioning,
        paymentSetting: {
            ...settings.paymentSetting,
            _type: DealershipSettingType.PaymentSetting,
        },
        market,
        bankIds: [],
        insurerIds: [],
        dealerFinanceProducts: [],
        dealerInsuranceProducts: [],
        vehicleModuleId: settings.vehicleModuleId,
        bankModuleId: settings.bankModuleId,
        insuranceModuleId: settings.insuranceModuleId,

        vehicleDataWithPorscheCodeIntegrationSettingId: settings.vehicleDataWithPorscheCodeIntegrationSettingId,
        coeBiddingTerms: defaultEmptyTranslatedString,
        specificationTerms: defaultEmptyTranslatedString,
        vsaSigningInstructions: defaultEmptyTranslatedString,
        vsaTerms: defaultEmptyTranslatedString,
        emailContents: {
            combinedTemplate: defaultEmptyEmailContent,
            salesOfferTemplate: defaultEmptyEmailContent,
            singlePreOfferTemplate: defaultEmptyEmailContent,
        },
    };

    await collections.modules.insertOne(document);
    await mainQueue.add({ type: 'upsertPermissions', target: 'module', moduleId: document._id });

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createAutoplayModule' })(requiresLoggedUser(mutation));
