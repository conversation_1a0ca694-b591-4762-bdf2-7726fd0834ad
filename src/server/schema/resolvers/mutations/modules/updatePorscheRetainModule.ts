import { ModuleType } from '../../../../database/documents/modules';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updatePorscheRetainModuleIntegrationSetting } from '../../../../database/helpers/settings';
import { ModulePolicyAction } from '../../../../permissions';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const mutation: GraphQLMutationResolvers['updatePorscheRetainModule'] = async (
    root,
    { moduleId, settings },
    { getPermissionController, getUser }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const versioning = getSimpleVersioningByUserForUpdate(user._id);

    await updatePorscheRetainModuleIntegrationSetting(moduleId, settings, true);

    const value = await collections.modules.findOneAndUpdate(
        {
            $and: [
                { _id: moduleId, _type: ModuleType.PorscheRetainModule },
                permissionController.modules.getFilterQueryForAction(ModulePolicyAction.Update),
            ],
        },
        {
            $set: {
                ...versioning,
                displayName: settings.displayName,
                link: settings.link,
                launchPadModuleId: settings.launchPadModuleId,
            },
        },
        { returnDocument: 'after' }
    );

    return value;
};

export default requiresLoggedUser(mutation);
