import bcrypt from 'bcryptjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { ModuleType, TradeInModule, Authorization } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateTradeInSetting } from '../../../../database/helpers/settings';
import { CompanyPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const mutate: GraphQLMutationResolvers['createTradeInModule'] = async (
    root,
    { companyId, settings },
    { getUser, getPermissionController }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const company = await collections.companies.findOne({
        $and: [
            { _id: companyId },
            permissionController.companies.getFilterQueryForAction(CompanyPolicyAction.CreateModule),
        ],
    });

    if (!company) {
        throw new InvalidInput({ companyId: 'invalid company ID' });
    }

    const simpleVersioning = getSimpleVersioningByUserForCreation(user._id);

    const accessKey = nanoid(10);
    const salt = await bcrypt.genSalt(10);
    const secretKey = await bcrypt.hash(nanoid(20), salt);

    const authorization: Authorization = {
        accessKey,
        secretKey,
        _versioning: simpleVersioning,
        lastUsage: new Date(),
    };

    const document: TradeInModule = {
        _id: new ObjectId(),
        _type: ModuleType.TradeInModule,
        companyId,
        displayName: settings.displayName,
        _versioning: simpleVersioning,
        authorization,
    };

    await collections.modules.insertOne(document);

    await updateTradeInSetting(document._id, { ...settings.setting });

    await mainQueue.add({ type: 'upsertPermissions', target: 'module', moduleId: document._id });

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createTradeInModule' })(requiresLoggedUser(mutate));
