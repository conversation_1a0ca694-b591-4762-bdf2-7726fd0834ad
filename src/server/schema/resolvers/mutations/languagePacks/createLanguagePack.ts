import { ObjectId } from 'mongodb';
import { LanguageOrientation, LanguagePack } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getDefaultLocale } from '../../../../database/helpers/shared';
import { LanguagePolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const mutation: GraphQLMutationResolvers['createLanguagePack'] = async (
    root,
    { referenceName, displayName },
    { getPermissionController, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    if (!permissionController.languages.hasPolicyForAction(LanguagePolicyAction.Create)) {
        throw new InvalidPermission();
    }

    const user = await getUser();

    const document: LanguagePack = {
        _id: new ObjectId(),
        referenceName,
        displayName,
        orientation: LanguageOrientation.LeftToRight,
        code: await getDefaultLocale(),
        translations: {},
        _versioning: getSimpleVersioningByUserForCreation(user._id),
    };

    await collections.languagePacks.insertOne(document);
    await mainQueue.add({ type: 'upsertPermissions', target: 'language', languagePackId: document._id });

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createLanguagePack' })(requiresLoggedUser(mutation));
