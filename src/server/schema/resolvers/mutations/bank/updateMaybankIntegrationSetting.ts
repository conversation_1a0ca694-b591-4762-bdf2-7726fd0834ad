import { BankIntegrationProvider, SettingId } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateMaybankIntegrationSetting } from '../../../../database/helpers/settings';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { preformBankIntegrationActionValidations } from './shared';

const mutation: GraphQLMutationResolvers['updateMaybankIntegrationSetting'] = async (
    root,
    { bankId, settings },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();

    const permissionController = await getPermissionController();

    const bank = await preformBankIntegrationActionValidations(bankId, permissionController);
    const current = bank
        ? await collections.settings.findOne({ settingId: SettingId.MaybankIntegration, bankId: bank._id })
        : null;

    if (
        bank?.integration.provider !== BankIntegrationProvider.Maybank ||
        current?.settingId !== SettingId.MaybankIntegration
    ) {
        throw new Error('You cannot update the Maybank integration setting for this bank');
    }

    await updateMaybankIntegrationSetting(
        bank._id,
        {
            ...settings,
            encryptionPrivateKey: settings.encryptionPrivateKey || current.secrets.encryptionPrivateKey,
            signaturePrivateKey: settings.signaturePrivateKey || current.secrets.signaturePrivateKey,
        },
        true
    );

    return bank;
};

export default requiresLoggedUser(mutation);
