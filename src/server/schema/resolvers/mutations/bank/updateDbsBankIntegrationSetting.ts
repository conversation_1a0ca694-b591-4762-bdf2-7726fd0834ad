import { BankIntegrationProvider } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateDbsBankIntegrationSetting } from '../../../../database/helpers/settings';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { preformBankIntegrationActionValidations } from './shared';

const mutation: GraphQLMutationResolvers['updateDbsBankIntegrationSetting'] = async (
    root,
    { bankId, settings },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();

    const permissionController = await getPermissionController();

    const bank = await preformBankIntegrationActionValidations(bankId, permissionController);

    if (bank.integration.provider !== BankIntegrationProvider.DBS) {
        throw new Error('You cannot update the DBS integration setting for this bank');
    }

    await updateDbsBankIntegrationSetting(bankId, settings);

    return collections.banks.findOneAndUpdate(
        { _id: bankId, isDeleted: false },
        { $set: { 'integration.provider': BankIntegrationProvider.DBS } },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutation);
