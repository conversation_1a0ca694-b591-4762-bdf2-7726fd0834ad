import { ObjectId } from 'mongodb';
import { BankIntegration, BankIntegrationProvider, BankKind, ModuleType } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    updateUobBankIntegrationSetting,
    updateHlfBankIntegrationSetting,
    updateHlfBankV2IntegrationSetting,
    updateDbsBankIntegrationSetting,
    updateMaybankIntegrationSetting,
} from '../../../../database/helpers/settings';
import { BankPolicyAction, PermissionController } from '../../../../permissions';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { GraphQLBankIntegrationUpdateSettings } from '../../definitions';

export const getSystemBankAndModuleFromId = async (id: ObjectId) => {
    const { views, collections } = await getDatabaseContext();

    const bank = await views.systemBanks.findOne({ _id: id, isDeleted: false });

    if (!bank) {
        throw new InvalidInput({ bankId: 'Invalid Bank ID' });
    }

    const bankModule = await collections.modules.findOne({ _id: bank.moduleId });

    if (!bankModule || bankModule._type !== ModuleType.BankModule) {
        throw new InvalidInput({ moduleId: 'Invalid Module ID' });
    }

    return { bank, bankModule };
};

export const preformBankIntegrationActionValidations = async (
    bankId: ObjectId,
    permissionController: PermissionController
) => {
    const { collections } = await getDatabaseContext();

    const bank = await collections.banks.findOne({ _id: bankId, isDeleted: false });
    if (!bank) {
        throw new InvalidInput({ bankId: 'Invalid Bank ID' });
    }

    const module = await collections.modules.findOne({ _id: bank.moduleId });

    switch (bank.kind) {
        case BankKind.System: {
            if (!module || module._type !== ModuleType.BankModule) {
                throw new InvalidInput({ moduleId: 'Invalid Module ID' });
            }

            if (!permissionController.banks.mayOperateOn(bank, BankPolicyAction.Update, module)) {
                throw new InvalidPermission();
            }

            return bank;
        }

        case BankKind.External: {
            if (!module) {
                throw new InvalidInput({ moduleId: 'Invalid Module ID' });
            }

            return bank;
        }

        default:
            throw new Error('invalid bank kind');
    }
};

export const upsertBankIntegration = async (
    bankId: ObjectId,
    integrationSetting: GraphQLBankIntegrationUpdateSettings,
    upsert: boolean
): Promise<BankIntegration> => {
    if (integrationSetting.email) {
        return { provider: BankIntegrationProvider.Email, email: integrationSetting.email.email };
    }

    if (integrationSetting.hlf) {
        const value = await updateHlfBankIntegrationSetting(bankId, integrationSetting.hlf);

        return { provider: BankIntegrationProvider.HLF, settingId: value._id };
    }

    if (integrationSetting.hlfV2) {
        const value = await updateHlfBankV2IntegrationSetting(bankId, integrationSetting.hlfV2);

        return { provider: BankIntegrationProvider.HLFV2, settingId: value._id };
    }

    if (integrationSetting.uob) {
        const value = await updateUobBankIntegrationSetting(bankId, integrationSetting.uob);

        return { provider: BankIntegrationProvider.UOB, settingId: value._id };
    }

    if (integrationSetting.dbs) {
        const value = await updateDbsBankIntegrationSetting(bankId, integrationSetting.dbs);

        return { provider: BankIntegrationProvider.DBS, settingId: value._id };
    }

    if (integrationSetting.maybank) {
        const { _id } = await updateMaybankIntegrationSetting(bankId, integrationSetting.maybank, upsert);

        return { provider: BankIntegrationProvider.Maybank, settingId: _id };
    }

    if (integrationSetting.enbd) {
        return {
            provider: BankIntegrationProvider.ENBD,
            financeManager: integrationSetting.enbd.financeManager,
            companyName: integrationSetting.enbd.companyName,
            dealerCode: integrationSetting.enbd.dealerCode,
        };
    }

    throw new Error('Bank integration not found');
};
