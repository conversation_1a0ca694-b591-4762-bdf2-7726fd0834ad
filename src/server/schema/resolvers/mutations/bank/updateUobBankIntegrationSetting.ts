import { BankIntegrationProvider } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateUobBankIntegrationSetting } from '../../../../database/helpers/settings';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { preformBankIntegrationActionValidations } from './shared';

const mutation: GraphQLMutationResolvers['updateUobBankIntegrationSetting'] = async (
    root,
    { bankId, settings },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();

    const permissionController = await getPermissionController();

    const bank = await preformBankIntegrationActionValidations(bankId, permissionController);

    if (bank.integration.provider !== BankIntegrationProvider.UOB) {
        throw new Error('You cannot update the UOB integration setting for this bank');
    }

    await updateUobBankIntegrationSetting(bankId, settings);

    return collections.banks.findOneAndUpdate(
        { _id: bankId, isDeleted: false },
        { $set: { 'integration.provider': BankIntegrationProvider.UOB } },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutation);
