import { BankIntegrationProvider } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateHlfBankIntegrationSetting } from '../../../../database/helpers/settings';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { preformBankIntegrationActionValidations } from './shared';

const mutation: GraphQLMutationResolvers['updateHlfBankIntegrationSetting'] = async (
    root,
    { bankId, settings },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();

    const permissionController = await getPermissionController();

    const bank = await preformBankIntegrationActionValidations(bankId, permissionController);

    if (bank.integration.provider !== BankIntegrationProvider.HLF) {
        throw new Error('You cannot update the HLF integration setting for this bank');
    }

    await updateHlfBankIntegrationSetting(bankId, settings);

    return collections.banks.findOneAndUpdate(
        { _id: bankId, isDeleted: false },
        { $set: { 'integration.provider': BankIntegrationProvider.HLF } },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutation);
