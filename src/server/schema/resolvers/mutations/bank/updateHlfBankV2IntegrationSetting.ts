import { BankIntegrationProvider } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { updateHlfBankV2IntegrationSetting } from '../../../../database/helpers/settings';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { preformBankIntegrationActionValidations } from './shared';

const mutation: GraphQLMutationResolvers['updateHlfBankV2IntegrationSetting'] = async (
    root,
    { bankId, settings },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();

    const permissionController = await getPermissionController();

    const bank = await preformBankIntegrationActionValidations(bankId, permissionController);

    if (bank.integration.provider !== BankIntegrationProvider.HLFV2) {
        throw new Error('You cannot update the HLF V2 integration setting for this bank');
    }

    await updateHlfBankV2IntegrationSetting(bankId, settings);

    return collections.banks.findOneAndUpdate(
        { _id: bankId, isDeleted: false },
        { $set: { 'integration.provider': BankIntegrationProvider.HLFV2 } },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutation);
