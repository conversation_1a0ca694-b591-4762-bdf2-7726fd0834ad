import isEqual from 'fast-deep-equal';
import { TFunction } from 'i18next';
import { isEmpty } from 'lodash/fp';
import {
    ApplicationInsurancing,
    Collections,
    SalesOfferModule,
    SalesOfferFeatureStatus,
    SalesOfferFeatureKind,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
// eslint-disable-next-line max-len
import { validateInsurancingRequiredFields } from '../../../../utils/application/validations/applicationInsurancing';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { ApplicationMarket, GraphQLApplicationInsuranceSettings, GraphQLMutationResolvers } from '../../definitions';
import { getImpactedUpdates } from './helpers';
import { checkAndCancelOrDeleteApplication } from './shared';

const validateInsuranceInputs = async (
    insuranceInputs: GraphQLApplicationInsuranceSettings,
    salesOfferModule: SalesOfferModule,
    collections: Collections,
    t: TFunction
) => {
    // resolve the financing market
    const insurance = ((): ApplicationInsurancing => {
        if (insuranceInputs.default && salesOfferModule.market.type === ApplicationMarket.Default) {
            return { market: ApplicationMarket.Default, ...insuranceInputs.default };
        }

        if (insuranceInputs.singapore && salesOfferModule.market.type === ApplicationMarket.Singapore) {
            return {
                market: ApplicationMarket.Singapore,
                ...insuranceInputs.singapore,
                coe: insuranceInputs.singapore.coe,
            };
        }

        if (insuranceInputs.newZealand && salesOfferModule.market.type === ApplicationMarket.NewZealand) {
            return {
                market: ApplicationMarket.NewZealand,
                ...insuranceInputs.newZealand,
                ppsr: insuranceInputs.newZealand.ppsr,
                estFee: insuranceInputs.newZealand.estFee,
            };
        }

        throw new InvalidInput();
    })();

    const insuranceProduct = await collections.insuranceProducts.findOne({ _id: insurance.insuranceProductId });

    const hasErrors = validateInsurancingRequiredFields(insurance, insuranceProduct, t);
    if (!isEmpty(hasErrors)) {
        throw new InvalidInput();
    }

    return insurance;
};

const mutate: GraphQLMutationResolvers['updateInsuranceSalesOffer'] = async (
    root,
    { id, insurance },
    { loaders, getUser, getTranslations }
) => {
    const { t } = await getTranslations(['errors']);
    const { collections } = await getDatabaseContext();
    const user = await getUser();

    const salesOffer = await collections.salesOffers.findOne({
        _id: id,
    });

    const salesOfferModule = (await collections.modules.findOne({
        _id: salesOffer.moduleId,
    })) as SalesOfferModule;

    const latestInsurance = insurance.isEnabled
        ? await validateInsuranceInputs(insurance.insurance, salesOfferModule, collections, t)
        : null;

    const isSameInsurance = isEqual(salesOffer.insurance?.insurance, latestInsurance);
    // if insurance no change, then return the existing sales offer
    if (isSameInsurance) {
        return salesOffer;
    }

    // if insurance change, we need to check if has existing application to cancel or delete it
    await checkAndCancelOrDeleteApplication({ salesOffer, user, collections }, SalesOfferFeatureKind.Insurance);

    const impactedUpdates = await getImpactedUpdates(
        salesOffer,
        [SalesOfferFeatureKind.Insurance, SalesOfferFeatureKind.VSA],
        collections,
        user
    );

    const pipeline = {
        insurance: {
            ...salesOffer.insurance,
            insurance: latestInsurance,
            isEnabled: insurance.isEnabled,
            status: SalesOfferFeatureStatus.Updated,
            lastUpdatedAt: new Date(),
        },
        vsa: {
            ...salesOffer.vsa,
            status: SalesOfferFeatureStatus.Updated,
            lastUpdatedAt: new Date(),
        },
        latestInsuranceApplicationSuiteId: null,
        ...impactedUpdates,
    };

    const value = await collections.salesOffers.findOneAndUpdate(
        {
            _id: id,
        },
        {
            $set: {
                ...pipeline,
                _versioning: {
                    ...salesOffer._versioning,
                    ...getSimpleVersioningByUserForUpdate(user._id),
                },
            },
        },
        { returnDocument: 'after' }
    );

    return value;
};

export default mutate;
