import isEqual from 'fast-deep-equal';
import { TFunction } from 'i18next';
import { isEmpty } from 'lodash/fp';
import {
    ApplicationFinancing,
    Collections,
    SalesOfferModule,
    SalesOfferFeatureStatus,
    SalesOfferFeatureKind,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
// eslint-disable-next-line max-len
import { validateFinancingInputForRequiredFields } from '../../../../utils/application/validations/applicationFinancing';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { ApplicationMarket, GraphQLApplicationFinanceSettings, GraphQLMutationResolvers } from '../../definitions';
import { getImpactedUpdates } from './helpers';
import { checkAndCancelOrDeleteApplication } from './shared';

const validateFinancingInputs = async (
    financingInputs: GraphQLApplicationFinanceSettings,
    salesOfferModule: SalesOfferModule,
    collections: Collections,
    t: TFunction
) => {
    // resolve the financing market
    const financing = ((): ApplicationFinancing => {
        if (financingInputs.default && salesOfferModule.market.type === ApplicationMarket.Default) {
            return { market: ApplicationMarket.Default, ...financingInputs.default };
        }

        if (financingInputs.singapore && salesOfferModule.market.type === ApplicationMarket.Singapore) {
            return {
                market: ApplicationMarket.Singapore,
                ...financingInputs.singapore,
                coe: financingInputs.singapore.coe,
            };
        }

        if (financingInputs.newZealand && salesOfferModule.market.type === ApplicationMarket.NewZealand) {
            return {
                market: ApplicationMarket.NewZealand,
                ...financingInputs.newZealand,
                ppsr: financingInputs.newZealand.ppsr,
                estFee: financingInputs.newZealand.estFee,
            };
        }

        if (financingInputs.newZealand) {
            return { market: ApplicationMarket.NewZealand, ...financingInputs.newZealand };
        }

        throw new InvalidInput();
    })();

    const financeProduct = await collections.financeProducts.findOne({ _id: financing.financeProductId });

    const hasErrors = validateFinancingInputForRequiredFields(financing, financeProduct, t);
    if (!isEmpty(hasErrors)) {
        throw new InvalidInput();
    }

    return financing;
};

const mutate: GraphQLMutationResolvers['updateFinanceSalesOffer'] = async (
    root,
    { id, finance },
    { loaders, getUser, getTranslations }
) => {
    const { t } = await getTranslations(['errors']);
    const { collections } = await getDatabaseContext();
    const user = await getUser();

    const salesOffer = await collections.salesOffers.findOne({
        _id: id,
    });

    const salesOfferModule = (await collections.modules.findOne({
        _id: salesOffer.moduleId,
    })) as SalesOfferModule;

    const latestFinancing = finance.isEnabled
        ? await validateFinancingInputs(finance.finance, salesOfferModule, collections, t)
        : null;

    const isSameFinancing = isEqual(salesOffer.finance.finance, latestFinancing);
    // if financing no change, then return the existing sales offer
    if (isSameFinancing) {
        return salesOffer;
    }

    // if finance change, we need to check for existing application to cancel or delete it
    await checkAndCancelOrDeleteApplication({ salesOffer, user, collections }, SalesOfferFeatureKind.Finance);

    const impactedUpdates = await getImpactedUpdates(
        salesOffer,
        [SalesOfferFeatureKind.Finance, SalesOfferFeatureKind.VSA],
        collections,
        user
    );

    const pipeline = {
        finance: {
            ...salesOffer.finance,
            finance: latestFinancing,
            isEnabled: finance.isEnabled,
            status: SalesOfferFeatureStatus.Updated,
            lastUpdatedAt: new Date(),
        },
        vsa: {
            ...salesOffer.vsa,
            status: SalesOfferFeatureStatus.Updated,
            lastUpdatedAt: new Date(),
        },

        latestFinancingApplicationSuiteId: null,
        ...impactedUpdates,
    };

    const value = await collections.salesOffers.findOneAndUpdate(
        {
            _id: id,
        },
        {
            $set: {
                ...pipeline,
                _versioning: {
                    ...salesOffer._versioning,
                    ...getSimpleVersioningByUserForUpdate(user._id),
                },
            },
        },
        { returnDocument: 'after' }
    );

    return value;
};

export default mutate;
