import { ObjectId } from 'mongodb';
import { generateToken, consumeToken } from '../../../../core/tokens';
import { SalesOfferFeatureKind } from '../../../../database';

export const TOKEN_EXPIRE_TIME = 15 * 60;

export type Origin = 'remote-deposit' | 'remote-signing';

export type SalesOfferJourneyTokenPayload = {
    userId: ObjectId | null;
    featureKinds: SalesOfferFeatureKind[];
    salesOfferId: ObjectId;
    leadSuiteId: ObjectId;
    origin: Origin;
};

const generateSalesOfferJourneyToken = (
    payload: SalesOfferJourneyTokenPayload,
    /** Expires in minute: i.e: 1 -> 1 minute, 15 -> 15minutes */
    expiresIn: number
) => generateToken<SalesOfferJourneyTokenPayload>('journey', payload, expiresIn * 60);

export const consumeSalesOfferJourneyToken = (token: string) =>
    consumeToken<SalesOfferJourneyTokenPayload>('journey', token);

export default generateSalesOfferJourneyToken;
