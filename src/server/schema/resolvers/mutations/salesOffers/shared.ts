import isEqual from 'fast-deep-equal';
import { isNil, uniq, uniqWith } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    AdvancedVersioning,
    AgreementsJourney,
    ApplicationFinancing,
    ApplicationInsurancing,
    ApplicationJourney,
    ApplicationStage,
    AuthorKind,
    CitizenshipType,
    Collections,
    ConsentsAndDeclarations,
    Customer,
    getKYCFieldsFromPresets,
    getKYCPresetsForLead,
    getKYCPresetsForSalesOffer,
    KYCPreset,
    LaunchpadLead,
    LaunchPadModule,
    LocalCustomerField,
    LocalCustomerManagementModule,
    LocalCustomerManagementModuleKycField,
    MarketingPlatform,
    ModuleType,
    SalesOffer,
    SalesOfferApplication,
    SalesOfferFeatureKind,
    SalesOfferFeatureStatus,
    SalesOfferModule,
    User,
    VehicleDataWithPorscheCodeIntegrationSetting,
} from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import {
    convertLocalCustomerGraphQLFields,
    createNewCustomerVersion,
    getLocalCustomerAggregatedFields,
    mergeLocalCustomerFields,
    sortLocalCustomerFields,
} from '../../../../database/helpers/customers';
// eslint-disable-next-line max-len
// eslint-disable-next-line max-len
import fetchVehicleDataWithPorscheCode from '../../../../integrations/porscheVehicleData/fetchVehicleDataWithPorscheCode';
import {
    IrisBackground,
    IrisEnvironment,
    IrisFormat,
    Marketplace,
    Top,
} from '../../../../integrations/porscheVehicleData/graphql/schema';
// eslint-disable-next-line max-len
import {} from '../../../../../app/api';
// eslint-disable-next-line max-len
import retrievePorscheCodeLocalVariant from '../../../../integrations/porscheVehicleData/retrievePorscheCodeLocalVariant';
import { Loaders } from '../../../../loaders';
import { ApplicationCancelSource } from '../../../../queues/implementations/onApplicationCancelled';
import { mainQueue } from '../../../../queues/mainQueue';
import { updateFinancing, updateInsurance } from '../../../../utils/calculator';
import { InputPrices } from '../../../../utils/calculator/shared/typings';
import { Authoring, getAuthorFromAuthoring } from '../../../../utils/versioning';
import {
    ApplicationKind,
    ConsentsAndDeclarationsType,
    CustomerKind,
    GraphQLApplicantAgreementSettings,
    GraphQLLocalCustomerFieldSettings,
    KycFieldPurpose,
    LocalCustomerFieldKey,
    SalesOfferAgreementKind,
} from '../../definitions';
import { AgreedConsentsPayload, getSalesOfferAgreements } from '../../types/applications/shared';

/**
 * Create Sales Offer
 * :: initialize finance calculator
 * :: initialize insurance calculator
 *
 * [Main Details, Vehicle]
 * :: update finance calculator BE
 * :: update insurance calculator BE
 *
 * before update finance calculator / insurance calculator
 * - need to check `finance.isEnabled` / `insurance.isEnabled`,
 * :: if turn off = no need to recalculate
 *
 * [Finance, Insurance]
 * FE will handle the computation logic (similar to application feature calcalator)
 * so we just need to pass it in payload exact same as `ApplicationFinancing` / `ApplicationInsurancing`
 */

// Please refers to this link for more details:
// https://appvantage.atlassian.net/browse/PSE-65

export const defaultPorscheVehicleApiVariable = {
    environment: IrisEnvironment.STUDIO,
    format: IrisFormat.PNG,
    width: 1080,
    height: 1920,
    top: Top.OPEN,
    background: IrisBackground.TRANSPARENT,
    marketplace: Marketplace.SG,
    languageTags: ['sg'],
};

export const retrievePorscheVehicleDataByPorscheCode = async (
    collections: Collections,
    salesOfferId: ObjectId,
    porscheCode: string
) => {
    const salesOffer = await collections.salesOffers.findOne({
        _id: salesOfferId,
    });

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const salesOfferModule = (await collections.modules.findOne({
        _id: salesOffer.moduleId,
    })) as SalesOfferModule;

    if (!salesOfferModule || salesOfferModule._type !== ModuleType.SalesOfferModule) {
        throw new Error('Sales offer module not found');
    }

    const existedLead = await collections.leads.findOne({
        '_versioning.suiteId': salesOffer.leadSuiteId,
        '_versioning.isLatest': true,
    });

    const launchpadModule = (await collections.modules.findOne({
        _id: existedLead.moduleId,
    })) as LaunchPadModule;

    const setting = (await collections.settings.findOne({
        _id: salesOfferModule.vehicleDataWithPorscheCodeIntegrationSettingId,
    })) as VehicleDataWithPorscheCodeIntegrationSetting;

    const resultAPI = await fetchVehicleDataWithPorscheCode(
        porscheCode,
        defaultPorscheVehicleApiVariable.languageTags,
        defaultPorscheVehicleApiVariable.environment,
        defaultPorscheVehicleApiVariable.format,
        defaultPorscheVehicleApiVariable.width,
        defaultPorscheVehicleApiVariable.height,
        defaultPorscheVehicleApiVariable.top,
        defaultPorscheVehicleApiVariable.background,
        defaultPorscheVehicleApiVariable.marketplace,
        setting
    );

    if (!resultAPI) {
        throw new Error('No Porsche Vehicle Data found');
    }

    const { orderTypeCode } = resultAPI.vehicleDataForCustomerSpecifiedVehicle;

    const { vehicleId } = await retrievePorscheCodeLocalVariant(orderTypeCode, launchpadModule.vehicleModuleId);

    return { vehicleId, salesOffer, resultAPI, orderTypeCode };
};

export const updateCalculator = async (
    salesOffer: SalesOffer,
    prices: InputPrices,
    loaders: Loaders
): Promise<{ finance: ApplicationFinancing; insurance: ApplicationInsurancing }> => {
    let finance;
    let insurance;
    if (salesOffer.finance.isEnabled) {
        finance = await updateFinancing({
            variantId: salesOffer.vehicle.vehicleId,
            salesOfferModuleId: salesOffer.moduleId,
            leadSuiteId: salesOffer.leadSuiteId,
            prices,
            existingFinancing: salesOffer.finance.kind === SalesOfferFeatureKind.Finance && salesOffer.finance.finance,
            loaders,
        });
    }

    if (salesOffer.insurance.isEnabled) {
        insurance = await updateInsurance({
            variantId: salesOffer.vehicle.vehicleId,
            salesOfferModuleId: salesOffer.moduleId,
            leadSuiteId: salesOffer.leadSuiteId,
            prices,
            existingInsurance:
                salesOffer.insurance.kind === SalesOfferFeatureKind.Insurance && salesOffer.insurance.insurance,
            loaders,
        });
    }

    return {
        finance,
        insurance,
    };
};

export const updateFinanceInsurancePipeline = async (
    salesOffer: SalesOffer,
    finance: ApplicationFinancing,
    insurance: ApplicationInsurancing,
    user: User,
    collections: Collections
) => {
    const hasFinanceChanges = !isEqual(salesOffer.finance.finance, finance);
    const hasInsuranceChanges = !isEqual(salesOffer.insurance.insurance, insurance);

    if (hasFinanceChanges) {
        await checkAndCancelOrDeleteApplication({ salesOffer, user, collections }, SalesOfferFeatureKind.Finance);
    }

    if (hasInsuranceChanges) {
        await checkAndCancelOrDeleteApplication({ salesOffer, user, collections }, SalesOfferFeatureKind.Insurance);
    }

    return {
        // overwrite the finance and insurance if they are enabled and changed
        ...(!isNil(finance) &&
            hasFinanceChanges && {
                finance: {
                    ...salesOffer.finance,
                    finance,
                    status: SalesOfferFeatureStatus.Updated,
                    lastUpdatedAt: new Date(),
                },
                latestFinancingApplicationSuiteId: null,
            }),
        ...(!isNil(insurance) &&
            hasInsuranceChanges && {
                insurance: {
                    ...salesOffer.insurance,
                    insurance,
                    status: SalesOfferFeatureStatus.Updated,
                    lastUpdatedAt: new Date(),
                },
                latestInsuranceApplicationSuiteId: null,
            }),
    };
};

export const constructBaseDataForSalesOfferFeatureUpdate = async (
    collections: Collections,
    salesOfferModuleId: ObjectId,
    getUser: (optional?: false) => Promise<User>,
    leadSuiteId: ObjectId
) => {
    const salesOfferModule = (await collections.modules.findOne({
        _id: salesOfferModuleId,
    })) as SalesOfferModule;

    const user = await getUser();

    if (!salesOfferModule || salesOfferModule._type !== ModuleType.SalesOfferModule) {
        throw new Error('Sales offer module not found');
    }

    const existedLead = await collections.leads.findOne({
        '_versioning.suiteId': leadSuiteId,
        '_versioning.isLatest': true,
    });
    if (!existedLead) {
        throw new Error('Lead not found');
    }

    const dealer = await collections.dealers.findOne({
        _id: existedLead.dealerId,
    });
    if (!dealer) {
        throw new Error('Dealer not found');
    }

    const { companyId } = salesOfferModule;
    const company = await collections.companies.findOne({
        _id: companyId,
    });
    if (!company) {
        throw new Error('Company not found');
    }

    return {
        company,
        dealer,
        existedLead,
        salesOfferModule,
        user,
    };
};

export default defaultPorscheVehicleApiVariable;

const validateKYCInputs = (
    kycFieldSettings: LocalCustomerManagementModuleKycField[],
    kycPresets: KYCPreset[],
    payloadKYCFields: LocalCustomerField[]
) => {
    // extract fields from kycPresets
    const allFields = getKYCFieldsFromPresets(kycFieldSettings, kycPresets);

    // get aggreatedFields
    const aggreatedFields = getLocalCustomerAggregatedFields({ fields: payloadKYCFields } as Customer);

    // extract LocalCustomerFieldKey which are mandatory
    const requiredFields: LocalCustomerFieldKey[] = uniq(
        allFields
            .filter(field => {
                const common = field.isRequired === true && field.purpose.includes(KycFieldPurpose.KYC);

                switch (field.key) {
                    case LocalCustomerFieldKey.IdentityNumber:
                        if (
                            aggreatedFields.citizenship === CitizenshipType.SingaporeanOrPr ||
                            aggreatedFields.citizenship === CitizenshipType.Malaysian
                        ) {
                            return common;
                        }

                        return false;

                    case LocalCustomerFieldKey.Passport:
                        if (aggreatedFields.citizenship === CitizenshipType.Others) {
                            return common;
                        }

                        return false;

                    // trade in vehicle validation will be done when store it in application
                    case LocalCustomerFieldKey.CurrentVehicleSource:
                    case LocalCustomerFieldKey.CurrentVehicleOwnership:
                    case LocalCustomerFieldKey.CurrentVehicleMake:
                    case LocalCustomerFieldKey.CurrentVehicleModel:
                    case LocalCustomerFieldKey.CurrentVehicleEquipmentLine:
                    case LocalCustomerFieldKey.CurrentVehicleModelYear:
                    case LocalCustomerFieldKey.CurrentVehiclePurchaseYear:
                    case LocalCustomerFieldKey.CurrentVehicleEngineType:
                    case LocalCustomerFieldKey.CurrentVehicleRegistrationNumber:
                    case LocalCustomerFieldKey.CurrentVehicleMileage:
                    case LocalCustomerFieldKey.CurrentVehicleContractEnd:
                    case LocalCustomerFieldKey.CurrentVehiclePotentialReplacement:
                    case LocalCustomerFieldKey.CurrentVehicleVin:
                        return false;

                    default:
                        return common;
                }
            })
            .map(field => field.key)
    );

    return !requiredFields.some(key => isNil(aggreatedFields[key]));
};

type SalesOfferAgreementFeatureKind = {
    feature: SalesOfferFeatureKind;
    salesOfferAgreementFeature: SalesOfferAgreementKind;
};

const mappingSalesOfferFeatureKinds = (featureKinds: SalesOfferFeatureKind[]): SalesOfferAgreementFeatureKind[] =>
    featureKinds.map(kind => {
        switch (kind) {
            case SalesOfferFeatureKind.Finance:
                return { feature: SalesOfferFeatureKind.Finance, salesOfferAgreementFeature: null };

            case SalesOfferFeatureKind.Insurance:
                return { feature: SalesOfferFeatureKind.Insurance, salesOfferAgreementFeature: null };

            case SalesOfferFeatureKind.MainDetails:
                return {
                    feature: SalesOfferFeatureKind.MainDetails,
                    salesOfferAgreementFeature: SalesOfferAgreementKind.COE,
                };

            case SalesOfferFeatureKind.Vehicle:
                return {
                    feature: SalesOfferFeatureKind.Vehicle,
                    salesOfferAgreementFeature: SalesOfferAgreementKind.Specification,
                };

            case SalesOfferFeatureKind.VSA: {
                return {
                    feature: SalesOfferFeatureKind.VSA,
                    salesOfferAgreementFeature: SalesOfferAgreementKind.VSA,
                };
            }

            default:
                throw new Error(`Unsupported feature kind: ${kind}`);
        }
    });

const updateCustomer = async (
    featureKinds: SalesOfferFeatureKind[],
    existingCustomer: Customer,
    existingLead: LaunchpadLead,
    salesOffer: SalesOffer,
    salesOfferModule: SalesOfferModule,
    launchpadModule: LaunchPadModule,
    customerModule: LocalCustomerManagementModule,
    payloadKYCFields: LocalCustomerField[],
    user: User | null
) => {
    // getting the KYC presets for the sales offer based on the feature kinds
    const kycPresetsForSalesOffer = (
        await Promise.all(
            mappingSalesOfferFeatureKinds(featureKinds).map(({ feature, salesOfferAgreementFeature }) =>
                getKYCPresetsForSalesOffer(
                    salesOffer,
                    salesOfferModule,
                    launchpadModule,
                    feature,
                    salesOfferAgreementFeature
                )
            )
        )
    ).flat();

    // getting kyc presets from launchpad lead
    const kycPresetsFromLead = await getKYCPresetsForLead(existingLead);

    // validate payload kycFields
    const isValid = validateKYCInputs(customerModule.kycFields, kycPresetsForSalesOffer, payloadKYCFields);
    if (!isValid) {
        throw new Error('KYC inputs are not valid. Please check the required fields.');
    }

    // merging the KYC presets from sales offer and lead
    const kycPresets = [...kycPresetsFromLead, ...kycPresetsForSalesOffer];

    kycPresets.forEach(kyc => {
        if (kyc.fields.some(field => field.key === LocalCustomerFieldKey.UAEDrivingLicense)) {
            kyc.fields.push({
                isRequired: true,
                key: LocalCustomerFieldKey.Birthday,
                purpose: [KycFieldPurpose.KYC],
            });
        }

        const citizenship = kyc.fields.some(field => field.key === LocalCustomerFieldKey.Citizenship);
        const identityNumber = kyc.fields.some(field => field.key === LocalCustomerFieldKey.IdentityNumber);

        if (citizenship) {
            kyc.fields.push({
                isRequired: false,
                key: LocalCustomerFieldKey.Passport,
                purpose: [KycFieldPurpose.KYC],
            });

            if (!identityNumber) {
                kyc.fields.push({
                    isRequired: false,
                    key: LocalCustomerFieldKey.IdentityNumber,
                    purpose: [KycFieldPurpose.KYC],
                });
            }
        }
    });

    const newCustomerId = new ObjectId();

    // build up versioning
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id }
        : { kind: AuthorKind.Customer, customerId: newCustomerId };

    const versioningUpdate: AdvancedVersioning = {
        ...existingCustomer._versioning,
        updatedBy: getAuthorFromAuthoring(authoring),
        updatedAt: new Date(),
        isLatest: true,
    };

    // create updated customer document
    const newCustomer: Customer = {
        ...existingCustomer,
        _id: newCustomerId,
        _kind: CustomerKind.Local,
        moduleId: launchpadModule.customerModuleId,
        kycPresetIds: uniqWith(
            (a, b) => a.equals(b),
            kycPresets.map(kyc => kyc._id)
        ),
        fields: sortLocalCustomerFields(
            customerModule.kycFields.sort((a, b) => a.order - b.order),
            kycPresets,
            mergeLocalCustomerFields(existingCustomer.fields, payloadKYCFields)
        ),
        isDeleted: false,
        _versioning: versioningUpdate,
    };

    const upsertedCustomer = await createNewCustomerVersion(newCustomer);

    return upsertedCustomer;
};

const retrieveAgreements = async (
    salesOffer: SalesOffer,
    featureKinds: SalesOfferFeatureKind[],
    consents: GraphQLApplicantAgreementSettings[]
) => {
    const salesOfferAgreements = await getSalesOfferAgreements(
        salesOffer,
        SalesOfferFeatureKind.VSA,
        'local',
        SalesOfferAgreementKind.VSA
    );
    const coe = await getSalesOfferAgreements(
        salesOffer,
        SalesOfferFeatureKind.MainDetails,
        'local',
        SalesOfferAgreementKind.COE
    );
    const specification = await getSalesOfferAgreements(
        salesOffer,
        SalesOfferFeatureKind.Vehicle,

        'local',
        SalesOfferAgreementKind.Specification
    );

    const finance = await getSalesOfferAgreements(salesOffer, SalesOfferFeatureKind.Finance, 'local', null);
    const insurance = await getSalesOfferAgreements(salesOffer, SalesOfferFeatureKind.Insurance, 'local', null);

    return {
        salesOffer: featureKinds.includes(SalesOfferFeatureKind.VSA) ? salesOfferAgreements : null,
        coe: featureKinds.includes(SalesOfferFeatureKind.MainDetails) ? coe : null,
        specification: featureKinds.includes(SalesOfferFeatureKind.Vehicle) ? specification : null,
        finance: featureKinds.includes(SalesOfferFeatureKind.Finance) ? finance : null,
        insurance: featureKinds.includes(SalesOfferFeatureKind.Insurance) ? insurance : null,
    };
};

const consolidateAgreedConsent = (
    featureConsent: ConsentsAndDeclarations,
    payloadConsents: GraphQLApplicantAgreementSettings[]
): AgreementsJourney => {
    const find = payloadConsents.find(payload => payload.id.equals(featureConsent._id));
    switch (featureConsent._type) {
        case ConsentsAndDeclarationsType.Marketing: {
            const clearPlatform = Object.keys(featureConsent.platform)
                .map(key => ({ [key]: false }))
                .reduce((acc, i) => ({ ...acc, ...i }), {});

            return {
                ...featureConsent,
                consentId: featureConsent._id,
                isAgreed: !!find,
                date: new Date(),
                platformsAgreed: (find ? find?.platformsAgreed : clearPlatform) as MarketingPlatform,
            };
        }

        default: {
            return {
                ...featureConsent,
                consentId: featureConsent._id,
                isAgreed: !!find,
                date: new Date(),
            };
        }
    }
};

export type AgreedConsentsJourney = {
    coeAgreedConsent: AgreementsJourney[];
    specificationAgreedConsent: AgreementsJourney[];
    financeAgreedConsent: AgreementsJourney[];
    insuranceAgreedConsent: AgreementsJourney[];
    salesOfferAgreedConsent: AgreementsJourney[];
};

const resolveFeatureAgreementException = async (
    feature: AgreedConsentsPayload[] | null,
    payloadConsents: GraphQLApplicantAgreementSettings[]
) => {
    if (isNil(feature)) {
        return [];
    }

    return feature.map(featureConsent => consolidateAgreedConsent(featureConsent, payloadConsents));
};

const getConsentChanges = async (
    featureKinds: SalesOfferFeatureKind[],
    payloadConsents: GraphQLApplicantAgreementSettings[],
    salesOffer: SalesOffer
): Promise<AgreedConsentsJourney> => {
    const {
        coe,
        specification,
        finance,
        insurance,
        salesOffer: salesOfferConsents,
    } = await retrieveAgreements(salesOffer, featureKinds, payloadConsents);

    const specificationAgreedConsent = await resolveFeatureAgreementException(specification, payloadConsents);
    const salesOfferAgreedConsent = await resolveFeatureAgreementException(salesOfferConsents, payloadConsents);
    const financeAgreedConsent = await resolveFeatureAgreementException(finance, payloadConsents);
    const insuranceAgreedConsent = await resolveFeatureAgreementException(insurance, payloadConsents);
    const coeAgreedConsent = await resolveFeatureAgreementException(coe, payloadConsents);

    return {
        salesOfferAgreedConsent,
        coeAgreedConsent,
        specificationAgreedConsent,
        financeAgreedConsent,
        insuranceAgreedConsent,
    };
};

const extractArguments = async (id: ObjectId, collections: Collections, loaders: Loaders) => {
    const salesOffer = await collections.salesOffers.findOne({
        _id: id,
    });

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const salesOfferModule = await loaders.moduleById.load(salesOffer.moduleId);
    if (!salesOfferModule || salesOfferModule._type !== ModuleType.SalesOfferModule) {
        throw new Error('Sales offer module not found or is not a SalesOffer module');
    }

    const lead = await collections.leads.findOne({
        '_versioning.suiteId': salesOffer.leadSuiteId,
        '_versioning.isLatest': true,
    });

    if (!lead || lead.kind !== ApplicationKind.Launchpad) {
        throw new Error('Lead not found for the sales offer');
    }

    const launchpadModule = await loaders.moduleById.load(lead.moduleId);
    if (!launchpadModule || launchpadModule._type !== ModuleType.LaunchPadModule) {
        throw new Error('Module not found or is not a LaunchPad module');
    }

    const customerModule = await loaders.moduleById.load(launchpadModule.customerModuleId);
    if (!customerModule || customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('Customer module not found or is not a Customer module');
    }
    const existingCustomer = await loaders.customerById.load(lead.customerId);
    if (!existingCustomer) {
        throw new Error('Customer not found for the lead');
    }

    return {
        salesOffer,
        lead,
        launchpadModule,
        customerModule,
        existingCustomer,
        salesOfferModule,
    };
};

export const updateKYCConsentSalesOffer = async (
    consents: GraphQLApplicantAgreementSettings[],
    customer: GraphQLLocalCustomerFieldSettings[],
    featureKinds: SalesOfferFeatureKind[],
    id: ObjectId,
    user: User | null,
    loaders: Loaders
) => {
    const { collections } = await getDatabaseContext();

    const payloadKYCFields = convertLocalCustomerGraphQLFields(customer);
    const { salesOffer, lead, launchpadModule, customerModule, existingCustomer, salesOfferModule } =
        await extractArguments(id, collections, loaders);

    // Update the customer with the provided consents and KYC fields
    const upsertedCustomer = await updateCustomer(
        featureKinds,
        existingCustomer,
        lead,
        salesOffer,
        salesOfferModule,
        launchpadModule,
        customerModule,
        payloadKYCFields,
        user
    );

    const consentChanges = await getConsentChanges(featureKinds, consents, salesOffer);

    // build up versioning
    const authoring: Authoring = user
        ? { kind: AuthorKind.User, userId: user._id }
        : { kind: AuthorKind.Customer, customerId: upsertedCustomer._id };

    const latestLead: LaunchpadLead = {
        ...lead,
        _id: new ObjectId(),
        customerId: upsertedCustomer._id,
        _versioning: {
            ...lead._versioning,
            updatedBy: getAuthorFromAuthoring(authoring),
            updatedAt: new Date(),
            isLatest: true,
        },
    };

    await collections.leads.findOneAndUpdate(
        { _id: lead._id },
        {
            $set: { '_versioning.isLatest': false },
        }
    );

    await collections.leads.insertOne(latestLead);

    return { updatedLead: latestLead, consentChanges };
};

export const getApplicationSuiteIdKey = (
    featureKind: SalesOfferFeatureKind.Deposit | SalesOfferFeatureKind.Finance | SalesOfferFeatureKind.Insurance
) => {
    switch (featureKind) {
        case SalesOfferFeatureKind.Deposit:
            return 'latestReservationApplicationSuiteId';

        case SalesOfferFeatureKind.Finance:
            return 'latestFinancingApplicationSuiteId';

        case SalesOfferFeatureKind.Insurance:
            return 'latestInsuranceApplicationSuiteId';

        default:
            throw new Error('Invalid feature kind');
    }
};

/**
 * if feature has existing application,
 * if application is submitted, we need to cancel it
 * if application is draft we need to delete it
 */
export const checkAndCancelOrDeleteApplication = async (
    {
        salesOffer,
        user,
        collections,
    }: {
        salesOffer: SalesOffer;
        user: User;
        collections: Collections;
    },
    featureKind: SalesOfferFeatureKind.Deposit | SalesOfferFeatureKind.Finance | SalesOfferFeatureKind.Insurance
) => {
    const applicationSuiteIdKey = getApplicationSuiteIdKey(featureKind);

    if (salesOffer[applicationSuiteIdKey]) {
        const application = await collections.applications.findOne<SalesOfferApplication>({
            '_versioning.suiteId': salesOffer[applicationSuiteIdKey],
            '_versioning.isLatest': true,
        });

        if (application) {
            const applicationJourney = await collections.applicationJourneys.findOne<ApplicationJourney>({
                applicationSuiteId: salesOffer[applicationSuiteIdKey],
            });

            if (applicationJourney.isReceived) {
                await mainQueue.add({
                    type: 'onApplicationCancelled',
                    source: ApplicationCancelSource.User,
                    applicationId: application._id,
                    userId: user._id,
                    stage: ApplicationStage.Reservation,
                });
            } else {
                await collections.applications.deleteMany({ '_versioning.suiteId': salesOffer[applicationSuiteIdKey] });
                await collections.applicationJourneys.deleteOne({
                    applicationSuiteId: application._versioning.suiteId,
                });
            }
        }
    }
};
