import isEqual from 'fast-deep-equal';
import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { Collections, LaunchpadApplication, SalesOffer, User, SalesOfferFeatureKind } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import {
    ApplicationKind,
    GraphQLMutationResolvers,
    GraphQLTradeInSalesOfferInput,
    SalesOfferFeatureStatus,
} from '../../definitions';
import { createTradeInLaunchpadApplication } from '../applications/draftLaunchpadApplication';
import { updateTradeInLaunchpadApplication } from '../applications/updateLaunchpadApplicationTradeIn';
import { getImpactedUpdates } from './helpers';

const updateSalesOfferDocument = async (
    id: ObjectId,
    collections: Collections,
    salesOffer: SalesOffer,
    user: User,
    tradeIn: GraphQLTradeInSalesOfferInput
) => {
    const impactedUpdates = await getImpactedUpdates(salesOffer, [SalesOfferFeatureKind.VSA], collections, user);

    await collections.salesOffers.findOneAndUpdate(
        {
            _id: id,
        },
        {
            $set: {
                tradeIn: {
                    ...salesOffer.tradeIn,
                    isEnabled: tradeIn.isEnabled,
                    lastUpdatedAt: new Date(),
                    status: SalesOfferFeatureStatus.Updated,
                },
                vsa: {
                    ...salesOffer.vsa,
                    status: SalesOfferFeatureStatus.Updated,
                    lastUpdatedAt: new Date(),
                },
                _versioning: {
                    ...salesOffer._versioning,
                    ...getSimpleVersioningByUserForUpdate(user._id),
                },
                ...impactedUpdates,
            },
        }
    );
};

const mutate: GraphQLMutationResolvers['updateTradeInSalesOffer'] = async (
    root,
    { id, tradeIn, endpointId, languageId },
    context
) => {
    const { collections } = await getDatabaseContext();
    const user = await context.getUser();

    const salesOffer = await collections.salesOffers.findOne({
        _id: id,
    });
    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const lead = await collections.leads.findOne({
        '_versioning.suiteId': salesOffer.leadSuiteId,
        '_versioning.isLatest': true,
    });

    /**
     * If only when we turn on isEnabled ::
     * If there is no application suite id, it means that the trade-in application has not been created yet.
     * So we need to create a new trade-in application.
     *
     * If there is an application suite id, it means that the trade-in application has already been created.
     * So we need to update the existing trade-in application.
     */
    if (tradeIn.isEnabled) {
        if (isNil(tradeIn.applicationSuiteId)) {
            await createTradeInLaunchpadApplication(
                lead._id,
                [tradeIn.tradeInVehicle],
                endpointId ?? lead.endpointId,
                languageId ?? lead.languageId,
                lead.kind === ApplicationKind.Launchpad && lead.moduleId,
                context
            );

            await updateSalesOfferDocument(id, collections, salesOffer, user, tradeIn);
        } else {
            const existedApplication = (await collections.applications.findOne({
                '_versioning.suiteId': tradeIn.applicationSuiteId,
                '_versioning.isLatest': true,
            })) as LaunchpadApplication;

            if (!existedApplication) {
                throw new Error('Trade-in application not found');
            }

            const isSameTradeIn = isEqual(existedApplication.tradeInVehicle, tradeIn.tradeInVehicle);

            if (isSameTradeIn) {
                return lead;
            }

            await updateTradeInLaunchpadApplication([tradeIn.tradeInVehicle], tradeIn.applicationSuiteId, context);

            await updateSalesOfferDocument(id, collections, salesOffer, user, tradeIn);
        }
    } else if (salesOffer.tradeIn.isEnabled !== tradeIn.isEnabled) {
        await updateSalesOfferDocument(id, collections, salesOffer, user, tradeIn);
    }

    const updatedLead = await collections.leads.findOne({
        '_versioning.suiteId': salesOffer.leadSuiteId,
        '_versioning.isLatest': true,
    });

    return updatedLead;
};

export default mutate;
