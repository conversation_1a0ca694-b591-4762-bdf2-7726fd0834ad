import isEqual from 'fast-deep-equal';
import { pick } from 'lodash/fp';
import { SalesOfferFeatureStatus, SalesOfferFeatureKind } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { GraphQLMutationResolvers } from '../../definitions';
import { getImpactedUpdates } from './helpers';
import { checkAndCancelOrDeleteApplication } from './shared';

const mutate: GraphQLMutationResolvers['updateDepositSalesOffer'] = async (
    root,
    { id, deposit },
    { loaders, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const user = await getUser();

    const salesOffer = await collections.salesOffers.findOne({
        _id: id,
    });

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const isSameDeposit = isEqual(deposit, pick(['depositMethod', 'depositAmount'], salesOffer.deposit));
    // if deposit no change, then return the existing sales offer
    if (isSameDeposit) {
        return salesOffer;
    }

    // if deposit method change, we need to check if has existing application to cancel and delete it
    await checkAndCancelOrDeleteApplication({ salesOffer, user, collections }, SalesOfferFeatureKind.Deposit);

    const impactedUpdates = await getImpactedUpdates(
        salesOffer,
        [SalesOfferFeatureKind.Deposit, SalesOfferFeatureKind.VSA],
        collections,
        user
    );

    const result = await collections.salesOffers.findOneAndUpdate(
        {
            _id: id,
        },
        {
            $set: {
                deposit: {
                    ...salesOffer.deposit,
                    ...deposit,
                    status: SalesOfferFeatureStatus.Updated,
                    lastUpdatedAt: new Date(),
                },
                vsa: {
                    ...salesOffer.vsa,
                    status: SalesOfferFeatureStatus.Updated,
                    lastUpdatedAt: new Date(),
                },
                _versioning: {
                    ...salesOffer._versioning,
                    ...getSimpleVersioningByUserForUpdate(user._id),
                },
                latestReservationApplicationSuiteId: null,
                ...impactedUpdates,
            },
        },
        { returnDocument: 'after' }
    );

    return result;
};

export default mutate;
