import isEqual from 'fast-deep-equal';
import { ObjectId } from 'mongodb';
import { Customer, AuthorKind, AuditTrailKind, CustomerAmendmentsAuditTrail } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { mergeLocalCustomerFields } from '../../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../../database/helpers/customers/shared';
import { CustomerPolicyAction } from '../../../../permissions';
import { getAuthorFromAuthoring } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getCustomerChanges } from '../applications/submitChanges/amendmentAuditTrail';
import { calculateCustomerChanges } from '../applications/submitChanges/updateHandlers/getUpdateHandlers';

const mutation: GraphQLMutationResolvers['updateCustomer'] = async (
    root,
    { customerId, updates },
    { getUser, loaders, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissions = await getPermissionController();

    const customer = await collections.customers.findOne({
        $and: [{ _id: customerId, '_versioning.isLatest': true }],
    });

    if (!customer) {
        throw new InvalidPermission();
    }

    const module = await loaders.moduleById.load(customer.moduleId);
    if (!permissions.customers.mayOperateOn(customer, CustomerPolicyAction.Update, module)) {
        throw new InvalidPermission();
    }

    const computedCustomerFields = await calculateCustomerChanges(updates, module.companyId, loaders, customer._id);
    const mergedFields = mergeLocalCustomerFields(customer.fields, computedCustomerFields);

    // if there is no any update
    if (isEqual(getLocalCustomerAggregatedFields(customer), mergedFields)) {
        throw new Error('No changes were made');
    }

    // set previous customer has outdated
    await collections.customers.findOneAndUpdate({ _id: customerId }, { $set: { '_versioning.isLatest': false } });

    const user = await getUser(true);

    // prepare new customer document
    const newCustomer: Customer = {
        ...customer,
        _id: new ObjectId(),
        fields: mergedFields,
        _versioning: {
            ...customer._versioning,
            updatedBy: { kind: AuthorKind.User, id: user._id },
            updatedAt: new Date(),
            isLatest: true,
        },
    };

    // insert it in database
    await collections.customers.insertOne(newCustomer);

    // add audit trail
    const customerChanges = await getCustomerChanges(customerId, newCustomer._id, { loaders });
    if (customerChanges.length) {
        const auditTrail: CustomerAmendmentsAuditTrail = {
            _id: new ObjectId(),
            _date: new Date(),
            _kind: AuditTrailKind.CustomerAmendments,
            customerId: newCustomer._id,
            customerSuiteId: newCustomer._versioning.suiteId,
            author: getAuthorFromAuthoring({ kind: AuthorKind.User, userId: user._id, date: new Date() }),
            changes: customerChanges,
        };

        await collections.auditTrails.insertOne(auditTrail);
    }

    return newCustomer;
};

export default requiresLoggedUser(mutation);
