import crypto from 'crypto';
import * as Sentry from '@sentry/node';
import chalk from 'chalk';
import { Handler, Request, Response } from 'express';
import { isEmpty, isNil } from 'lodash/fp';
import { renderToStaticMarkup } from 'react-dom/server';
import { HelmetData } from 'react-helmet';
import urljoin from 'url-join';
import { RuntimeConfig } from '../../app/runtimeConfig';
import { Company, EndpointType, Router, WebPageEndpoint } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { getSearchEnginesConfiguration } from '../database/helpers/settings';
import { getDefaultLocale } from '../database/helpers/shared';
import coerce from '../utils/coerce';
import Document from './Document';
import config from './config';
import getManifest from './getManifest';
import renderMetadata from './renderMetadata';
import { getUrlForUpload } from './storage';
import { getDefaultTranslationPackForRouter } from './translations';

enum CompanyTheme {
    Default = 'Default',
    Admin = 'Admin',
    Porsche = 'Porsche',
    PorscheV3 = 'PorscheV3',
    Volkswagen = 'Volkswagen',
    Skoda = 'Skoda',
}

enum LayoutType {
    None = 'None',
    Basic = 'Basic',
    BasicPro = 'BasicPro',
    PorscheV3 = 'PorscheV3',
}

const getRouter = async ({ hostname, path: pathname }: Request) => {
    const { collections } = await getDatabaseContext();

    // get routers for this same hostname
    const routers = await collections.routers.find({ hostname }).toArray();

    // and sort them by pathname (in reverse) then filter matches only
    // finally get the first result
    const matchingRouter = routers
        .filter(router => new RegExp(`^${router.pathname}`, 'i').test(pathname))
        .sort((a, b) => a.pathname.localeCompare(b.pathname) * -1)[0];

    if (!matchingRouter) {
        return { pathname: '/', router: null };
    }

    const [matchPathname] = pathname.match(new RegExp(`^${matchingRouter.pathname}`, 'i'));

    const company = matchingRouter?.companyId
        ? await collections.companies.findOne({ _id: matchingRouter.companyId })
        : null;

    return { pathname: matchPathname, router: matchingRouter, company };
};

const getLanguageFromRequest = async (
    router: Router | null,
    req: Request
): Promise<RuntimeConfig['initialLanguage']> => {
    if (router) {
        const language = await getDefaultTranslationPackForRouter(router);

        if (language) {
            return { internalCode: language._id.toHexString(), translationCode: language.code };
        }
    }

    const language = await getDefaultLocale();

    return { internalCode: language, translationCode: language };
};

const getTheme = async (router: Router | null, company?: Company) => {
    if (!router) {
        // no theme implemented
        return CompanyTheme.Default;
    }

    return !isNil(company) ? coerce(company.theme, CompanyTheme) : CompanyTheme.Default;
};

const getLayoutType = (router: Router | null) => {
    if (!router) {
        return null;
    }

    return coerce(router.layout._type, LayoutType);
};

const getFavicon = async (router: Router | null, company?: Company) => {
    if (!router) {
        return null;
    }

    return getUrlForUpload(company?.favicon);
};

const getLanguageCaches = async () => {
    const { collections } = await getDatabaseContext();

    // to minimize the data to fetch
    // project only the ID and versioning object
    // the documents may weight a lot because of the translations themselves
    const languages = await collections.languagePacks.find({}, { projection: { _id: 1, _versioning: 1 } }).toArray();

    return {
        ...languages.reduce(
            (acc, language) => ({
                ...acc,
                [language._id.toHexString()]: `${config.version}-${language._versioning.updatedAt.getTime()}`,
            }),
            {} as { [language: string]: string }
        ),

        ...config.i18n.locales.reduce(
            (acc, locale) => ({ ...acc, [locale]: config.version }),
            {} as { [language: string]: string }
        ),
    };
};

const getInstance = (instanceConfig: string): RuntimeConfig['instance'] => {
    switch (instanceConfig as RuntimeConfig['instance']) {
        case 'audi':
        case 'psme':
        case 'default':
            return instanceConfig as RuntimeConfig['instance'];
        default: {
            console.warn(chalk.yellow(`⚠️ Unsupported instance type ${instanceConfig}`));

            return 'default';
        }
    }
};

const getRuntime = async (
    router: Router | null,
    req: Request,
    pathname: string,
    company?: Company
): Promise<RuntimeConfig> => {
    const initialLanguage = await getLanguageFromRequest(router, req);

    const theme = await getTheme(router, company);
    const languageCaches = await getLanguageCaches();
    const favicon = await getFavicon(router, company);
    const instance = getInstance(config.instance);

    const hasPathScript = router
        ? router.pathScripts.length > 0 ||
          router.endpoints.some(
              endpoint => endpoint._type === EndpointType.EventApplicationEntrypoint && endpoint.pathScripts.length
          )
        : false;

    const hasNonce =
        hasPathScript ||
        [
            router?.pixelId,
            router?.linkedInInsightTagId,
            router?.googleTagManagerId,
            router?.tikTokId,
            router?.snapChatId,
            router?.blueKaiId,
        ].some(Boolean);

    const {
        bedrock: { agentId, agentAliasId },
    } = config;

    const enableGenAI = !!(agentId && agentAliasId);

    return {
        version: config.version,
        publicPath: config.publicPath,
        locales: config.i18n.locales,
        sentry: config.sentry,
        initialLanguage,
        theme,
        favicon,
        languageCaches,
        vat: config.vat,
        instance,
        enableGenAI,

        router: router
            ? {
                  id: router._id.toHexString(),
                  hostname: router.hostname,
                  pathname,
                  withAdmin: router.withAdmin,
                  layoutType: getLayoutType(router),
                  ...(!isEmpty(router.googleTagManagerId) &&
                      router.googleTagManagerId && {
                          googleTagManager: {
                              id: router.googleTagManagerId,
                          },
                      }),
                  // facebook pixel
                  ...(!isEmpty(router.pixelId) &&
                      router.pixelId && {
                          pixel: {
                              id: router.pixelId,
                          },
                      }),
                  ...(!isEmpty(router.linkedInInsightTagId) &&
                      router.linkedInInsightTagId && {
                          linkedInInsight: {
                              id: router.linkedInInsightTagId,
                          },
                      }),

                  ...(!isEmpty(router.snapChatId) && {
                      snapChat: {
                          id: router.snapChatId,
                      },
                  }),

                  ...(!isEmpty(router.blueKaiId) && {
                      blueKai: {
                          id: router.blueKaiId,
                      },
                  }),

                  ...(!isEmpty(router.tikTokId) && {
                      tikTok: {
                          id: router.tikTokId,
                      },
                  }),
                  ...(hasNonce && { nonce: crypto.randomBytes(16).toString('base64') }),
              }
            : {
                  id: null,
                  hostname: req.hostname,
                  pathname,
                  withAdmin: config.enableDefaultAdmin,
              },
    };
};

const externalCdn = config.publicPath.startsWith('http')
    ? // we are using a CDN for serving the assets
      new URL(config.publicPath).hostname
    : null;

// generate CSP rule
const getCspRule = (nonces: string[]) =>
    `${[
        'script-src',
        "'self'",
        externalCdn,
        'cdn.ui.porsche.com',
        'pay.porsche-preview.com',
        'pay.porsche.com',
        'checkoutshopper-live.adyen.com',
        'connect.facebook.net',
        'www.googletagmanager.com',
        'snap.licdn.com',
        'analytics.tiktok.com',
        'sc-static.net',
        'tags.bkrtx.com',
        'userlike-cdn-umm.b-cdn.net',
        ...nonces.map(nonce => `'nonce-${nonce}'`),
        process.useIstanbul && "'unsafe-eval'",
        ';',
        'worker-src',
        "'self' blob: ",
    ]
        .filter(Boolean)
        .join(' ')};`;

if (process.useIstanbul && process.env.NODE_ENV === 'production') {
    throw new Error('Unsafe-Eval cannot be granted in the Content-Security-Policy rule when running for production');
}

if (process.useIstanbul) {
    console.info(chalk.redBright('Unsafe-Eval is granted in the Content-Security-Policy rule (Istanbul support)'));
}

/**
 * Make local business schema
 *
 * https://schema.org/LocalBusiness
 */
const makeLocalBusinessSchema = async (company: Company) => {
    const logo = await getUrlForUpload(company.logo);

    return {
        '@context': 'http://schema.org',
        '@type': 'LocalBusiness',
        name: company.displayName,
        legalName: company.legalName.defaultValue,

        currenciesAccepted: company.currency,
        priceRange: '$$$',

        address: {
            '@type': 'PostalAddress',
            addressCountry: company.countryCode,
            streetAddress: company.address,
        },
        telephone: company.phone?.value ? `+${company.phone.prefix}${company.phone?.value}` : undefined,
        logo,
        image: logo,

        contactPoint: {
            '@type': 'ContactPoint',
            contactType: 'customer service',
            telephone: company.phone?.value ? `+${company.phone.prefix}${company.phone?.value}` : undefined,
            email: company.email,
            areaServed: company.countryCode,
        },

        publicAccess: true,
    };
};

const execute = async (req: Request, res: Response): Promise<void> => {
    const { router, pathname, company } = await getRouter(req);

    const runtime = await getRuntime(router, req, pathname, company);

    const { css, js } = getManifest();
    // first determine endpoint
    const webPageEndpoint = router?.endpoints?.find(
        endpoint => endpoint._type === EndpointType.WebPage
    ) as WebPageEndpoint | null;

    // for canonical tag, we always take the first one in the endpoints
    const canonicalTag = webPageEndpoint
        ? urljoin(`${config.protocol}://`, router.hostname, router.pathname, webPageEndpoint.pathname)
        : null;
    let helmet: HelmetData | null | undefined;
    // metadata is optional, proceed to render the application even if any error happens
    try {
        helmet = await renderMetadata(req, router, pathname);
    } catch (error) {
        console.error(error);

        Sentry.captureException(error);
    }

    const localBusinessSchema = company ? await makeLocalBusinessSchema(company) : null;

    const document = (
        <Document
            canonicalTag={canonicalTag}
            cssScripts={css}
            favicon={runtime.favicon}
            helmet={helmet}
            isAllowSearchEngine={await getSearchEnginesConfiguration()}
            jsScripts={js}
            localBusinessSchema={localBusinessSchema}
            locale={runtime.initialLanguage.translationCode}
            runtime={runtime}
        />
    );

    const html = `<!doctype html>${renderToStaticMarkup(document)}`;

    res.set({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'SAMEORIGIN',
        'X-Xss-Protection': '"1; mode=block"',
        'Content-Security-Policy': getCspRule([runtime.router.nonce].filter(Boolean)),
        'Referrer-Policy': 'no-referrer',
        'Permissions-Policy': 'geolocation=()',
    });

    res.send(html);
};

const handler: Handler = async (req, res, next) => {
    try {
        await execute(req, res);
    } catch (error) {
        next(error);
    }
};

export default handler;
