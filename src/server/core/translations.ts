import { IncomingMessage } from 'http';
import parser from 'accept-language-parser';
import { ObjectId } from 'bson';
import { RequestHandler } from 'express';
import { i18n, TFunction } from 'i18next';
import { Router } from '../database/documents';
import getDatabaseContext from '../database/getDatabaseContext';
import { getDefaultLocale } from '../database/helpers/shared';
import createI18nInstance from '../utils/createI18nInstance';

export type I18nContext = { i18n: i18n; t: TFunction };

export type GetTranslations = (namespaces?: string[]) => Promise<I18nContext>;

export const getLazyTranslations = (languageOrPackId?: string): GetTranslations => {
    let instance: I18nContext | null = null;
    let promise: Promise<I18nContext> | null = null;

    const getter = async (namespaces?: string[]): Promise<I18nContext> => {
        if (instance) {
            if (namespaces) {
                await instance.i18n.loadNamespaces(namespaces);
            }

            return instance;
        }

        if (!promise) {
            promise = new Promise((resolve, reject) => {
                createI18nInstance(languageOrPackId)
                    .then(({ i18n, initPromise }) =>
                        initPromise.then(t => {
                            // update the instance on local scope
                            instance = { i18n, t };
                            // set promise back to null
                            promise = null;

                            resolve(instance);
                        })
                    )
                    .catch(reject);
            });
        }

        // first wait for it to be resolved
        await promise;

        // and retry
        return getter(namespaces);
    };

    return getter;
};

export const getLanguageFromRequest = async (req: IncomingMessage): Promise<string> => {
    const detectedLanguage = req.headers['accept-language'];

    if (!detectedLanguage) {
        return getDefaultLocale();
    }

    const isValidObjectId = ObjectId.isValid(detectedLanguage);

    if (isValidObjectId) {
        return detectedLanguage;
    }

    const languages = parser.parse(detectedLanguage);
    if (languages.length > 0 && languages[0]?.code) {
        return languages[0].code;
    }

    return getDefaultLocale();
};

export const serveTranslations: RequestHandler<{ lng: string; ns: string; hash?: string }> = async (
    request,
    response,
    next
) => {
    try {
        // get the instance
        const { i18n, initPromise } = await createI18nInstance(request.params.lng);
        // wait for initialization to be done
        await initPromise;
        // then load namespace
        await i18n.loadNamespaces([request.params.ns]);
        // get the bundle
        const bundle = i18n.getResourceBundle(request.params.lng, request.params.ns);

        if (!bundle) {
            // return 404
            response.send(404);
        } else {
            // only set cache header for production
            if (!__IS_DEV__) {
                // set cache for one year validity
                response.set({ 'Cache-control': 'public, max-age=31536000' });
                response.removeHeader('Pragma');
            }

            // send the bundle as JSON
            response.json(bundle);
        }
    } catch (error) {
        // forward the error
        next(error);
    }
};

export const getDefaultTranslationPackForRouter = async (router: Router) => {
    if (!router) {
        return null;
    }

    if (!router.languages.length) {
        return null;
    }

    const { collections } = await getDatabaseContext();

    return collections.languagePacks.findOne({ _id: router.languages[0] });
};
