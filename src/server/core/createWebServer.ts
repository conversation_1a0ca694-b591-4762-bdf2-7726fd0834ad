// create http server
import { existsSync } from 'fs';
import http, { Server } from 'http';
import path from 'path';
import * as Sentry from '@sentry/node';
import { ApolloServerPluginDrainHttpServer } from 'apollo-server-core';
import { ApolloServer, ApolloServerExpressConfig } from 'apollo-server-express';
import compression from 'compression';
import cors from 'cors';
import express, { Express, Handler, Request, Response, Router } from 'express';
import depthLimit from 'graphql-depth-limit';
import { graphqlUploadExpress } from 'graphql-upload';
import { useServer } from 'graphql-ws/lib/use/ws';
import morgan from 'morgan';
import urljoin from 'url-join';
import { WebSocketServer } from 'ws';
import createCallbackRouter from '../callback';
import { getSearchEnginesConfiguration } from '../database/helpers/settings';
import createCtsEndpoints from '../integrations/cts';
import createFiservEndpoints from '../integrations/payments/fiserv';
import createPayGateEndpoints from '../integrations/payments/paygate';
import createTtbEndpoints from '../integrations/payments/ttb/endpoints';
import createPorsche from '../integrations/porsche/endpoints';
import createTradeInAppEndpoints from '../integrations/tradeInApp/endpoints';
import createProxyRouter from '../proxy';
import schema from '../schema';
import createContext, { Context, RootDocument } from '../schema/context';
import { initializeSitemaps, sitemapDirectoryPath } from '../utils/manageSitemaps';
import { setupBullDashboard } from './bullBoard';
import config from './config';
import createDownloads from './createDownloads';
import createExportRouter from './createExportRouter';
import createOIDCRouter from './createOIDCRouter';
import createWebhooks from './createWebhooks';
import createWellKnownEndpoints from './createWellKnownEndpoints';
import { attachContext, authenticationRequired } from './express';
import setupPrometheusMetrics, { ApolloMetricsPlugin } from './prometheus';
import { expressRateLimiter } from './rateLimiter';
import renderApplication from './renderApplication';
import serveCoverage, { isCoverageEnabled } from './serveCoverage';

export type WebServerCreation = {
    httpServer: Server;
    expressServer: Express;
    apolloServer: ApolloServer;
};

const rateLimiterMiddleware: Handler = (req, res, next) => {
    expressRateLimiter
        .consume(req.ip, 1)
        .then(() => {
            // move on to next handler
            next();
        })
        .catch(() => {
            // reject request
            res.status(429).send('Too Many Requests');
        });
};

const protectEndpoint: Handler = (req, res, next) => {
    // prevent XSS attacks
    res.set({ 'X-Content-Type-Options': 'nosniff' });

    // move on to next handler
    next();
};

const disableCaching: Handler = (req, res, next) => {
    // update headers to disable caching behaviors
    res.set({
        'Cache-control': 'no-store',
        Pragma: 'no-cache',
    });

    // move on to next handler
    next();
};

const createApiEndpoints = () => {
    const router = Router();

    router.use('/webhooks', createWebhooks());
    router.use('/downloads', createDownloads());
    router.use('/export', attachContext, authenticationRequired, createExportRouter());

    router.use('/cts', createCtsEndpoints());
    router.use('/tradeInApp', createTradeInAppEndpoints());

    router.use('/porsche', createPorsche());
    router.use('/fiserv', createFiservEndpoints());
    router.use('/paygate', createPayGateEndpoints());
    router.use('/ttb', createTtbEndpoints());
    router.use('/oidc', createOIDCRouter());

    return router;
};

const serveRobotTxt: Handler = async (req, res, next) => {
    const isAllowSearchEngines = await getSearchEnginesConfiguration();

    if (isAllowSearchEngines) {
        const { host } = req.headers;
        const baseUrl = `${config.protocol}://${host}/`;
        const sitemapUrl = urljoin(baseUrl, sitemapDirectoryPath, `${host}.xml`);
        res.type('text/plain');
        if (existsSync(path.join(__dirname, sitemapDirectoryPath, `${host}.xml`))) {
            res.send(`User-agent: *\nDisallow:\n\nSitemap: ${sitemapUrl}`);
        } else {
            res.send(`User-agent: *\nDisallow:`);
        }
    } else {
        res.send(`User-agent: *\nDisallow: /`);
    }
};

const createWebServer = async (): Promise<WebServerCreation> => {
    // create express server
    const expressServer = express();

    // disable informational headers
    expressServer.disable('x-powered-by');

    if (config.gzip) {
        // enable compression
        // we might want to disable if it's delegated to a reverse proxy
        expressServer.use(compression());
    }

    // enable JSON and url encoded support
    expressServer.use(express.json({ limit: config.jsonLimit }));
    expressServer.use(express.urlencoded({ extended: true }));
    expressServer.use(express.text({ type: ['application/jose', 'text/plain'] }));

    if (config.verbose) {
        // enable logs
        expressServer.use(morgan(process.env.NODE_ENV === 'production' ? 'tiny' : 'dev'));
    }

    // enable Sentry scope
    expressServer.use(Sentry.Handlers.requestHandler());

    // setup prometheus metrics
    await setupPrometheusMetrics(expressServer);

    if (config.sentry.tracing) {
        expressServer.use(Sentry.Handlers.tracingHandler());
    }

    // serve static files
    expressServer.use('/public', express.static('public'));

    // various apis: webhooks, downloads and more
    expressServer.use('/api', createApiEndpoints());

    // apply cors
    expressServer.use(
        cors((req, callback) => {
            // in production we expect the app to be served behind a reverse proxy such as the ingress controller
            // if so we rely on those information which are trust worthy as those are defined by the proxy itself
            const host = req.header('X-Forwarded-Host');
            const scheme = req.header('X-Forwarded-Scheme') || 'https';

            // apply cors
            callback(null, { origin: host ? `${scheme}://${host}` : false });
        })
    );

    // then from here use rate limiter
    expressServer.use(rateLimiterMiddleware);

    // update cache policy
    expressServer.use(disableCaching);

    // serve code coverage when the runtime provides it
    if (isCoverageEnabled) {
        expressServer.get('/.coverage', serveCoverage);
    }

    // create the http server
    const httpServer = http.createServer(expressServer);

    // create web socket server
    const wsServer = new WebSocketServer({ server: httpServer });

    // start listening with the ws server
    const wsServerCleanup = useServer(
        {
            schema,
            context: async context => {
                const params = context.connectionParams as { authorization?: string };

                return createContext(context.extra.request, undefined, params.authorization);
            },
        },
        wsServer
    );

    // prepare plugins for apollo
    const plugins: ApolloServerExpressConfig['plugins'] = [
        // sentry custom plugin for bug tracking
        // ApolloSentryPlugin,

        // help apollo server to gracefully shutdown
        ApolloServerPluginDrainHttpServer({ httpServer }),

        // proper shutdown for the WebSocket server.
        {
            async serverWillStart() {
                return {
                    async drainServer() {
                        await wsServerCleanup.dispose();
                    },
                };
            },
        },
    ];

    if (config.prometheus.enabled) {
        // custom plugin to extract prometheus metrics
        plugins.push(ApolloMetricsPlugin);
    }

    // create apollo server
    const apolloServer = new ApolloServer({
        schema,

        // protect against DDoS by using deep depth on GraphQL APIs
        validationRules: [depthLimit(15)],

        // provide a custom context
        context: ({ req, res }: { req: Request; res: Response }): Promise<Context> => createContext(req, res),

        // provide a custom root document
        rootValue: (): RootDocument => null,

        // enable introspection based on the configuration
        introspection: config.introspection,

        // debug mode
        debug: config.debug,

        // apollo plugins
        plugins,
    });

    // start apollo server
    await apolloServer.start();

    // serve graphql API
    expressServer.use(
        '/graphql',
        protectEndpoint,
        graphqlUploadExpress(),
        // disable CORS as we manage those at an upper level
        apolloServer.getMiddleware({ bodyParserConfig: { limit: '50mb' }, path: '/', cors: false })
    );

    // we might want to monitor bull queues
    // mostly for debug purposes as this endpoint could leak sensitive information
    if (config.bull.enableMonitor) {
        setupBullDashboard(expressServer);
    }

    // serve translations
    expressServer.use('/.proxy', protectEndpoint, createProxyRouter());

    // serve callbacks
    expressServer.use('/.callback', createCallbackRouter());

    // well-known endpoints
    expressServer.use('/.well-known', createWellKnownEndpoints());

    // serve Robot.txt file
    expressServer.use('/robots.txt', serveRobotTxt);

    // fallback on the application for all other paths
    expressServer.get('*', (req, res, next) => {
        renderApplication(req, res, next);
    });

    // sse the sentry error handler before any other error handler
    expressServer.use(Sentry.Handlers.errorHandler());

    // then here comes our error handler
    // eslint-disable-next-line no-unused-vars
    expressServer.use((error, request, response, next) => {
        // print it for logs
        console.error(error);
        // answer as 500 response
        response.status(500).send('Internal error');
    });

    await initializeSitemaps();

    return { httpServer, apolloServer, expressServer };
};

export default createWebServer;
