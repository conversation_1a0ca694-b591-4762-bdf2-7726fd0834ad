import chalk from 'chalk';
import type { SMTPSettings } from '../emails/transporters/types';
import { getClientSideFieldLevelEncryptionSettings } from './encryption';
import {
    getBoolean,
    getInteger,
    getNumber,
    getPrefix,
    getString,
    getStringListFromComma,
    getStringListFromSpace,
} from './env';

const getSmtpSettings = (): SMTPSettings => {
    const base = {
        host: getString(getPrefix('SMTP_HOST'), 'localhost'),
        port: getInteger(getPrefix('SMTP_PORT'), 465),

        /**
         * adding this check is to set e2e (automate testing)
         * to be secured. otherwise, we are facing [SSL Routines, wrong version number]
         */
        secure: getBoolean(getPrefix('SMTP_SECURE'), false),
    };

    const user = getString(getPrefix('SMTP_USER'));

    if (!user) {
        return base;
    }

    return {
        ...base,
        auth: {
            user,
            pass: getString(getPrefix('SMTP_PASSWORD'), ''),
        },
    };
};

const version = getString('VERSION', '0.0.0-development');

const port = getNumber(getPrefix('PORT'), 3000);
const protocol = getString(getPrefix('PROTOCOL'), `https`);

const config = {
    version,

    // miscellaneous
    verbose: getBoolean(getPrefix('VERBOSE'), true),

    // introspection state on the GraphQL API
    introspection: getBoolean(getPrefix('INTROSPECTION'), process.env.NODE_ENV !== 'production'),

    debug: getBoolean(getPrefix('DEBUG'), false),
    port,
    protocol,
    publicPath: getString(getPrefix('PUBLIC_PATH'), '/public/'),
    applicationEndpoint: getString(getPrefix('ENDPOINT'), `${protocol}://localhost:${port}`),
    enableDefaultAdmin: getBoolean(getPrefix('ENABLE_DEFAULT_ADMIN'), true),

    // secure cookies
    cookiePolicy: getString(getPrefix('COOKIE_POLICY'), 'strict') as boolean | 'lax' | 'strict' | 'none',
    secureCookie: getBoolean(getPrefix('SECURE_COOKIE'), true),

    // health checks
    healthChecks: {
        enabled: getBoolean(getPrefix('HEALTH_ENABLED'), false),
        port: getNumber(getPrefix('HEALTH_PORT'), 4000),
        allowed: getStringListFromComma(getPrefix('HEALTH_ALLOWED'), ['::1/128', '*********/8']),
        workerBeat: getString(getPrefix('HEALTH_WORKER_BEAT')),
    },

    // JSON parser limit
    jsonLimit: getString(getPrefix('JSON_LIMIT'), '1MB'),

    // gzip module
    gzip: getBoolean(getPrefix('GZIP'), true),

    // internationalization
    i18n: {
        locales: ['en'],
    },

    // server runtime
    db: {
        uri: getString(getPrefix('DB_URI'), 'mongodb+srv://localhost:27017'),
        name: getString(getPrefix('DB_NAME'), 'app'),
        pool: getInteger(getPrefix('DB_POOL'), 10),

        // Client Side Field Level Encryption (CSFLE)
        // https://docs.mongodb.com/drivers/security/client-side-field-level-encryption-guide/
        encryption: getClientSideFieldLevelEncryptionSettings(),

        // mongocryptd settings
        // only used if encryption is enabled
        cryptd: {
            uri: getString(getPrefix('DB_CRYPTD_URI')),
            mongocryptdBypassSpawn: getBoolean(getPrefix('DB_CRYPTD_BYPASS_SPAWN'), false),
            mongocryptdSpawnArgs: getStringListFromSpace(getPrefix('DB_CRYPTD_SPWAN_ARGS'), []),
        },
    },

    bull: {
        persistJobs: getBoolean(getPrefix('BULL_PERSIST'), false),
        enableMonitor: getBoolean(getPrefix('BULL_MONITOR'), false),
    },

    redis: {
        uri: getString(getPrefix('REDIS_URI'), 'redis://127.0.0.1:6379'),
    },

    session: {
        secret: getString(getPrefix('SESSION_SECRET')),
        lifetime: getString(getPrefix('SESSION_LIFETIME'), '1h'),
    },

    // default/system SMTP settings
    smtp: {
        transporter: getSmtpSettings(),
        sender: getString(getPrefix('SMTP_FROM'), '<EMAIL>'),
    },

    // system SMS settings
    sms: {
        twilio: {
            account: getString(getPrefix('SMS_TWILIO_ACCOUNT')),
            token: getString(getPrefix('SMS_TWILIO_TOKEN')),
            sender: getString(getPrefix('SMS_TWILIO_SENDER')),
        },
    },

    storage: {
        provider: {
            endPoint: getString(getPrefix('STORAGE_ENDPOINT')),
            accessKey: getString(getPrefix('STORAGE_ACCESS_KEY')),
            secretKey: getString(getPrefix('STORAGE_SECRET_KEY')),
            useSSL: getBoolean(getPrefix('STORAGE_SSL'), true),
            port: getInteger(getPrefix('STORAGE_PORT')),
            region: getString(getPrefix('STORAGE_REGION'), 'ap-southeast-1'),
        },
        privateBucket: getString(getPrefix('STORAGE_PRIVATE_BUCKET'), 'app-private'),
        publicBucket: getString(getPrefix('STORAGE_PUBLIC_BUCKET'), 'app-public'),
        publicEndpoint: getString(getPrefix('STORAGE_PUBLIC_ENDPOINT'), ''),
    },

    rekognition: {
        endPoint: getString(getPrefix('REKOGNITION_ENDPOINT')),
        accessKey: getString(getPrefix('REKOGNITION_ACCESS_KEY')),
        secretKey: getString(getPrefix('REKOGNITION_SECRET_KEY')),
        region: getString(getPrefix('REKOGNITION_REGION'), 'ap-southeast-1'),
    },

    sentry: {
        dsn: getString(getPrefix('SENTRY_DSN')),
        release: getString(getPrefix('SENTRY_RELEASE')),
        environment: getString(getPrefix('SENTRY_ENVIRONMENT')),
        tracing: getBoolean(getPrefix('SENTRY_TRACING'), true),
        profiling: getBoolean(getPrefix('SENTRY_PROFILING'), false),
        tracesSampleRate: getNumber(getPrefix('SENTRY_TRACES_SAMPLE_RATE'), 1.0),
        replay: getBoolean(getPrefix('SENTRY_REPLAY'), false),
        replaySampleRate: getNumber(getPrefix('SENTRY_REPLAY_SAMPLE_RATE'), 1.0),
        profilingSampleRate: getNumber(getPrefix('SENTRY_PROFILING_SAMPLE_RATE'), 1.0),
    },

    html2pdf: {
        endpoint: getString(getPrefix('HTML2PDF_ENDPOINT')),
    },

    mailpit: {
        url: getString(getPrefix('MAILPIT_URL')),
    },

    limiter: {
        api: getNumber(getPrefix('LIMITER_API'), 1000),
    },

    prometheus: {
        enabled: getBoolean(getPrefix('PROMETHEUS_ENABLED'), false),
        internal: getBoolean(getPrefix('PROMETHEUS_INTERNAL'), false),
        internalPort: getInteger(getPrefix('PROMETHEUS_INTERNAL_PORT'), 7788),
        external: getBoolean(getPrefix('PROMETHEUS_EXTERNAL'), false),
        externalPath: getString(getPrefix('PROMETHEUS_EXTERNAL_PATH'), '/metrics'),
        prefix: getString(getPrefix('PROMETHEUS_PREFIX'), 'app_'),
    },

    download: {
        passwordProtect: getBoolean(getPrefix('PASSWORD_PROTECT_DOCUMENTS'), true),
    },

    myinfo: {
        isMyinfoTestEnvironment: getBoolean(getPrefix('IS_MYINFO_TEST_ENVIRONMENT'), false),
    },

    // for quotation ENBD
    vat: getNumber(getPrefix('VAT_AMOUNT'), 0.05), // VAT is 5% by default

    // instance: 'audi' | 'psme' | 'default'
    instance: getString(getPrefix('INSTANCE'), 'default'),

    bedrock: {
        region: getString(getPrefix('BEDROCK_REGION'), 'us-east-1'),
        accessKeyId: getString(getPrefix('BEDROCK_ACCESS_KEY')),
        secretAccessKey: getString(getPrefix('BEDROCK_SECRET_ACCESS_KEY')),
        agentId: getString(getPrefix('BEDROCK_AGENT')),
        agentAliasId: getString(getPrefix('BEDROCK_AGENT_ALIAS')),
    },

    cap: {
        ppnHostname: getString(getPrefix('CAP_PPN_HOSTNAME')),
        ppnResource: getString(getPrefix('CAP_PPN_RESOURCE')),
        ppnClientId: getString(getPrefix('CAP_PPN_CLIENT_ID')),
        ppnClientSecret: getString(getPrefix('CAP_PPN_CLIENT_SECRET')),
        baseUrl: getString(getPrefix('CAP_BASEURL'), 'eu-0.test.api.porsche.io'),
        region: getString(getPrefix('CAP_REGION'), 'asia'),
        serviceGroup: getString(getPrefix('CAP_SERVICE_GROUP'), 'porsche-group'),
        env: getString(getPrefix('CAP_ENV'), 'test'),
        clientId: getString(getPrefix('CAP_CLIENT_ID')),
        clientSecret: getString(getPrefix('CAP_CLIENT_SECRET')),
        authGroup: getString(getPrefix('CAP_AUTH_GROUP'), 'PAP'),
        marketId: getString(getPrefix('CAP_MARKET_ID')),
        language: getString(getPrefix('CAP_LANGUAGE'), 'EN'),
        dataOrigin: getString(getPrefix('CAP_DATA_ORIGIN'), 'Z0DG'),
    },

    mapboxApi: {
        url: getString(getPrefix('MAPBOX_API_URL'), 'https://api.mapbox.com/geocoding/v5/mapbox.places/'),
        accessToken: getString(getPrefix('MAPBOX_API_ACCESS_TOKEN')),
    },
};

export type RuntimeConfig = typeof config;

const exitOnError = (error: string): void => {
    console.error(error);
    process.exit(1);
};

export const runValidityChecks = () => {
    if (!config.session.secret) {
        exitOnError(chalk.red('APP_SESSION_SECRET is missing in environment variables'));
    }

    if (config.bull.enableMonitor && process.env.NODE_ENV === 'production') {
        exitOnError(chalk.red('APP_BULL_MONITOR cannot be turned on on production environment'));
    }
};

const getDefaultEventExportConfigByCountry = (countryCode: string) => {
    switch (countryCode) {
        case 'JP':
            return {
                eventExportImp: 'PJ',
                eventExportType: 'VEHI',
                eventExportSource: 'Z0AF',
                eventExportLanguageCorr: 'J',
            };

        case 'KR':
            return {
                eventExportImp: 'PKO',
                eventExportType: 'VEHI',
                eventExportSource: 'Z0DF',
                eventExportLanguageCorr: '3',
            };

        default:
            return {
                eventExportImp: 'PAP',
                eventExportType: 'Z0DD',
                eventExportSource: 'Z0DF',
                eventExportLanguageCorr: 'E',
            };
    }
};

export const getCountryEventExportConfig = (tenant: string, countryCode: string) => ({
    EVENT_EXPORT_IMP: getString(
        `${tenant}_EVENT_EXPORT_IMP`,
        getDefaultEventExportConfigByCountry(countryCode).eventExportImp
    ),
    EVENT_EXPORT_IDTYPE: getString(`${tenant}_EVENT_EXPORT_IDTYPE`, 'Z0DC'),
    EVENT_EXPORT_TYPE: getString(
        `${tenant}_EVENT_EXPORT_TYPE`,
        getDefaultEventExportConfigByCountry(countryCode).eventExportType
    ),
    EVENT_EXPORT_SOURCE: getString(
        `${tenant}_EVENT_EXPORT_SOURCE`,
        getDefaultEventExportConfigByCountry(countryCode).eventExportSource
    ),
    EVENT_EXPORT_LAST_NAME: getString(`${tenant}_EVENT_EXPORT_LAST_NAME`, ' '),
    EVENT_EXPORT_LANGUAGE_CORR: getString(
        `${tenant}_EVENT_EXPORT_LANGUAGE_CORR`,
        getDefaultEventExportConfigByCountry(countryCode).eventExportLanguageCorr
    ),
    EVENT_EXPORT_ADDRESS_TYPE: getString(`${tenant}_EVENT_EXPORT_ADDRESS_TYPE`, 'HOME'),
    EVENT_EXPORT_MOBILE_TYPE: getString(`${tenant}_EVENT_EXPORT_MOBILE_TYPE`, 'HOME'),
    EVENT_EXPORT_MAIL_TYPE: getString(`${tenant}_EVENT_EXPORT_MAIL_TYPE`, 'HOME'),
    EVENT_EXPORT_CUSTOMER_STATUS_P: getString(`${tenant}_EVENT_EXPORT_CUSTOMER_STATUS_P`, 'X'),
});

export default config;
