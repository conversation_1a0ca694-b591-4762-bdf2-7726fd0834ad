import { parse } from 'path';
import * as Sentry from '@sentry/node';
import { RequestHandler } from 'express';
import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getFileStream } from '../../core/storage';
import { ExternalLinkKind } from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { isApplicationWithDocuments } from '../../database/helpers/applications';
import { getApplicationType } from '../../export/exportApplications';
import { mainQueue } from '../../queues';
import { getApplicationIdentifier } from '../../utils/application';
import getEncryptedZip from '../../utils/getEncryptedZip';

type Param = {
    secret: string;
};

const cleanup = async (linkId: ObjectId, deleteOnFetch: boolean) => {
    if (deleteOnFetch) {
        const { collections } = await getDatabaseContext();
        collections.externalLinks.findOneAndDelete({ _id: linkId });
    }
};

const download: RequestHandler<Param, unknown, unknown> = async (req, res, next) => {
    if (req.method !== 'GET') {
        // The app.get() function is automatically called for the HTTP HEAD method
        // in addition to the GET method if app.head() was not called for the path before app.get().
        // https://expressjs.com/en/api.html#app.METHOD
        res.status(200).send();

        return;
    }

    const { secret } = req.params;

    const { collections } = await getDatabaseContext();

    const link = await collections.externalLinks.findOne({ secret });

    if (isNil(link) || link._kind !== ExternalLinkKind.DownloadApplicationDocument) {
        res.status(404).send();

        return;
    }

    const {
        data: { applicationId, documentId, encrypt, password, stage },
        deleteOnFetch,
    } = link;

    const application = await collections.applications.findOne({ _id: applicationId, 'documents._id': documentId });

    if (isNil(application) || !isApplicationWithDocuments(application)) {
        res.status(404).send();

        return;
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    if (isNil(lead)) {
        res.status(404).send();

        return;
    }

    const document = application.documents.find(({ _id }) => _id.equals(documentId));

    try {
        const source = await getFileStream(document);
        if (!encrypt) {
            res.setHeader(
                'Content-Disposition',
                `attachment; filename="${document.filename}"; filename*="${document.filename}"`
            );

            source.pipe(res);

            await cleanup(link._id, deleteOnFetch);

            return;
        }

        const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
        const company = applicationModule?.companyId
            ? await collections.companies.findOne({ _id: applicationModule.companyId })
            : null;

        const user = await collections.users.findOne({ _id: link.data.userId });

        const encryptedZip = await getEncryptedZip([{ source, filename: document.filename }], password);
        const fileNameWithoutExtension = parse(document.filename).name;

        res.set({
            ...(!isNil(password) ? { 'X-DOWNLOAD-PASSWORD': password } : {}),
            'Content-Disposition': `attachment; filename="${fileNameWithoutExtension}.zip"`,
            'Content-Type': 'application/zip',
        });

        const identifier = getApplicationIdentifier(application, lead, stage);

        await mainQueue.add({
            type: 'sendExportApplicationPasswordEmail',
            password,
            date: new Date(),
            company,
            applicationType: [
                stage ? getApplicationType(stage) : 'Application',
                identifier,
                fileNameWithoutExtension,
            ].join(' '),
            documentType: 'Documents',
            user,
        });

        res.send(encryptedZip);

        await cleanup(link._id, deleteOnFetch);
    } catch (error) {
        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('download', 'applications');
            scope.setLevel('fatal');
            scope.setContext('application', application);
            Sentry.captureException(error);
        });

        next(error);
    }
};

export default download;
