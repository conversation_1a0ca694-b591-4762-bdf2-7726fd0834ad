import { Document } from 'bson';
import { Job } from 'bull';
import { isEmpty, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { ApplicationKind, ApplicationSigningStatus, ApplicationStatus, ModuleType } from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getApplicantIdFromApplication } from '../../database/helpers/applications';
import { createSigning } from '../../journeys/helper';
import { generateInsuranceAgreementPdf } from '../../journeys/helper/generateInsuranceAgreement';
import { getSigningSettings } from '../../signing/helper';
import { getStatusUpdates } from '../../utils/application';

export type OnSigningInsuranceInitializedMessage = {
    applicationId: ObjectId;
    clientAction: string;
};

export const onSigningInsuranceInitialized = async (
    message: OnSigningInsuranceInitializedMessage,
    job: Job<Document>
) => {
    const { collections } = await getDatabaseContext();
    const { applicationId, clientAction } = message;

    const application = await collections.applications.findOne({ _id: applicationId });

    if (!application) {
        throw new Error('Application is missing');
    }

    if (
        application.kind !== ApplicationKind.Standard &&
        application.kind !== ApplicationKind.Configurator &&
        application.kind !== ApplicationKind.Finder
    ) {
        throw new Error('Insurance signing is not supported for this application');
    }

    if (!application.insurancing?.insurerId) {
        throw new Error('Insurer id is missing');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    const module = await collections.modules.findOne({ _id: application.moduleId });
    const company = await collections.companies.findOne({ _id: module.companyId });

    // Check also if insurance application has the same signing
    const insurerId =
        (application.kind === ApplicationKind.Standard ||
            application.kind === ApplicationKind.Configurator ||
            application.kind === ApplicationKind.Finder) &&
        application.insurancing?.insurerId;

    const insurer = insurerId
        ? await collections.insurers.findOne({ _id: insurerId, isActive: true, isDeleted: false })
        : undefined;
    const isResubmitInsurance = !isNil(journey.insuranceSubmission);
    const insuranceSigningModuleId = isResubmitInsurance
        ? insurer?.reSubmissionApprovalModuleId
        : insurer?.submissionApprovalModuleId;

    const setting = await getSigningSettings(insuranceSigningModuleId);
    const customer = await collections.customers.findOne({ _id: getApplicantIdFromApplication(application) });

    const customerModule = await collections.modules.findOne({
        _id: customer.moduleId,
    });

    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('module type not supported');
    }

    try {
        const insuranceAgreementPdf = await generateInsuranceAgreementPdf(application);

        const pdfs = [{ name: `${application.insuranceStage.identifier}.pdf`, pdf: insuranceAgreementPdf }];

        // create the signing envelope
        const { envelopeId, redirectionUrl, signingMode, nonce } = await createSigning(
            pdfs,
            {
                application,
                customer,
                setting,
                journeyIdentifier: 'insurance-namirial-signing',
                company,
                customerModule,
            },
            { clientAction }
        );

        await collections.applicationJourneys.findOneAndUpdate(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    insuranceApplicantSigning: {
                        ...journey.insuranceApplicantSigning,
                        status: ApplicationSigningStatus.Initialized,
                        moduleId: insuranceSigningModuleId,
                        kind: signingMode,
                        envelopeId,
                        redirectionUrl,
                        nonce,
                    },
                },
            }
        );
    } catch (error) {
        const statusUpdates = getStatusUpdates(application, lead, ApplicationStatus.SigningCreationFailed);
        if (!isEmpty(statusUpdates)) {
            await collections.applications.findOneAndUpdate({ _id: application._id }, { $set: statusUpdates });
        }

        throw new Error('Failed to create signing ', error);
    }
};
