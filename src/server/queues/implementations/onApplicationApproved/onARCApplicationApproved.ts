import dayjs from 'dayjs';
import { keyBy, last } from 'lodash/fp';
import { getUrlForUpload } from '../../../core/storage';
import {
    ApplicationKind,
    ApplicationStage,
    AuditTrailKind,
    AuthorKind,
    BankKind,
    CommentAuditTrail,
    ConfiguratorApplication,
    EventApplication,
    FinderApplication,
    FinderVehicle,
    getKYCPresetsForCustomerModule,
    LocalVariant,
    ModuleType,
    StandardApplication,
    StandardApplicationModule,
    User,
    VehicleKind,
} from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import {
    getCustomerEmail,
    getCustomerFullNameWithTitle,
    getCustomerFullName,
} from '../../../database/helpers/customers';
import { AudienceMessage, createCompanySMTPTransports, sendApplicationApproveConfirmation } from '../../../emails';
import getTranslatedString from '../../../emails/utils/getTranslatedString';
import { StandardApplicationModuleAsset } from '../../../schema/resolvers/enums';
import { getApplicationIdentifierForAction } from '../../../utils/application';
import createI18nInstance from '../../../utils/createI18nInstance';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../../utils/getEdmEmailFooterContext';
import { canSendEmailToCustomer } from '../sendApplicationSubmissionMail/shared';
import checkIsBankViewable from '../shared/checkIsBankViewable';
import getApplicationAssignee from '../shared/getApplicationAssignee';
import getApplicationContext from '../shared/getApplicationContext';
import getApplicationDetailsLink from '../shared/getApplicationDetailsLink';
import getApplicationStagesLink from '../shared/getApplicationStagesLink';
import { getStandardApplicationEmailContent } from '../shared/getStandardApplicationEmailContents';
import { ApplicationApproveSource, OnApplicationApprovedMessage } from './types';

const onARCApplicationApproved = async (
    message: OnApplicationApprovedMessage,
    application: ConfiguratorApplication | StandardApplication | FinderApplication | EventApplication
) => {
    const { collections } = await getDatabaseContext();
    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['emails', 'common', 'calculators', 'variant']);

    const { t } = i18n;

    const context = await getApplicationContext<LocalVariant | FinderVehicle>(
        application._id,
        ApplicationStage.Financing
    );
    const { customer, company, dealer, variant, customerModule, lead } = context;

    // for now only banks and user actions in financing details may trigger the queue, hence `Financing` stage
    const assignee = await getApplicationAssignee(application, lead, ApplicationStage.Financing);

    // load mail context
    const emailContext = await getCompanyEmailContext(company);
    const edmEmailFooterContext = await getEdmEmailFooterContext({ company });
    const dealerEmailContext = await getDealerEmailContext(dealer);

    const transporter = await createCompanySMTPTransports(company);

    const defaulIntroImage =
        variant._kind === VehicleKind.FinderVehicle
            ? last(variant.listing.vehicle.images.edges[0].node.variants).url
            : await getUrlForUpload(variant?.images?.[0]);

    const link = await getApplicationDetailsLink(application, lead);

    const renderDate = (value: string | Date) =>
        dayjs(value).tz(company.timeZone).format(t('common:formats.dateTimeFormat'));

    const bankName =
        context.bank?.kind === BankKind.System
            ? getTranslatedString(context.bank.legalName, i18n.language)
            : context.bank.displayName;
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);

    const applicationComments = (await collections.auditTrails
        .find({
            _kind: AuditTrailKind.ApplicationComment,
            applicationSuiteId: application._versioning.suiteId,
        })
        .sort({ _date: 1 })
        .toArray()) as CommentAuditTrail[];

    const userIds = applicationComments
        .map(comment => {
            if (comment.author.kind === AuthorKind.User) {
                return comment.author.id;
            }

            return null;
        })
        .filter(Boolean);

    const users = await collections.users.find({ _id: { $in: userIds } }).toArray();

    const userMapping = keyBy((user: User) => user._id.toHexString(), users);

    const comments = applicationComments
        .map(comment => {
            if (comment.author.kind === AuthorKind.User) {
                const displayName = userMapping[comment.author.id.toHexString()]?.displayName;

                return {
                    displayName,
                    date: renderDate(comment._date),
                    comment: comment.comment,
                };
            }

            return null;
        })
        .filter(Boolean);

    const isBankViewable = await checkIsBankViewable(variant._versioning.suiteId, application.moduleId);

    const applicationStagesLink = await getApplicationStagesLink(application, lead);

    const getEmailData = (audience: AudienceMessage) => ({
        ...context,
        assignee,

        emailContext,
        edmEmailFooterContext,
        dealerEmailContext,
        introImageUrl: defaulIntroImage,

        link,
        comments,
        initiatorName: message.source === ApplicationApproveSource.User ? assignee.displayName : bankName,

        audience,
        isBankViewable,
        applicationStagesLink,
    });

    if (application.kind === ApplicationKind.Standard) {
        const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
            _id: application.moduleId,
            _type: ModuleType.StandardApplicationModule,
        });

        const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
            await getStandardApplicationEmailContent({
                i18n,
                standardApplicationModule,
                standardApplicationAsset: StandardApplicationModuleAsset.SalesPersonApproved,
                emailContext,
                dealer,
                customer,
                customerModule,
                user: assignee,
                link,
                initiatorName: message.source === ApplicationApproveSource.User ? assignee.displayName : bankName,
                identifier: getApplicationIdentifierForAction(application, lead),
                lead,
            });

        // send the email to sales person
        await sendApplicationApproveConfirmation(
            {
                i18n,
                data: {
                    ...getEmailData(AudienceMessage.Salesperson),
                    introImageUrl: introImageUrl ?? defaulIntroImage,
                    introTitle,
                    contentText,
                    isSummaryVehicleVisible,
                },
                to: { name: assignee.displayName, address: assignee.email },
                subject: emailSubject,
            },
            transporter,
            emailContext.sender
        );

        // send the email to customer
        if (canSendEmailToCustomer(application, context.bank, null, context.journey, false, customer, t)) {
            const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
                await getStandardApplicationEmailContent({
                    i18n,
                    standardApplicationModule,
                    standardApplicationAsset: StandardApplicationModuleAsset.CustomerApproved,
                    emailContext,
                    dealer,
                    customer,
                    customerModule,
                    user: assignee,
                    link,
                    initiatorName: message.source === ApplicationApproveSource.User ? assignee.displayName : bankName,
                    identifier: getApplicationIdentifierForAction(application, lead),
                    lead,
                });
            await sendApplicationApproveConfirmation({
                i18n,
                data: {
                    ...getEmailData(AudienceMessage.Customer),
                    introImageUrl: introImageUrl ?? defaulIntroImage,
                    introTitle,
                    contentText,
                    isSummaryVehicleVisible,
                },
                to: {
                    name: getCustomerFullName(t, customer, company, kycPresets),
                    address: getCustomerEmail(t, customer),
                },
                subject: emailSubject,
            });
        }

        return;
    }

    // send the email to sales person
    await sendApplicationApproveConfirmation(
        {
            i18n,
            data: getEmailData(AudienceMessage.Salesperson),
            to: { name: assignee.displayName, address: assignee.email },
            subject: t('emails:salesPersonApproveConfirmation.subject', {
                companyName: emailContext.companyName,
                identifier: getApplicationIdentifierForAction(application, lead),
                customerName: customerFullName,
            }),
        },
        transporter,
        emailContext.sender
    );

    // send the email to customer
    if (canSendEmailToCustomer(application, context.bank, null, context.journey, false, customer, t)) {
        await sendApplicationApproveConfirmation({
            i18n,
            data: getEmailData(AudienceMessage.Customer),
            to: {
                name: getCustomerFullName(t, customer, company, kycPresets),
                address: getCustomerEmail(t, customer),
            },
            subject: t('emails:customerApproveConfirmation.subject', {
                companyName: emailContext.companyName,
                identifier: getApplicationIdentifierForAction(application, lead),
                customerName: customerFullName,
            }),
        });
    }
};

export default onARCApplicationApproved;
