import { promises as fs } from 'fs';
import * as os from 'os';
import * as path from 'path';
import { Document } from 'bson';
import { Job } from 'bull';
import dayjs from 'dayjs';
import { i18n, TFunction } from 'i18next';
import { AggregationCursor, ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { LeadStatus } from '../../database';
import { Company, PasswordConfiguration } from '../../database/documents/Company';
import { ExportLead } from '../../database/documents/Lead/kinds';
import { User } from '../../database/documents/User';
import { EventApplicationModule } from '../../database/documents/modules';
import { LeadStageOption } from '../../database/documents/shared';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    createCompanySMTPTransports,
    sendEventLeadExportReady,
    sendEventLeadExportPassword,
    sendEventLeadExportFail,
} from '../../emails';
import { setHeaderColumnsWidth } from '../../export/exportApplications/shared';
import { getLeadFilterByStage, getLeadStageName } from '../../export/streamExportLeads/shared';
import { PeriodPayload } from '../../export/type';
import { getDealerIdsFromRequest, getPassword, getPeriodFilter } from '../../export/utils';
import createLoaders, { Loaders } from '../../loaders';
import { createPermissionController, LeadPolicyAction, PermissionController } from '../../permissions';
import createI18nInstance from '../../utils/createI18nInstance';
import { FormatCapPurpose } from '../../utils/excel/applications/cap/types';
import { ExcelExportFormat } from '../../utils/excel/enums';
import getExcelLeadRows, { type FormatSetting } from '../../utils/excel/leads';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

const BATCH_SIZE = 500;

export type ProcessEventLeadExportMessage = {
    userId: ObjectId;
    eventId: string;
    leadIds: string[];
    stage: LeadStageOption;
    dealerIds: string[];
    period?: PeriodPayload;
    format: ExcelExportFormat;
    capPurpose?: FormatCapPurpose[];
    nonce?: string;
    languageId?: string;
    filename: string[];
};

// Export context similar to processLeadExport.ts
type EventExportContext = {
    user: User;
    event: any;
    eventModule: EventApplicationModule;
    company: Company;
    loaders: Loaders;
    permissions: PermissionController;
    i18n: i18n;
    t: TFunction;
};

// Utility function to create format settings (extracted and optimized)
type GetLeadExcelFormatSettingsParams = {
    format: ExcelExportFormat;
    capPurpose?: FormatCapPurpose;
    company: {
        displayName: string;
        countryCode: string;
        currency: string;
        timeZone?: string;
    };
    validatedLeadStage: LeadStageOption;
    languageId: string | null | undefined;
};

const getLeadExcelFormatSettings = ({
    format,
    capPurpose,
    company,
    validatedLeadStage,
    languageId,
}: GetLeadExcelFormatSettingsParams): FormatSetting => {
    if (format === ExcelExportFormat.cap) {
        return {
            format,
            capPurpose: capPurpose as FormatCapPurpose,
            tenant: `${company.displayName}_${company.countryCode}`.toUpperCase(),
            stage: validatedLeadStage,
            routerFirstLanguage: languageId || null,
            timeZone: company?.timeZone,
        };
    }

    return {
        format,
        stage: validatedLeadStage,
        currencyCode: company.currency,
        timeZone: company.timeZone,
        routerFirstLanguage: languageId || null,
    };
};

// Initialize export context once to avoid repeated database calls (aligned with processLeadExport.ts)
const initializeEventExportContext = async (message: ProcessEventLeadExportMessage): Promise<EventExportContext> => {
    const { userId, eventId: inputEventId } = message;
    const { collections } = await getDatabaseContext();

    // Parallel initialization of independent resources
    const [user, loaders, { i18n }] = await Promise.all([
        collections.users.findOne({ _id: userId }),
        createLoaders(),
        createI18nInstance(),
    ]);

    await i18n.loadNamespaces(['emails', 'common']);
    const { t } = i18n;

    const permissions = await createPermissionController(user);

    // Load event-related data
    const eventId = new ObjectId(inputEventId);
    const [event, eventModule] = await Promise.all([
        collections.events.findOne({ _id: eventId }),
        collections.events.findOne({ _id: eventId }).then(async event => {
            if (!event) {
                throw new Error(`Event not found: ${eventId}`);
            }

            return collections.modules.findOne({ _id: event.moduleId }) as Promise<EventApplicationModule>;
        }),
    ]);

    const company = await collections.companies.findOne({ _id: eventModule.companyId });

    return {
        user,
        event,
        eventModule,
        company,
        loaders,
        permissions,
        i18n,
        t,
    };
};

// Optimized batch iterator similar to processLeadExport.ts
const createEventBatchIterator = (cursor: AggregationCursor<ExportLead>, batchSize: number) => ({
    async *[Symbol.asyncIterator]() {
        let batchCount = 0;

        while (true) {
            const batch: ExportLead[] = [];

            try {
                while (batch.length < batchSize) {
                    // eslint-disable-next-line no-await-in-loop
                    const hasNext = await cursor.hasNext();
                    if (!hasNext) {
                        break;
                    }

                    // eslint-disable-next-line no-await-in-loop
                    const lead = await cursor.next();
                    if (lead) {
                        batch.push(lead);
                    }
                }

                if (batch.length === 0) {
                    break;
                }

                yield { batch, batchIndex: batchCount++ };

                // Memory pressure check - yield control periodically
                if (batchCount % 10 === 0) {
                    // eslint-disable-next-line no-await-in-loop
                    await new Promise(resolve => {
                        setImmediate(resolve);
                    });
                }
            } catch (error) {
                console.error(`Error fetching batch ${batchCount}:`, error);
                throw error;
            }
        }
    },
});

// Streaming workbook generation aligned with processLeadExport.ts
const generateEventWorkbookStreaming = async (
    cursor: AggregationCursor<ExportLead>,
    context: EventExportContext,
    format: ExcelExportFormat,
    stage: LeadStageOption,
    languageId?: string,
    currentPurpose?: FormatCapPurpose
) => {
    const workbook = await XlsxPopulate.fromBlankAsync();
    const worksheet = workbook.sheet(0).name('Export Data');

    let headerAdded = false;
    let currentRow = 1;
    let totalProcessed = 0;

    const batchIterator = createEventBatchIterator(cursor, BATCH_SIZE);

    try {
        for await (const { batch, batchIndex } of batchIterator) {
            const rows = await processEventLeads(
                context.loaders,
                batch,
                [context.eventModule], // Always include the event module
                format,
                context.company,
                stage,
                languageId,
                currentPurpose
            );

            if (!rows || !Array.isArray(rows) || rows.length === 0) {
                continue;
            }

            // Add header only once
            if (!headerAdded && rows.length > 0) {
                const headerRow = rows[0];
                if (Array.isArray(headerRow)) {
                    // eslint-disable-next-line no-loop-func
                    headerRow.forEach((cell, colIndex) => {
                        worksheet.cell(currentRow, colIndex + 1).value(cell ?? '');
                    });
                    currentRow++;
                    headerAdded = true;

                    // Set column widths based on header
                    setHeaderColumnsWidth(worksheet, headerRow);
                }
            }

            // Add data rows (skip header)
            const dataStartIndex = 1;
            for (let i = dataStartIndex; i < rows.length; i++) {
                const row = rows[i];
                if (Array.isArray(row)) {
                    // eslint-disable-next-line no-loop-func
                    row.forEach((cell, colIndex) => {
                        worksheet.cell(currentRow, colIndex + 1).value(cell ?? '');
                    });
                    currentRow++;
                }
            }

            totalProcessed += batch.length;

            // Progress logging
            if (batchIndex % 20 === 0) {
                console.info(`Processed ${totalProcessed} event leads (${batchIndex + 1} batches)`);
            }
        }

        console.info(`Event export completed: ${totalProcessed} total leads processed`);

        return workbook;
    } catch (error) {
        console.error('Error in event workbook generation:', error);
        throw error;
    }
};

// Generate export files aligned with processLeadExport.ts structure
const generateEventExportFiles = async (context: EventExportContext, message: ProcessEventLeadExportMessage) => {
    const {
        eventId: inputEventId,
        leadIds: leadStringIds,
        dealerIds: queryDealerIds,
        stage,
        period,
        format,
        capPurpose,
        languageId,
        filename: inputFilename,
        nonce: inputNonce,
    } = message;

    const purposesToProcess =
        format === ExcelExportFormat.cap && Array.isArray(capPurpose) && capPurpose.length > 0
            ? capPurpose
            : [undefined];

    const results: Array<{ filename: string; path: string; password?: string }> = [];

    const nonce = inputNonce ?? nanoid();
    const password = await getPassword(nonce);
    const isPasswordProtected = context.company.passwordConfiguration !== PasswordConfiguration.Off;

    const { collections } = await getDatabaseContext();
    const eventId = new ObjectId(inputEventId);

    // Prepare query parameters
    const dealerIdsInRequest = getDealerIdsFromRequest(queryDealerIds);
    const leadPermissions = await context.permissions.leads.getFilterQueryForAction(LeadPolicyAction.View);
    const leadStageFilter = getLeadFilterByStage(stage);

    const start: Date = period?.start ? dayjs(period.start).startOf('day').toDate() : null;
    const end: Date = period?.end ? dayjs(period.end).endOf('day').toDate() : null;

    const leadIds = (leadStringIds ?? []).filter(id => ObjectId.isValid(id)).map(id => new ObjectId(id));

    // Process purposes sequentially to manage memory usage
    for (let index = 0; index < purposesToProcess.length; index++) {
        const currentPurpose = purposesToProcess[index];
        let cursor = null;

        try {
            // Create aggregation cursor (aligned with original logic)
            cursor = collections.leads
                .aggregate<ExportLead>([
                    {
                        $match: {
                            ...leadPermissions,
                            ...leadStageFilter,
                            ...(leadIds.length > 0 ? { _id: { $in: leadIds } } : {}),
                            eventId,
                            isDraft: false,
                            status: { $ne: LeadStatus.Drafted },
                            dealerId: { $in: dealerIdsInRequest },
                            ...getPeriodFilter(start, end),
                        },
                    },
                    {
                        $lookup: {
                            from: 'events',
                            localField: 'eventId',
                            foreignField: '_id',
                            as: 'event',
                        },
                    },
                    {
                        $unwind: {
                            path: '$event',
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $addFields: {
                            eventDisplayName: '$event.displayName',
                        },
                    },
                    {
                        $project: {
                            event: 0,
                        },
                    },
                ])
                .batchSize(BATCH_SIZE);

            // eslint-disable-next-line no-await-in-loop
            const workbook = await generateEventWorkbookStreaming(
                cursor,
                context,
                format,
                stage,
                languageId,
                currentPurpose
            );

            // Generate filename
            let filename = 'export.xlsx';
            if (inputFilename && inputFilename.length > 0) {
                filename = inputFilename[index] || inputFilename[0];
                if (!filename.endsWith('.xlsx')) {
                    filename = `${filename}.xlsx`;
                }
            }

            // eslint-disable-next-line no-await-in-loop
            const buffer = await workbook.outputAsync({
                ...(isPasswordProtected && { password }),
            });

            // Use async file operations
            const tempDir = os.tmpdir();
            const filePath = path.join(tempDir, filename);
            // eslint-disable-next-line no-await-in-loop
            await fs.writeFile(filePath, buffer);

            results.push({
                filename,
                path: filePath,
                ...(isPasswordProtected && { password }),
            });
        } catch (error) {
            console.error(`Error generating event workbook ${index}:`, error);
            throw error;
        } finally {
            // Cleanup resources
            if (cursor) {
                try {
                    // eslint-disable-next-line no-await-in-loop
                    await cursor.close();
                } catch (error) {
                    console.warn('Warning: Could not close cursor:', error.message);
                }
            }
        }
    }

    return results;
};

// Process event leads function (extracted and optimized)
const processEventLeads = async (
    loaders: Loaders,
    leads: ExportLead[],
    modules: EventApplicationModule[],
    format: ExcelExportFormat,
    company: Company,
    stage: LeadStageOption,
    languageId?: string,
    currentPurpose?: FormatCapPurpose
) => {
    // No leads to process, we do nothing
    if (leads.length === 0) {
        return [];
    }

    try {
        return getExcelLeadRows(
            leads,
            modules,
            getLeadExcelFormatSettings({
                format,
                capPurpose: currentPurpose,
                company,
                validatedLeadStage: stage,
                languageId,
            }),
            loaders
        );
    } catch (error) {
        throw new Error(`Error processing event batch: ${error.message}`);
    }
};

// Optimized email sending with better error handling (aligned with processLeadExport.ts)
const sendEventExportEmails = async (
    context: EventExportContext,
    filePaths: Array<{ filename: string; path: string; password?: string }>,
    leadStageType: string,
    exportPassword?: string
) => {
    const emailContext = await getCompanyEmailContext(context.company);
    const transporter = await createCompanySMTPTransports(context.company);

    try {
        // Send export ready email
        await sendEventLeadExportReady(
            {
                i18n: context.i18n,
                subject: context.t('emails:eventLeadExportReady.subject', {
                    companyName: context.company.displayName,
                    leadStageType,
                }),
                data: {
                    user: context.user,
                    requestDate: new Date(),
                    emailContext,
                    leadStageType,
                },
                to: { name: context.user.displayName, address: context.user.email },
                attachments: filePaths.map(file => ({
                    filename: file.filename,
                    path: file.path,
                    contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                })),
            },
            transporter,
            emailContext.sender
        );

        // Send password email if needed
        if (exportPassword) {
            await sendEventLeadExportPassword(
                {
                    i18n: context.i18n,
                    subject: context.t('emails:eventLeadExportPassword.subject', {
                        companyName: context.company.displayName,
                        leadStageType,
                    }),
                    data: {
                        user: context.user,
                        password: exportPassword,
                        emailContext,
                        leadStageType,
                    },
                    to: { name: context.user.displayName, address: context.user.email },
                },
                transporter,
                emailContext.sender
            );
        }
    } catch (error) {
        console.error('Error sending event export emails:', error);
        throw error;
    }
};

// Cleanup files function (aligned with processLeadExport.ts)
const cleanupFiles = async (filePaths: Array<{ path: string }>) => {
    await Promise.allSettled(
        filePaths.map(async file => {
            try {
                await fs.unlink(file.path);
            } catch (error) {
                console.warn(`Warning: Could not delete temp file ${file.path}:`, error.message);
            }
        })
    );
};

// Send failure notification (extracted function)
const sendEventFailureNotification = async (message: ProcessEventLeadExportMessage, originalError: Error) => {
    try {
        const context = await initializeEventExportContext(message);
        const leadStageType = `Events-${getLeadStageName(message.stage)}`;

        const emailContext = await getCompanyEmailContext(context.company);
        const transporter = await createCompanySMTPTransports(context.company);

        await sendEventLeadExportFail(
            {
                i18n: context.i18n,
                subject: context.t('emails:eventLeadExportFail.subject', {
                    companyName: context.company.displayName,
                    leadStageType,
                }),
                data: {
                    user: context.user,
                    requestDate: new Date(),
                    emailContext,
                    leadStageType,
                },
                to: { name: context.user.displayName, address: context.user.email },
            },
            transporter,
            emailContext.sender
        );
    } catch (emailError) {
        console.error('Error sending event lead export failure notification:', emailError);
    }
};

// Main export function (refactored to align with processLeadExport.ts)
export const processEventLeadExport = async (message: ProcessEventLeadExportMessage, _job: Job<Document>) => {
    let filePaths: Array<{ filename: string; path: string; password?: string }> = [];

    try {
        // Initialize export context
        const context = await initializeEventExportContext(message);

        // Generate export files
        filePaths = await generateEventExportFiles(context, message);

        // Send emails
        const exportPassword = filePaths[0]?.password;
        const leadStageType = `Events-${getLeadStageName(message.stage)}`;

        await sendEventExportEmails(context, filePaths, leadStageType, exportPassword);
    } catch (error) {
        console.error('Unexpected error in processEventLeadExport:', error);

        // Send failure notification
        await sendEventFailureNotification(message, error);

        throw error;
    } finally {
        // Clean up the temporary files
        if (filePaths.length > 0) {
            await cleanupFiles(filePaths);
        }
    }
};
