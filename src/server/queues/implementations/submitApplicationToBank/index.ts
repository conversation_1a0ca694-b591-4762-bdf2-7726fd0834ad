import { Readable as ReadableStream } from 'stream';
import { Job } from 'bull';
import { TFunction } from 'i18next';
import { head, isEmpty } from 'lodash/fp';
import { Document, Filter, MatchKeysAndValues, ObjectId } from 'mongodb';
import { getFileStream } from '../../../core/storage';
import {
    Application,
    ApplicationJourney,
    ApplicationJourneySubmissionKind,
    ApplicationKind,
    Bank,
    BankIntegrationProvider,
    Company,
    Dealer,
    FinderVehicle,
    LocalCustomerManagementModule,
    LocalFinanceProduct,
    LocalMake,
    LocalModel,
    LocalVariant,
    Module,
    ModuleType,
    SettingId,
} from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getBankIdFromApplication } from '../../../database/helpers/applications';
import { getPreviousApplicationStages } from '../../../database/queries/application';
import type { HlfAmendRequest } from '../../../integrations/banks/hlf/amend';
import type { HlfSubmitRequest } from '../../../integrations/banks/hlf/submit';
import type { UobAmendRequest } from '../../../integrations/banks/uob/amend';
import type { UobSubmitRequest } from '../../../integrations/banks/uob/submit';
import { getAgreementDocument } from '../../../journeys/helper';
import createI18nInstance from '../../../utils/createI18nInstance';
import { mainQueue } from '../../mainQueue';
import { sendApplicationEmails } from '../onApplicationSubmitted';
import { isAbleToSendSubmissionEmail } from '../sendApplicationSubmissionMail/shared';
import { getFinderVehicleLatestListing } from '../shared';
import { persistFailure, persistSuccess } from './shared';
import {
    DbsCallbacks,
    DbsSubmissionDocuments,
    prepareDbsRequestByPayloads,
    submitApplicationByDbsIntegration,
} from './submitApplicationByDbsIntegration';
import submitApplicationByEmail, { EmailCallbacks } from './submitApplicationByEmail';
import submitApplicationByEnbd from './submitApplicationByEnbd';
import {
    HlfCallbacks,
    prepareHlfAmendRequest,
    prepareHlfSubmitRequest,
    resubmitApplicationByHlfIntegration,
    submitApplicationByHlfIntegration,
} from './submitApplicationByHlfIntegration';
import {
    MaybankCallbacks,
    prepareMaybankRequest,
    submitApplicationByMaybankIntegration,
} from './submitApplicationByMaybankIntegration';
import {
    prepareUobAmendRequest,
    prepareUobSubmitRequest,
    resubmitApplicationByUobIntegration,
    submitApplicationByUobIntegration,
    UobCallbacks,
} from './submitApplicationByUobIntegration';
import {
    SubmitApplicationToBankAttachments,
    SubmitApplicationToBankMessage,
    SubmitApplicationToBankSource,
} from './types';

type SubmitApplicationToBankHandler = (
    message: SubmitApplicationToBankMessage,
    documents: {
        company: Company;
        module: Module;
        application: Application;
        bank: Bank;
        financeProduct?: LocalFinanceProduct | null;
    },
    files: SubmitApplicationToBankAttachments,
    payloads: {},
    retries: number
) => Promise<void>;

type SubmitApplicationToHlf = (
    bank: Bank,
    callbacks: HlfCallbacks,
    options: {
        isResubmit: boolean;
        documents: {
            module: Module;
            application: Application;
            journey: ApplicationJourney;
            financeProduct?: LocalFinanceProduct | null;
        };
        files: SubmitApplicationToBankAttachments;
        payloads: {};
    }
) => Promise<void>;

const getCustomerId = (application: Application): ObjectId | null => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Event:
        case ApplicationKind.Configurator:
        case ApplicationKind.Finder:
            return application.applicantId;

        default:
            return null;
    }
};

type VehicleAggregate = {
    model: LocalModel;
    make: LocalMake;
} & LocalVariant;

const standardVehicleAggregatePipelines = (vehicleId: ObjectId) => [
    {
        $match: {
            _id: vehicleId,
        },
    },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'modelId',
            foreignField: '_id',
            as: 'model',
        },
    },
    {
        $unwind: {
            path: '$model',
            preserveNullAndEmptyArrays: false,
        },
    },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'model.makeId',
            foreignField: '_id',
            as: 'make',
        },
    },
    {
        $unwind: {
            path: '$make',
            preserveNullAndEmptyArrays: false,
        },
    },
    {
        $limit: 1,
    },
];

const finderVehicleAggregatePipelines = (vehicleId: ObjectId) => [
    {
        $match: {
            _id: vehicleId,
        },
    },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'listing.vehicle.orderTypeCode',
            foreignField: 'identifier',
            as: 'relatedVariant',
            pipeline: [
                {
                    $match: {
                        '_versioning.isLatest': true,
                    },
                },
                {
                    $limit: 1,
                },
                {
                    $project: {
                        bankCodeMappings: 1,
                        identifier: 1,
                    },
                },
            ],
        },
    },
    {
        $unwind: {
            path: '$relatedVariant',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const submitApplicationToHlf: SubmitApplicationToHlf = async (
    bank,
    callbacks,
    { isResubmit, documents: { module, application, journey, financeProduct }, files, payloads }
) => {
    const { collections } = await getDatabaseContext();

    await collections.applicationJourneys.findOneAndUpdate(
        { _id: journey._id },
        {
            $set: {
                'submission.kind': ApplicationJourneySubmissionKind.HLF,
                'submission.bankId': bank._id,
                isImmutable: false,
            },
        }
    );

    const setting =
        'settingId' in bank.integration
            ? await collections.settings.findOne({
                  settingId: { $in: [SettingId.HlfBankIntegration, SettingId.HlfBankV2Integration] },
                  _id: bank.integration.settingId,
              })
            : null;

    if (setting?.settingId !== SettingId.HlfBankIntegration && setting?.settingId !== SettingId.HlfBankV2Integration) {
        throw new Error(
            // eslint-disable-next-line max-len
            `invalid bank integration setting, expecting ${SettingId.HlfBankIntegration}, got ${setting?.settingId}`
        );
    }

    const { onSuccess, ...otherCallbacks } = callbacks;
    const { agreementPdf } = files;

    if (isResubmit) {
        let request: HlfAmendRequest | undefined;

        switch (application.kind) {
            default: {
                request = await prepareHlfAmendRequest(setting.settingId, application, journey);
            }
        }

        return resubmitApplicationByHlfIntegration(setting, request, callbacks);
    }

    let request: HlfSubmitRequest | undefined;

    switch (application.kind) {
        default: {
            request = await prepareHlfSubmitRequest(setting.settingId, application, agreementPdf);
        }
    }

    // persist the submission date, used for `bookingdate` in future amendments
    const updateSubmittedAt = () =>
        collections.applicationJourneys.findOneAndUpdate(
            {
                _id: journey._id,
            },
            { $set: { 'submission.submittedAt': new Date(), isImmutable: false } }
        );

    return submitApplicationByHlfIntegration(setting, request, {
        ...otherCallbacks,
        onSuccess: async reference => {
            await onSuccess(reference);

            await updateSubmittedAt();
        },
    });
};

type SubmitApplicationToUob = (
    bank: Bank,
    callbacks: UobCallbacks,
    options: {
        isResubmit: boolean;
        documents: {
            module: Module;
            company: Company;
            application: Application;
            journey: ApplicationJourney;
            financeProduct?: LocalFinanceProduct | null;
        };
        payloads: {};
    }
) => Promise<void>;
const submitApplicationToUob: SubmitApplicationToUob = async (
    bank,
    callbacks,
    { isResubmit, documents: { module, company, application, journey, financeProduct }, payloads }
) => {
    const { collections } = await getDatabaseContext();

    await collections.applicationJourneys.findOneAndUpdate(
        { _id: journey._id },
        {
            $set: {
                'submission.kind': ApplicationJourneySubmissionKind.UOB,
                'submission.bankId': bank._id,
                isImmutable: false,
            },
        }
    );

    const setting =
        'settingId' in bank.integration
            ? await collections.settings.findOne({
                  settingId: SettingId.UobBankIntegration,
                  _id: bank.integration.settingId,
              })
            : null;

    if (setting?.settingId !== SettingId.UobBankIntegration) {
        throw new Error(
            // eslint-disable-next-line max-len
            `invalid bank integration setting, expecting ${SettingId.UobBankIntegration}, got ${setting?.settingId}`
        );
    }

    if (isResubmit) {
        let request: UobAmendRequest | undefined;
        switch (application.kind) {
            default: {
                request = await prepareUobAmendRequest(application, journey);
            }
        }

        return resubmitApplicationByUobIntegration(company, setting, request, callbacks);
    }

    let request: UobSubmitRequest | undefined;
    switch (application.kind) {
        default: {
            request = await prepareUobSubmitRequest(application);
        }
    }

    return submitApplicationByUobIntegration(company, setting, request, callbacks);
};

type SubmitApplicationToDbs = (
    bank: Bank,
    callbacks: DbsCallbacks,
    options: {
        documents: DbsSubmissionDocuments;
        payloads: {};
    },
    t: TFunction
) => Promise<void>;
const submitApplicationToDbs: SubmitApplicationToDbs = async (bank, callbacks, { documents, payloads }, t) => {
    const { collections } = await getDatabaseContext();

    const updatedJourney = await collections.applicationJourneys.findOneAndUpdate(
        { _id: documents.journey._id },
        {
            $set: {
                'submission.kind': ApplicationJourneySubmissionKind.DBS,
                'submission.bankId': bank._id,
                isImmutable: false,
            },
        },
        { returnDocument: 'after' }
    );

    const setting =
        'settingId' in bank.integration
            ? await collections.settings.findOne({
                  settingId: SettingId.DbsBankIntegration,
                  _id: bank.integration.settingId,
              })
            : null;

    if (setting?.settingId !== SettingId.DbsBankIntegration) {
        throw new Error(
            `invalid bank integration setting, expecting ${SettingId.DbsBankIntegration}, got ${setting?.settingId}`
        );
    }

    return submitApplicationByDbsIntegration(
        setting,
        prepareDbsRequestByPayloads(setting, { ...documents, journey: updatedJourney }, payloads, t),
        callbacks
    );
};

type SubmitApplicationToMaybank = (
    bank: Bank,
    callbacks: MaybankCallbacks,
    options: {
        isResubmit: boolean;
        documents: {
            module: Module;
            application: Application;
            journey: ApplicationJourney;
            files: SubmitApplicationToBankAttachments;
        };
        payloads: {};
    },
    t: TFunction
) => Promise<void>;

const submitApplicationToMaybank: SubmitApplicationToMaybank = async (
    bank,
    callbacks,
    { isResubmit, documents: { module, application, journey, files }, payloads },
    t
) => {
    const { collections } = await getDatabaseContext();

    await collections.applicationJourneys.findOneAndUpdate(
        { _id: journey._id },
        {
            $set: {
                'submission.kind': ApplicationJourneySubmissionKind.Maybank,
                'submission.bankId': bank._id,
                isImmutable: false,
            },
        }
    );

    const setting =
        'settingId' in bank.integration
            ? await collections.settings.findOne({
                  settingId: SettingId.MaybankIntegration,
                  _id: bank.integration.settingId,
              })
            : null;

    if (setting?.settingId !== SettingId.MaybankIntegration) {
        throw new Error(
            `invalid bank integration setting, expecting ${SettingId.MaybankIntegration}, got ${setting?.settingId}`
        );
    }

    const submittedAt =
        journey?.submission?.kind === ApplicationJourneySubmissionKind.Maybank ? journey.submission.submittedAt : null;
    const updateSubmittedAt = () =>
        collections.applicationJourneys.findOneAndUpdate(
            {
                _id: journey._id,
            },
            { $set: { 'submission.submittedAt': new Date() } }
        );

    switch (application.kind) {
        default: {
            if (isResubmit) {
                return submitApplicationByMaybankIntegration(
                    setting,
                    await prepareMaybankRequest(
                        { application, setting },
                        {
                            AmtIndicator: 'Y',
                            DOSalAgmt: submittedAt,
                        },
                        files.agreementPdf,
                        t
                    ),
                    callbacks
                );
            }

            return submitApplicationByMaybankIntegration(
                setting,
                await prepareMaybankRequest({ application, setting }, { AmtIndicator: 'N' }, null, t),
                {
                    ...callbacks,
                    onSuccess: async (...args) => {
                        await updateSubmittedAt();

                        return callbacks.onSuccess(...args);
                    },
                }
            );
        }
    }
};

const getIsResubmit = (message: SubmitApplicationToBankMessage, journey: ApplicationJourney) => {
    const { source } = message;

    switch (source) {
        case SubmitApplicationToBankSource.System: {
            // for integrations other than email, check if reference already presents
            return (
                journey.submission &&
                (journey.submission.kind === ApplicationJourneySubmissionKind.Email ||
                    journey.submission.kind === ApplicationJourneySubmissionKind.Enbd ||
                    !isEmpty(journey.submission.reference))
            );
        }
        default:
            throw new Error(`invalid message source: ${source}`);
    }
};

const submit: SubmitApplicationToBankHandler = async (message, documents, files, payloads, retries) => {
    const { collections } = await getDatabaseContext();

    const { application, bank, company, module, financeProduct } = documents;
    const { provider } = bank.integration;

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });

    const isResubmit = getIsResubmit(message, journey);

    const updateImmutableState = async () => {
        await collections.applicationJourneys.updateOne({ _id: journey._id }, { $set: { isImmutable: false } });
    };

    // use language on company if `languageId` not present on applications
    const { i18n } = await createI18nInstance(
        application.languageId?.toHexString() ?? head(company.languages)?.toHexString()
    );
    await i18n.loadNamespaces('common');
    const { t } = i18n;

    const onSuccess = async (reference?: string) => {
        await updateImmutableState();

        await persistSuccess(application, isResubmit, bank, reference);

        // what to do next for resubmission
        const latestJourney = await collections.applicationJourneys.findOne({
            applicationSuiteId: application._versioning.suiteId,
        });
        // try to send email
        if (
            application.kind !== ApplicationKind.Mobility &&
            (await isAbleToSendSubmissionEmail(application, latestJourney))
        ) {
            const previousApplicationStages = await getPreviousApplicationStages(application._versioning.suiteId);

            await sendApplicationEmails(application, latestJourney, previousApplicationStages, isResubmit);
        }
    };

    const isLastRetry = retries >= 3;
    const onFailure = async (reason: string, terminated?: boolean) => {
        if (terminated || isLastRetry) {
            // Send email notification to salesperson
            mainQueue.add({
                type: 'sendSalespersonSubmissionToBankFailed',
                applicationId: application._id,
                reason,
            });

            await updateImmutableState();
        }

        await persistFailure(application, isResubmit, reason, bank);
    };

    const retry = async () => {
        if (retries < 3) {
            await mainQueue.add(
                {
                    type: 'submitApplicationToBank',
                    ...message,
                    retries: retries + 1,
                },
                { delay: 1000 * 30 * (retries + 1) }
            );
        } else {
            await updateImmutableState();
        }
    };

    const updateJourney = (sets: MatchKeysAndValues<ApplicationJourney>) =>
        collections.applicationJourneys.findOneAndUpdate(
            {
                _id: journey._id,
            },
            { $set: sets }
        );

    switch (provider) {
        case BankIntegrationProvider.ENBD: {
            await collections.applicationJourneys.findOneAndUpdate(
                { _id: journey._id },
                {
                    $set: {
                        'submission.kind': ApplicationJourneySubmissionKind.Enbd,
                        'submission.bankId': bank._id,
                        isImmutable: false,
                    },
                }
            );

            const callbacks: EmailCallbacks = { onSuccess, onFailure, retry };

            return submitApplicationByEnbd(application, company, bank, files.agreementPdf, callbacks);
        }

        case BankIntegrationProvider.Email: {
            await collections.applicationJourneys.findOneAndUpdate(
                { _id: journey._id },
                {
                    $set: {
                        'submission.kind': ApplicationJourneySubmissionKind.Email,
                        'submission.bankId': bank._id,
                        isImmutable: false,
                    },
                }
            );

            const callbacks: EmailCallbacks = { onSuccess, onFailure, retry };

            return submitApplicationByEmail(application, company, bank, files.agreementPdf, callbacks);
        }

        case BankIntegrationProvider.HLF:
        case BankIntegrationProvider.HLFV2: {
            return submitApplicationToHlf(
                bank,
                { onSuccess, onFailure, retry, updateJourney },
                {
                    isResubmit,
                    documents: { module, application, journey, financeProduct },
                    files,
                    payloads,
                }
            );
        }

        case BankIntegrationProvider.UOB: {
            return submitApplicationToUob(
                bank,
                { onSuccess, onFailure, retry, updateJourney },
                { isResubmit, documents: { module, company, application, journey, financeProduct }, payloads }
            );
        }

        case BankIntegrationProvider.DBS: {
            if (
                module._type !== ModuleType.StandardApplicationModule &&
                module._type !== ModuleType.FinderApplicationPrivateModule &&
                module._type !== ModuleType.FinderApplicationPublicModule
            ) {
                throw new Error('Invalid module type');
            }

            const dealerFilter: Filter<Dealer> = {};

            if (application.kind === ApplicationKind.Standard || application.kind === ApplicationKind.Finder) {
                dealerFilter._id = application.dealerId;
            }

            const dealer = isEmpty(dealerFilter) ? null : await collections.dealers.findOne(dealerFilter);
            const customerId = getCustomerId(application);
            const customer = customerId ? await collections.customers.findOne({ _id: customerId }) : null;
            const customerModule = customerId
                ? ((await collections.modules.findOne({ _id: customer.moduleId })) as LocalCustomerManagementModule)
                : null;

            const documents = {
                application,
                journey,
                dealer,
                customer,
                company,
                localVehicle: null,
                assignee: null,
                customerModule,
                bank,
            };

            if (application.kind === ApplicationKind.Standard) {
                const [variant] = await collections.vehicles
                    .aggregate<VehicleAggregate>(standardVehicleAggregatePipelines(application.vehicleId))
                    .toArray();

                if (variant) {
                    documents.localVehicle = {
                        variant,
                        model: variant.model,
                        make: variant.make,
                    };
                }

                documents.assignee = application.financingStage?.assigneeId
                    ? await collections.users.findOne({ _id: application.financingStage.assigneeId })
                    : null;
            }

            if (application.kind === ApplicationKind.Finder) {
                const [variantData] = await collections.vehicles
                    .aggregate<FinderVehicle>(finderVehicleAggregatePipelines(application.vehicleId))
                    .toArray();

                if (variantData) {
                    const variant = await getFinderVehicleLatestListing(variantData);
                    documents.localVehicle = variant;
                }

                documents.assignee = application.financingStage?.assigneeId
                    ? await collections.users.findOne({ _id: application.financingStage.assigneeId })
                    : null;
            }

            return submitApplicationToDbs(
                bank,
                { onSuccess, onFailure, retry, updateJourney },
                { documents, payloads },
                t
            );
        }

        case BankIntegrationProvider.Maybank: {
            return submitApplicationToMaybank(
                bank,
                { onSuccess, onFailure, retry, updateJourney },
                { isResubmit, documents: { module, application, journey, files }, payloads },
                t
            );
        }

        default:
            throw new Error(`bank integration provider not implemented: ${provider}`);
    }
};

/*
this job deals with following integration combinations:
- email ( standard/configurator )
- hlf ( standard/configurator )
- uob ( standard/configurator )
 */
const submitApplicationToBank = async (message: SubmitApplicationToBankMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();
    const { applicationId, retries = 0 } = message;

    const application = await collections.applications.findOne({ _id: applicationId });
    const bankId = getBankIdFromApplication(application);
    const bank = bankId ? await collections.banks.findOne({ _id: bankId }) : null;
    const financeProduct: LocalFinanceProduct | null = null;

    if (!application._versioning.isLatest || !bank) {
        return;
    }

    const module = await collections.modules.findOne({ _id: application.moduleId });

    const company = await collections.companies.findOne({ _id: module.companyId });

    let agreementPdf: ReadableStream | null = null;

    const payloads: {} = {};

    switch (message.source) {
        case SubmitApplicationToBankSource.System: {
            const agreement = getAgreementDocument(application);
            agreementPdf = agreement ? await getFileStream(agreement) : null;

            break;
        }
    }

    await submit(
        message,
        { module, company, application, bank, financeProduct },
        { agreementPdf, nricFront: null, nricBack: null, logCard: null },
        payloads,
        retries
    );
};

export default submitApplicationToBank;
export { submitApplicationByEmail, submitApplicationByHlfIntegration, submitApplicationByUobIntegration };
export { persistSuccess, persistFailure } from './shared';
export type { SubmitApplicationToBankMessage } from './types';
export { SubmitApplicationToBankSource } from './types';
