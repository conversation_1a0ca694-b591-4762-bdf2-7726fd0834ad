import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { get, isArray, isEmpty } from 'lodash/fp';
import { MatchKeysAndValues } from 'mongodb';
import countries from '../../../../app/datasets/countries';
import {
    Application,
    ApplicationJourney,
    ApplicationKind,
    Company,
    Customer,
    DbsBankIntegrationSetting,
    Dealer,
    EngineType,
    LocalCustomerManagementModule,
    LocalMake,
    LocalModel,
    LocalVariant,
    Bank,
    User,
    SystemBank,
    DbsPayloadScheme,
    FinderVehicle,
    StandardApplication,
    FinderApplication,
} from '../../../database/documents';
import { getKYCPresetsForCustomerModule } from '../../../database/helpers';
import {
    getCustomerCountry,
    getBirthday,
    getCitizenship,
    getCustomerPhone,
    getCustomerPassport,
    getCustomerIdentityNumber,
    getCustomerEmail,
    getCustomerFullName,
} from '../../../database/helpers/customers';
import {
    dateFormat,
    DbsFailedApiResponse,
    DbsFailedResponse,
    DbsSubmitBaseRequest,
    getDbsErrorDescription,
    timezone,
} from '../../../integrations/banks/dbs/shared';
import dbsSubmit, { type DbsSubmitRequest, type DbsSubmitResponse } from '../../../integrations/banks/dbs/submit';
import { getBankVariantCode } from './shared';

export type DbsCallbacks = {
    onSuccess?: (reference?: string) => Promise<void>;
    onFailure?: (reason: string, terminated?: boolean) => Promise<void>;
    retry?: () => Promise<void>;
    updateJourney: (sets: MatchKeysAndValues<ApplicationJourney>) => Promise<unknown>;
};

const getDbsEngineType = (engineType: EngineType): DbsSubmitRequest['engineType'] => {
    switch (engineType) {
        case EngineType.Petrol:
        case EngineType.Diesel:
            return 'Petrol/Diesel';

        case EngineType.Hybrid:
            return 'Hybrid';

        case EngineType.Electric:
            return 'Electric';

        default:
            return null;
    }
};

// months -> years & months
// e.g. 40 months -> 3 years & 4 months
const getDbsTenures = (months: number) => {
    const remaining = months % 12;

    return [(months - remaining) / 12, remaining];
};

export type LocalVehicle = {
    variant: LocalVariant;
    model: LocalModel;
    make: LocalMake;
};

export type DbsFinderVehicle = FinderVehicle & {
    relatedVariant: Pick<LocalVariant, 'bankCodeMappings' | 'identifier'>;
};

const getDbsPricingDetails = (interestRate: number, mappings: DbsBankIntegrationSetting['mappings']) => {
    const mapping = mappings.find(({ appliedInterestRate }) => appliedInterestRate === interestRate);

    return {
        appliedInterestRate: interestRate,
        schemeCode: mapping?.schemeCode,
        packageCode: mapping?.packageCode,
    };
};

export type DbsSubmissionDocuments<TApplication = StandardApplication> = {
    application: Application;
    localVehicle: TApplication extends FinderApplication ? DbsFinderVehicle : LocalVehicle | null;
    customer: Customer | null;
    journey: ApplicationJourney;
    dealer: Dealer | null;
    assignee: User | null;
    company: Company;
    customerModule: LocalCustomerManagementModule;
    bank: Bank | null;
};
type PrepareFn<TResult = DbsSubmitRequest, TApplication = StandardApplication> = (
    setting: DbsBankIntegrationSetting,
    documents: DbsSubmissionDocuments<TApplication>,
    // TODO: update once we have integration
    payloads: {},
    t: TFunction
) => TResult;

const prepareCommonDbsRequestForStdAndBmw: PrepareFn<
    Pick<
        DbsSubmitRequest,
        | 'authorisedPartnerCode'
        | 'salesAgreedDate'
        | 'fullName'
        | 'email'
        | 'NRIC_Passport'
        | 'mobileNo'
        | 'loanPurchase'
        | 'dob'
        | 'accessoriesAmount'
    >,
    StandardApplication | FinderApplication
> = (setting, documents, payloads, t) => {
    const { dealer, customer, company, customerModule } = documents;

    const getDob = () => {
        const birthday = getBirthday(customer);

        return birthday?.isValid() ? birthday.tz(timezone).format(dateFormat) : null;
    };

    const getNricPassport = () => {
        const citizenship = getCitizenship(customer);

        if (citizenship?.toLowerCase()?.includes('others')) {
            return getCustomerPassport(customer);
        }

        return getCustomerIdentityNumber(t, customer);
    };

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    return {
        authorisedPartnerCode: dealer?.integrationDetails?.dealerCode,
        salesAgreedDate: dayjs().tz(timezone).format(dateFormat),
        fullName: getCustomerFullName(t, customer, company, kycPresets),
        email: getCustomerEmail(t, customer),
        NRIC_Passport: getNricPassport(),
        mobileNo: getCustomerPhone(t, customer).toString(),
        loanPurchase: null,
        dob: getDob(),
        // use `0` if there's no value, see: https://appvantage.atlassian.net/browse/AN-964
        accessoriesAmount: 0,
    };
};

const getDbsV1NationalityForStd = (customer: Customer): DbsSubmitBaseRequest['nationality'] => {
    const citizenship = getCitizenship(customer);

    switch (citizenship) {
        case 'Singapore Citizen/PR':
            return 'Singapore citizen/PR';

        case 'Malaysian':
            return 'Malaysian';

        case 'Other Nationality':
            return 'Other Nationality';

        default:
            return null;
    }
};

const getDbsV2NationalityForStd = (customer: Customer) => {
    const country = getCustomerCountry(customer);

    if (country) {
        return countries.find(item => item?.name?.common === country)?.dbs;
    }

    return null;
};

const getDbsV2IdTypeForStd = (customer: Customer): DbsSubmitBaseRequest['idType'] => {
    const citizenship = getCitizenship(customer);

    switch (citizenship) {
        case 'Singapore Citizen/PR':
            return 'SingaporeID';

        case 'Malaysian':
            return 'MalaysiaID';

        case 'Other Nationality':
            return 'Passport';

        default:
            return null;
    }
};

const prepareDbsRequestForStd: PrepareFn<DbsSubmitRequest, StandardApplication> = (setting, documents, payloads, t) => {
    const { mappings } = setting;
    const { application, localVehicle, customer, assignee, bank } = documents;

    if (application.kind !== ApplicationKind.Standard || isEmpty(localVehicle)) {
        throw new Error(`DBS integration is not supported for current application kind: ${application.kind}`);
    }

    const { financing } = application;
    const [loanTenureYear, loanTenureMonth] = getDbsTenures(financing.term);

    return {
        ...prepareCommonDbsRequestForStdAndBmw(setting, documents, payloads, t),
        applicationType: 'New',
        vehicleMaker: localVehicle.make.name.defaultValue,
        vehicleModel: getBankVariantCode(localVehicle.variant, bank as SystemBank),
        discountPrice: financing.promoCodeValue ?? 0,
        purchasePrice: financing.totalPrice,
        omv: financing.carPrice,
        offPeakCar: 'No',
        parallelImport: 'No',
        salesConsultant: assignee.mobile.value,
        interestRate: financing.interestRate,
        loanAmount: financing.loan.amount,
        loanTenureMonth,
        loanTenureYear,
        registrationDate: null,
        registrationNo: null,
        ...(setting.secrets.payloadScheme === DbsPayloadScheme.v1 && {
            nationality: getDbsV1NationalityForStd(customer),
        }),
        ...(setting.secrets.payloadScheme === DbsPayloadScheme.v2 && {
            idType: getDbsV2IdTypeForStd(customer),
            nationality: getDbsV2NationalityForStd(customer),
        }),
        engineType: getDbsEngineType(localVehicle.variant.engineType),
        pricingDetl: getDbsPricingDetails(financing.interestRate, mappings),
    };
};

const prepareDbsRequestForFinder: PrepareFn<DbsSubmitRequest, FinderApplication> = (
    setting,
    documents,
    payloads,
    t
) => {
    const { mappings } = setting;
    const { application, localVehicle, customer, assignee, bank } = documents;

    if (application.kind !== ApplicationKind.Finder || isEmpty(localVehicle)) {
        throw new Error(`DBS integration is not supported for current application kind: ${application.kind}`);
    }

    const { financing } = application;
    const [loanTenureYear, loanTenureMonth] = getDbsTenures(financing.term);
    const isPreowned = localVehicle?.listing?.vehicle?.condition?.value?.toLowerCase() !== 'new';
    const engineType = localVehicle?.listing?.vehicle?.engineType?.value?.toLowerCase() as EngineType;

    const common: Omit<DbsSubmitRequest, 'applicationType' | 'registrationDate' | 'registrationNo'> = {
        ...prepareCommonDbsRequestForStdAndBmw(setting, documents, payloads, t),
        vehicleMaker: 'Porsche',
        vehicleModel: getBankVariantCode(localVehicle.relatedVariant, bank as SystemBank),
        discountPrice: financing.promoCodeValue ?? 0,
        purchasePrice: financing.totalPrice,
        omv: financing.carPrice,
        offPeakCar: 'No',
        parallelImport: 'No',
        salesConsultant: assignee.mobile.value,
        interestRate: financing.interestRate,
        loanAmount: financing.loan.amount,
        loanTenureMonth,
        loanTenureYear,
        ...(setting.secrets.payloadScheme === DbsPayloadScheme.v1 && {
            nationality: getDbsV1NationalityForStd(customer),
        }),
        ...(setting.secrets.payloadScheme === DbsPayloadScheme.v2 && {
            idType: getDbsV2IdTypeForStd(customer),
            nationality: getDbsV2NationalityForStd(customer),
        }),
        engineType: getDbsEngineType(engineType),
        pricingDetl: getDbsPricingDetails(financing.interestRate, mappings),
    };

    if (!isPreowned) {
        return {
            ...common,
            applicationType: 'New',
            registrationDate: null,
            registrationNo: null,
        };
    }

    const { lta, listing } = localVehicle;

    return {
        ...common,
        applicationType: 'Used',
        chassisNo: listing.vehicle?.vin,
        manufacturingYear: listing?.vehicle?.modelYear ? listing?.vehicle?.modelYear.toString() : null,
        ...(!isEmpty(lta) && {
            COEValue: lta?.coe,
            engineNo: lta?.engineNumber,
            registrationDate: dayjs(lta?.originalRegistrationDate).tz(timezone).format(dateFormat),
            registrationNo: lta?.registrationNumber,
        }),
    };
};

export const prepareDbsRequestByPayloads: PrepareFn<DbsSubmitRequest, StandardApplication | FinderApplication> = (
    setting,
    documents,
    payloads,
    t
) => {
    switch (documents.application?.kind) {
        case ApplicationKind.Standard:
            return prepareDbsRequestForStd(setting, documents, payloads, t);

        case ApplicationKind.Finder:
            return prepareDbsRequestForFinder(setting, documents, payloads, t);

        default:
            throw new Error(
                `DBS integration is not supported for current application kind: ${documents.application?.kind}`
            );
    }
};

export const submitApplicationByDbsIntegration = (
    setting: DbsBankIntegrationSetting,
    request: DbsSubmitRequest,
    callbacks: DbsCallbacks
) => handleResponse(dbsSubmit(setting, request), callbacks);

const handleResponse = async (promise: Promise<DbsSubmitResponse>, callbacks: DbsCallbacks) => {
    try {
        const response = await promise;

        const sets: MatchKeysAndValues<ApplicationJourney> = {
            updatedAt: new Date(),
        };

        const referenceNo = get('data[0].referenceNo', response);
        const errorList = get('data[0].errorList', response) as DbsFailedResponse['data'][number]['errorList'];
        const error = get('error', response) as DbsFailedApiResponse['error'];

        if (referenceNo) {
            sets['submission.reference'] = referenceNo;

            if (callbacks.onSuccess) {
                await callbacks.onSuccess(referenceNo);
            }
        } else if (!isEmpty(errorList)) {
            let code: string | undefined;
            let message: string | undefined;
            let moreInfo: string | undefined;

            if (isArray(errorList)) {
                code = errorList.map(error => error.code).join(', ');
                message = errorList.map(error => error.message).join(', ');
                moreInfo = errorList.map(error => error.moreInfo).join(', ');
            } else {
                code = errorList.code;
                message = errorList.message;
                moreInfo = errorList.moreInfo;
            }

            sets['submission.error'] = {
                code,
                message,
                moreInfo,
            };

            if (callbacks.onFailure) {
                await callbacks.onFailure(
                    isArray(errorList) ? message : getDbsErrorDescription(errorList.code, errorList.message),
                    true
                );
            }
        } else if (!isEmpty(error)) {
            sets['submission.error'] = {
                code: error.code,
                message: error.description,
                moreInfo: error.status,
            };

            if (callbacks.onFailure) {
                await callbacks.onFailure(getDbsErrorDescription(error.code, error.description), true);
            }
        }

        // update journey
        await callbacks.updateJourney(sets);
    } catch (error) {
        if (callbacks.onFailure) {
            await callbacks.onFailure('unable to submit to dbs');
        }

        if (callbacks.retry) {
            await callbacks.retry();
        }

        throw error;
    }
};
