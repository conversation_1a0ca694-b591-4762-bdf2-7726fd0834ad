import { Readable } from 'stream';
import { nanoid } from 'nanoid';
import { getFileStream } from '../../../core/storage';
import {
    Application,
    ApplicationDocumentKind,
    ApplicationKind,
    Bank,
    BankIntegrationProvider,
    BankKind,
    Company,
    getKYCPresetsForCustomerModule,
    LocalVariant,
    ModuleType,
    PasswordConfiguration,
} from '../../../database';
import { getCustomerFullNameWithTitle } from '../../../database/helpers/customers';
import { createCompanySMTPTransports, sendEmailToBank, sendPasswordForAgreementToBank } from '../../../emails';
import createLoaders from '../../../loaders';
import createI18nInstance from '../../../utils/createI18nInstance';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getEncryptedZip from '../../../utils/getEncryptedZip';
import getApplicationDetailsLink from '../shared/getApplicationDetailsLink';
import { getApplicationPDFFilename } from './shared';
import { EmailCallbacks } from './submitApplicationByEmail';

const getApplicationAttachmentSendToBank = (application: Application) => {
    if (
        application.kind !== ApplicationKind.Standard &&
        application.kind !== ApplicationKind.Configurator &&
        application.kind !== ApplicationKind.Finder
    ) {
        return [];
    }

    return application.documents.filter(item =>
        [
            ApplicationDocumentKind.CorporateIdentity,
            ApplicationDocumentKind.CustomerIdentity,
            ApplicationDocumentKind.GuarantorIdentity,
            ApplicationDocumentKind.VSOUpload,
        ].includes(item.kind)
    );
};

export type SubmitApplicationByEnbdFn = (
    application: Application,
    company: Company,
    bank: Bank,
    agreementPdf: Readable,
    callbacks: EmailCallbacks
) => Promise<void>;

const submitLegalApplicationByEnbd = async (
    application: Extract<
        Application,
        {
            kind:
                | ApplicationKind.Standard
                | ApplicationKind.Finder
                | ApplicationKind.Configurator
                | ApplicationKind.Event;
        }
    >,
    company: Company,
    bank: Bank,
    agreementPdf: Readable,
    callbacks: EmailCallbacks
) => {
    if (bank.integration.provider !== BankIntegrationProvider.ENBD) {
        throw new Error('Bank integration is not ENBD');
    }

    if (bank.kind !== BankKind.System) {
        throw new Error('Bank is not a system bank. Email submission for ENBD will use the system bank only');
    }

    const transporter = await createCompanySMTPTransports(company);
    const emailContext = await getCompanyEmailContext(company);

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails', 'applicationPdf']);
    const { t } = i18n;

    const loaders = createLoaders();

    const applicationModule = await loaders.moduleById.load(application.moduleId);

    const [variant, customer, assignee, financeProduct, lead] = await Promise.all([
        loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>,
        loaders.customerById.load(application.applicantId),
        application.financingStage?.assigneeId
            ? loaders.userById.load(application.financingStage.assigneeId)
            : Promise.resolve(null),
        loaders.financeProductById.load(application.financing.financeProductId),
        loaders.leadById.load(application.leadId),
    ]);

    const customerModule = await loaders.moduleById.load(customer.moduleId);
    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('module not found ');
    }
    const randomPassword = nanoid();

    // collect attachments
    const attachments = await Promise.all(
        getApplicationAttachmentSendToBank(application).map(async doc => {
            const source = await getFileStream(doc);

            return { source, filename: doc.filename };
        })
    );

    // create encrypted zip
    const files = [
        agreementPdf
            ? { source: agreementPdf, filename: getApplicationPDFFilename(application.financingStage.identifier) }
            : null,
        ...attachments,
    ].filter(Boolean);

    const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

    const encryptedZip =
        files.length > 0 ? await getEncryptedZip(files, isPasswordProtected ? randomPassword : undefined) : null;
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);

    const hint = isPasswordProtected ? t('emails:bankSubmission.hint') : '';

    const link = await getApplicationDetailsLink(application, lead);

    // send the email
    try {
        await sendEmailToBank(
            {
                i18n,
                data: {
                    emailContext,
                    bank,
                    application,
                    variant,
                    financeProduct,
                    assignee,
                    hint,
                    link,
                    module: applicationModule,
                    dealerId: application.dealerId,
                },
                subject: t('emails:bankSubmission.subject', {
                    companyName: emailContext.companyName,
                    identifier: application.financingStage?.identifier,
                    customerName: customerFullName,
                }),
                to: { name: bank.displayName, address: bank.email },
                attachments: [
                    encryptedZip && {
                        filename: 'Agreement.zip',
                        contentType: 'application/zip',
                        content: encryptedZip,
                    },
                ].filter(Boolean),
            },
            transporter,
            emailContext.sender
        );

        if (isPasswordProtected) {
            await sendPasswordForAgreementToBank(
                {
                    i18n,
                    subject: t('emails:bankSubmission.subject', {
                        companyName: emailContext.companyName,
                        identifier: application.financingStage?.identifier,
                        customerName: customerFullName,
                    }),
                    data: { emailContext, application, randomPassword },
                    to: { name: bank.displayName, address: bank.email },
                },
                transporter,
                emailContext.sender
            );
        }

        if (callbacks.onSuccess) {
            await callbacks.onSuccess();
        }
    } catch (error) {
        if (callbacks.onFailure) {
            await callbacks.onFailure('There was an error while sending the email to the bank', true);
        }

        throw error;
    }
};

const submitApplicationByEnbd: SubmitApplicationByEnbdFn = async (
    application,
    company,
    bank,
    agreementPdf,
    callbacks
) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Finder:
        case ApplicationKind.Configurator: {
            await submitLegalApplicationByEnbd(application, company, bank, agreementPdf, callbacks);

            break;
        }

        // Do nothing on event application
        // Since only applied for financing
        case ApplicationKind.Event:
            break;

        default:
            throw new Error(`Email bank submission not implemented for application kind: ${application.kind}`);
    }
};

export default submitApplicationByEnbd;
