import { Readable } from 'stream';
import { nanoid } from 'nanoid';
import { getFileStream } from '../../../core/storage';
import {
    Application,
    ApplicationDocumentKind,
    ApplicationKind,
    Bank,
    BankIntegrationProvider,
    Company,
    getKYCPresetsForCustomerModule,
    LocalVariant,
    ModuleType,
    PasswordConfiguration,
} from '../../../database';
import { getCustomerFullNameWithTitle } from '../../../database/helpers/customers';
import { createCompanySMTPTransports, sendEmailToBank, sendPasswordForAgreementToBank } from '../../../emails';
import { hasFinancingScenario } from '../../../journeys/common/helpers';
import createLoaders from '../../../loaders';
import createI18nInstance from '../../../utils/createI18nInstance';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getEncryptedZip from '../../../utils/getEncryptedZip';
import getApplicationDetailsLink from '../shared/getApplicationDetailsLink';
import { getApplicationPDFFilename } from './shared';

export type EmailCallbacks = {
    onSuccess?: () => Promise<void>;
    onFailure?: (reason: string, terminated?: boolean) => Promise<void>;
    retry?: () => Promise<void>;
};

const getApplicationAttachmentSendToBank = (application: Application) => {
    if (
        application.kind !== ApplicationKind.Standard &&
        application.kind !== ApplicationKind.Configurator &&
        application.kind !== ApplicationKind.Finder
    ) {
        return [];
    }

    return application.documents.filter(item =>
        [
            ApplicationDocumentKind.CorporateIdentity,
            ApplicationDocumentKind.CustomerIdentity,
            ApplicationDocumentKind.GuarantorIdentity,
            ApplicationDocumentKind.VSOUpload,
        ].includes(item.kind)
    );
};

export type SubmitApplicationByEmailFn = (
    application: Application,
    company: Company,
    bank: Bank,
    agreementPdf: Readable,
    callbacks: EmailCallbacks
) => Promise<void>;

const submitLegalApplicationByEmail = async (
    application: Extract<
        Application,
        {
            kind:
                | ApplicationKind.Standard
                | ApplicationKind.Finder
                | ApplicationKind.Configurator
                | ApplicationKind.Event;
        }
    >,
    company: Company,
    bank: Bank,
    agreementPdf: Readable,
    callbacks: EmailCallbacks
) => {
    if (bank.integration.provider !== BankIntegrationProvider.Email) {
        throw new Error('Bank integration is not email');
    }

    const transporter = await createCompanySMTPTransports(company);
    const emailContext = await getCompanyEmailContext(company);

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails', 'applicationPdf', 'calculators']);
    const { t } = i18n;

    const loaders = createLoaders();

    const applicationModule = await loaders.moduleById.load(application.moduleId);

    const [variant, customer, assignee, financeProduct, promoCode, lead] = await Promise.all([
        loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>,
        loaders.customerById.load(application.applicantId),
        application.financingStage?.assigneeId
            ? loaders.userById.load(application.financingStage.assigneeId)
            : Promise.resolve(null),
        loaders.financeProductById.load(application.financing.financeProductId),
        application.promoCodeId ? loaders.promoCodeById.load(application.promoCodeId) : Promise.resolve(null),
        loaders.leadById.load(application.leadId),
    ]);

    const customerModule = await loaders.moduleById.load(customer.moduleId);
    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('module not found ');
    }
    const randomPassword = nanoid();

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    // collect attachments
    const attachments = await Promise.all(
        getApplicationAttachmentSendToBank(application).map(async doc => {
            const source = await getFileStream(doc);

            return { source, filename: doc.filename };
        })
    );

    // create encrypted zip
    const files = [
        agreementPdf
            ? { source: agreementPdf, filename: getApplicationPDFFilename(application.financingStage.identifier) }
            : null,
        ...attachments,
    ].filter(Boolean);

    const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

    const encryptedZip =
        files.length > 0 ? await getEncryptedZip(files, isPasswordProtected ? randomPassword : undefined) : null;
    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);

    const hint = isPasswordProtected ? t('emails:bankSubmission.hint') : '';

    const link = await getApplicationDetailsLink(application, lead);

    if (!hasFinancingScenario(application.scenarios)) {
        return;
    }
    // send the email
    try {
        await sendEmailToBank(
            {
                i18n,
                data: {
                    emailContext,
                    bank,
                    application,
                    variant,
                    financeProduct,
                    assignee,
                    promoCode,
                    hint,
                    link,
                    module: applicationModule,
                    dealerId: application.dealerId,
                },
                subject: t('emails:bankSubmission.subject', {
                    companyName: emailContext.companyName,
                    identifier: application.financingStage?.identifier,
                    customerName: customerFullName,
                }),
                to: { name: bank.displayName, address: bank.integration.email },
                attachments: [
                    encryptedZip && {
                        filename: 'Agreement.zip',
                        contentType: 'application/zip',
                        content: encryptedZip,
                    },
                ].filter(Boolean),
            },
            transporter,
            emailContext.sender
        );

        if (isPasswordProtected) {
            await sendPasswordForAgreementToBank(
                {
                    i18n,
                    subject: t('emails:bankSubmission.subject', {
                        companyName: emailContext.companyName,
                        identifier: application.financingStage?.identifier,
                        customerName: customerFullName,
                    }),
                    data: { emailContext, application, randomPassword },
                    to: { name: bank.displayName, address: bank.integration.email },
                },
                transporter,
                emailContext.sender
            );
        }

        if (callbacks.onSuccess) {
            await callbacks.onSuccess();
        }
    } catch (error) {
        if (callbacks.onFailure) {
            await callbacks.onFailure('There was an error while sending the email to the bank', true);
        }

        throw error;
    }
};

const submitApplicationByEmail: SubmitApplicationByEmailFn = async (
    application,
    company,
    bank,
    agreementPdf,
    callbacks
) => {
    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Finder:
        case ApplicationKind.Event:
        case ApplicationKind.Configurator: {
            await submitLegalApplicationByEmail(application, company, bank, agreementPdf, callbacks);

            break;
        }

        default:
            throw new Error(`Email bank submission not implemented for application kind: ${application.kind}`);
    }
};

export default submitApplicationByEmail;
