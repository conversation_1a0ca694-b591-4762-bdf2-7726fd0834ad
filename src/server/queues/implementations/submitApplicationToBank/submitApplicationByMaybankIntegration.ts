import { Readable } from 'stream';
import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { MatchKeysAndValues } from 'mongodb';
import { getKYCPresetsForCustomerModule } from '../../../database';
import {
    Application,
    ApplicationFinancing,
    ApplicationJourney,
    LocalMake,
    LocalModel,
    LocalVariant,
    MaybankIntegrationSetting,
    ModuleType,
    VehicleKind,
} from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getCustomerFullName } from '../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import maybankSubmit, {
    type MaybankSubmitRequest,
    type MaybankSubmitResponse,
} from '../../../integrations/banks/maybank/submit';
import { streamToBuffer } from '../../../utils';

const MAYBANK_DATE_FORMAT = 'DDMMYYYY';

export const prepareMaybankRequest = async (
    documents: { application: Application; setting: MaybankIntegrationSetting },
    fields: { AmtIndicator: 'Y' | 'N'; DOSalAgmt?: Date },
    agreementPdf: Readable | null,
    t: TFunction
): Promise<MaybankSubmitRequest> => {
    const { application, setting } = documents;

    const { collections } = await getDatabaseContext();
    const applicant =
        'applicantId' in application && application.applicantId
            ? await collections.customers.findOne({ _id: application.applicantId })
            : null;
    const dealer =
        'dealerId' in application && application.dealerId
            ? await collections.dealers.findOne({ _id: application.dealerId })
            : null;
    const variant =
        'vehicleId' in application && application.vehicleId
            ? ((await collections.vehicles.findOne({
                  _id: application.vehicleId,
                  _kind: VehicleKind.LocalVariant,
              })) as LocalVariant)
            : null;
    const model =
        variant?._kind === VehicleKind.LocalVariant
            ? ((await collections.vehicles.findOne({
                  _id: variant.modelId,
                  _kind: VehicleKind.LocalModel,
              })) as LocalModel)
            : null;
    const make =
        model?._kind === VehicleKind.LocalModel
            ? ((await collections.vehicles.findOne({
                  _id: model.makeId,
                  _kind: VehicleKind.LocalMake,
              })) as LocalMake)
            : null;
    const assignee = application.financingStage?.assigneeId
        ? await collections.users.findOne({ _id: application.financingStage.assigneeId })
        : null;
    const finacing = 'financing' in application ? (application.financing as ApplicationFinancing) : null;
    const company = await collections.companies.findOne({ _id: dealer.companyId });
    const customer = applicant ? getLocalCustomerAggregatedFields(applicant) : null;

    const customerModule = await collections.modules.findOne({ _id: applicant.moduleId });
    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('module not found ');
    }
    const { AmtIndicator } = fields;

    const kycPresets = applicant ? getKYCPresetsForCustomerModule(customerModule, applicant._kind) : null;

    return {
        msg: {
            applicationData: {
                DealerID: setting?.secrets?.dealerId,
                SalesAgreementNo: application.financingStage?.identifier,
                IDNumber: customer?.identityNumber,
                CustName: applicant ? getCustomerFullName(t, applicant, company, kycPresets) : null,
                CustHP: customer?.phone?.value,
                VehicleMake: make?.identifier,
                VehicleModel: model?.identifier,
                DOSalAgmt: dayjs(fields.DOSalAgmt).format(MAYBANK_DATE_FORMAT),
                VehicleType: 'New Car',
                OMVG20K: finacing?.totalPrice > 20_000 ? 'Y' : 'N',
                FinAmt: finacing?.loan?.amount,
                FinPeriod: finacing?.term,
                IntRates: finacing?.interestRate,
                DealerUsrID: dealer?.integrationDetails?.dealerCode,
                DealerEmpNo: assignee?.alias,
                AmtIndicator,
                CustEmail: customer?.email,
                PurchasePrice: finacing?.totalPrice,
            },
            ...(agreementPdf && {
                documentData: {
                    DocData: (await streamToBuffer(agreementPdf)).toString('base64'),
                },
            }),
        },
    };
};

export type MaybankCallbacks = {
    onSuccess?: (reference?: string) => Promise<void>;
    onFailure?: (reason: string, terminated?: boolean) => Promise<void>;
    retry?: () => Promise<void>;
    updateJourney: (sets: MatchKeysAndValues<ApplicationJourney>) => Promise<unknown>;
};

const handleResponse = async (promise: Promise<MaybankSubmitResponse>, callbacks: MaybankCallbacks) => {
    try {
        const response = await promise;

        const sets: MatchKeysAndValues<ApplicationJourney> = {
            updatedAt: new Date(),
        };

        if (response?.msg?.status?.StatusCode === 'S') {
            const reference = response.msg.applicationData.LOSRefNumber;
            sets['submission.reference'] = reference;

            if (callbacks.onSuccess) {
                await callbacks.onSuccess(reference);
            }
        } else if (callbacks.onFailure) {
            await callbacks.onFailure(response?.msg?.status?.ErrorDesc ?? 'unknown error', true);
        }

        await callbacks.updateJourney(sets);
    } catch (error) {
        if (callbacks.onFailure) {
            await callbacks.onFailure('unable to submit to maybank');
        }

        if (callbacks.retry) {
            await callbacks.retry();
        }

        throw error;
    }
};

export const submitApplicationByMaybankIntegration = (
    setting: MaybankIntegrationSetting,
    request: MaybankSubmitRequest,
    callbacks: MaybankCallbacks
) => handleResponse(maybankSubmit(setting, request), callbacks);
