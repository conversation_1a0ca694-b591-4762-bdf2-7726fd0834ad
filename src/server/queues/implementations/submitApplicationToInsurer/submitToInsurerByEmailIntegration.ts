import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import {
    ApplicationJourneyInsuranceSubmissionKind,
    ApplicationKind,
    AuditTrailKind,
    getKYCPresetsForCustomerModule,
    InsurerIntegrationProvider,
    ModuleType,
    PasswordConfiguration,
} from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getCustomerFullNameWithTitle } from '../../../database/helpers/customers';
import {
    AudienceMessage,
    createCompanySMTPTransports,
    sendEmailToInsurer,
    sendPasswordForInsuranceAgreementToInsurer,
} from '../../../emails';
import createLoaders from '../../../loaders';
import { getApplicationLogStages } from '../../../utils/application';
import createI18nInstance from '../../../utils/createI18nInstance';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getEncryptedZip from '../../../utils/getEncryptedZip';
import { SubmitToInsurerProps } from './types';

const submitToInsurerByEmailIntegration = async ({
    application,
    insurer,
    company,
    insuranceAgreementPdf,
    callback,
}: SubmitToInsurerProps) => {
    if (insurer.integration.provider !== InsurerIntegrationProvider.Email) {
        throw new Error('Insurer integration is not email');
    }

    // Ensure application is one of the supported types
    if (
        application.kind !== ApplicationKind.Standard &&
        application.kind !== ApplicationKind.Configurator &&
        application.kind !== ApplicationKind.Finder
    ) {
        throw new Error(`Email insurance integration is not supported for ${application.kind}`);
    }

    const { collections } = await getDatabaseContext();

    // Before we send an email, add journey submission
    await collections.applicationJourneys.findOneAndUpdate(
        { applicationSuiteId: application._versioning.suiteId },
        {
            $set: {
                'insuranceSubmission.kind': ApplicationJourneyInsuranceSubmissionKind.Email,
                'insuranceSubmission.insurerId': insurer._id,
                isImmutable: false,
            },
        }
    );

    const transporter = await createCompanySMTPTransports(company);
    const emailContext = await getCompanyEmailContext(company);

    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails', 'applicationPdf']);
    const { t } = i18n;

    const loaders = createLoaders();

    const [customer, assignee, variant, insuranceProduct] = await Promise.all([
        loaders.customerById.load(application.applicantId),
        application.insuranceStage?.assigneeId ? loaders.userById.load(application.insuranceStage.assigneeId) : null,
        loaders.vehicleById.load(application.vehicleId),
        application.insurancing?.insuranceProductId
            ? loaders.insuranceProductById.load(application.insurancing.insuranceProductId)
            : null,
    ]);

    const customerModule = await loaders.moduleById.load(customer.moduleId);
    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('module not found');
    }
    const randomPassword = nanoid();

    const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

    const encryptedZip = await getEncryptedZip(
        [{ source: insuranceAgreementPdf, filename: 'InsuranceAgreement.pdf' }],
        isPasswordProtected ? randomPassword : undefined
    );

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);
    const hint = isPasswordProtected ? t('emails:insurerSubmission.hint') : '';

    try {
        // Send email to insurer with the insurance agreement pdf
        await sendEmailToInsurer(
            {
                i18n,
                data: {
                    emailContext,
                    insurer,
                    application,
                    assignee,
                    variant,
                    customer,
                    customerModule,
                    insuranceProduct,
                    hint,
                },
                subject: t('emails:insurerSubmission.subject', {
                    companyName: emailContext.companyName,
                    identifier: application.insuranceStage?.identifier,
                    customerName: customerFullName,
                }),
                to: {
                    name: insurer.displayName,
                    address: insurer.integration.email,
                },
                attachments: [
                    {
                        filename: 'InsuranceAgreement.zip',
                        contentType: 'application/zip',
                        content: encryptedZip,
                    },
                ],
            },
            transporter,
            emailContext.sender
        );

        if (isPasswordProtected) {
            // Send password for the agreement as well
            await sendPasswordForInsuranceAgreementToInsurer(
                {
                    i18n,
                    subject: t('emails:insurerSubmission.subject', {
                        companyName: emailContext.companyName,
                        identifier: application.insuranceStage?.identifier,
                        customerName: customerFullName,
                    }),
                    data: { emailContext, application, randomPassword },
                    to: { name: insurer.displayName, address: insurer.integration.email },
                },
                transporter,
                emailContext.sender
            );
        }

        // Add audit trail to indicate that email to insurer has been sent
        // Since callback in on success is for updating audit log submit for insurance or else
        // outside of email
        await collections.auditTrails.insertOne({
            _id: new ObjectId(),
            _kind: AuditTrailKind.ApplicationEmailSent,
            _date: new Date(),
            applicationId: application._id,
            applicationSuiteId: application._versioning.suiteId,
            stages: getApplicationLogStages(
                application,
                AuditTrailKind.ApplicationEmailSent,
                undefined,
                AudienceMessage.Insurer
            ),
            audience: AudienceMessage.Insurer,
        });

        if (callback?.onSuccess) {
            await callback.onSuccess();
        }
    } catch (error) {
        if (callback?.onFailure) {
            await callback.onFailure(`There was an error while sending the email to the insurer`, true);
        }

        throw error;
    }
};

export default submitToInsurerByEmailIntegration;
