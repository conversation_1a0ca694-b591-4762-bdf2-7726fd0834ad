import { Document } from 'bson';
import { Job } from 'bull';
import { i18n } from 'i18next';
import { get, last, xor } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../core/storage';
import {
    ApplicationKind,
    Company,
    Customer,
    Dealer,
    FinderApplicationModule,
    FinderApplicationModuleReminderEmailContents,
    FinderApplicationPrivateModule,
    FinderApplicationPublicModule,
    FinderReservationReminder,
    FinderVehicle,
    LocalCustomerManagementModule,
    ModuleType,
    PasswordConfiguration,
    ReminderEmailSent,
    getKYCPresetsForCustomerModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    getCustomerEmail,
    getCustomerFullNameWithTitle,
    getCustomerName,
    getCustomerTitleOrSalutation,
} from '../../database/helpers/customers';
import { getPreviousApplicationStages } from '../../database/queries/application';
import { AudienceMessage, createCompanySMTPTransports, sendFinderEmail, sendPasswordToCustomer } from '../../emails';
import getTranslatedEmailContent from '../../emails/utils/getTranslatedEmailContent';
import createLoaders from '../../loaders';
import { AuditTrailKind, FinderApplicationModuleAsset } from '../../schema/resolvers/enums';
import { DEFAULT_STAGES, getApplicationStage } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';
import getPlaceholderValues from '../../utils/getPlaceholderValues';
import { populateParamsToAllCases } from '../../utils/replaceCurlyBracketString';
import {
    canSendEmailToCustomer,
    canSendPDFToCustomer,
    prepareApplicationSubmissionAttachment,
    prepareAttachmentsForEmail,
} from './sendApplicationSubmissionMail/shared';
import { getApplicationDataFromApplication } from './sendProceedWithCustomerEmail';
import { getFinderVehicleLatestListing } from './shared';
import checkIsBankViewable from './shared/checkIsBankViewable';
import getEmailContentForApplicationStages from './shared/getEmailContentForApplicationStages';

const finderEmailContents = async (
    i18n: i18n,
    moduleEmailContent: FinderApplicationModuleReminderEmailContents,
    reminder: FinderReservationReminder,
    variant: FinderVehicle,
    customer: Customer,
    company: Company,
    dealer: Dealer,
    customerModule: LocalCustomerManagementModule
) => {
    const { t } = i18n;
    const introImageUrl = moduleEmailContent?.introImage
        ? await getUrlForUpload(moduleEmailContent.introImage)
        : last(variant.listing.vehicle.images.edges[0].node.variants).url;

    const dealerId = dealer._id.toHexString();

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerName(t, customer, company, kycPresets);
    const customerTitle = getCustomerTitleOrSalutation(t, customer);

    const { finderReservationValues } = getPlaceholderValues();

    const finderValues = finderReservationValues(t, reminder);

    const translationParameters = {
        title: customerTitle || '',
        customer_fullname: customerName,
        customerName,
        carCondition: variant.listing.vehicle.condition.localize,
        ...finderValues,
    };

    const populatedTranslationParameters = populateParamsToAllCases(translationParameters);

    const emailSubject = moduleEmailContent?.subject
        ? getTranslatedEmailContent({
              dealerTranslationText: moduleEmailContent.subject,
              preferredDealerId: dealerId,
              i18nLanguage: i18n.language,
              params: translationParameters,
          })
        : t('emails:finderEmail.porscheFinder.subject', populatedTranslationParameters);

    const introTitle = (
        moduleEmailContent?.introTitle
            ? getTranslatedEmailContent({
                  dealerTranslationText: moduleEmailContent.introTitle,
                  preferredDealerId: dealerId,
                  i18nLanguage: i18n.language,
                  params: translationParameters,
              })
            : t('emails:finderEmail.porscheFinder.intro', populatedTranslationParameters)
    )?.replaceAll('\n', '<br />');

    const contentText = (
        moduleEmailContent?.contentText
            ? getTranslatedEmailContent({
                  dealerTranslationText: moduleEmailContent.contentText,
                  preferredDealerId: dealerId,
                  i18nLanguage: i18n.language,
                  params: translationParameters,
              })
            : t('emails:finderEmail.porscheFinder.content', populatedTranslationParameters)
    )?.replaceAll('\n', '<br />');

    return {
        introImageUrl,
        emailSubject,
        introTitle,
        contentText,
    };
};

export type SendFinderReminderHandlerMessage = {
    reminder: FinderReservationReminder;
};

export const sendFinderReminderHandler = async ({ reminder }: SendFinderReminderHandlerMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();

    const { applicationSuiteId } = reminder;

    const application = await collections.applications.findOne({
        kind: ApplicationKind.Finder,
        '_versioning.suiteId': applicationSuiteId,
        '_versioning.isLatest': true,
    });
    if (!application || application.kind !== ApplicationKind.Finder) {
        throw new Error('application not found');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    const applicationModule = (await collections.modules.findOne({
        _id: application.moduleId,
    })) as FinderApplicationPublicModule | FinderApplicationPrivateModule;
    if (
        applicationModule?._type !== ModuleType.FinderApplicationPublicModule &&
        applicationModule?._type !== ModuleType.FinderApplicationPrivateModule
    ) {
        throw new Error('Module type is not supported');
    }

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    if (!journey) {
        throw new Error('Application Journey not found');
    }

    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['emails', 'calculators', 'common', 'variant']);
    const { t } = i18n;

    const [vehicleId, customerId] = getApplicationDataFromApplication(application);

    const loaders = createLoaders();

    const [variantData, applicant, bank, company, dealer, financeProduct, insurer, promoCode, insuranceProduct] =
        await Promise.all([
            loaders.vehicleById.load(vehicleId) as Promise<FinderVehicle>,
            loaders.customerById.load(customerId),
            application.bankId ? loaders.bankById.load(application.bankId) : Promise.resolve(null),
            loaders.companyById.load(applicationModule.companyId),
            loaders.dealerById.load(application.dealerId),
            application.financing?.financeProductId
                ? loaders.financeProductById.load(application.financing.financeProductId)
                : Promise.resolve(null),
            application.insurancing?.insurerId
                ? loaders.insurerById.load(application.insurancing.insurerId)
                : Promise.resolve(null),
            application.promoCodeId ? loaders.promoCodeById.load(application.promoCodeId) : Promise.resolve(null),
            application.insurancing?.insuranceProductId
                ? loaders.insuranceProductById.load(application.insurancing.insuranceProductId)
                : Promise.resolve(null),
        ]);

    const variant = await getFinderVehicleLatestListing(variantData);

    const previousApplicationStages = await getPreviousApplicationStages(application._versioning.suiteId);

    const customerModule = (await loaders.moduleById.load(applicant.moduleId)) as LocalCustomerManagementModule;

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const customerEmail = getCustomerEmail(t, applicant);
    const dealerEmailContext = await getDealerEmailContext(dealer);

    const transporter = await createCompanySMTPTransports(company);

    const [emailContext, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getEdmEmailFooterContext({ company }),
    ]);

    const currentStages = xor(previousApplicationStages, application.stages);

    const stage = getApplicationStage(application, lead, DEFAULT_STAGES);

    const submissionType = t(`emails:component.applicationStage.${stage?.stage}`);

    const emailContents = getEmailContentForApplicationStages(
        applicationModule.emailContents.reminderEmail,
        currentStages
    );

    const { introImageUrl, emailSubject, introTitle, contentText } = await finderEmailContents(
        i18n,
        emailContents,
        reminder,
        variant,
        applicant,
        company,
        dealer,
        customerModule
    );

    const isResubmit = false;
    if (!canSendEmailToCustomer(application, bank, insurer, journey, isResubmit, applicant, t)) {
        return;
    }

    const [canSendApplicationPDF, canSendInsurancePDF] = canSendPDFToCustomer({
        application,
        journey,
        isResubmit,
        insurer,
        bank,
    });

    const attachmentInfo = await prepareApplicationSubmissionAttachment(
        application,
        applicant,
        company,
        i18n,
        canSendApplicationPDF,
        canSendInsurancePDF
    );

    const attachments = prepareAttachmentsForEmail(application, attachmentInfo, emailContext, stage?.value?.identifier);
    const isBankViewable = await checkIsBankViewable(variant._versioning.suiteId, application.moduleId);

    await sendFinderEmail(
        {
            i18n,
            data: {
                emailContext,
                variant,
                dealer,
                edmEmailFooterContext,
                introImageUrl,
                introTitle,
                contentText,
                dealerEmailContext,
                application,
                applicationModule,
                financeProduct,
                insuranceProduct,
                journey,
                applicant,
                submissionType,
                audience: AudienceMessage.Customer,
                bank,
                insurer,
                promoCode,
                applicationId: stage?.value?.identifier,
                isBankViewable,
                customerModule,
                currentStages,
            },
            to: { name: customerFullName, address: customerEmail },
            subject: emailSubject,
            ...(!!attachments.length && {
                attachments,
            }),
        },
        transporter,
        emailContext.sender
    );

    const auditTrail: ReminderEmailSent = {
        _id: new ObjectId(),
        _date: new Date(),
        _kind: AuditTrailKind.ReminderEmailSent,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: [stage.stage],
        audience: AudienceMessage.Customer,
    };
    await collections.auditTrails.insertOne(auditTrail);

    const currentDate = new Date();

    const updatedEmails = reminder.emails.map(email => {
        if (email.willSendAt < currentDate) {
            return { ...email, isSent: true };
        }

        return email;
    });

    await collections.reminders.updateOne(
        { applicationSuiteId: reminder.applicationSuiteId },
        { $set: { emails: updatedEmails } }
    );

    // Only send password to customer when configuration is random
    if (attachmentInfo?.password && company.passwordConfiguration === PasswordConfiguration.Random) {
        await sendPasswordToCustomer(
            {
                i18n,
                subject: t('emails:sendPasswordToCustomer.subject', {
                    companyName: emailContext.companyName,
                }),
                data: { emailContext, application, password: attachmentInfo.password, lead },
                to: { name: customerFullName, address: customerEmail },
            },
            transporter,
            emailContext.sender
        );
    }
};

export default sendFinderReminderHandler;
