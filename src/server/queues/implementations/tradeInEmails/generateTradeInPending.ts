import { Job } from 'bull';
import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationKind,
    getKYCPresetsForCustomerModule,
    LocalCustomerManagementModule,
    ModuleType,
} from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getCustomerFullName } from '../../../database/helpers/customers';
import { createCompanySMTPTransports, sendTradeInPendingToSalesManager } from '../../../emails';
import createI18nInstance from '../../../utils/createI18nInstance';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getLaunchPadTradeInDetailsLink from '../shared/getLaunchPadTradeInDetailsLink';

export type GenerateTradeInPendingMessage = {
    applicationId: ObjectId;
};

export const generateTradeInPendingHandler = async (message: GenerateTradeInPendingMessage, job: Job) => {
    const { applicationId } = message;
    const { collections } = await getDatabaseContext();
    const application = await collections.applications.findOne({ _id: applicationId });

    const [lead, module] = await Promise.all([
        collections.leads.findOne({ _id: application.leadId }),
        collections.modules.findOne({ _id: application.moduleId }),
    ]);

    const saleManagerId = module._type === ModuleType.LaunchPadModule && module.salesManager;
    const tradeInVehicles = application.kind === ApplicationKind.Launchpad && application.tradeInVehicle;
    const tradeInVehicle = tradeInVehicles?.length && tradeInVehicles[0];

    if (!saleManagerId || !tradeInVehicle) {
        return;
    }

    const [salesManager, salesConsultant, customer] = await Promise.all([
        collections.users.findOne({ _id: saleManagerId }),
        collections.users.findOne({ _id: lead.assigneeId }),
        collections.customers.findOne({ _id: lead.customerId }),
    ]);

    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['emails', 'common']);
    const { t } = i18n;

    const company = await collections.companies.findOne({ _id: module.companyId });

    const customerModule = (await collections.modules.findOne({
        _id: customer.moduleId,
    })) as LocalCustomerManagementModule;
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerName = getCustomerFullName(t, customer, company, kycPresets);

    const tradeInDetailLink = await getLaunchPadTradeInDetailsLink(application, lead);

    const emailContext = await getCompanyEmailContext(company);

    const transporter = await createCompanySMTPTransports(company);

    await sendTradeInPendingToSalesManager(
        {
            i18n,
            data: {
                emailContext,
                salesManagerName: salesManager.displayName,
                salesConsultantName: salesConsultant.displayName,
                customerName,
                vehicleMakeModel: `${tradeInVehicle.make} ${tradeInVehicle.model}`,
                vehicleYear: isNil(tradeInVehicle.yearOfManufacture) ? null : tradeInVehicle.yearOfManufacture,
                vin: tradeInVehicle.chassisNumber,
                mileage: tradeInVehicle.mileage,
                transfersCount: tradeInVehicle.noOfTransfers,
                vehicleHandoverDate: tradeInVehicle.vehicleHandoverDate
                    ? dayjs(tradeInVehicle.vehicleHandoverDate).format('DD MMM YYYY')
                    : null,
                tradeInDetailLink,
            },
            subject: t('emails:tradeInPendingToSalesManager.subject', {
                companyName: emailContext.companyName,
                identifier: lead.identifier,
            }),
            to: { name: salesManager.displayName, address: salesManager.email },
        },
        transporter,
        emailContext.sender
    );
};

export default generateTradeInPendingHandler;
