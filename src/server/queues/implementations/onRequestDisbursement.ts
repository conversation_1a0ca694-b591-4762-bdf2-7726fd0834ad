import { Document } from 'bson';
import { Job } from 'bull';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { getFileStream } from '../../core/storage';
import {
    Application,
    ApplicationDocumentKind,
    ApplicationJourneySubmissionKind,
    ApplicationKind,
    AuditTrailKind,
    AuthorKind,
    Bank,
    BankIntegrationProvider,
    BankKind,
    getKYCPresetsForCustomerModule,
    LocalCustomerManagementModule,
    LocalVariant,
    PasswordConfiguration,
    RequestDisbursementAuditTrail,
    RequestDisbursementFailAuditTrail,
    SettingId,
    User,
    VehicleKind,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getBankIdFromApplication } from '../../database/helpers/applications';
import { getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import { createCompanySMTPTransports, sendPasswordForAgreementToBank, sendRequestDisbursement } from '../../emails';
import { DbsDocumentType, DbsUploadDocumentRequest } from '../../integrations/banks/dbs/uploadDocument';
import uploadDocuments from '../../integrations/banks/dbs/uploadDocuments';
import uploadMaybankDocument, {
    MaybankDocumentKind,
    MaybankUploadDocumentRequest,
} from '../../integrations/banks/maybank/uploadDocument';
import { streamToBuffer } from '../../utils';
import { getApplicationLogStages } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getEncryptedZip from '../../utils/getEncryptedZip';
import getDbsPartnerCode from './shared/getDbsPartnerCode';

export type onRequestDisbursementMessage = {
    applicationId: ObjectId;
    userId: ObjectId;
};

const onEmailRequestDisbursement = async (application: Application, bank: Bank) => {
    if (bank.integration.provider !== BankIntegrationProvider.Email) {
        throw new Error(
            `invalid integration provider, expect ${BankIntegrationProvider.Email} but got ${bank.integration.provider}`
        );
    }

    const { collections } = await getDatabaseContext();
    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Event:
        case ApplicationKind.Finder: {
            const transporter = await createCompanySMTPTransports(company);
            const emailContext = await getCompanyEmailContext(company);

            // load translations
            const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
            await i18n.loadNamespaces(['common', 'emails']);
            const { t } = i18n;

            const [variant, customer, assignee, financeProduct] = await Promise.all([
                collections.vehicles.findOne({
                    _id: application.vehicleId,
                    _kind: VehicleKind.LocalVariant,
                }) as Promise<LocalVariant>,
                collections.customers.findOne({ _id: application.applicantId }),
                application.financingStage?.assigneeId
                    ? collections.users.findOne({ _id: application.financingStage.assigneeId })
                    : Promise.resolve(null),
                application.financing?.financeProductId
                    ? collections.financeProducts.findOne({ _id: application.financing.financeProductId })
                    : Promise.resolve(null),
            ]);

            const customerModule = (await collections.modules.findOne({
                _id: customer.moduleId,
            })) as LocalCustomerManagementModule;

            const documents = application.documents.filter(document =>
                [
                    ApplicationDocumentKind.SignedVsa,
                    ApplicationDocumentKind.InsuranceCoverNote,
                    ApplicationDocumentKind.LogCard,
                    ApplicationDocumentKind.Invoice,
                ].includes(document.kind)
            );

            if (documents.length === 0) {
                break;
            }

            const randomPassword = nanoid();
            const files = await Promise.all(
                documents.map(async document => {
                    const source = await getFileStream(document);

                    return { source, filename: document.filename };
                })
            );

            const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;
            const encryptedZip = await getEncryptedZip(files, isPasswordProtected ? randomPassword : undefined);
            const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

            const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);

            const hint = isPasswordProtected ? t('emails:requestDisbursement.hint') : '';

            // send the email
            await sendRequestDisbursement(
                {
                    i18n,
                    data: {
                        emailContext,
                        bank,
                        application,
                        variant,
                        financeProduct,
                        assignee,
                        hint,
                        module: applicationModule,
                        dealerId: application.dealerId,
                    },
                    subject: t('emails:requestDisbursement.subject', {
                        companyName: emailContext.companyName,
                        identifier: application.financingStage?.identifier,
                        customerName: customerFullName,
                    }),
                    to: { name: bank.displayName, address: bank.integration.email },
                    attachments: [
                        {
                            filename: 'DisbursementDocuments.zip',
                            contentType: 'application/zip',
                            content: encryptedZip,
                        },
                    ],
                },
                transporter,
                emailContext.sender
            );

            if (isPasswordProtected) {
                await sendPasswordForAgreementToBank(
                    {
                        i18n,
                        subject: t('emails:requestReleaseLetter.subject', {
                            companyName: emailContext.companyName,
                            identifier: application.financingStage?.identifier,
                            customerName: customerFullName,
                        }),
                        data: { emailContext, application, randomPassword },
                        to: { name: bank.displayName, address: bank.integration.email },
                    },
                    transporter,
                    emailContext.sender
                );
            }
            break;
        }
    }
};

const onDbsRequestDisbursement = async (application: Application, bank: Bank, user: User | null) => {
    if (bank.integration.provider !== BankIntegrationProvider.DBS) {
        throw new Error(
            `invalid integration provider, expect ${BankIntegrationProvider.DBS} but got ${bank.integration.provider}`
        );
    }

    const { collections } = await getDatabaseContext();

    const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    const submission = journey?.submission;
    if (
        setting.settingId !== SettingId.DbsBankIntegration ||
        submission?.kind !== ApplicationJourneySubmissionKind.DBS
    ) {
        return;
    }

    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Event:
        case ApplicationKind.Finder: {
            const documents: DbsUploadDocumentRequest[] = (
                await Promise.all(
                    application.documents.map(async document => {
                        let documentType: DbsDocumentType | undefined;
                        switch (document.kind) {
                            case ApplicationDocumentKind.SignedVsa:
                                documentType = DbsDocumentType.VehicleSalesAgreement;
                                break;

                            case ApplicationDocumentKind.InsuranceCoverNote:
                                documentType = DbsDocumentType.InsuranceCoverNote;
                                break;

                            case ApplicationDocumentKind.LogCard:
                                documentType = DbsDocumentType.LogCard;
                                break;

                            case ApplicationDocumentKind.Invoice:
                                documentType = DbsDocumentType.Invoice;
                                break;
                        }

                        if (!documentType) {
                            return null;
                        }

                        return {
                            applicationReference: submission.reference,
                            documentName: document.filename,
                            documentType,
                            documentContent: (await streamToBuffer(await getFileStream(document))).toString('base64'),
                        };
                    })
                )
            ).filter(Boolean);

            if (documents.length > 0) {
                try {
                    await uploadDocuments(setting, documents, {
                        'PARTNER-CODE': await getDbsPartnerCode(application),
                    });
                } catch (error) {
                    // create audit trail for the failure
                    const auditTrail: RequestDisbursementFailAuditTrail = {
                        _id: new ObjectId(),
                        _kind: AuditTrailKind.RequestDisbursementFail,
                        _date: new Date(),
                        applicationId: application._id,
                        applicationSuiteId: application._versioning.suiteId,
                        stages: getApplicationLogStages(application, AuditTrailKind.RequestDisbursementFail),
                        author: user?._id ? { kind: AuthorKind.User, id: user._id } : { kind: AuthorKind.System },
                        reason: error.message,
                    };

                    await collections.auditTrails.insertOne(auditTrail);

                    // raise the error again to prevent the queue from continuing
                    throw error;
                }
            }

            break;
        }
    }
};

const onMaybankRequestDisbursement = async (application: Application, bank: Bank, user: User | null) => {
    if (bank.integration.provider !== BankIntegrationProvider.Maybank) {
        throw new Error(
            `invalid integration provider, expect ${BankIntegrationProvider.DBS} but got ${bank.integration.provider}`
        );
    }

    const { collections } = await getDatabaseContext();

    const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    const submission = journey?.submission;
    if (
        setting.settingId !== SettingId.MaybankIntegration ||
        submission?.kind !== ApplicationJourneySubmissionKind.Maybank
    ) {
        return;
    }

    const dealer =
        'dealerId' in application && application.dealerId
            ? await collections.dealers.findOne({ _id: application.dealerId })
            : null;

    const dealerCode = bank.kind === BankKind.External ? bank.dealerCode : dealer?.integrationDetails?.dealerCode;

    switch (application.kind) {
        case ApplicationKind.Standard:
        case ApplicationKind.Configurator:
        case ApplicationKind.Event:
        case ApplicationKind.Finder: {
            const documents: MaybankUploadDocumentRequest[] = (
                await Promise.all(
                    application.documents.map(async document => {
                        let documentType: MaybankDocumentKind | undefined;
                        switch (document.kind) {
                            case ApplicationDocumentKind.SignedVsa:
                                documentType = MaybankDocumentKind.HPA;
                                break;

                            case ApplicationDocumentKind.InsuranceCoverNote:
                                documentType = MaybankDocumentKind.ICN;
                                break;

                            case ApplicationDocumentKind.LogCard:
                                documentType = MaybankDocumentKind.LTC;
                                break;

                            case ApplicationDocumentKind.Invoice:
                                documentType = MaybankDocumentKind.TAI;
                                break;
                        }

                        if (!documentType) {
                            return null;
                        }

                        return {
                            msg: {
                                applicationData: {
                                    DealerID: dealerCode,
                                    SalesAgreementNo: application.financingStage?.identifier,
                                    LOSRefNumber: submission.reference,
                                    AmtIndicator: 'N' as const,
                                    DocType: documentType,
                                },
                                documentData: {
                                    DocData: (await streamToBuffer(await getFileStream(document))).toString('base64'),
                                },
                            },
                        };
                    })
                )
            ).filter(Boolean);

            if (documents.length > 0) {
                try {
                    await Promise.all(documents.map(document => uploadMaybankDocument(setting, document)));
                } catch (error) {
                    // create audit trail for the failure
                    const auditTrail: RequestDisbursementFailAuditTrail = {
                        _id: new ObjectId(),
                        _kind: AuditTrailKind.RequestDisbursementFail,
                        _date: new Date(),
                        applicationId: application._id,
                        applicationSuiteId: application._versioning.suiteId,
                        stages: getApplicationLogStages(application, AuditTrailKind.RequestDisbursementFail),
                        author: user?._id ? { kind: AuthorKind.User, id: user._id } : { kind: AuthorKind.System },
                        reason: error.message,
                    };

                    await collections.auditTrails.insertOne(auditTrail);

                    // raise the error again to prevent the queue from continuing
                    throw error;
                }
            }
        }
    }
};

export const onRequestDisbursementHandler = async (message: onRequestDisbursementMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();
    const { applicationId, userId } = message;

    const application = await collections.applications.findOne({ _id: applicationId });
    const bankId = getBankIdFromApplication(application);
    const bank = bankId ? await collections.banks.findOne({ _id: bankId }) : null;
    const user = await collections.users.findOne({ _id: userId });

    if (!application || !bank || !user) {
        return;
    }

    switch (bank.integration.provider) {
        case BankIntegrationProvider.Email:
            await onEmailRequestDisbursement(application, bank);
            break;

        case BankIntegrationProvider.DBS:
            await onDbsRequestDisbursement(application, bank, user);
            break;

        case BankIntegrationProvider.Maybank:
            await onMaybankRequestDisbursement(application, bank, user);
            break;
    }

    const auditTrail: RequestDisbursementAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.RequestDisbursement,
        _date: new Date(),
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.RequestDisbursement),
        author: { kind: AuthorKind.User, id: user._id },
    };

    // create audit trail
    await collections.auditTrails.insertOne(auditTrail);
};
