import { Document } from 'bson';
import { Job } from 'bull';
import { isEmpty, isNil, last } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../core/storage';
import {
    Application,
    ApplicationCancelledAuditTrail,
    ApplicationJourneySubmissionKind,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    BankIntegrationProvider,
    ConfiguratorApplication,
    EventApplication,
    FinderApplication,
    FinderVehicle,
    InventoryKind,
    LocalModel,
    LocalVariant,
    MobilityInventory,
    MobilityStockInventory,
    ModuleType,
    SettingId,
    StandardApplication,
    StockInventoryKind,
    VehicleKind,
    DealerTranslationText,
    getKYCPresetsForCustomerModule,
    StandardApplicationModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { updateAuditTrailForMobilityEmailSent } from '../../database/helpers/auditTrails';
import {
    getCustomerEmail,
    getCustomerFullNameWithTitle,
    getCustomerFullName,
    getCustomerName,
} from '../../database/helpers/customers';
import {
    AudienceMessage,
    createCompanySMTPTransports,
    sendCancellationEmailToBank,
    sendCustomerCancelConfirmation,
    sendMobilityEmail,
    sendSalespersonCancelConfirmation,
} from '../../emails';
import getTranslatedEmailContent from '../../emails/utils/getTranslatedEmailContent';
import { DbsWebhookPayload } from '../../integrations/banks/dbs/types';
import hlfCancel from '../../integrations/banks/hlf/cancel';
import maybankCancel from '../../integrations/banks/maybank/cancel';
import uobCancel, { UobCancelResponse } from '../../integrations/banks/uob/cancel';
import { UobErrorResponse } from '../../integrations/banks/uob/shared';
import { MobilityAsset, MobilityRecipient, StandardApplicationModuleAsset } from '../../schema/resolvers/enums';
import { getApplicationIdentifier, getApplicationLogStages, getStatusUpdates } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';
import { releaseStock } from './onApplicationDeclined';
import { canSendEmailToCustomer } from './sendApplicationSubmissionMail/shared';
import checkIsBankViewable from './shared/checkIsBankViewable';
import getApplicationAssignee from './shared/getApplicationAssignee';
import getApplicationContext from './shared/getApplicationContext';
import getApplicationDetailsLink from './shared/getApplicationDetailsLink';
import getMobilityEmailActionLinks from './shared/getMobilityEmailActionLinks';
import getMobilityEmailContent from './shared/getMobilityEmailContent';
import { getStandardApplicationEmailContent } from './shared/getStandardApplicationEmailContents';

export enum ApplicationCancelSource {
    UOB = 'uob',
    User = 'user',
    DBS = 'dbs',
    Customer = 'customer',
}

export type OnUserApplicationCancelledMessage = {
    source: ApplicationCancelSource.User;
    applicationId: ObjectId;
    stage: ApplicationStage;
    userId: ObjectId;
};

export type OnCustomerBookingCancelledMessage = {
    source: ApplicationCancelSource.Customer;
    applicationId: ObjectId;
    applicantId: ObjectId;
};

export type OnUobIntegrationApplicationCancelledMessage = {
    source: ApplicationCancelSource.UOB;
    bankId: ObjectId;
    applicationId: ObjectId;
    reference: string;
    subDescription: string | null | undefined;
};

export type OnDbsApplicationCancelledMessage = {
    source: ApplicationCancelSource.DBS;
    bankId: ObjectId;
    applicationId: ObjectId;
    partnerCode: string;
    payload: DbsWebhookPayload;
};

export type OnApplicationCancelledMessage =
    | OnUserApplicationCancelledMessage
    | OnUobIntegrationApplicationCancelledMessage
    | OnDbsApplicationCancelledMessage
    | OnCustomerBookingCancelledMessage;

const generateAuditTrail = (
    message: OnApplicationCancelledMessage,
    application: Application,
    reference: string | null | undefined
): ApplicationCancelledAuditTrail => {
    const auditTrail: Omit<ApplicationCancelledAuditTrail, 'author'> = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationCancelled,
        _date: new Date(),
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(
            application,
            AuditTrailKind.ApplicationCancelled,
            message.source === ApplicationCancelSource.User ? [message.stage] : null
        ),
        ...(reference && { reference }),
    };

    switch (message.source) {
        case ApplicationCancelSource.User:
            return {
                ...auditTrail,
                author: { kind: AuthorKind.User, id: message.userId },
            };

        case ApplicationCancelSource.UOB:
        case ApplicationCancelSource.DBS:
            return {
                ...auditTrail,
                author: { kind: AuthorKind.Bank, id: message.bankId },
            };

        case ApplicationCancelSource.Customer: {
            return {
                ...auditTrail,
                author: { kind: AuthorKind.Customer, id: message.applicantId },
            };
        }

        default:
            return null;
    }
};

type CancelApplicationByBankApiResult =
    | {
          kind: 'success';
          reference?: string;
      }
    | {
          kind: 'failure';
          reason: string;
      };

const cancelApplicationByBankApi = async (
    application: Application,
    salesRef?: string,
    remark?: string
): Promise<CancelApplicationByBankApiResult> => {
    const { collections } = await getDatabaseContext();
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });

    let result: CancelApplicationByBankApiResult = {
        kind: 'success',
    };
    if ('bankId' in application && application.bankId && !!journey) {
        const { moduleId } = application;
        const bank = await collections.banks.findOne({ _id: application.bankId });
        const { submission } = journey;

        switch (bank?.integration?.provider) {
            case BankIntegrationProvider.HLF:
            case BankIntegrationProvider.HLFV2: {
                const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
                if (
                    (setting.settingId === SettingId.HlfBankIntegration ||
                        setting.settingId === SettingId.HlfBankV2Integration) &&
                    submission?.kind === ApplicationJourneySubmissionKind.HLF
                ) {
                    const assignee = application?.financingStage?.assigneeId
                        ? await collections.users.findOne({ _id: application.financingStage.assigneeId })
                        : null;

                    const { hlfref } = await hlfCancel(setting, {
                        hlfref: submission.reference,
                        bookingref: application.financingStage?.identifier,
                        salesref: salesRef ?? assignee?.alias ?? '',
                    });

                    result = {
                        kind: 'success',
                        reference: hlfref,
                    };
                }

                break;
            }

            case BankIntegrationProvider.UOB: {
                const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
                if (
                    setting.settingId === SettingId.UobBankIntegration &&
                    submission?.kind === ApplicationJourneySubmissionKind.UOB
                ) {
                    const module = await collections.modules.findOne({ _id: moduleId });
                    const company = module ? await collections.companies.findOne({ _id: module.companyId }) : null;

                    const response = await uobCancel(
                        setting,
                        {
                            referenceNumber: submission.reference,
                            salesAgreementRefNumber: application.financingStage?.identifier,
                            ...(remark && { cancellationReason: remark }),
                        },
                        company?.countryCode ?? ''
                    );

                    if (response.code === '0000000') {
                        const casted = response as UobCancelResponse;

                        result = {
                            kind: 'success',
                            reference: casted.referenceNumber,
                        };
                    } else {
                        const casted = response as UobErrorResponse;

                        result = {
                            kind: 'failure',
                            reason: casted.subDescription,
                        };
                    }
                }

                break;
            }

            case BankIntegrationProvider.Maybank: {
                const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
                if (
                    setting.settingId === SettingId.MaybankIntegration &&
                    submission?.kind === ApplicationJourneySubmissionKind.Maybank
                ) {
                    const response = await maybankCancel(setting, {
                        msg: {
                            applicationData: {
                                DealerID: setting.secrets.dealerId,
                                LOSRefNumber: submission.reference,
                                SalesAgreementNo: application.financingStage?.identifier,
                            },
                        },
                    });

                    if (response?.msg?.status?.StatusCode === 'S') {
                        result = {
                            kind: 'success',
                            reference: response.msg.applicationData.LOSRefNumber,
                        };
                    } else {
                        result = {
                            kind: 'failure',
                            reason: response?.msg?.status?.ErrorDesc,
                        };
                    }
                }
            }
        }
    }

    return result;
};

const canSendCancelEmailToSalesperson = (stage: ApplicationStage) => stage !== ApplicationStage.Appointment;

const sendOutCancelEmail = async (
    application: ConfiguratorApplication | StandardApplication | FinderApplication | EventApplication,
    stage: ApplicationStage
) => {
    const { collections } = await getDatabaseContext();

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails', 'calculators', 'variant']);

    const { t } = i18n;

    const context = await getApplicationContext<LocalVariant | FinderVehicle>(application._id, stage);
    const { customer, company, variant, financeProduct, bank, dealer, customerModule, lead } = context;

    const assignee = await getApplicationAssignee(application, lead, stage);

    // load mail context
    const emailContext = await getCompanyEmailContext(company);
    const edmEmailFooterContext = await getEdmEmailFooterContext({ company });
    const dealerEmailContext = await getDealerEmailContext(dealer);

    const defaultIntroImage =
        variant._kind === VehicleKind.FinderVehicle
            ? last(variant.listing.vehicle.images.edges[0].node.variants).url
            : await getUrlForUpload(variant?.images?.[0]);

    const transporter = await createCompanySMTPTransports(company);
    const link = await getApplicationDetailsLink(application, lead, stage);
    const identifier = getApplicationIdentifier(application, lead, stage);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);
    const isBankViewable = context.bank
        ? await checkIsBankViewable(variant._versioning.suiteId, application.moduleId)
        : false;

    const hasTestDriveProcess = !!context.journey.testDriveSetting;

    if (application.kind !== ApplicationKind.Standard && canSendCancelEmailToSalesperson(stage)) {
        await sendSalespersonCancelConfirmation(
            {
                i18n,
                data: {
                    ...context,
                    assignee,

                    emailContext,
                    edmEmailFooterContext,
                    dealerEmailContext,
                    introImageUrl: defaultIntroImage,

                    link,
                    stage,
                    isBankViewable,
                    hasTestDriveProcess,
                },
                to: { name: assignee.displayName, address: assignee.email },
                subject: t('emails:salesPersonCancelConfirmation.subject', {
                    companyName: emailContext.companyName,
                    identifier,
                    customerName: customerFullName,
                }),
            },
            transporter,
            emailContext.sender
        );
    }
    if (application.kind === ApplicationKind.Standard && canSendCancelEmailToSalesperson(stage)) {
        const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
            _id: application.moduleId,
            _type: ModuleType.StandardApplicationModule,
        });
        const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
            await getStandardApplicationEmailContent({
                i18n,
                standardApplicationModule,
                standardApplicationAsset: StandardApplicationModuleAsset.SalesPersonCancelled,
                emailContext,
                dealer,
                customer,
                customerModule,
                user: assignee,
                link,
                identifier,
                lead,
            });

        await sendSalespersonCancelConfirmation(
            {
                i18n,
                data: {
                    ...context,
                    assignee,

                    emailContext,
                    edmEmailFooterContext,
                    dealerEmailContext,
                    introImageUrl: defaultIntroImage,

                    link,
                    stage,
                    isBankViewable,
                    hasTestDriveProcess,

                    ...(application.kind === ApplicationKind.Standard
                        ? {
                              introTitle,
                              contentText,
                              isSummaryVehicleVisible,
                              introImageUrl: introImageUrl ?? defaultIntroImage,
                          }
                        : {}),
                },
                to: { name: assignee.displayName, address: assignee.email },
                subject: emailSubject,
            },
            transporter,
            emailContext.sender
        );
    }

    if (
        application.kind === ApplicationKind.Standard &&
        canSendEmailToCustomer(application, context.bank, null, context.journey, false, customer, t)
    ) {
        const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
            _id: application.moduleId,
            _type: ModuleType.StandardApplicationModule,
        });

        const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
            await getStandardApplicationEmailContent({
                i18n,
                standardApplicationModule,
                standardApplicationAsset: StandardApplicationModuleAsset.CustomerCancelled,
                emailContext,
                dealer,
                customer,
                customerModule,
                user: assignee,
                link,
                identifier,
                lead,
            });
        await sendCustomerCancelConfirmation(
            {
                i18n,
                data: {
                    ...context,
                    assignee,

                    emailContext,
                    edmEmailFooterContext,
                    dealerEmailContext,
                    introImageUrl: defaultIntroImage,
                    stage,
                    isBankViewable,
                    hasTestDriveProcess,
                    ...(application.kind === ApplicationKind.Standard
                        ? {
                              introTitle,
                              contentText,
                              isSummaryVehicleVisible,
                              introImageUrl: introImageUrl ?? defaultIntroImage,
                          }
                        : {}),
                },
                to: {
                    name: getCustomerFullName(t, customer, company, kycPresets),
                    address: getCustomerEmail(t, customer),
                },
                subject:
                    application.kind === ApplicationKind.Standard
                        ? emailSubject
                        : t('emails:customerCancelConfirmation.subject', {
                              companyName: emailContext.companyName,
                              identifier,
                          }),
            },
            transporter,
            emailContext.sender
        );
    }

    // send the email to customer
    if (
        application.kind !== ApplicationKind.Standard &&
        canSendEmailToCustomer(application, context.bank, null, context.journey, false, customer, t)
    ) {
        await sendCustomerCancelConfirmation(
            {
                i18n,
                data: {
                    ...context,
                    assignee,

                    emailContext,
                    edmEmailFooterContext,
                    dealerEmailContext,
                    introImageUrl: defaultIntroImage,
                    stage,
                    isBankViewable,
                    hasTestDriveProcess,
                },
                to: {
                    name: getCustomerFullName(t, customer, company, kycPresets),
                    address: getCustomerEmail(t, customer),
                },
                subject: t('emails:customerCancelConfirmation.subject', {
                    companyName: emailContext.companyName,
                    identifier,
                }),
            },
            transporter,
            emailContext.sender
        );
    }

    if (stage === ApplicationStage.Financing && bank.integration.provider === BankIntegrationProvider.Email) {
        await sendCancellationEmailToBank(
            {
                i18n,
                data: {
                    emailContext,
                    bank,
                    application,
                    variant,
                    financeProduct,
                    assignee,
                    module: context.applicationModule,
                    dealerId: dealer._id,
                },
                subject: t('emails:bankCancellation.subject', {
                    companyName: emailContext.companyName,
                    identifier: application.financingStage?.identifier,
                    customerName: customerFullName,
                }),
                to: { name: bank.displayName, address: bank.integration.email },
            },
            transporter,
            emailContext.sender
        );
    }
};

export const onApplicationCancelled = async (message: OnApplicationCancelledMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();

    const { applicationId } = message;

    const application = await collections.applications.findOne({ _id: applicationId, '_versioning.isLatest': true });

    if (isNil(application)) {
        throw new Error('unable to locate application');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    let remark: string | undefined;
    let salesRef: string | undefined;

    let reference: string | undefined;
    if (message.source === ApplicationCancelSource.User && message.stage === ApplicationStage.Financing) {
        if (application.kind !== ApplicationKind.Mobility && application.kind !== ApplicationKind.Launchpad) {
            const result = await cancelApplicationByBankApi(application, salesRef, remark);

            if (result.kind === 'success') {
                reference = result.reference;
            } else {
                // create the audit trail for cancellation failures
                await collections.auditTrails.insertOne({
                    _id: new ObjectId(),
                    _kind: AuditTrailKind.ApplicationCancellationToBankFailed,
                    _date: new Date(),
                    applicationId: application._id,
                    applicationSuiteId: application._versioning.suiteId,
                    stages: getApplicationLogStages(application, AuditTrailKind.ApplicationCancellationToBankFailed),
                    bankId: application.bankId,
                    reason: result.reason,
                });

                return;
            }
        }
    }

    if (message.source === ApplicationCancelSource.UOB) {
        const { reference, subDescription } = message;
        await collections.applicationJourneys.updateOne(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    'submission.reference': reference,
                    'submission.subDescription': subDescription,
                },
            }
        );
    }

    if (message.source === ApplicationCancelSource.DBS) {
        const { partnerCode } = message;
        await collections.applicationJourneys.updateOne(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    'submission.partnerCode': partnerCode,
                },
            }
        );
    }

    // update application
    const statusUpdates = getStatusUpdates(
        application,
        lead,
        ApplicationStatus.Cancelled,
        message.source === ApplicationCancelSource.User ? [message.stage] : null
    );
    await collections.applications.findOneAndUpdate(
        { _id: application._id },
        {
            ...(!isEmpty(statusUpdates) && { $set: statusUpdates }),
            $unset: { promoCodeId: 1 },
        }
    );

    // create audit trail
    await collections.auditTrails.insertOne(generateAuditTrail(message, application, reference));
    switch (application.kind) {
        case ApplicationKind.Configurator: {
            await releaseStock(
                InventoryKind.ConfiguratorInventory,
                application.configuratorId,
                collections,
                application._versioning.suiteId
            );

            if (message.source === ApplicationCancelSource.User) {
                await sendOutCancelEmail(application, message.stage);
            }

            break;
        }

        case ApplicationKind.Event:
        case ApplicationKind.Standard:
        case ApplicationKind.Finder: {
            if (message.source === ApplicationCancelSource.User) {
                await sendOutCancelEmail(application, message.stage);
            }

            break;
        }

        case ApplicationKind.Mobility: {
            const journey = await collections.applicationJourneys.findOne({
                applicationSuiteId: application._versioning.suiteId,
            });

            const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null ?? null);
            await i18n.loadNamespaces(['common', 'emails']);
            const { t } = i18n;
            const module = await collections.modules.findOne({ _id: application.moduleId });
            if (module._type !== ModuleType.MobilityModule) {
                throw new Error('ModuleType not support');
            }

            const inventory = (await collections.inventories.findOne({
                _kind: InventoryKind.MobilityInventory,
                moduleId: application.moduleId,
                'stocks._id': application.mobilityBookingDetails.inventoryStockId,
            })) as MobilityInventory;

            const stock = inventory.stocks.find(
                stock =>
                    stock._kind === StockInventoryKind.MobilityStock &&
                    stock.reservations.find(reserve => reserve.applicationId.equals(application._versioning.suiteId))
            ) as MobilityStockInventory;

            const reservation = stock.reservations.find(reserve =>
                reserve.applicationId.equals(application._versioning.suiteId)
            );

            const [applicant, variant, company, dealer] = await Promise.all([
                collections.customers.findOne({ _id: application.applicantId }),
                collections.vehicles.findOne({ _id: application.vehicleId }) as Promise<LocalVariant>,
                collections.companies.findOne({ _id: module.companyId }),
                collections.dealers.findOne({ _id: application.dealerId }),
            ]);
            const customerModule = await collections.modules.findOne({ _id: applicant.moduleId });

            if (customerModule._type !== ModuleType.LocalCustomerManagement) {
                throw new Error('Customer Module not found');
            }
            const model = (await collections.vehicles.findOne({ _id: variant.modelId })) as LocalModel;

            const [emailContext, edmEmailFooterContext] = await Promise.all([
                getCompanyEmailContext(company),
                getEdmEmailFooterContext({ company }),
            ]);
            const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

            const customerName = getCustomerName(t, applicant, company, kycPresets);

            const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
            const customerEmail = getCustomerEmail(t, applicant);
            const dealerEmailContext = await getDealerEmailContext(dealer);

            const transporter = await createCompanySMTPTransports(company);

            const introImageUrl = await getUrlForUpload(variant.images?.[0]);

            // release the booked stock to become available
            await collections.inventories.updateOne(
                {
                    _kind: InventoryKind.MobilityInventory,
                    moduleId: application.moduleId,
                    variantSuiteId: variant._versioning.suiteId,
                    'stocks._id': application.mobilityBookingDetails.inventoryStockId,
                },
                {
                    $pull: {
                        'stocks.$[stock].reservations': reservation,
                    },
                },
                {
                    arrayFilters: [{ 'stock._id': application.mobilityBookingDetails.inventoryStockId }],
                }
            );

            const { amendLink, cancelLink } = await getMobilityEmailActionLinks(module._id, application);

            const getEmailContent = getMobilityEmailContent(application, module);

            const customerSubject: DealerTranslationText = getEmailContent('customers.bookingCancellation', 'subject');
            const operatorSubject: DealerTranslationText = getEmailContent('operators.bookingCancellation', 'subject');

            // send to customer
            await sendMobilityEmail(
                {
                    i18n,
                    data: {
                        emailContext,
                        mobilityRecipient: MobilityRecipient.Customers,
                        mobilityAsset: MobilityAsset.BookingCancellation,
                        dealer,

                        customerName,
                        customerFullName,
                        customerEmail,
                        variant,
                        modelName: model.name,

                        application,
                        applicant,

                        edmEmailFooterContext,
                        introImageUrl,
                        introTitle: getEmailContent('customers.bookingCancellation', 'introTitle'),
                        contentText: getEmailContent('customers.bookingCancellation', 'contentText'),

                        payment: journey.deposit,
                        amendLink,
                        cancelLink,
                        dealerEmailContext,
                    },
                    to: { name: getCustomerEmail(t, applicant), address: getCustomerEmail(t, applicant) },
                    subject: getTranslatedEmailContent({
                        i18nLanguage: i18n.language,
                        dealerTranslationText: customerSubject,
                        preferredDealerId: dealer._id.toHexString(),
                    }),
                },
                transporter,
                emailContext.sender
            );

            // send to Operators/ administrator of company
            await sendMobilityEmail(
                {
                    i18n,
                    data: {
                        emailContext,
                        mobilityRecipient: MobilityRecipient.Operators,
                        mobilityAsset: MobilityAsset.BookingCancellation,
                        dealer,

                        // this is a temporarily name to dealer, will have new ticket to resolve dealer email
                        customerName: dealer.displayName,
                        customerFullName: dealer.displayName,

                        // this is a temporarily email to dealer, will have new ticket to resolve dealer email
                        customerEmail: dealer.contact.email,
                        variant,
                        modelName: model.name,

                        application,
                        applicant,

                        edmEmailFooterContext,
                        introImageUrl,
                        introTitle: getEmailContent('operators.bookingCancellation', 'introTitle'),
                        contentText: getEmailContent('operators.bookingCancellation', 'contentText'),

                        payment: journey.deposit,
                        amendLink,
                        cancelLink,
                        dealerEmailContext,
                    },
                    to: { name: dealer.displayName, address: dealer.contact.email },
                    subject: getTranslatedEmailContent({
                        i18nLanguage: i18n.language,
                        dealerTranslationText: operatorSubject,
                        preferredDealerId: dealer._id.toHexString(),
                    }),
                },
                transporter,
                emailContext.sender
            );

            await updateAuditTrailForMobilityEmailSent(
                application,
                AuditTrailKind.BookingCancellationEmailSent,
                AudienceMessage.Customer
            );
            await updateAuditTrailForMobilityEmailSent(
                application,
                AuditTrailKind.BookingCancellationEmailSent,
                AudienceMessage.Dealer
            );

            break;
        }

        default:
            throw new Error('ApplicationKind not support');
    }
};
