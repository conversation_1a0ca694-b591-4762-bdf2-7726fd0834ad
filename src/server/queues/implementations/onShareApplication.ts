import { Document } from 'bson';
import { Job } from 'bull';
import { ObjectId } from 'mongodb';
import {
    ApplicationKind,
    getKYCPresetsForCustomerModule,
    LocalCustomerManagementModule,
    LocalVariant,
    ModuleType,
    StandardApplication,
    StandardApplicationModule,
    User,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../database/helpers/customers/shared';
import { createCompanySMTPTransports, sendShareDetails } from '../../emails';
import generateSharePdf from '../../journeys/helper/generateSharePdf';
import { StandardApplicationModuleAsset } from '../../schema/resolvers/enums';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import { getStandardApplicationEmailContent } from './shared/getStandardApplicationEmailContents';

export type OnShareApplicationMessage = { applicationId: ObjectId; userId?: ObjectId };

export const onShareApplicationHandler = async (message: OnShareApplicationMessage, job: Job<Document>) => {
    const { applicationId, userId } = message;
    const { collections } = await getDatabaseContext();
    const application = await collections.applications.findOne({ _id: applicationId });
    if (!application) {
        throw new Error('Application not found');
    }

    const user = await collections.users.findOne({ _id: userId });
    switch (application.kind) {
        case ApplicationKind.Standard:
            await onShareStandardApplication(application, user);

            return;

        default:
            throw new Error('Unsupported application kind');
    }
};

const onShareStandardApplication = async (application: StandardApplication, user: User | undefined) => {
    const { collections } = await getDatabaseContext();
    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });

    if (applicationModule._type !== ModuleType.StandardApplicationModule) {
        throw new Error('Application module is not a standard application module');
    }

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['emails', 'common', 'calculators']);
    const { t } = i18n;

    const lead = await collections.leads.findOne({ _id: application.leadId });
    const vehicle = (await collections.vehicles.findOne({ _id: application.vehicleId })) as LocalVariant;
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });
    const dealer = await collections.dealers.findOne({ _id: application.dealerId });
    const emailContext = await getCompanyEmailContext(company);
    const transporter = await createCompanySMTPTransports(company);
    const customer = await collections.customers.findOne({ _id: application.applicantId });
    const applicant = getLocalCustomerAggregatedFields(customer);

    const customerModule = (await collections.modules.findOne({
        _id: customer.moduleId,
    })) as LocalCustomerManagementModule;
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);
    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);

    const pdf = await generateSharePdf(application);

    const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
        _id: application.moduleId,
        _type: ModuleType.StandardApplicationModule,
    });

    const { introImageUrl, emailSubject, introTitle, contentText, fileName } = await getStandardApplicationEmailContent(
        {
            i18n,
            standardApplicationModule,
            standardApplicationAsset: StandardApplicationModuleAsset.CustomerShare,
            emailContext,
            vehicle,
            dealer,
            customer,
            customerModule,
            user,
            lead,
        }
    );

    await sendShareDetails(
        {
            i18n,
            subject: emailSubject,
            data: {
                emailContext,
                introImageUrl,
                introTitle,
                contentText,
            },
            to: { name: customerFullName, address: applicant.email },
            attachments: [
                {
                    filename: `${fileName}.pdf`,
                    contentType: 'application/pdf',
                    content: pdf,
                },
            ],
        },
        transporter,
        emailContext.sender
    );
};
