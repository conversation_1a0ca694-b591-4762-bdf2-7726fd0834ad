import { Job } from 'bull';
import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { isCapAvailableApplication } from '../../database/documents/Applications/utils';
import { AuditTrailKind } from '../../database/documents/AuditTrail';
import { LeadStatus } from '../../database/documents/Lead';
import getDatabaseContext from '../../database/getDatabaseContext';
import ppnAuth from '../../integrations/cap/ppnAuth';
import updateActivityStatus from '../../integrations/cap/submissions/updateActivityStatus';
import {
    createCapSubmissionAuditTrails,
    getCapModuleIdFromApplicationModule,
    updateCapSubmissionStatus,
} from '../../integrations/cap/utils';
import { ActivityKind, ActivityStatus } from '../../integrations/cap/utils/types';
import createLoaders from '../../loaders';
import createI18nInstance from '../../utils/createI18nInstance';

export type UpdateVisitAppointmentBookingActivityStatusMessage = {
    applicationId: ObjectId;
    userId: ObjectId;
    activityStatus: ActivityStatus;
};

/**
 * Updates the showroom visit booking activity status in CAP.
 * Applicable to applications with CAP integration & visit appointment scenario is the only scenario
 */
export const updateVisitAppointmentBookingActivityStatus = async (
    message: UpdateVisitAppointmentBookingActivityStatusMessage,
    job: Job
) => {
    const { collections } = await getDatabaseContext();
    const loaders = createLoaders();
    const { applicationId, userId, activityStatus } = message;

    const application = await collections.applications.findOne({ _id: applicationId, '_versioning.isLatest': true });
    if (isNil(application) || !isCapAvailableApplication(application)) {
        throw new Error('Unable to locate application');
    }

    if (isNil(application.visitAppointmentStage)) {
        throw new Error("Application doesn't have stage data");
    }

    const lead = await collections.leads.findOne({ _id: application.leadId, '_versioning.isLatest': true });
    if (isNil(lead)) {
        throw new Error('Unable to locate lead');
    }

    if (!lead.capValues) {
        return;
    }

    const { businessPartnerGuid, leadGuid, showroomVisitActivityGuid, showroomVisitId } = lead.capValues;

    if (!businessPartnerGuid || !leadGuid || !showroomVisitActivityGuid) {
        return;
    }

    const capModuleId = await getCapModuleIdFromApplicationModule(lead);
    if (!capModuleId) {
        return;
    }

    const capSetting = await loaders.capSettingByModuleId.load(capModuleId);
    if (isNil(capSetting)) {
        console.error(`CAP setting not found for module ID: ${capModuleId}`);

        return;
    }

    const user = await loaders.userById.load(userId);
    if (isNil(user)) {
        console.error(`User not found for ID: ${userId}`);

        return;
    }

    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['auditTrails']);
    const { t } = i18n;

    const authentication = await ppnAuth(capSetting);
    if (authentication.error) {
        const errorMessage = t('auditTrails:application.defaultCapError.authFailed');

        await createCapSubmissionAuditTrails({
            application,
            lead,
            capActionAuditTrail: AuditTrailKind.CapActivityCompleteShowroomVisitSubmitted,
            success: false,
            errorMessage,
        });

        console.error({
            message: errorMessage,
            data: {
                application: application._id,
                moduleId: capModuleId,
                capSettingId: capSetting._id,
            },
        });

        await updateCapSubmissionStatus({
            application,
            lead,
            status: LeadStatus.SubmittedWithError,
            user,
        });

        return;
    }

    await updateActivityStatus({
        activityGuid: showroomVisitActivityGuid,
        activityId: showroomVisitId,
        activityKind: ActivityKind.ShowroomVisit,
        activityStatus,
        application,
        lead,
        t,
        capSetting,
        auth: authentication.access_token,
    });
};
