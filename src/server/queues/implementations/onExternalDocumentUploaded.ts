/* eslint-disable max-len */
import { Document } from 'bson';
import { Job } from 'bull';
import { isEmpty } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import fetch from 'node-fetch';
import {
    Application,
    ApplicationJourneySubmissionKind,
    AuditTrailKind,
    AuthorKind,
    Bank,
    BankIntegrationProvider,
    RequestDisbursementAuditTrail,
    RequestDisbursementFailAuditTrail,
    SettingId,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getBankIdFromApplication } from '../../database/helpers/applications';
import { DbsDocumentType } from '../../integrations/banks/dbs/uploadDocument';
import uploadDbsDocuments from '../../integrations/banks/dbs/uploadDocuments';
import { getApplicationLogStages } from '../../utils/application';
import { SharedDocumentType } from '../../utils/documentType';
import getDbsPartnerCode from './shared/getDbsPartnerCode';

export type OnExternalDocumentUploadedMessage = {
    applicationId: ObjectId;
    documents: Array<{
        name: string;
        documentType: SharedDocumentType;
        url: string;
    }>;
};

const getFileContentFromUrl = async (url: string) => {
    if (isEmpty(url)) {
        return null;
    }

    try {
        const response = await fetch(url);
        const buffer = await response.buffer();

        return buffer.toString('base64');
    } catch (error) {
        return null;
    }
};

const onDbsExternalDocumentUploaded = async (
    application: Application,
    bank: Bank,
    documents: OnExternalDocumentUploadedMessage['documents']
) => {
    if (bank.integration.provider !== BankIntegrationProvider.DBS) {
        throw new Error(
            `invalid integration provider, expecting ${BankIntegrationProvider.DBS} but got ${bank.integration.provider}`
        );
    }

    const { collections } = await getDatabaseContext();
    const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    const submission = journey?.submission;
    if (
        setting?.settingId !== SettingId.DbsBankIntegration ||
        submission?.kind !== ApplicationJourneySubmissionKind.DBS
    ) {
        return;
    }

    const dbsDocuments = (
        await Promise.all(
            documents.map(async document => {
                let documentType: DbsDocumentType | undefined;
                switch (document.documentType) {
                    case SharedDocumentType.FINPassFront:
                    case SharedDocumentType.FINPassBack:
                        documentType = DbsDocumentType.FINPass;
                        break;

                    case SharedDocumentType.IncomeDocument:
                        documentType = DbsDocumentType.IncomeDocument;
                        break;

                    case SharedDocumentType.LogCard:
                        documentType = DbsDocumentType.LogCard;
                        break;

                    case SharedDocumentType.MYIdentityCardFront:
                    case SharedDocumentType.MYIdentityCardBack:
                        documentType = DbsDocumentType.MYIdentityCard;
                        break;

                    case SharedDocumentType.SGIdentityCardFront:
                    case SharedDocumentType.SGIdentityCardBack:
                        documentType = DbsDocumentType.SGIdentityCard;
                        break;

                    case SharedDocumentType.Passport:
                        documentType = DbsDocumentType.Passport;
                        break;

                    case SharedDocumentType.VehicleSalesAgreement:
                        documentType = DbsDocumentType.VehicleSalesAgreement;
                        break;
                }

                if (!documentType) {
                    return null;
                }

                return {
                    applicationReference: submission.reference,
                    documentName: document.name,
                    documentType,
                    documentContent: await getFileContentFromUrl(document.url),
                };
            })
        )
    ).filter(item => item && item.documentContent);

    if (dbsDocuments.length > 0) {
        try {
            await uploadDbsDocuments(setting, dbsDocuments, {
                'PARTNER-CODE': await getDbsPartnerCode(application),
            });
        } catch (error) {
            // create audit trail for the failure
            const auditTrail: RequestDisbursementFailAuditTrail = {
                _id: new ObjectId(),
                _kind: AuditTrailKind.RequestDisbursementFail,
                _date: new Date(),
                applicationId: application._id,
                applicationSuiteId: application._versioning.suiteId,
                stages: getApplicationLogStages(application, AuditTrailKind.RequestDisbursementFail),
                author: { kind: AuthorKind.System },
                reason: error.message,
            };

            await collections.auditTrails.insertOne(auditTrail);

            // raise the error again to prevent the queue from continuing
            throw error;
        }
    }
};

export const onExternalDocumentUploaded = async (message: OnExternalDocumentUploadedMessage, job: Job<Document>) => {
    const { applicationId, documents } = message;

    const { collections } = await getDatabaseContext();
    const application = await collections.applications.findOne({ _id: applicationId });
    const bankId = getBankIdFromApplication(application);
    const bank = bankId ? await collections.banks.findOne({ _id: bankId }) : null;

    if (!application || !bank) {
        return;
    }

    switch (bank.integration.provider) {
        case BankIntegrationProvider.DBS:
            await onDbsExternalDocumentUploaded(application, bank, documents);
            break;

        default:
            break;
    }

    const auditTrail: RequestDisbursementAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.RequestDisbursement,
        _date: new Date(),
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.RequestDisbursement),
        author: { kind: AuthorKind.System },
    };

    // create audit trail
    await collections.auditTrails.insertOne(auditTrail);
};
