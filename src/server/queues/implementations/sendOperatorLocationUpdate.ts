import { Document } from 'bson';
import { Job } from 'bull';
import { TFunction, i18n } from 'i18next';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../core/storage';
import { getKYCPresetsForCustomerModule, LocalVariant } from '../../database';
import {
    ApplicationKind,
    ApplicationStage,
    AuditTrailKind,
    AuthorKind,
    MobilityBookingLocationType,
    ModuleType,
    Author,
    MobilityApplication,
    MobilityModule,
    User,
    Customer,
    ApplicationEmailSent,
    Company,
    LocalCustomerManagementModule,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import {
    AudienceMessage,
    createCompanySMTPTransports,
    sendOperatorLocationUpdateEmail,
    sendSalespersonUpdate,
} from '../../emails';
import createLoaders from '../../loaders';
import { DEFAULT_STAGES, getApplicationIdentifier, getApplicationStage } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';
import getApplicationContext from './shared/getApplicationContext';
import getApplicationDetailsLink from './shared/getApplicationDetailsLink';

export type SendOperatorLocationUpdateMessage = {
    previousApplicationId: ObjectId;
    nextApplicationId: ObjectId;
    author: Author;
};

const retrieveAssignedBy = (
    t: TFunction,
    company: Company,
    assignedByUser?: User,
    assignedByCustomer?: Customer,
    customerModule?: LocalCustomerManagementModule
) => {
    const kycPresets = assignedByCustomer
        ? getKYCPresetsForCustomerModule(customerModule, assignedByCustomer._kind)
        : null;

    const customerName = assignedByCustomer
        ? getCustomerFullNameWithTitle(t, assignedByCustomer, company, kycPresets)
        : '';
    const assignedBy = {
        displayName: assignedByUser?.displayName ?? customerName,
    };

    return assignedBy;
};

const retrieveRecipient = async (
    application: MobilityApplication,
    applicationModule: MobilityModule,
    loaders = createLoaders()
) => {
    const userOperatorId =
        application.mobilityBookingDetails.location._type === MobilityBookingLocationType.Home
            ? applicationModule.homeDelivery.assigneeId
            : applicationModule.locations.find(location =>
                  location._id.equals(application.mobilityBookingDetails.location._id)
              )?.assigneeId;

    const userOperator = userOperatorId ? await loaders.userById.load(userOperatorId) : null;

    const dealer = await loaders.dealerById.load(application.dealerId);

    return {
        displayName: userOperator?.displayName ?? dealer.displayName,
        email: userOperator?.email ?? dealer.contact?.email,
    };
};

const retrieveApplication = async (applicationId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const application = await collections.applications.findOne({
        _id: applicationId,
        kind: ApplicationKind.Mobility,
    });

    if (!application || application.kind !== ApplicationKind.Mobility) {
        return null;
    }

    const applicationModule = await collections.modules.findOne({
        _id: application.moduleId,
        _type: ModuleType.MobilityModule,
    });
    if (!applicationModule || applicationModule._type !== ModuleType.MobilityModule) {
        return null;
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    return { application, applicationModule, lead };
};

const sendPreviousOperatorEmail = async (
    applicationId: ObjectId,
    i18n: i18n,
    assignedByUser?: User,
    assignedByCustomer?: Customer,
    customerModule?: LocalCustomerManagementModule,
    loaders = createLoaders()
) => {
    const { collections } = await getDatabaseContext();

    const result = await retrieveApplication(applicationId);
    if (!result) {
        return;
    }

    const { application, applicationModule, lead } = result;

    const recipient = await retrieveRecipient(application, applicationModule);
    if (!recipient.email) {
        return;
    }

    const { t } = i18n;

    const company = await loaders.companyById.load(applicationModule.companyId);
    const transporter = await createCompanySMTPTransports(company);
    const emailContext = await getCompanyEmailContext(company);

    const assignedBy = retrieveAssignedBy(t, company, assignedByUser, assignedByCustomer, customerModule);

    const identifier = getApplicationIdentifier(application, lead, [ApplicationStage.Mobility]);

    // send the operator location update email
    await sendOperatorLocationUpdateEmail(
        {
            i18n,
            data: {
                application,
                lead,
                emailContext,
                assignedBy,
                recipient,
                dealerId: application.dealerId,
                module: applicationModule,
            },
            to: { name: recipient.displayName, address: recipient.email },
            subject: t('emails:updateLocationOperator.subject', {
                companyName: emailContext.companyName,
                identifier,
            }),
        },
        transporter,
        emailContext.sender
    );

    // add to activity log
    const auditTrail: ApplicationEmailSent = {
        _id: new ObjectId(),
        _date: new Date(),
        _kind: AuditTrailKind.ApplicationEmailSent,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: [ApplicationStage.Mobility],
        audience: AudienceMessage.Operator,
    };
    await collections.auditTrails.insertOne(auditTrail);
};

const sendNextOperatorEmail = async (
    applicationId: ObjectId,
    i18n: i18n,
    customerModule: LocalCustomerManagementModule,
    assignedByUser?: User,
    assignedByCustomer?: Customer,
    loaders = createLoaders()
) => {
    const { collections } = await getDatabaseContext();

    const result = await retrieveApplication(applicationId);
    if (!result) {
        return;
    }

    const { application, applicationModule, lead } = result;

    const recipient = await retrieveRecipient(application, applicationModule);
    if (!recipient.email) {
        return;
    }

    const { t } = i18n;

    const { variant, dealer } = await getApplicationContext(application._id);

    const company = await loaders.companyById.load(applicationModule.companyId);
    const transporter = await createCompanySMTPTransports(company);
    const emailContext = await getCompanyEmailContext(company);
    const edmEmailFooterContext = await getEdmEmailFooterContext({ company });

    const introImageUrl = await getUrlForUpload((variant as LocalVariant)?.images?.[0]);

    const dealerEmailContext = await getDealerEmailContext(dealer);

    const assignedBy = retrieveAssignedBy(t, company, assignedByUser, assignedByCustomer, customerModule);

    const stage = getApplicationStage(application, lead, DEFAULT_STAGES);
    const submissionType = t(`emails:component.applicationStage.${stage?.stage}`);
    const identifier = getApplicationIdentifier(application, lead, [ApplicationStage.Mobility]);

    const link = await getApplicationDetailsLink(application, lead, ApplicationStage.Mobility);

    await sendSalespersonUpdate(
        {
            i18n,
            data: {
                application,
                assignee: recipient,
                emailContext,
                assignedBy,
                link,
                identifier,
                submissionType,
                edmEmailFooterContext,
                introImageUrl,
                dealerEmailContext,
                module: applicationModule,
            },
            to: { name: recipient.email, address: recipient.email },
            subject: t('emails:updateSalesperson.subject', {
                companyName: emailContext.companyName,
                applicationId: identifier,
                submissionType,
                identifier,
            }),
        },
        transporter,
        emailContext.sender
    );

    // add to activity log
    const auditTrail: ApplicationEmailSent = {
        _id: new ObjectId(),
        _date: new Date(),
        _kind: AuditTrailKind.ApplicationEmailSent,
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: [ApplicationStage.Mobility],
        audience: AudienceMessage.Operator,
    };
    await collections.auditTrails.insertOne(auditTrail);
};

export const sendOperatorLocationUpdate = async (message: SendOperatorLocationUpdateMessage, job: Job<Document>) => {
    const loaders = createLoaders();

    const { previousApplicationId, nextApplicationId, author } = message;

    // load translations
    const { i18n } = await createI18nInstance();
    await i18n.loadNamespaces(['emails', 'common', 'calculators']);

    const [assignedByUser, assignedByCustomer] = await Promise.all([
        author.kind === AuthorKind.User ? loaders.userById.load(author.id) : null,
        author.kind === AuthorKind.Customer ? loaders.customerById.load(author.id) : null,
    ]);

    const customerModule = assignedByCustomer
        ? ((await loaders.moduleById.load(assignedByCustomer.moduleId)) as LocalCustomerManagementModule)
        : null;

    await sendPreviousOperatorEmail(
        previousApplicationId,
        i18n,
        assignedByUser,
        assignedByCustomer,
        customerModule,
        loaders
    );
    await sendNextOperatorEmail(nextApplicationId, i18n, customerModule, assignedByUser, assignedByCustomer, loaders);
};
