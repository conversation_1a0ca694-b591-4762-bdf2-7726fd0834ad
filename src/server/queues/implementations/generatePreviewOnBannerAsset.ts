import { extname } from 'path';
import { Document } from 'bson';
import { Job } from 'bull';
import { head, get } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { deleteUploadedFile } from '../../core/storage';
import getDatabaseContext from '../../database/getDatabaseContext';
import { BannerAsset } from '../../schema/resolvers/enums';
import { generateImagePreview, resizableImageExtensions } from '../../utils/preview';

export type GeneratePreviewOnBannerAssetMessage = {
    bannerId: ObjectId;
    bannerAsset: BannerAsset;
};

const getSavedPath = (bannerAsset: BannerAsset) => {
    switch (bannerAsset) {
        case BannerAsset.ConfiguratorBanner:
        case BannerAsset.EventBanner:
            return 'bannerImage';

        case BannerAsset.ConfiguratorMobileBanner:
        case BannerAsset.EventMobileBanner:
            return 'mobileBannerImage';

        default:
            throw new Error('BannerAsset not support');
    }
};

export const generatePreviewOnBannerAsset = async (
    { bannerId, bannerAsset }: GeneratePreviewOnBannerAssetMessage,
    job: Job<Document>
) => {
    const { collections } = await getDatabaseContext();

    const banner = await collections.banners.findOne({ _id: bannerId });
    if (!banner) {
        return;
    }

    // right now only 1 image in the BannerImage, will support multiple support image in future
    const source = head(get(getSavedPath(bannerAsset), banner));

    if (!source) {
        return;
    }

    const extension = extname(source.filename).toLowerCase();

    if (!resizableImageExtensions.includes(extension)) {
        return;
    }

    const preview = await generateImagePreview(source);

    if (source.preview) {
        // delete previous image
        await deleteUploadedFile(source.preview);
    }

    await collections.banners.updateOne(
        {
            _id: bannerId,
        },
        {
            $set: { 'bannerImage.0.preview': preview },
        }
    );
};
