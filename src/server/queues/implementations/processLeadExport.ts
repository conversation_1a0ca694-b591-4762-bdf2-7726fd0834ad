import { promises as fs } from 'fs';
import * as os from 'os';
import * as path from 'path';
import { Document } from 'bson';
import { Job } from 'bull';
import dayjs from 'dayjs';
import { i18n, TFunction } from 'i18next';
import { FindCursor, ObjectId, WithId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { Company, PasswordConfiguration } from '../../database/documents/Company';
import { ExportLead, Lead } from '../../database/documents/Lead/kinds';
import { User } from '../../database/documents/User';
import { LeadModule } from '../../database/documents/modules';
import { LeadStageOption } from '../../database/documents/shared';
import getDatabaseContext from '../../database/getDatabaseContext';
import {
    createCompanySMTPTransports,
    sendLeadExportReady,
    sendLeadExportPassword,
    sendLeadExportFail,
} from '../../emails';
import { setHeaderColumnsWidth } from '../../export/exportApplications/shared';
import { getLeadFilterByStage } from '../../export/streamExportLeads/shared';
import { PeriodPayload } from '../../export/type';
import { getPassword, getPeriodFilter } from '../../export/utils';
import createLoaders, { Loaders } from '../../loaders';
import { createPermissionController, LeadPolicyAction, PermissionController } from '../../permissions';
import createI18nInstance from '../../utils/createI18nInstance';
import { FormatCapPurpose } from '../../utils/excel/applications/cap/types';
import { ExcelExportFormat } from '../../utils/excel/enums';
import getExcelLeadRows from '../../utils/excel/leads';
import { uniqueObjectIds } from '../../utils/fp';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

const BATCH_SIZE = 500;

type ExportFormat = 'system' | 'cap';

export type ProcessLeadExportMessage = {
    userId: ObjectId;
    moduleIds: string[];
    stage: LeadStageOption;
    dealerIds: string[];
    period?: PeriodPayload;
    format: ExportFormat;
    capPurpose?: FormatCapPurpose[];
    nonce?: string;
    languageId?: string;
    filename: string[];
};

type ExportContext = {
    user: User;
    modules: LeadModule[];
    companies: Company[];
    company: Company;
    loaders: Loaders;
    permissions: PermissionController;
    i18n: i18n;
    t: TFunction;
};

// initialize export context once to avoid repeated database calls
const initializeExportContext = async (message: ProcessLeadExportMessage): Promise<ExportContext> => {
    const { userId, moduleIds: inputModuleIds } = message;
    const { collections } = await getDatabaseContext();

    // Parallel initialization of independent resources
    const [user, loaders, { i18n }] = await Promise.all([
        collections.users.findOne({ _id: userId }),
        createLoaders(),
        createI18nInstance(),
    ]);

    await i18n.loadNamespaces(['emails', 'common']);
    const { t } = i18n;

    const permissions = await createPermissionController(user);

    const moduleIds = inputModuleIds.map(id => new ObjectId(id));
    const modules = (await collections.modules.find({ _id: { $in: moduleIds } }).toArray()) as LeadModule[];

    const companies = await collections.companies
        .find({ _id: { $in: uniqueObjectIds(modules.map(m => m.companyId)) } })
        .toArray();

    return {
        user,
        modules,
        companies,
        company: companies[0],
        loaders,
        permissions,
        i18n,
        t,
    };
};

// Optimized batch fetching with better error handling and memory management
export const createBatchIterator = (cursor: FindCursor<ExportLead>, batchSize: number) => ({
    async *[Symbol.asyncIterator]() {
        let batchCount = 0;

        while (true) {
            const batch: ExportLead[] = [];

            try {
                while (batch.length < batchSize) {
                    // eslint-disable-next-line no-await-in-loop
                    const hasNext = await cursor.hasNext();
                    if (!hasNext) {
                        break;
                    }

                    // eslint-disable-next-line no-await-in-loop
                    const lead = await cursor.next();
                    if (lead) {
                        batch.push(lead);
                    }
                }

                if (batch.length === 0) {
                    break;
                }

                yield { batch, batchIndex: batchCount++ };

                // Memory pressure check - yield control periodically
                if (batchCount % 10 === 0) {
                    // eslint-disable-next-line no-await-in-loop
                    await new Promise(resolve => {
                        setImmediate(resolve);
                    });
                }
            } catch (error) {
                console.error(`Error fetching batch ${batchCount}:`, error);
                throw error;
            }
        }
    },
});

const generateWorkbookStreaming = async (
    cursor: FindCursor<WithId<Lead>>,
    context: ExportContext,
    format: ExportFormat,
    stage: LeadStageOption,
    languageId?: string,
    currentPurpose?: FormatCapPurpose
) => {
    const workbook = await XlsxPopulate.fromBlankAsync();
    const worksheet = workbook.sheet(0).name('Export Data');

    let headerAdded = false;
    let currentRow = 1;
    let totalProcessed = 0;

    const batchIterator = createBatchIterator(cursor, BATCH_SIZE);

    try {
        for await (const { batch, batchIndex } of batchIterator) {
            const rows = await processLeads(
                context.loaders,
                batch,
                context.modules,
                format,
                context.company,
                context.companies,
                stage,
                languageId,
                currentPurpose
            );

            if (!rows || !Array.isArray(rows) || rows.length === 0) {
                continue;
            }

            // Add header only once
            if (!headerAdded && rows.length > 0) {
                const headerRow = rows[0];
                if (Array.isArray(headerRow)) {
                    // eslint-disable-next-line no-loop-func
                    headerRow.forEach((cell, colIndex) => {
                        worksheet.cell(currentRow, colIndex + 1).value(cell ?? '');
                    });
                    currentRow++;
                    headerAdded = true;

                    // Set column widths based on header
                    setHeaderColumnsWidth(worksheet, headerRow);
                }
            }

            // Add data rows
            const dataStartIndex = 1;
            for (let i = dataStartIndex; i < rows.length; i++) {
                const row = rows[i];
                if (Array.isArray(row)) {
                    // eslint-disable-next-line no-loop-func
                    row.forEach((cell, colIndex) => {
                        worksheet.cell(currentRow, colIndex + 1).value(cell ?? '');
                    });
                    currentRow++;
                }
            }

            totalProcessed += batch.length;

            // Progress logging
            if (batchIndex % 20 === 0) {
                console.info(`Processed ${totalProcessed} leads (${batchIndex + 1} batches)`);
            }
        }

        console.info(`Export completed: ${totalProcessed} total leads processed`);

        return workbook;
    } catch (error) {
        console.error('Error in workbook generation:', error);
        throw error;
    }
};

const generateExportFiles = async (context: ExportContext, message: ProcessLeadExportMessage) => {
    const {
        format,
        capPurpose,
        languageId,
        stage,
        filename: inputFilename,
        nonce: inputNonce,
        dealerIds: inputDealerIds,
        period,
    } = message;

    const purposesToProcess = format === 'cap' && Array.isArray(capPurpose) ? capPurpose : [undefined];
    const results: Array<{ filename: string; path: string; password?: string }> = [];

    const nonce = inputNonce ?? nanoid();
    const password = await getPassword(nonce);
    const isPasswordProtected = context.company.passwordConfiguration !== PasswordConfiguration.Off;
    const { collections } = await getDatabaseContext();

    const dealerIds = inputDealerIds.map(id => new ObjectId(id));

    const start = period?.start ? dayjs(period.start).startOf('day').toDate() : null;
    const end = period?.end ? dayjs(period.end).endOf('day').toDate() : null;

    const leadPermission = context.permissions.leads.getFilterQueryForAction(LeadPolicyAction.View);
    const leadStageFilter = getLeadFilterByStage(stage);

    // Process purposes sequentially to manage memory usage
    for (let index = 0; index < purposesToProcess.length; index++) {
        const currentPurpose = purposesToProcess[index];
        let cursor = null;

        try {
            cursor = collections.leads
                .find({
                    $and: [
                        leadPermission,
                        leadStageFilter,
                        { isDraft: { $ne: true } },
                        {
                            // check wether we remove or not
                            $or: [
                                context.modules.length && {
                                    moduleId: { $in: context.modules.map(module => module._id) },
                                    dealerId: { $in: dealerIds },
                                    ...getPeriodFilter(start, end),
                                    '_versioning.isLatest': true,
                                },
                            ].filter(Boolean),
                        },
                    ],
                })
                .batchSize(BATCH_SIZE)
                .maxTimeMS(300000);
            // eslint-disable-next-line no-await-in-loop
            const workbook = await generateWorkbookStreaming(
                cursor,
                context,
                format,
                stage,
                languageId,
                currentPurpose
            );

            // Generate filename
            let filename = 'export.xlsx';
            if (inputFilename && inputFilename.length > 0) {
                filename = inputFilename[index] || inputFilename[0];
                if (!filename.endsWith('.xlsx')) {
                    filename = `${filename}.xlsx`;
                }
            }

            // eslint-disable-next-line no-await-in-loop
            const buffer = await workbook.outputAsync({
                ...(isPasswordProtected && { password }),
            });

            // Use async file operations
            const tempDir = os.tmpdir();
            const filePath = path.join(tempDir, filename);
            // eslint-disable-next-line no-await-in-loop
            await fs.writeFile(filePath, buffer);

            results.push({
                filename,
                path: filePath,
                ...(isPasswordProtected && { password }),
            });

            // Reset cursor for next iteration if multiple purposes
            if (index < purposesToProcess.length - 1) {
                // Note: You may need to recreate the cursor here depending on your MongoDB driver version
                // cursor.rewind(); // This might not be available in all versions
            }
        } catch (error) {
            console.error(`Error generating workbook ${index}:`, error);
            throw error;
        } finally {
            // Cleanup resources
            if (cursor) {
                try {
                    // eslint-disable-next-line no-await-in-loop
                    await cursor.close();
                } catch (error) {
                    console.warn('Warning: Could not close cursor:', error.message);
                }
            }
        }
    }

    return results;
};

// process leads to excel lead rows
const processLeads = async (
    loaders: Loaders,
    leads: ExportLead[],
    modules: LeadModule[],
    format: ExportFormat,
    company: Company,
    companies: Company[],
    stage: LeadStageOption,
    languageId?: string,
    currentPurpose?: FormatCapPurpose
) => {
    // no leads to process, we do nothing
    if (leads.length === 0) {
        return [];
    }

    try {
        return getExcelLeadRows(
            leads,
            modules,
            format === ExcelExportFormat.cap
                ? {
                      format: ExcelExportFormat.cap,
                      capPurpose: currentPurpose as FormatCapPurpose,
                      tenant: `${company.displayName}_${company.countryCode}`.toUpperCase(),
                      stage,
                      routerFirstLanguage: languageId || null,
                      timeZone: company?.timeZone,
                  }
                : {
                      format: ExcelExportFormat.system,
                      currencyCode: companies.length > 1 ? undefined : company.currency,
                      timeZone: companies.length > 1 ? undefined : company.timeZone,
                      stage,
                      routerFirstLanguage: languageId || null,
                  },
            loaders
        );
    } catch (error) {
        throw new Error(`Error processing batch: ${error.message}`);
    }
};

// Optimized email sending with better error handling
const sendExportEmails = async (
    context: ExportContext,
    filePaths: Array<{ filename: string; path: string; password?: string }>,
    exportPassword?: string
) => {
    const emailContext = await getCompanyEmailContext(context.company);
    const transporter = await createCompanySMTPTransports(context.company);

    try {
        // Send export ready email
        await sendLeadExportReady(
            {
                i18n: context.i18n,
                subject: context.t('emails:leadExportReady.subject', {
                    companyName: context.company.displayName,
                }),
                data: {
                    user: context.user,
                    requestDate: new Date(),
                    emailContext,
                },
                to: { name: context.user.displayName, address: context.user.email },
                attachments: filePaths.map(file => ({
                    filename: file.filename,
                    path: file.path,
                    contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                })),
            },
            transporter,
            emailContext.sender
        );

        // Send password email if needed
        if (exportPassword) {
            await sendLeadExportPassword(
                {
                    i18n: context.i18n,
                    subject: context.t('emails:leadExportPassword.subject', {
                        companyName: context.company.displayName,
                    }),
                    data: {
                        user: context.user,
                        password: exportPassword,
                        emailContext,
                    },
                    to: { name: context.user.displayName, address: context.user.email },
                },
                transporter,
                emailContext.sender
            );
        }
    } catch (error) {
        console.error('Error sending export emails:', error);
        throw error;
    }
};

const cleanupFiles = async (filePaths: Array<{ path: string }>) => {
    await Promise.allSettled(
        filePaths.map(async file => {
            try {
                await fs.unlink(file.path);
            } catch (error) {
                console.warn(`Warning: Could not delete temp file ${file.path}:`, error.message);
            }
        })
    );
};

export const processLeadExport = async (message: ProcessLeadExportMessage, _job: Job<Document>) => {
    let filePaths: Array<{ filename: string; path: string; password?: string }> = [];

    try {
        const context = await initializeExportContext(message);

        filePaths = await generateExportFiles(context, message);

        const exportPassword = filePaths[0]?.password;
        await sendExportEmails(context, filePaths, exportPassword);
    } catch (error) {
        console.error('Unexpected error in processLeadExport:', error);

        // Send failure notification
        try {
            const context = await initializeExportContext(message);
            const emailContext = await getCompanyEmailContext(context.company);
            const transporter = await createCompanySMTPTransports(context.company);

            await sendLeadExportFail(
                {
                    i18n: context.i18n,
                    subject: context.t('emails:leadExportFail.subject', {
                        companyName: context.company.displayName,
                    }),
                    data: {
                        user: context.user,
                        requestDate: new Date(),
                        emailContext,
                    },
                    to: { name: context.user.displayName, address: context.user.email },
                },
                transporter,
                emailContext.sender
            );
        } catch (emailError) {
            console.error('Failed to send failure notification:', emailError);
        }
    } finally {
        if (filePaths.length > 0) {
            await cleanupFiles(filePaths);
        }
    }
};
