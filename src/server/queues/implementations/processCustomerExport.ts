import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { Document } from 'bson';
import { Job } from 'bull';
import dayjs from 'dayjs';
import { isEmpty, uniqBy } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import XlsxPopulate from 'xlsx-populate';
import { ModuleType, PasswordConfiguration } from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getLocalCustomerAggregatedFields } from '../../database/helpers/customers/shared';
import { isApplicationModule } from '../../database/helpers/modules';
import {
    createCompanySMTPTransports,
    sendCustomerExportFail,
    sendCustomerExportPassword,
    sendCustomerExportReady,
} from '../../emails';
import { setHeaderColumnsWidth } from '../../export/exportApplications/shared';
import { PeriodPayload } from '../../export/type';
import { getPassword } from '../../export/utils';
import createLoaders from '../../loaders';
import { CustomerSortingField, SortingOrder } from '../../schema/resolvers/enums';
import createI18nInstance from '../../utils/createI18nInstance';
import { getFormattedDate } from '../../utils/date';
import getSystemConsentSetting from '../../utils/excel/applications/system/setting/consent';
import getSystemKycSetting from '../../utils/excel/applications/system/setting/kyc';
import { SystemMainSettingKey } from '../../utils/excel/applications/system/setting/main';
import {
    consentDataByApplicationVersionIdFromApplicationJourneys,
    journeyByApplicationSuiteIdFromApplications,
} from '../../utils/excel/applications/system/utils';
import { uniqueObjectIds } from '../../utils/fp';
import getApplicationFileName from '../../utils/getApplicationFileName';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

export type ProcessCustomerExportMessage = {
    userId: ObjectId;
    moduleIds: string[];
    dealerIds: string[];
    period?: PeriodPayload;
    nonce?: string;
    languageId?: string;
    leadModuleName?: string;
};

const BATCH_SIZE = 500;

// Helper function to get the maximum number of application references
const getApplicationReferencesMax = (customers: any[]) => {
    let max = 0;
    customers?.forEach((customer: any) => {
        customer?.support?.referenceApplications?.forEach((application: any) => {
            const references = application.stages
                .map((listStage: string) => {
                    if (application?.[`${listStage}Stage`]) {
                        return application?.[`${listStage}Stage`]?.identifier;
                    }

                    return null;
                })
                .filter(Boolean);
            if (references?.length > max) {
                max = references?.length;
            }
        });
    });

    return max;
};

// Helper function to get application references
const getApplicationReferences = (customers: any[]) => {
    const max = getApplicationReferencesMax(customers);

    return Array.from({ length: max }).map((_, i) => ({
        key: `${SystemMainSettingKey.Reference}${i + 1}`,
        header: `Application Reference ${i + 1}`,
        getCellValue: ({ support }: any, _support: any, { key }: any) => {
            const filter = support.referenceApplications?.flatMap((app: any) => {
                const references = app?.stages
                    .map((listStage: string) => {
                        if (app?.[`${listStage}Stage`]) {
                            return app?.[`${listStage}Stage`]?.identifier;
                        }

                        return null;
                    })
                    .filter(Boolean);

                return references;
            });

            return filter?.[+key.slice(-1) - 1];
        },
    }));
};

export const processCustomerExport = async (message: ProcessCustomerExportMessage, _job: Job<Document>) => {
    const { userId, moduleIds: inputModuleIds, dealerIds: inputDealerIds, period, nonce: inputNonce } = message;

    try {
        const { collections } = await getDatabaseContext();
        const loaders = createLoaders();
        const { i18n } = await createI18nInstance();
        await i18n.loadNamespaces([
            'emails',
            'common',
            'applicationList',
            'auditTrails',
            'customerDetails',
            'applicationExcel',
        ]);
        const { t } = i18n;

        // Get the user who initiated the export
        const user = await collections.users.findOne({ _id: userId });
        if (!user) {
            throw new Error('User not found');
        }

        // Validate input module and input dealers
        const moduleIds = uniqueObjectIds(
            (inputModuleIds ?? []).filter(id => ObjectId.isValid(id)).map(id => new ObjectId(id))
        );

        const dealerIds = uniqueObjectIds(
            (inputDealerIds ?? []).filter(id => ObjectId.isValid(id)).map(id => new ObjectId(id))
        );

        if (isEmpty(moduleIds) || isEmpty(dealerIds)) {
            throw new Error('Invalid moduleIds or dealerIds');
        }

        // Get application modules
        const applicationModules = (
            await collections.modules
                .find({
                    _id: { $in: moduleIds },
                })
                .toArray()
        ).filter(isApplicationModule);

        if (isEmpty(applicationModules)) {
            throw new Error('No application modules found');
        }

        // Get company
        const company = await collections.companies.findOne({
            _id: applicationModules[0].companyId,
        });

        if (!company) {
            throw new Error('Company not found');
        }

        const start: Date = period?.start ? dayjs(period.start).startOf('day').toDate() : null;
        const end: Date = period?.end ? dayjs(period.end).endOf('day').toDate() : null;

        // Get the necessary pipeline components from listCustomers.ts
        // We need to use dynamic import to get access to the non-exported variables
        const listCustomersModule = await import('../../schema/resolvers/queries/customers/listCustomers');

        // Get the exported functions
        const { lookupModule, lookupLatestCustomer, getCustomerLatestFullNamePipelines } = listCustomersModule;

        // Define the pipeline stages that aren't exported but we need to use
        // These are copied directly from listCustomers.ts to ensure consistency
        const latestApplicationLookup = [
            {
                $lookup: {
                    from: 'applications',
                    localField: '_id',
                    foreignField: 'applicantId',
                    pipeline: [
                        { $match: { '_versioning.isLatest': true, isDraft: false } },
                        { $sort: { '_versioning.updatedAt': -1 } },
                        { $limit: 1 },
                    ],
                    as: 'latestApplication',
                },
            },
            {
                $unwind: { path: '$latestApplication', preserveNullAndEmptyArrays: true },
            },
        ];

        const latestLeadLookup = [
            {
                $lookup: {
                    from: 'leads',
                    localField: '_id',
                    foreignField: 'customerId',
                    pipeline: [
                        { $match: { '_versioning.isLatest': true, isDraft: false } },
                        { $sort: { '_versioning.updatedAt': -1 } },
                        { $limit: 1 },
                    ],
                    as: 'latestLead',
                },
            },
            {
                $unwind: { path: '$latestLead', preserveNullAndEmptyArrays: true },
            },
        ];

        const businessPartnerIdField = [
            {
                $set: {
                    businessPartnerIds: {
                        $cond: [
                            {
                                $and: [
                                    { $ne: ['$latestLead', null] },
                                    { $ifNull: ['$latestLead.capValues.businessPartnerId', false] },
                                ],
                            },
                            ['$latestLead.capValues.businessPartnerId'],
                            [],
                        ],
                    },
                },
            },
        ];

        // Define a filter function that matches the one in listCustomers.ts
        const getFilter = (rule: any) => {
            const filter: any = {};

            if (!rule) {
                return filter;
            }

            if (rule.creationDate) {
                const { start, end } = rule.creationDate;
                // Use the exact same date handling as in listCustomers.ts
                filter['_versioning.createdAt'] = { $gte: start, $lte: end };
            }

            if (rule.fullName) {
                filter.latestFullName = { $regex: rule.fullName, $options: 'i' };
            }

            if (rule.companyIds?.length) {
                filter['latestApplication.module.companyId'] = {
                    $in: rule.companyIds.map((id: string) => new ObjectId(id)),
                };
            }

            if (rule.leadDealerIds?.length) {
                filter.$or = [
                    { 'latestApplication.dealerId': { $in: rule.leadDealerIds.map((id: string) => new ObjectId(id)) } },
                    { 'latestLead.dealerId': { $in: rule.leadDealerIds.map((id: string) => new ObjectId(id)) } },
                ];
            }

            if (rule.leadModuleName) {
                filter['latestLead.module.displayName'] = { $regex: rule.leadModuleName, $options: 'i' };
            }

            return filter;
        };

        // Define a sort function that matches the one in listCustomers.ts
        const getSortCustomer = (rule: any) => {
            if (!rule) {
                return { '_versioning.createdAt': -1 };
            }

            if (rule.field === CustomerSortingField.CreationDate) {
                return { '_versioning.createdAt': rule.order === SortingOrder.Asc ? 1 : -1 };
            }

            return { '_versioning.createdAt': -1 };
        };

        // Create a filter object that matches what's used in the admin page
        // Check if we're dealing with a specific module or "All modules"
        // When moduleIds.length > 1, it means "All modules" is selected
        const isAllModulesSelected = moduleIds.length > 1;

        // Determine which leadModuleName to use
        let leadModuleNameFilter = {};

        // Only add leadModuleName filter if:
        // 1. A specific module name was passed in the message (from the UI), or
        // 2. A single module is selected (not "All modules") and we have its displayName
        if (message.leadModuleName) {
            // If leadModuleName is explicitly passed but it's not "All", use it
            if (message.leadModuleName !== 'All') {
                leadModuleNameFilter = { leadModuleName: message.leadModuleName };
            }
        } else if (!isAllModulesSelected && applicationModules[0]?.displayName) {
            // If a single module is selected, use its displayName
            leadModuleNameFilter = { leadModuleName: applicationModules[0].displayName };
        }

        const filter = {
            companyIds: [company._id.toString()],
            leadDealerIds: dealerIds.map(id => id.toString()),
            ...leadModuleNameFilter,
            ...(start &&
                end && {
                    creationDate: { start, end },
                }),
        };

        // Create a sort object that matches what's used in the admin page
        const sort = {
            field: CustomerSortingField.CreationDate,
            order: SortingOrder.Desc,
        };

        // Use the exact same pipeline approach as listCustomers.ts
        const pipeline = [
            {
                $match: {
                    isDeleted: false,
                    '_versioning.isLatest': true,
                    fields: { $exists: true, $ne: [] },
                },
            },
            ...latestApplicationLookup,
            ...latestLeadLookup,
            {
                $match: {
                    $or: [{ latestApplication: { $ne: null } }, { latestLead: { $ne: null } }],
                },
            },
            ...lookupModule,
            ...lookupLatestCustomer,
            ...getCustomerLatestFullNamePipelines(),
            ...businessPartnerIdField,
            { $match: getFilter(filter) },
            { $sort: getSortCustomer(sort) },
        ];

        const cursor = collections.customers.aggregate(pipeline).batchSize(BATCH_SIZE);

        // Process customers in batches
        const fetchNextBatch = async () => {
            const batch = [];

            const fetchNextDocument = async () => {
                if (batch.length >= BATCH_SIZE) {
                    return;
                }

                const hasNext = await cursor.hasNext();
                if (!hasNext) {
                    return;
                }

                const doc = await cursor.next();
                batch.push(doc);

                await fetchNextDocument();
            };

            await fetchNextDocument();

            return batch;
        };

        let allCustomers: any[] = [];
        let batchCount = 0;

        const processBatch = async (batch: any[]) => {
            if (batch.length === 0) {
                return [];
            }

            const currentBatchCount = batchCount++;

            try {
                const processedCustomers = await Promise.all(
                    batch.map(async (result: any) => {
                        // Skip if no latestCustomer
                        if (!result?.latestCustomer) {
                            return null;
                        }

                        const customer = getLocalCustomerAggregatedFields(result.latestCustomer);

                        // Skip if empty customer data
                        if (isEmpty(customer)) {
                            return null;
                        }

                        // Ensure we have a valid application to work with
                        const application = result?.latestApplication || {};

                        // Ensure referenceApplications is always an array
                        const referenceApplications = application ? [application] : [];

                        // Only process journey if we have applications
                        let consentData = null;

                        if (referenceApplications.length > 0 && application?._versioning?.suiteId) {
                            try {
                                const journeyMap = await journeyByApplicationSuiteIdFromApplications(
                                    referenceApplications,
                                    loaders
                                );

                                const consentDataByApplicationSuiteId =
                                    await consentDataByApplicationVersionIdFromApplicationJourneys(
                                        journeyMap.journeys,
                                        loaders
                                    );

                                // Safely get the suiteId
                                const suiteIdHex = application._versioning.suiteId.toHexString();
                                if (suiteIdHex) {
                                    consentData = consentDataByApplicationSuiteId?.[suiteIdHex];
                                }
                            } catch (err) {
                                // Silently handle journey data errors
                            }
                        }

                        return {
                            ...application,
                            // Ensure we have moduleId and _id
                            moduleId:
                                application.moduleId || result?.contextModule?._id || result?.latestLead?.moduleId,
                            _id: application._id || result?._id,
                            _versioning: application._versioning || result?._versioning,
                            support: {
                                customer,
                                referenceApplications,
                                consentData,
                            },
                        };
                    })
                );

                return processedCustomers.filter(Boolean);
            } catch (error) {
                throw new Error(`Error processing batch ${currentBatchCount}: ${error.message}`);
            }
        };

        const processAllBatches = async () => {
            const batch = await fetchNextBatch();
            if (batch.length === 0) {
                return;
            }

            const customers = await processBatch(batch);
            allCustomers = [...allCustomers, ...customers];

            await processAllBatches();
        };

        await processAllBatches();

        if (allCustomers.length === 0) {
            // Send failure email if no customers found
            const emailContext = await getCompanyEmailContext(company);
            const transporter = await createCompanySMTPTransports(company);

            await sendCustomerExportFail(
                {
                    i18n,
                    subject: t('emails:customerExportFail.subject', {
                        companyName: company.displayName,
                    }),
                    data: {
                        user,
                        emailContext,
                        requestDate: new Date(),
                    },
                    to: { name: user.displayName, address: user.email },
                },
                transporter,
                emailContext.sender
            );

            return;
        }

        // Generate Excel file
        const support = {
            t,
        };

        const [, allConsentColumns] = await getSystemConsentSetting(
            t,
            applicationModules
                .map(module => module._type !== ModuleType.SalesOfferModule && module?.agreementsModuleId)
                .filter(Boolean)
        );

        const allKycColumns = uniqBy(
            'key',
            (
                await Promise.all(
                    applicationModules.map(
                        module => module._type !== ModuleType.SalesOfferModule && getSystemKycSetting(module, t)
                    )
                )
            )
                .filter(Boolean)
                .flatMap(([, allKycColumns]) => allKycColumns)
        );

        const referenceColumns = getApplicationReferences(allCustomers);

        const companyTimeZone = company.timeZone;
        const customerCreationDates = {};

        const customerIds = allCustomers
            .map(item => item.applicantId)
            .filter(Boolean)
            .map(id => id.toString());

        if (customerIds.length > 0) {
            const objectIds = customerIds.filter(id => ObjectId.isValid(id)).map(id => new ObjectId(id));

            const customers = await collections.customers
                .find({
                    _id: { $in: objectIds },
                })
                .toArray();

            customers.forEach(customer => {
                if (customer._id && customer._versioning?.createdAt) {
                    customerCreationDates[customer._id.toString()] = customer._versioning.createdAt;
                }
            });
        }

        const createdAtColumn = {
            key: SystemMainSettingKey.CreatedAt,
            header: 'Date Created',
            getCellValue: (item: any, { t }: { t: any; timeZonesMap: any }) => {
                if (!item || !item.applicantId) {
                    return '';
                }

                try {
                    // Get the customer's creation date from the map
                    const customerId = item.applicantId.toString();
                    const createdAt = customerCreationDates[customerId];

                    if (!createdAt) {
                        // If we couldn't find the customer's creation date, fall back to the item's creation date
                        return item._versioning?.createdAt
                            ? getFormattedDate(t, item._versioning.createdAt, companyTimeZone)
                            : '';
                    }

                    return getFormattedDate(t, createdAt, companyTimeZone);
                } catch (err) {
                    return '';
                }
            },
        };

        const settings = [createdAtColumn, ...allKycColumns, ...referenceColumns, ...allConsentColumns];
        const header = [[...settings.filter(Boolean).map(setting => setting.header)]];

        const body = allCustomers
            .map(item =>
                !isEmpty(item)
                    ? settings
                          .filter(Boolean)
                          .flatMap(setting => setting.getCellValue(item, support, { key: setting.key }))
                    : null
            )
            .filter(Boolean);

        const rows = [...header, ...body];
        const sheetName = 'Default Worksheet';

        const workbook = await XlsxPopulate.fromBlankAsync();
        const worksheet = workbook.sheet(0).name(sheetName);
        worksheet.cell('A1').value(rows);
        setHeaderColumnsWidth(worksheet, rows[0]);

        const nonce = inputNonce ?? nanoid();
        const password = await getPassword(nonce);
        const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;
        const filename = getApplicationFileName(company.legalName.defaultValue, period, null, 'Customer_Records');

        const buffer = await workbook.outputAsync({ ...(isPasswordProtected && { password }) });

        const tempDir = os.tmpdir();
        const filePath = path.join(tempDir, filename);
        fs.writeFileSync(filePath, buffer);

        const emailContext = await getCompanyEmailContext(company);
        const transporter = await createCompanySMTPTransports(company);

        // Send the file attachment email
        await sendCustomerExportReady(
            {
                i18n,
                subject: t('emails:customerExportReady.subject', {
                    companyName: company.displayName,
                }),
                data: {
                    user,
                    requestDate: new Date(),
                    emailContext,
                },
                to: { name: user.displayName, address: user.email },
                attachments: [
                    {
                        filename,
                        path: filePath,
                        contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    },
                ],
            },
            transporter,
            emailContext.sender
        );

        // Send the password in a separate email if password protection is enabled
        if (isPasswordProtected) {
            await sendCustomerExportPassword(
                {
                    i18n,
                    subject: t('emails:customerExportPassword.subject', {
                        companyName: company.displayName,
                    }),
                    data: {
                        user,
                        password,
                        emailContext,
                    },
                    to: { name: user.displayName, address: user.email },
                },
                transporter,
                emailContext.sender
            );
        }

        // Clean up the temporary file
        fs.unlinkSync(filePath);
    } catch (error) {
        console.error('Unexpected error in processCustomerExport:', error);

        try {
            const { collections } = await getDatabaseContext();
            const { i18n } = await createI18nInstance();
            await i18n.loadNamespaces([
                'emails',
                'common',
                'applicationList',
                'auditTrails',
                'customerDetails',
                'applicationExcel',
            ]);
            const { t } = i18n;

            const user = await collections.users.findOne({ _id: userId });
            if (!user) {
                return;
            }

            const applicationModule = await collections.modules.findOne({
                _id: { $in: inputModuleIds.map(id => new ObjectId(id)) },
            });
            if (!applicationModule) {
                return;
            }

            const company = await collections.companies.findOne({
                _id: applicationModule.companyId,
            });
            if (!company) {
                return;
            }

            const emailContext = await getCompanyEmailContext(company);
            const transporter = await createCompanySMTPTransports(company);

            await sendCustomerExportFail(
                {
                    i18n,
                    subject: t('emails:customerExportFail.subject', {
                        companyName: company.displayName,
                    }),
                    data: {
                        user,
                        emailContext,
                        requestDate: new Date(),
                    },
                    to: { name: user.displayName, address: user.email },
                },
                transporter,
                emailContext.sender
            );
        } catch (emailError) {
            console.error('Error sending failure email:', emailError);
        }
    }
};
