import { i18n } from 'i18next';
import { groupBy, isNil, last, xor } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../../core/storage';
import {
    FinderApplication,
    FinderApplicationPrivateModule,
    FinderApplicationPublicModule,
    FinderVehicle,
    ModuleType,
    PasswordConfiguration,
    getKYCPresetsForCustomerModule,
} from '../../../database';
import {
    getCustomerEmail,
    getCustomerFullNameWithTitle,
    getCustomerName,
    getCustomerTitleOrSalutation,
} from '../../../database/helpers/customers';
import { getPreviousApplicationStages } from '../../../database/queries/application';
import { AudienceMessage, createCompanySMTPTransports, sendFinderEmail, sendPasswordToCustomer } from '../../../emails';
import getTranslatedEmailContent from '../../../emails/utils/getTranslatedEmailContent';
import { hasAppointmentScenario } from '../../../journeys/common/helpers';
import { Loaders } from '../../../loaders';
import {
    DEFAULT_STAGES,
    getApplicationIdentifier,
    getApplicationStage,
    getApplicationStages,
    sortStages,
} from '../../../utils/application';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../../utils/getEdmEmailFooterContext';
import { getApplicationDataFromApplication } from '../sendProceedWithCustomerEmail';
import { getFinderVehicleLatestListing } from '../shared';
import checkIsBankViewable from '../shared/checkIsBankViewable';
import getApplicationStagesLink from '../shared/getApplicationStagesLink';
import getEmailContentForApplicationStages from '../shared/getEmailContentForApplicationStages';
import {
    SendApplicationSubmissionMail,
    canSendEmailToCustomer,
    canSendPDFToCustomer,
    prepareApplicationSubmissionAttachment,
    prepareAttachmentsForEmail,
} from './shared';

const sendFinderApplicationSubmissionMail = async (
    application: FinderApplication,
    loaders: Loaders,
    i18n: i18n,
    message: SendApplicationSubmissionMail
) => {
    const { t } = i18n;
    const applicationModule = (await loaders.moduleById.load(application.moduleId)) as
        | FinderApplicationPublicModule
        | FinderApplicationPrivateModule;
    if (
        applicationModule?._type !== ModuleType.FinderApplicationPublicModule &&
        applicationModule?._type !== ModuleType.FinderApplicationPrivateModule
    ) {
        throw new Error('Module type is not supported');
    }

    const [vehicleId, customerId] = getApplicationDataFromApplication(application);

    const [
        variantData,
        applicant,
        bank,
        company,
        dealer,
        financeProduct,
        journey,
        insurer,
        promoCode,
        insuranceProduct,
        lead,
    ] = await Promise.all([
        loaders.vehicleById.load(vehicleId) as Promise<FinderVehicle>,
        loaders.customerById.load(customerId),
        application.bankId ? loaders.bankById.load(application.bankId) : Promise.resolve(null),
        loaders.companyById.load(applicationModule.companyId),
        loaders.dealerById.load(application.dealerId),
        application.financing?.financeProductId
            ? loaders.financeProductById.load(application.financing.financeProductId)
            : Promise.resolve(null),
        loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId),
        application.insurancing?.insurerId
            ? loaders.insurerById.load(application.insurancing.insurerId)
            : Promise.resolve(null),
        application.promoCodeId ? loaders.promoCodeById.load(application.promoCodeId) : Promise.resolve(null),
        application.insurancing?.insuranceProductId
            ? loaders.insuranceProductById.load(application.insurancing.insuranceProductId)
            : Promise.resolve(null),
        loaders.leadById.load(application.leadId),
    ]);

    const variant = await getFinderVehicleLatestListing(variantData);

    const customerModule = await loaders.moduleById.load(applicant.moduleId);
    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('Customer Module not found ');
    }
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerName = getCustomerName(t, applicant, company, kycPresets);

    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const customerTitle = getCustomerTitleOrSalutation(t, applicant);
    const customerEmail = getCustomerEmail(t, applicant);
    const dealerEmailContext = await getDealerEmailContext(dealer);

    const transporter = await createCompanySMTPTransports(company);

    const dealerId = dealer._id.toHexString();

    const [emailContext, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getEdmEmailFooterContext({ company }),
    ]);

    const previousStages = await getPreviousApplicationStages(application._versioning.suiteId);
    const currentStages = xor(previousStages ?? [], application.stages);

    const moduleEmailContent = getEmailContentForApplicationStages(
        applicationModule.emailContents?.submitOrder,
        currentStages
    );

    const introImageUrl = moduleEmailContent.introImage
        ? await getUrlForUpload(moduleEmailContent.introImage)
        : last(variant.listing.vehicle.images.edges[0].node.variants).url;

    const systemSubject = t('emails:finderEmail.porscheFinder.subject');
    const emailSubject = moduleEmailContent.subject
        ? getTranslatedEmailContent({
              i18nLanguage: i18n.language,
              dealerTranslationText: moduleEmailContent.subject,
              preferredDealerId: dealerId,
              systemDefaultValue: systemSubject,
          })
        : systemSubject;

    const isBankViewable = await checkIsBankViewable(variant._versioning.suiteId, application.moduleId);

    const applicationStagesLink = await getApplicationStagesLink(application, lead);

    const onlyAppointmentScenario = hasAppointmentScenario(application.scenarios) && application.scenarios.length === 1;

    switch (message.audience) {
        case AudienceMessage.Customer: {
            if (!canSendEmailToCustomer(application, bank, insurer, journey, message.isResubmit, applicant, t)) {
                break;
            }

            const [canSendApplicationPDF, canSendInsurancePDF] = canSendPDFToCustomer({
                application,
                journey,
                isResubmit: message.isResubmit,
                insurer,
                bank,
                applyNewSubmission: message.applyNewSubmission,
                currentStages: message.currentStages,
            });

            const stages = (() => {
                if (message.isResubmit && message.resubmitStage) {
                    return [message.resubmitStage];
                }

                if (!isNil(message.applyNewSubmission) && message.currentStages.length) {
                    return message.currentStages;
                }

                return DEFAULT_STAGES;
            })();
            const stage = getApplicationStage(application, lead, stages);
            const submissionType = t(`emails:component.applicationStage.${stage?.stage}`);
            const applicationId = stage?.value?.identifier;
            const attachmentInfo = await prepareApplicationSubmissionAttachment(
                application,
                applicant,
                company,
                i18n,
                canSendApplicationPDF,
                canSendInsurancePDF
            );

            const attachments = prepareAttachmentsForEmail(
                application,
                attachmentInfo,
                emailContext,
                stage?.value?.identifier
            );

            const systemIntro = t('emails:finderEmail.porscheFinder.intro');
            const introTitle = (
                moduleEmailContent.introTitle
                    ? getTranslatedEmailContent({
                          i18nLanguage: i18n.language,
                          dealerTranslationText: moduleEmailContent.introTitle,
                          preferredDealerId: dealerId,
                          systemDefaultValue: systemIntro,
                      })
                    : systemIntro
            )?.replaceAll('\n', '<br />');

            const isSummaryVehicleVisible = moduleEmailContent?.isSummaryVehicleVisible;

            const systemContent = t('emails:finderEmail.porscheFinder.content', {
                title: customerTitle || '',
                customerName,
                carCondition: variant.listing.vehicle.condition.localize,
            });
            const contentText = (
                moduleEmailContent.contentText
                    ? getTranslatedEmailContent({
                          i18nLanguage: i18n.language,
                          dealerTranslationText: moduleEmailContent.contentText,
                          preferredDealerId: dealerId,
                          params: {
                              customer_fullname: customerName,
                          },
                          systemDefaultValue: systemContent,
                      })
                    : systemContent
            )?.replaceAll('\n', '<br />');

            !onlyAppointmentScenario &&
                (await sendFinderEmail(
                    {
                        i18n,
                        data: {
                            emailContext,
                            edmEmailFooterContext,
                            dealerEmailContext,

                            variant,
                            dealer,
                            application,
                            applicationModule,
                            financeProduct,
                            journey,
                            applicant,
                            bank,
                            insurer,
                            promoCode,
                            customerModule,

                            insuranceProduct,
                            submissionType,
                            introImageUrl,
                            applicationId,
                            isBankViewable,

                            audience: AudienceMessage.Customer,
                            introTitle,
                            contentText,
                            applyNewSubmission: message.applyNewSubmission,
                            currentStages: message.currentStages,
                            isSummaryVehicleVisible,
                        },
                        to: { name: customerFullName, address: customerEmail },
                        subject: emailSubject,
                        ...(!!attachments.length && {
                            attachments,
                        }),
                    },
                    transporter,
                    emailContext.sender
                ));

            // Only send password to customer when configuration is random
            if (attachmentInfo?.password && company.passwordConfiguration === PasswordConfiguration.Random) {
                await sendPasswordToCustomer(
                    {
                        i18n,
                        subject: t('emails:sendPasswordToCustomer.subject', {
                            companyName: emailContext.companyName,
                        }),
                        data: { emailContext, application, password: attachmentInfo.password, lead },
                        to: { name: customerFullName, address: customerEmail },
                    },
                    transporter,
                    emailContext.sender
                );
            }

            break;
        }

        case AudienceMessage.Salesperson: {
            const stages = (() => {
                if (message.isResubmit && message.resubmitStage) {
                    return [getApplicationStage(application, lead, message.resubmitStage)];
                }

                if (!isNil(message.applyNewSubmission) && message.currentStages.length) {
                    return [getApplicationStage(application, lead, message.currentStages)];
                }

                return getApplicationStages(application, lead);
            })();
            const availableStages = stages.filter(stage => !!stage?.value?.assigneeId);
            const stagesByAssigneeId = groupBy(stage => stage.value.assigneeId.toHexString(), availableStages);
            await Promise.all(
                Object.entries(stagesByAssigneeId).map(async ([key, value]) => {
                    if (!ObjectId.isValid(key) || value.length === 0) {
                        return;
                    }

                    const stages = sortStages(value.map(({ stage }) => stage));
                    const assignee = await loaders.userById.load(new ObjectId(key));
                    const stage = getApplicationStage(application, lead, stages);
                    const submissionType = t(`emails:component.applicationStage.${stage?.stage}`);
                    const applicationId = getApplicationIdentifier(application, lead, stages);
                    !onlyAppointmentScenario &&
                        (await sendFinderEmail(
                            {
                                i18n,
                                data: {
                                    emailContext,
                                    edmEmailFooterContext,
                                    dealerEmailContext,

                                    dealer,
                                    variant,
                                    application,
                                    applicationModule,
                                    financeProduct,
                                    journey,
                                    applicant,
                                    bank,
                                    insurer,
                                    promoCode,

                                    submissionType,
                                    introImageUrl,
                                    applicationId,
                                    isBankViewable,

                                    audience: AudienceMessage.Salesperson,
                                    applicationStagesLink,
                                    applyNewSubmission: message.applyNewSubmission,
                                    customerModule,
                                    currentStages: message.currentStages,

                                    insuranceProduct,
                                },
                                to: { name: assignee.email, address: assignee.email },
                                subject: t(`emails:salesPersonSubmissionConfirmation.subject`, {
                                    companyName: emailContext.companyName,
                                    identifier: stage?.value?.identifier,
                                    customerName: customerFullName,
                                }),
                            },
                            transporter,
                            emailContext.sender
                        ));
                })
            );

            break;
        }
    }
};

export default sendFinderApplicationSubmissionMail;
