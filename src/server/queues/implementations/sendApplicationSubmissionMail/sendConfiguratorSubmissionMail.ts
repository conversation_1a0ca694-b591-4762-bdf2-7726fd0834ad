import { i18n } from 'i18next';
import { groupBy, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ConfiguratorApplication,
    ConfiguratorModule,
    LocalCustomerManagementModule,
    LocalVariant,
    ModuleType,
} from '../../../database/documents';
import { getKYCPresetsForCustomerModule } from '../../../database/helpers';
import { getCustomerEmail, getCustomerFullNameWithTitle } from '../../../database/helpers/customers';
import { AudienceMessage, createCompanySMTPTransports, sendSubmitConfiguratorApplication } from '../../../emails';
import getTranslatedEmailContent from '../../../emails/utils/getTranslatedEmailContent';
import { Loaders } from '../../../loaders';
import { DEFAULT_STAGES, getApplicationStage, getApplicationStages, sortStages } from '../../../utils/application';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getConfiguratorEmailContext from '../../../utils/getConfiguratorEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../../utils/getEdmEmailFooterContext';
import checkIsBankViewable from '../shared/checkIsBankViewable';
import getApplicationStagesLink from '../shared/getApplicationStagesLink';
import { SendApplicationSubmissionMail, canSendEmailToCustomer } from './shared';

const sendConfiguratorSubmissionMail = async (
    application: ConfiguratorApplication,
    loaders: Loaders,
    i18n: i18n,
    message: SendApplicationSubmissionMail
) => {
    const configuratorModule = (await loaders.moduleById.load(application.moduleId)) as ConfiguratorModule;
    if (configuratorModule._type !== ModuleType.ConfiguratorModule) {
        throw new Error('Module type is not Configurator Module');
    }

    // load supporting documents
    const [
        variant,
        applicant,
        company,
        bank,
        financeProduct,
        journey,
        dealer,
        variantConfigurator,
        insurer,
        insuranceProduct,
        lead,
    ] = await Promise.all([
        loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>,
        loaders.customerById.load(application.applicantId),
        loaders.companyById.load(configuratorModule.companyId),
        application.bankId ? loaders.bankById.load(application.bankId) : Promise.resolve(null),
        application.financing?.financeProductId
            ? loaders.financeProductById.load(application.financing.financeProductId)
            : Promise.resolve(null),
        loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId),
        loaders.dealerById.load(application.dealerId),
        loaders.loadVariantConfiguratorById.load(application.configuratorId),

        application.insurancing?.insurerId
            ? loaders.insurerById.load(application.insurancing?.insurerId)
            : Promise.resolve(null),
        application.insurancing?.insuranceProductId
            ? loaders.insuranceProductById.load(application.insurancing?.insuranceProductId)
            : Promise.resolve(null),
        loaders.leadById.load(application.leadId),
    ]);

    const customerModule = (await loaders.moduleById.load(applicant.moduleId)) as LocalCustomerManagementModule;
    // load mail context
    const [emailContext, dealerEmailContext, configuratorEmailContext, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getDealerEmailContext(dealer),
        getConfiguratorEmailContext({
            colorSectionTitle: 'emails:configurator.general.colorSection',
            packageSectionTitle: 'emails:configurator.general.packageSection',
            application,
            variantConfigurator,
            configuratorModule,
        }),
        getEdmEmailFooterContext({ company }),
    ]);

    const transporter = await createCompanySMTPTransports(company);

    const { t } = i18n;

    const dealerId = dealer._id.toHexString();

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const isBankViewable = await checkIsBankViewable(variant._versioning.suiteId, application.moduleId);
    switch (message.audience) {
        case AudienceMessage.Customer: {
            if (!canSendEmailToCustomer(application, bank, insurer, journey, message.isResubmit, applicant, t)) {
                return;
            }
            const recipient = getCustomerEmail(t, applicant);
            const stages = (() => {
                if (message.isResubmit && message.resubmitStage) {
                    return [message.resubmitStage];
                }

                if (!isNil(message.applyNewSubmission) && message.currentStages.length > 0) {
                    return message.currentStages;
                }

                return DEFAULT_STAGES;
            })();

            const stage = getApplicationStage(application, lead, stages);
            const submissionType = t(`emails:component.applicationStage.${stage?.stage}`);
            const applicationId = stage?.value?.identifier;
            const systemSubject = t('emails:configurator.submitOrder.subject', {
                companyName: emailContext.companyName,
                submissionType,
            });
            const subject = getTranslatedEmailContent({
                i18nLanguage: i18n.language,
                dealerTranslationText: configuratorEmailContext.emailContents.submitOrder.subject,
                preferredDealerId: dealerId,
                params: {
                    companyName: emailContext.companyName,
                    variantName: variant.name.defaultValue,
                    submissionType,
                    applicationId,
                    customerName: customerFullName,
                },
                systemDefaultValue: systemSubject,
            });

            const systemIntro = t('emails:configurator.submitOrder.introTitle');
            const introTitle = (
                configuratorEmailContext.emailContents.submitOrder.introTitle
                    ? getTranslatedEmailContent({
                          i18nLanguage: i18n.language,
                          dealerTranslationText: configuratorEmailContext.emailContents.submitOrder.introTitle,
                          preferredDealerId: dealerId,
                          systemDefaultValue: systemIntro,
                      })
                    : systemIntro
            )?.replaceAll('\n', '<br />');

            const systemContent = t('emails:configurator.submitOrder.contentText');
            const contentText = (
                configuratorEmailContext.emailContents.submitOrder.contentText
                    ? getTranslatedEmailContent({
                          i18nLanguage: i18n.language,
                          dealerTranslationText: configuratorEmailContext.emailContents.submitOrder.contentText,
                          preferredDealerId: dealerId,
                          systemDefaultValue: systemContent,
                      })
                    : systemContent
            )?.replaceAll('\n', '<br />');

            await sendSubmitConfiguratorApplication(
                {
                    i18n,
                    data: {
                        emailContext,
                        dealerEmailContext,
                        configuratorEmailContext,
                        edmEmailFooterContext,

                        application,
                        variant,
                        applicant,
                        bank,
                        financeProduct,
                        journey,
                        dealer,
                        applicationModule: configuratorModule,
                        insurer,
                        insuranceProduct,

                        audience: message.audience,
                        submissionType,
                        subject,
                        applicationId,
                        isBankViewable,
                        isRequestForFinancing: message.isRequestForFinancing,
                        applyNewSubmission: message.applyNewSubmission,
                        customerModule,
                        currentStages: message.currentStages,
                        introTitle,
                        contentText,
                    },
                    to: { name: recipient, address: recipient },
                    subject,
                },
                transporter,
                emailContext.sender
            );

            break;
        }

        case AudienceMessage.Salesperson: {
            const applicationStagesLink = await getApplicationStagesLink(application, lead);
            const stages = (() => {
                if (message.isResubmit && message.resubmitStage) {
                    return [getApplicationStage(application, lead, message.resubmitStage)];
                }

                if (!isNil(message.applyNewSubmission) && message.currentStages.length) {
                    return [getApplicationStage(application, lead, message.currentStages)];
                }

                return getApplicationStages(application, lead);
            })();
            const availableStages = stages.filter(stage => !!stage?.value?.assigneeId);
            const stagesByAssigneeId = groupBy(stage => stage.value.assigneeId.toHexString(), availableStages);
            await Promise.all(
                Object.entries(stagesByAssigneeId).map(async ([key, value]) => {
                    if (!ObjectId.isValid(key) || value.length === 0) {
                        return;
                    }

                    const stages = sortStages(value.map(({ stage }) => stage));
                    const assignee = await loaders.userById.load(new ObjectId(key));
                    const stage = getApplicationStage(application, lead, stages);
                    const recipient = assignee.email;
                    const submissionType = t(`emails:component.applicationStage.${stage?.stage}`);
                    const applicationId = stage?.value?.identifier;
                    const subject = message.isRequestForFinancing
                        ? t('emails:configurator.submitOrder.requestForFinancingSubjectSalesperson', {
                              companyName: emailContext.companyName,
                              variantName: variant.name.defaultValue,
                              submissionType,
                              applicationId,
                              customerName: customerFullName,
                          })
                        : t('emails:configurator.submitOrder.subjectSalesperson', {
                              companyName: emailContext.companyName,
                              variantName: variant.name.defaultValue,
                              submissionType,
                              applicationId,
                              customerName: customerFullName,
                          });

                    await sendSubmitConfiguratorApplication(
                        {
                            i18n,
                            data: {
                                emailContext,
                                dealerEmailContext,
                                configuratorEmailContext,
                                edmEmailFooterContext,

                                application,
                                variant,
                                applicant,
                                bank,
                                financeProduct,
                                journey,
                                dealer,
                                applicationModule: configuratorModule,
                                insurer,
                                customerModule,
                                audience: AudienceMessage.Salesperson,
                                submissionType,

                                insuranceProduct,

                                subject,
                                applicationId,
                                isBankViewable,
                                isRequestForFinancing: message.isRequestForFinancing,
                                applicationStagesLink,
                                applyNewSubmission: message.applyNewSubmission,
                                currentStages: message.currentStages,
                            },
                            to: { name: recipient, address: recipient },
                            subject,
                        },
                        transporter,
                        emailContext.sender
                    );
                })
            );

            break;
        }

        default:
            break;
    }
};

export default sendConfiguratorSubmissionMail;
