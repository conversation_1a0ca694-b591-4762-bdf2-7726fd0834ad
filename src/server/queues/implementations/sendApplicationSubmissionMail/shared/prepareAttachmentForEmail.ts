import { i18n } from 'i18next';
import { get, isNil } from 'lodash/fp';
import { nanoid } from 'nanoid';
import { getFileStream } from '../../../../core/storage';
import {
    Application,
    ApplicationDocumentKind,
    ApplicationJourney,
    ApplicationKind,
    ApplicationStage,
} from '../../../../database/documents/Applications';
import { Bank } from '../../../../database/documents/Bank';
import { Company, PasswordConfiguration } from '../../../../database/documents/Company';
import { Customer } from '../../../../database/documents/Customer';
import { Insurer } from '../../../../database/documents/Insurer';
import { ApplicationModule } from '../../../../database/documents/modules';
import { EmailContext } from '../../../../database/helpers/settings';
import getAgreementDocument, { TypeAgreementPDF } from '../../../../journeys/helper/getAgreementDocument';
import getInsuranceAgreementPdf from '../../../../journeys/helper/getInsuranceAgreementPdf';
import { streamToBuffer } from '../../../../utils';
import getEncryptedZip from '../../../../utils/getEncryptedZip';
import {
    canSendApplicationPDFToCustomer,
    canSendInsurancePDFToCustomer,
    customerBirthdayPresent,
    customerIdentityNumberPresent,
    getCustomerBirthday,
    getCustomerIdentityNumber,
} from './utils';

export type AttachmentInfo = null | {
    attaches: { source: Buffer; filename: string; kind: ApplicationDocumentKind }[];
    password: string | null;
    hint: string | null;
    encryptedZip: Buffer | null;
    passwordConfiguration: PasswordConfiguration;
};

export const prepareApplicationSubmissionAttachment = async (
    application: Application,
    customer: Customer,
    company: Company,
    { t }: i18n,
    canSendApplicationPDF?: boolean,
    canSendInsurancePDF?: boolean,
    canSendTestDrivePDF?: boolean
): Promise<AttachmentInfo> => {
    const attaches = [];
    if (canSendTestDrivePDF) {
        const agreement = getAgreementDocument(application, TypeAgreementPDF.TestDrive);
        const agreementPdf = agreement ? await streamToBuffer(await getFileStream(agreement)) : null;

        if (agreement?.filename) {
            attaches.push({
                source: agreementPdf,
                filename: agreement.filename,
                kind: ApplicationDocumentKind.TestDriveAgreement,
            });
        }
    }
    if (canSendApplicationPDF) {
        const agreement = getAgreementDocument(application);
        const agreementPdf = agreement ? await streamToBuffer(await getFileStream(agreement)) : null;

        if (agreement?.filename) {
            attaches.push({
                source: agreementPdf,
                filename: agreement.filename,
                kind: ApplicationDocumentKind.Agreement,
            });
        }
    }

    if (canSendInsurancePDF) {
        const insuranceAgreementDocument = getInsuranceAgreementPdf(application);
        const insuranceAgreementPdf = await getFileStream(insuranceAgreementDocument);

        if (insuranceAgreementDocument?.filename) {
            attaches.push({
                source: insuranceAgreementPdf,
                filename: insuranceAgreementDocument.filename,
                kind: ApplicationDocumentKind.InsuranceAgreement,
            });
        }
    }

    if (attaches.length === 0) {
        return null;
    }

    let password: string | null = null;
    let passwordHint: string | null = null;
    let encryptedZip: Buffer | null = null;

    const { passwordConfiguration } = company;

    switch (passwordConfiguration) {
        case PasswordConfiguration.Random: {
            password = nanoid();
            passwordHint = t('emails:customerSubmissionConfirmation.hint.random');
            break;
        }

        case PasswordConfiguration.DateOfBirth: {
            const { fields } = customer;
            const dobFormat = 'YYYYMMDD';

            // if birthday is not present
            if (!customerBirthdayPresent(fields)) {
                password = nanoid();
                passwordHint = t('emails:customerSubmissionConfirmation.hint.random');
            } else {
                const customerBirthday = getCustomerBirthday(fields, company, dobFormat);
                password = customerBirthday;
                passwordHint = t('emails:customerSubmissionConfirmation.hint.birthdayOnly', { dobFormat });
            }
            break;
        }

        case PasswordConfiguration.IdentityAndDateOfBirth: {
            const { fields } = customer;

            // if neither fields are present
            if (!customerIdentityNumberPresent(fields) && !customerBirthdayPresent(fields)) {
                password = nanoid();
                passwordHint = t('emails:customerSubmissionConfirmation.hint.random');
            }

            // only identity number present
            if (customerIdentityNumberPresent(fields) && !customerBirthdayPresent(fields)) {
                const customerIdentityNumber = getCustomerIdentityNumber(fields);
                password = customerIdentityNumber;
                passwordHint = t('emails:customerSubmissionConfirmation.hint.identityNumberOnly');
            }

            // only birthday present
            if (customerBirthdayPresent(fields) && !customerIdentityNumberPresent(fields)) {
                const customerBirthday = getCustomerBirthday(fields, company);
                password = customerBirthday;
                passwordHint = t('emails:customerSubmissionConfirmation.hint.birthdayOnly', { dobFormat: 'DDMMYYYY' });
            }

            // both fields present
            if (customerIdentityNumberPresent(fields) && customerBirthdayPresent(fields)) {
                const customerIdentityNumber = getCustomerIdentityNumber(fields);
                const customerBirthday = getCustomerBirthday(fields, company);
                password = `${customerIdentityNumber}${customerBirthday}`;
                passwordHint = t('emails:customerSubmissionConfirmation.hint.birthdayAndIdentityNumber');
            }
        }
    }

    if (password) {
        encryptedZip = await getEncryptedZip(attaches, password);
    }

    const hint = [t('emails:customerSubmissionConfirmation.hint.attachment'), passwordHint].filter(Boolean).join(' ');

    return { attaches, encryptedZip, passwordConfiguration, password, hint };
};

type CanSendPDFToCustomerProps = {
    application?: Application;
    journey?: ApplicationJourney;
    isResubmit?: boolean;
    bank?: Bank;
    insurer?: Insurer;
    applicationModule?: ApplicationModule;
    applyNewSubmission?: ApplicationJourney['applyNewSubmission'];
    currentStages?: ApplicationStage[];
};

/**
 * @returns [canSendApplicationPDF, canSendInsurancePDF]
 */
export const canSendPDFToCustomer = ({
    application,
    journey,
    isResubmit,
    insurer,
    bank,
    applicationModule,
    applyNewSubmission,
    currentStages,
}: CanSendPDFToCustomerProps) => {
    switch (application.kind) {
        case ApplicationKind.Configurator:
        case ApplicationKind.Event:
        case ApplicationKind.Standard:
        case ApplicationKind.Finder: {
            const isApplyNewSubmission = !isNil(currentStages) ? currentStages.length : false;
            if (isApplyNewSubmission && !isNil(applyNewSubmission)) {
                return [
                    currentStages.includes(ApplicationStage.Financing) &&
                        !!applyNewSubmission?.financingStage?.isReceived,
                    currentStages.includes(ApplicationStage.Insurance) &&
                        !!applyNewSubmission?.insuranceStage?.isReceived,
                ];
            }

            return [
                canSendApplicationPDFToCustomer(application, bank, journey, isResubmit),
                canSendInsurancePDFToCustomer(application, insurer, isResubmit),
            ];
        }

        case ApplicationKind.Mobility:
            return [get('sendPDFToCustomer', applicationModule), false];

        default:
            return [false, false];
    }
};

export type EmailAttachment = {
    content: Buffer;
    filename: string;
    contentType?: string;
};

export const prepareAttachmentsForEmail = (
    application: Application,
    attachmentInfo: AttachmentInfo,
    emailContext: EmailContext,
    stageIdentifier?: string
): EmailAttachment[] => {
    const attachments = [];
    if (attachmentInfo?.attaches?.length > 0) {
        switch (application.kind) {
            case ApplicationKind.Standard:
            case ApplicationKind.Finder:
            case ApplicationKind.Event: {
                if (attachmentInfo?.encryptedZip) {
                    attachments.push({
                        content: attachmentInfo.encryptedZip,
                        filename: `${emailContext.companyName}-${stageIdentifier}.zip`,
                    });
                } else {
                    attachments.push(
                        ...attachmentInfo.attaches.map(i => ({
                            content: i.source,
                            filename: `${emailContext.companyName}-${i.filename}`,
                        }))
                    );
                }

                break;
            }

            // mobility journey only can have one or zero PDF. because mobility do not have insurance
            case ApplicationKind.Mobility: {
                const attachmentFormat = !isNil(attachmentInfo) && attachmentInfo.encryptedZip ? 'zip' : 'pdf';
                // eslint-disable-next-line max-len
                const filename = `${emailContext.companyName}-${application?.mobilityStage?.identifier}.${attachmentFormat}`;
                const content = attachmentInfo?.encryptedZip || attachmentInfo?.attaches[0].source;
                const contentType = `application/${attachmentFormat}`;
                attachments.push({
                    content,
                    contentType,
                    filename,
                });

                break;
            }
        }
    }

    return attachments;
};
