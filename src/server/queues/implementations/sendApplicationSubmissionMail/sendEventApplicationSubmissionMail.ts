import { i18n } from 'i18next';
import { groupBy } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    EventApplication,
    EventApplicationModule,
    LocalCustomerManagementModule,
    LocalVariant,
    ModuleType,
} from '../../../database/documents';
import { getKYCPresetsForCustomerModule } from '../../../database/helpers';
import {
    getCustomerEmail,
    getCustomerNameInfo,
    getCustomerFullNameWithTitle,
} from '../../../database/helpers/customers';
import { AudienceMessage, createCompanySMTPTransports, sendSubmitEventApplication } from '../../../emails';
import getTranslatedEmailContent from '../../../emails/utils/getTranslatedEmailContent';
import { hasAppointmentScenario } from '../../../journeys/common/helpers';
import { Loaders } from '../../../loaders';
import { DEFAULT_STAGES, getApplicationStage, getApplicationStages, sortStages } from '../../../utils/application';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../../utils/getEdmEmailFooterContext';
import getEventEmailContext, { getEventEmailContextSalesperson } from '../../../utils/getEventEmailContext';
import getApplicationDetailsLink from '../shared/getApplicationDetailsLink';
import getApplicationStagesLink from '../shared/getApplicationStagesLink';
import { SendApplicationSubmissionMail, isAbleSendTestDriveEmail } from './shared';

const sendEventApplicationSubmissionMail = async (
    application: EventApplication,
    loaders: Loaders,
    i18n: i18n,
    message: SendApplicationSubmissionMail
) => {
    const eventModule = (await loaders.moduleById.load(application.moduleId)) as EventApplicationModule;
    if (eventModule?._type !== ModuleType.EventApplicationModule) {
        throw new Error('Module type is not Configurator Module');
    }

    // load supporting documents
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [variant, applicant, company, journey, dealer, event, guarantor, lead] = await Promise.all([
        application.vehicleId ? (loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>) : null,
        loaders.customerById.load(application.applicantId),
        loaders.companyById.load(eventModule.companyId),
        loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId),
        loaders.dealerById.load(application.dealerId),
        loaders.eventById.load(application.eventId),
        application?.guarantorId?.length
            ? loaders.customerById.load(application.guarantorId[0])
            : Promise.resolve(null),
        loaders.leadById.load(application.leadId),
    ]);

    const customerModule = (await loaders.moduleById.load(applicant.moduleId)) as LocalCustomerManagementModule;
    const checkModel = variant?.modelId ? await loaders.loadModelById.load(variant.modelId) : null;
    const model = checkModel?.parentModelId ? await loaders.loadModelById.load(checkModel.parentModelId) : checkModel;
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    // load mail context
    const [emailContext, dealerEmailContext, eventEmailContext, eventEmailContextSalesperson, edmEmailFooterContext] =
        await Promise.all([
            getCompanyEmailContext(company),
            getDealerEmailContext(dealer),
            getEventEmailContext(dealer, eventModule, event, i18n, application, variant),
            getEventEmailContextSalesperson(variant, eventModule, application),
            getEdmEmailFooterContext({ company }),
        ]);

    const transporter = await createCompanySMTPTransports(company);

    const dealerId = dealer._id.toHexString();

    const { t } = i18n;

    switch (message.audience) {
        case AudienceMessage.Customer: {
            const recipient = getCustomerEmail(t, applicant);
            const stages = message.isResubmit && message.resubmitStage ? [message.resubmitStage] : DEFAULT_STAGES;
            const stage = getApplicationStage(application, lead, stages);
            const submissionType = t(`emails:component.applicationStage.${stage?.stage}`);
            const applicationId = stage?.value?.identifier;
            const emailContent = eventEmailContext.emailContents.submitOrder;

            const subject = getTranslatedEmailContent({
                i18nLanguage: i18n.language,
                dealerTranslationText: emailContent.subject,
                preferredDealerId: dealerId,
                params: {
                    companyName: emailContext.companyName,
                    variantName: variant?.name.defaultValue,
                    submissionType,
                    applicationId,
                    ...getCustomerNameInfo(t, applicant, company, kycPresets, getCustomerFullNameWithTitle),
                },
            });

            if (
                !(await isAbleSendTestDriveEmail(
                    application?.appointmentStage?.appointmentModuleId,
                    loaders,
                    application._versioning.suiteId
                )) ||
                !hasAppointmentScenario(event.scenarios) ||
                event.scenarios.length > 1
            ) {
                await sendSubmitEventApplication(
                    {
                        i18n,
                        data: {
                            emailContext,
                            dealerEmailContext,
                            eventEmailContext,
                            edmEmailFooterContext,
                            dealer,
                            event,

                            application,
                            variant,
                            applicant,
                            journey,
                            guarantor,
                            model,
                            customerModule,

                            audience: message.audience,
                            submissionType,
                            subject,
                            applicationId,
                            stages,
                        },
                        to: { name: recipient, address: recipient },
                        subject,
                    },
                    transporter,
                    emailContext.sender
                );
            }

            break;
        }

        case AudienceMessage.Salesperson: {
            const availableStages = (
                message.isResubmit && message.resubmitStage
                    ? [getApplicationStage(application, lead, message.resubmitStage)]
                    : getApplicationStages(application, lead)
            ).filter(stage => !!stage?.value?.assigneeId);
            const stagesByAssigneeId = groupBy(stage => stage.value.assigneeId.toHexString(), availableStages);
            const applicationStagesLink = await getApplicationStagesLink(application, lead);
            await Promise.all(
                Object.entries(stagesByAssigneeId).map(async ([key, value]) => {
                    if (!ObjectId.isValid(key) || value.length === 0) {
                        return;
                    }
                    const stages = sortStages(value.map(({ stage }) => stage));
                    const assignee = await loaders.userById.load(new ObjectId(key));
                    const stage = getApplicationStage(application, lead, stages);
                    const recipient = assignee.email;
                    const submissionType = t(`emails:component.applicationStage.${stage?.stage}`);
                    const applicationId = stage?.value?.identifier;
                    const subject = t('emails:event.submitOrder.subjectSalesperson', {
                        companyName: emailContext.companyName,
                        variantName: variant?.name.defaultValue,
                        submissionType,
                        applicationId,
                        ...getCustomerNameInfo(t, applicant, company, kycPresets),
                    });

                    const link = await getApplicationDetailsLink(application, lead, stages);
                    if (
                        !(await isAbleSendTestDriveEmail(
                            application?.appointmentStage?.appointmentModuleId,
                            loaders,
                            application._versioning.suiteId
                        )) ||
                        !hasAppointmentScenario(event.scenarios) ||
                        event.scenarios.length > 1
                    ) {
                        await sendSubmitEventApplication(
                            {
                                i18n,
                                data: {
                                    emailContext,
                                    dealerEmailContext,
                                    eventEmailContext: eventEmailContextSalesperson,
                                    edmEmailFooterContext,
                                    dealer,
                                    event,
                                    customerModule,

                                    application,
                                    variant,
                                    applicant,
                                    journey,
                                    guarantor,
                                    model,

                                    audience: message.audience,
                                    submissionType,
                                    subject,
                                    applicationId,
                                    stages,
                                    link,
                                    applicationStagesLink,
                                },
                                to: { name: recipient, address: recipient },
                                subject,
                            },
                            transporter,
                            emailContext.sender
                        );
                    }
                })
            );

            break;
        }
    }
};

export default sendEventApplicationSubmissionMail;
