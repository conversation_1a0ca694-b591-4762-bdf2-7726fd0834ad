import { i18n } from 'i18next';
import { groupBy, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../../core/storage';
import {
    Insurer,
    LocalVariant,
    ModuleType,
    PasswordConfiguration,
    StandardApplication,
    StandardApplicationModule,
    getKYCPresetsForCustomerModule,
    ApplicationStage,
} from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { getCustomerEmail, getCustomerFullNameWithTitle } from '../../../database/helpers/customers';
import {
    AudienceMessage,
    createCompanySMTPTransports,
    sendCustomerSubmissionConfirmation,
    sendCustomerUpdateConfirmation,
    sendPasswordToCustomer,
} from '../../../emails';
import { Loaders } from '../../../loaders';
import { StandardApplicationModuleAsset } from '../../../schema/resolvers/enums';
import {
    DEFAULT_STAGES,
    getApplicationAssigneeId,
    getApplicationIdentifier,
    getApplicationStage,
    getApplicationStages,
    sortStages,
} from '../../../utils/application';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../../utils/getEdmEmailFooterContext';
import checkIsBankViewable from '../shared/checkIsBankViewable';
import getApplicationContext from '../shared/getApplicationContext';
import getApplicationStagesLink from '../shared/getApplicationStagesLink';
import { getStandardApplicationEmailContent } from '../shared/getStandardApplicationEmailContents';
import {
    SendApplicationSubmissionMail,
    canSendPDFToCustomer,
    prepareApplicationSubmissionAttachment,
    prepareAttachmentsForEmail,
} from './shared';

const sendStandardApplicationSubmissionMail = async (
    application: StandardApplication,
    loaders: Loaders,
    i18n: i18n,
    message: SendApplicationSubmissionMail
) => {
    const { collections } = await getDatabaseContext();
    const { t } = i18n;
    const applicationModule = await loaders.moduleById.load(application.moduleId);

    if (applicationModule?._type !== ModuleType.StandardApplicationModule) {
        throw new Error('Module type is not supported');
    }

    // load supporting documents
    const { financeProduct, company, bank, variant, customer, dealer, journey, promoCode, customerModule, lead } =
        await getApplicationContext(application._id);

    const { sendToCustomer, sendToSalesperson, salespersonTopic, customerTopic } = (() => {
        // this is resubmission
        if (message.isResubmit) {
            return {
                sendToCustomer: sendCustomerUpdateConfirmation,
                sendToSalesperson: sendCustomerUpdateConfirmation,
                customerTopic: 'customerUpdateConfirmation',
                salespersonTopic: 'salesPersonUpdateConfirmation',
            };
        }

        return {
            sendToCustomer: sendCustomerSubmissionConfirmation,
            sendToSalesperson: sendCustomerSubmissionConfirmation,
            customerTopic: 'customerSubmissionConfirmation',
            salespersonTopic: 'salesPersonSubmissionConfirmation',
        };
    })();

    const insurer: Insurer = application.insurancing?.insurerId
        ? await loaders.insurerById.load(application.insurancing.insurerId)
        : null;

    const insuranceProduct = application?.insurancing?.insuranceProductId
        ? await loaders.insuranceProductById.load(application.insurancing.insuranceProductId)
        : null;
    // load mail context
    const emailContext = await getCompanyEmailContext(company);
    const edmEmailFooterContext = await getEdmEmailFooterContext({ company });
    const transporter = await createCompanySMTPTransports(company);

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);

    const fallbackImageUrl = await getUrlForUpload((variant as LocalVariant)?.images?.[0]);

    const dealerEmailContext = await getDealerEmailContext(dealer);

    const isBankViewable = await checkIsBankViewable(variant._versioning.suiteId, application.moduleId);

    const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
        _id: application.moduleId,
        _type: ModuleType.StandardApplicationModule,
    });

    switch (message.audience) {
        case AudienceMessage.Customer: {
            const stages = (() => {
                if (message.isResubmit && message.resubmitStage) {
                    return [message.resubmitStage];
                }

                if (!isNil(message.applyNewSubmission) && message.currentStages.length) {
                    return message.currentStages;
                }

                return DEFAULT_STAGES;
            })();

            const identifier = getApplicationIdentifier(application, lead, stages);

            const [canSendApplicationPDF, canSendInsurancePDF] = canSendPDFToCustomer({
                application,
                journey,
                isResubmit: message.isResubmit,
                insurer,
                bank,
                applyNewSubmission: message.applyNewSubmission,
                currentStages: message.currentStages,
            });

            const attachmentInfo = await prepareApplicationSubmissionAttachment(
                application,
                customer,
                company,
                i18n,
                canSendApplicationPDF,
                canSendInsurancePDF
            );

            const attachments = prepareAttachmentsForEmail(application, attachmentInfo, emailContext, identifier);

            const customerEmail = getCustomerEmail(t, customer);

            const assigneeId = getApplicationAssigneeId(application, lead, [
                ApplicationStage.Financing,
                ApplicationStage.Insurance,
                ApplicationStage.Reservation,
            ]);

            const assignee = await (assigneeId ? loaders.userById.load(assigneeId) : null);

            const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
                await getStandardApplicationEmailContent({
                    i18n,
                    standardApplicationModule,
                    standardApplicationAsset: StandardApplicationModuleAsset.CustomerSubmissionConfirmation,
                    emailContext,
                    dealer,
                    customer,
                    customerModule,
                    hint: attachmentInfo?.hint,
                    user: assignee,
                    lead,
                });

            await sendToCustomer(
                {
                    i18n,
                    data: {
                        emailContext,
                        edmEmailFooterContext,
                        dealerEmailContext,

                        application,
                        variant,
                        customer,
                        bank,
                        financeProduct,
                        dealer,
                        journey,
                        applicationModule,
                        insurer,
                        insuranceProduct,
                        promoCode,

                        introImageUrl: introImageUrl ?? fallbackImageUrl,
                        identifier,
                        isBankViewable,
                        applyNewSubmission: message.applyNewSubmission,
                        customerModule,

                        audience: AudienceMessage.Customer,
                        hint: attachmentInfo?.hint,
                        currentStages: message.currentStages,

                        introTitle,
                        contentText,
                        isSummaryVehicleVisible,
                    },
                    to: {
                        name: customerFullName,
                        address: customerEmail,
                    },
                    subject: emailSubject,
                    ...(!!attachments.length && {
                        attachments,
                    }),
                },
                transporter,
                emailContext.sender
            );

            // Only send password to customer when configuration is random
            if (attachmentInfo?.password && company.passwordConfiguration === PasswordConfiguration.Random) {
                await sendPasswordToCustomer(
                    {
                        i18n,
                        subject: t('emails:sendPasswordToCustomer.subject', {
                            companyName: emailContext.companyName,
                        }),
                        data: { emailContext, application, password: attachmentInfo.password, lead },
                        to: { name: customerFullName, address: customerEmail },
                    },
                    transporter,
                    emailContext.sender
                );
            }

            break;
        }

        case AudienceMessage.Salesperson: {
            const stages = (() => {
                if (message.isResubmit && message.resubmitStage) {
                    return [getApplicationStage(application, lead, message.resubmitStage)];
                }

                if (!isNil(message.applyNewSubmission) && message.currentStages.length) {
                    return [getApplicationStage(application, lead, message.currentStages)];
                }

                return getApplicationStages(application, lead);
            })();
            const availableStages = stages.filter(stage => !!stage?.value?.assigneeId);
            const stagesByAssigneeId = groupBy(stage => stage.value.assigneeId.toHexString(), availableStages);

            await Promise.all(
                Object.entries(stagesByAssigneeId).map(async ([key, value]) => {
                    if (!ObjectId.isValid(key) || value.length === 0) {
                        return;
                    }

                    const stages = sortStages(value.map(({ stage }) => stage));

                    const assignee = await loaders.userById.load(new ObjectId(key));
                    const applicationStagesLink = await getApplicationStagesLink(application, lead);
                    const identifier = getApplicationIdentifier(application, lead, stages);

                    await sendToSalesperson(
                        {
                            i18n,
                            data: {
                                emailContext,
                                edmEmailFooterContext,
                                dealerEmailContext,

                                application,
                                variant,
                                customer,
                                bank,
                                financeProduct,
                                dealer,
                                journey,
                                applicationModule,
                                insurer,
                                promoCode,
                                customerModule,
                                insuranceProduct,

                                introImageUrl: fallbackImageUrl,
                                identifier,
                                isBankViewable,

                                audience: AudienceMessage.Salesperson,
                                assignee,
                                applicationStagesLink,
                                applyNewSubmission: message.applyNewSubmission,
                                currentStages: message.currentStages,
                            },
                            to: { name: assignee.email, address: assignee.email },
                            subject: t(`emails:${salespersonTopic}.subject`, {
                                companyName: emailContext.companyName,
                                identifier,
                                customerName: customerFullName,
                            }),
                        },
                        transporter,
                        emailContext.sender
                    );
                })
            );

            break;
        }
    }
};

export default sendStandardApplicationSubmissionMail;
