import { Document } from 'bson';
import { Job } from 'bull';
import { ObjectId } from 'mongodb';
import { getKYCPresetsForCustomerModule } from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../database/helpers/customers/shared';
import { createCompanySMTPTransports, sendApprovalWithOTPEmail } from '../../emails';
import { ApplicationSigningPurpose, ModuleType } from '../../schema/resolvers/definitions';
import { OTP } from '../../utils';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';

export type SendOTPNotificationMessage = { applicationId: ObjectId; otp: OTP; purpose: ApplicationSigningPurpose };

export const sendOTPNotificationHandler = async (message: SendOTPNotificationMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();
    const { applicationId, otp, purpose } = message;
    const application = await collections.applications.findOne({ _id: applicationId });

    if (!application._versioning.isLatest) {
        throw new Error(`Application ${applicationId} is not the latest`);
    }

    const applicant = await collections.customers.findOne({ _id: application.applicantId });
    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });
    const transporter = await createCompanySMTPTransports(company);
    const emailContext = await getCompanyEmailContext(company);
    const customerModule = await collections.modules.findOne({ _id: applicant.moduleId });
    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('Customer Module not found');
    }
    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails']);
    const { t } = i18n;

    const aggregatedFields = getLocalCustomerAggregatedFields(applicant);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const name = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const { email } = aggregatedFields;

    let identifier = '';

    switch (purpose) {
        case ApplicationSigningPurpose.Finance:
            identifier = application?.financingStage?.identifier;
            break;

        case ApplicationSigningPurpose.Insurance:
            identifier = application?.insuranceStage?.identifier;
            break;

        case ApplicationSigningPurpose.Mobility:
            identifier = application?.mobilityStage?.identifier;
            break;

        case ApplicationSigningPurpose.TestDrive:
            identifier = application?.appointmentStage?.identifier;
            break;

        default:
            throw new Error('Invalid ApplicationSigningPurpose type');
    }

    // send the email
    return sendApprovalWithOTPEmail(
        {
            i18n,
            data: { emailContext, otp },
            to: { name, address: email },
            subject: t('emails:otp.subject', {
                company: company.displayName,
                applicationId: identifier,
            }),
        },
        transporter,
        emailContext.sender
    );
};
