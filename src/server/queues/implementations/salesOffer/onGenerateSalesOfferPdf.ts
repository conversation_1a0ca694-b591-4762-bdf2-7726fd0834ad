import { Document } from 'bson';
import { Job } from 'bull';
import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { uploadFile } from '../../../core/storage';
import {
    SalesOfferFeatureKind,
    UploadedFile,
    BucketType,
    SalesOfferDocumentKind,
    SalesOfferDocumentStatus,
    Collections,
    ApplicationKind,
    LaunchpadLead,
} from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import createLoaders from '../../../loaders';
import { attachAgreementDocument, generateFinancingPdf } from './financingPdf';
import generateCOEBiddingAgreement from './generateCOEBiddingAgreement';
import { attachInsuranceDocument, generateInsurancePdf } from './insurancePdf';

const attachSalesOfferDocument = async ({
    documentFeature,
    pdf,
    salesOfferId,
    collections,
    filename,
    documentPath,
}: {
    documentFeature: SalesOfferDocumentKind;
    filename: string;
    documentPath: string;
    pdf: Buffer;
    salesOfferId: ObjectId;
    collections: Collections;
}) => {
    const dirName = `application/agreements/`;

    // upload file to S3
    const uploadedFile: UploadedFile = await uploadFile(BucketType.Private, dirName, filename, pdf);
    const document = {
        ...uploadedFile,
        kind: documentFeature,
        lastUpdatedAt: new Date(),
        status: SalesOfferDocumentStatus.PendingCustomer,
    };

    await collections.salesOffers.findOneAndUpdate(
        { _id: salesOfferId },
        { $push: { [documentPath]: document } },
        { returnDocument: 'after' }
    );
};

export type OnGenerateSalesOfferPdfMessage = {
    featureKinds: SalesOfferFeatureKind[];
    salesOfferId: ObjectId;
    endpointId: ObjectId;
    languageId: ObjectId;
};

export const onGenerateSalesOfferPdf = async (message: OnGenerateSalesOfferPdfMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();
    const loaders = createLoaders();

    const { featureKinds, salesOfferId, languageId } = message;
    const salesOffer = await loaders.salesOfferById.load(salesOfferId);
    const lead = (await loaders.leadBySuiteId.load(salesOffer.leadSuiteId)) as LaunchpadLead;

    const updateDocument = async (featureKind: SalesOfferFeatureKind) => {
        switch (featureKind) {
            case SalesOfferFeatureKind.Finance: {
                const financingApplication = salesOffer.latestFinancingApplicationSuiteId
                    ? await loaders.applicationBySuiteId.load(salesOffer.latestFinancingApplicationSuiteId)
                    : null;

                if (isNil(financingApplication)) {
                    break;
                }

                const pdf = await generateFinancingPdf(salesOffer, financingApplication, loaders);
                await attachAgreementDocument(collections, salesOffer, financingApplication, lead, pdf);

                break;
            }

            case SalesOfferFeatureKind.Insurance: {
                const insuranceApplication = salesOffer.latestInsuranceApplicationSuiteId
                    ? await loaders.applicationBySuiteId.load(salesOffer.latestInsuranceApplicationSuiteId)
                    : null;

                if (isNil(insuranceApplication) || insuranceApplication.kind !== ApplicationKind.SalesOffer) {
                    break;
                }

                const pdf = await generateInsurancePdf(salesOffer, insuranceApplication, loaders);
                await attachInsuranceDocument(collections, salesOffer, insuranceApplication, lead, pdf);

                break;
            }

            case SalesOfferFeatureKind.MainDetails: {
                const pdf = await generateCOEBiddingAgreement({
                    languageId,
                    salesOffer,
                    lead,
                    loaders,
                });

                await attachSalesOfferDocument({
                    pdf,
                    collections,
                    salesOfferId,
                    documentFeature: SalesOfferDocumentKind.MainDetails,
                    documentPath: 'mainDetails.documents',
                    // eslint-disable-next-line max-len
                    filename: `coe-bidding-agreement-${salesOffer.vsaSerialNumber}-${salesOffer.mainDetails.documents.length}.pdf`,
                });

                break;
            }
            case SalesOfferFeatureKind.Vehicle:
            case SalesOfferFeatureKind.VSA:
                break;

            default:
                throw new Error('Feature kind not supported');
        }
    };

    await Promise.allSettled(featureKinds.map(featureKind => updateDocument(featureKind)));
};
