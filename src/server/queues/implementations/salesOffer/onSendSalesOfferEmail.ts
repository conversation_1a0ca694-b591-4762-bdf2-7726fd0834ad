import { Document } from 'bson';
import { Job } from 'bull';
import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../../core/config';
import { getFileStream } from '../../../core/storage';
import {
    ApplicationStage,
    Collections,
    ExternalLinkKind,
    getCustomerEmail,
    getCustomerFirstName,
    getCustomerLastName,
    getCustomerName,
    getCustomerTitleOrSalutation,
    getKYCPresetsForLead,
    LaunchpadApplication,
    LaunchPadModule,
    LocalMake,
    LocalModel,
    LocalVariant,
    SalesOffer,
    SalesOfferDocument,
    SalesOfferDocumentKind,
    SalesOfferEmailContents,
    SalesOfferFeatureKind,
    SalesOfferFeatureStatus,
    SalesOfferModule,
    SendSalesOfferLink,
} from '../../../database';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { createCompanySMTPTransports, sendPreOfferSingleEmail, sendSalesOfferCombinedEmail } from '../../../emails';
import getTranslatedEmailContent from '../../../emails/utils/getTranslatedEmailContent';
import getTranslatedString from '../../../emails/utils/getTranslatedString';
import extractVehicleData from '../../../integrations/porscheVehicleData/extractVehicleData';
import createLoaders, { Loaders } from '../../../loaders';
import { validateEndpoint } from '../../../schema/resolvers/mutations/applications/helpers';
import { streamToBuffer } from '../../../utils';
import createI18nInstance from '../../../utils/createI18nInstance';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../../utils/getEdmEmailFooterContext';
import { EmailAttachment } from '../sendApplicationSubmissionMail/shared';

const generateExternalLink = async (origins, featureKinds, salesOffer, collections) => {
    const externalLink: SendSalesOfferLink = {
        _id: new ObjectId(),
        secret: nanoid(),
        expiresAt: dayjs().add(3, 'days').toDate(),
        deleteOnFetch: false,
        _kind: ExternalLinkKind.SendSalesOffer,
        data: {
            featureKinds,
            routerId: origins.routerId,
            endpointId: origins.endpointId,
            salesOfferId: salesOffer._id,
            leadSuiteId: salesOffer.leadSuiteId,
        },
    };

    await collections.externalLinks.insertOne(externalLink);

    return externalLink;
};

const getSignLink = async (origins, featureKinds, salesOffer, { collections, loaders }) => {
    const externalLink = await generateExternalLink(origins, featureKinds, salesOffer, collections);

    const router = await loaders.routerById.load(origins.routerId);

    return urljoin(`${config.protocol}://${router.hostname}`, router.pathname, `l/${externalLink.secret}`);
};

export type OnSendSalesOfferEmailMessage = {
    featureKinds: SalesOfferFeatureKind[];
    salesOfferId: ObjectId;
    endpointId: ObjectId;
    languageId: ObjectId;
};

const emailLeadContextFromSalesOfferHandler = async (
    salesOfferId: ObjectId,
    loaders: Loaders,
    collections: Collections
) => {
    const salesOffer = await collections.salesOffers.findOne({
        _id: salesOfferId,
    });

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const salesOfferModule = (await loaders.moduleById.load(salesOffer.moduleId)) as SalesOfferModule;

    const existedLead = await collections.leads.findOne({
        '_versioning.suiteId': salesOffer.leadSuiteId,
        '_versioning.isLatest': true,
    });

    const launchpadModule = (await collections.modules.findOne({
        _id: existedLead.moduleId,
    })) as LaunchPadModule;

    const { i18n } = await createI18nInstance(existedLead.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails', 'variant']);
    const { t, language } = i18n;
    const company = await loaders.companyById.load(launchpadModule.companyId);
    const customer = await loaders.customerById.load(existedLead.customerId);
    const dealer = await loaders.dealerById.load(existedLead.dealerId);
    const vehicle = (await loaders.vehicleById.load(existedLead.vehicleId)) as LocalVariant;
    const model = (await loaders.vehicleById.load(vehicle.modelId)) as LocalModel;
    const make = (await loaders.vehicleById.load(model.makeId)) as LocalMake;

    return {
        customer,
        company,
        dealer,
        salesOfferModule,
        salesOffer,
        launchpadModule,
        lead: existedLead,
        vehicle,
        model,
        make,
        t,
        language,
        i18n,
    };
};

const concatenateVehicleModelText = async (
    salesOffer: SalesOffer,
    variant: LocalVariant,
    make: LocalMake,
    model: LocalModel,
    t: TFunction,
    language: string,
    loaders: Loaders
) => {
    const applications = salesOffer.tradeIn.isEnabled
        ? await loaders.applicationByLeadSuiteId.load(salesOffer.leadSuiteId)
        : null;
    const configurator = extractVehicleData(salesOffer.vehicle.resultAPI);

    const totalFittedPrice = salesOffer.vehicle.localFittedOptions.reduce((acc, current) => acc + current.price, 0);
    const tradeInApplication =
        salesOffer.tradeIn.isEnabled && !isNil(applications)
            ? (applications.filter(app => app.stages.includes(ApplicationStage.TradeIn))[0] as LaunchpadApplication)
            : null;

    // eslint-disable-next-line max-len
    const vehicleModelText = `${getTranslatedString(make.name, language)} ${getTranslatedString(model.name, language)} ${getTranslatedString(variant.name, language)} `;

    const purschaseOfferText = `Purchase Offer: ${configurator.totalPrice + totalFittedPrice}`;

    const tradeInPriceText = !isNil(tradeInApplication)
        ? `Vehicle Trade-in: ${tradeInApplication.tradeInVehicle[0].price}`
        : null;

    // eslint-disable-next-line max-len
    const estimatedDeliveryDateText = `Estimated Delivery Date: ${dayjs(salesOffer.mainDetails.estimatedDeliveryDate).format(t('common:formats.dateFullFormat'))}`;

    // eslint-disable-next-line max-len
    return `<br/>${vehicleModelText}<br/>${purschaseOfferText}<br/> ${tradeInPriceText ? `${tradeInPriceText}<br/>` : ''}${estimatedDeliveryDateText}`;
};

const constructSalesOfferEmailContentVariables = async (
    t: TFunction,
    language: string,
    emailContent: SalesOfferEmailContents,
    dealerId: ObjectId,
    vehicle: LocalVariant,
    model: LocalModel,
    make: LocalMake,
    salesOffer: SalesOffer,
    params: object,
    attachments: EmailAttachment[],
    loaders: Loaders
) => {
    const updatedParams = {
        ...params,
        vehicleModel: await concatenateVehicleModelText(salesOffer, vehicle, make, model, t, language, loaders),
        variantName: getTranslatedString(vehicle.name, language),
        documents: `<br/>Documents:<br/>${attachments.map(attach => attach.filename).join(', <br />')}`,
    };

    const introTitle = getTranslatedEmailContent({
        i18nLanguage: language,
        dealerTranslationText: emailContent.introTitle,
        preferredDealerId: dealerId.toHexString(),
        params: updatedParams,
    });

    const contentText = getTranslatedEmailContent({
        i18nLanguage: language,
        dealerTranslationText: emailContent.contentText,
        preferredDealerId: dealerId.toHexString(),
        params: updatedParams,
    });

    const subject = getTranslatedEmailContent({
        i18nLanguage: language,
        dealerTranslationText: emailContent.subject,
        preferredDealerId: dealerId.toHexString(),
        params: updatedParams,
    });

    return {
        introTitle,
        contentText,
        updatedParams,
        subject,
    };
};

const fetchSalesOfferFeatureDocuments = async (documents: SalesOfferDocument<SalesOfferDocumentKind>[]) => {
    // TODISCUSS: this is a temporary solution to get the latest document
    // we should consider to have a better way to handle this
    // there is no application ID or identify to tell which document is the latest
    const featureDocuments = await Promise.all(
        documents
            .filter(
                doc =>
                    doc.status === SalesOfferFeatureStatus.Updated ||
                    doc.status === SalesOfferFeatureStatus.PendingCustomer ||
                    doc.status === SalesOfferFeatureStatus.PendingManager
            )
            .sort((a, b) => (dayjs(a.lastUpdatedAt).isAfter(dayjs(b.lastUpdatedAt)) ? -1 : 1))
            .map(async doc => {
                const pdfStream = await streamToBuffer(await getFileStream(doc));

                return {
                    source: pdfStream,
                    filename: doc.filename,
                    kind: doc.kind,
                };
            })
    );

    return featureDocuments[0];
};

const getSalesOfferDocuments = async (
    featureKinds: SalesOfferFeatureKind[],
    salesOffer: SalesOffer
): Promise<EmailAttachment[]> => {
    const attachments = [];
    if (featureKinds.includes(SalesOfferFeatureKind.MainDetails)) {
        attachments.push(await fetchSalesOfferFeatureDocuments(salesOffer.mainDetails.documents));
    }

    if (featureKinds.includes(SalesOfferFeatureKind.Vehicle)) {
        attachments.push(await fetchSalesOfferFeatureDocuments(salesOffer.vehicle.documents));
    }

    if (featureKinds.includes(SalesOfferFeatureKind.Finance)) {
        attachments.push(await fetchSalesOfferFeatureDocuments(salesOffer.finance.documents));
    }

    if (featureKinds.includes(SalesOfferFeatureKind.Insurance)) {
        attachments.push(await fetchSalesOfferFeatureDocuments(salesOffer.insurance.documents));
    }

    if (featureKinds.includes(SalesOfferFeatureKind.Deposit)) {
        attachments.push(await fetchSalesOfferFeatureDocuments(salesOffer.deposit.documents));
    }

    if (featureKinds.includes(SalesOfferFeatureKind.VSA)) {
        attachments.push(await fetchSalesOfferFeatureDocuments(salesOffer.vsa.documents));
    }

    if (featureKinds.includes(SalesOfferFeatureKind.TradeIn)) {
        attachments.push(await fetchSalesOfferFeatureDocuments(salesOffer.tradeIn.documents));
    }

    if (attachments.length === 0) {
        // if no documents are found, we return null
        return null;
    }

    return attachments.map(attachment => ({
        content: attachment.source,
        filename: attachment.filename,
        contentType: 'application/pdf',
    }));
};

export const onSendSalesOfferEmail = async (message: OnSendSalesOfferEmailMessage, job: Job<Document>) => {
    const { featureKinds, endpointId, salesOfferId } = message;
    const { collections } = await getDatabaseContext();
    const loaders = createLoaders();

    if (featureKinds.length === 0) {
        throw new Error('No sales offer feature need to be done');
    }

    const {
        customer,
        company,
        dealer,
        launchpadModule,
        salesOffer,
        salesOfferModule,
        lead,
        vehicle,
        model,
        make,
        t,
        language,
        i18n,
    } = await emailLeadContextFromSalesOfferHandler(salesOfferId, loaders, collections);

    const kycPresets = await getKYCPresetsForLead(lead, loaders);

    const customerName = getCustomerName(t, customer, company, kycPresets);
    const customerFirstName = getCustomerFirstName(customer);
    const customerLastName = getCustomerLastName(customer);
    const customerEmail = getCustomerEmail(t, customer);
    const customerTitle = getCustomerTitleOrSalutation(t, customer);

    const [emailContext, edmEmailFooterContext, dealerEmailContext] = await Promise.all([
        getCompanyEmailContext(company),
        getEdmEmailFooterContext({ company }),
        getDealerEmailContext(dealer),
    ]);

    const emailContent =
        featureKinds.length > 1
            ? salesOfferModule.emailContents.combinedTemplate
            : salesOfferModule.emailContents.singlePreOfferTemplate;

    const params = {
        customerName,
        customerTitle,
        dealerEmailContext,
        companyName: emailContext.companyName,
        firstName: customerFirstName,
        lastName: customerLastName,
    };
    const attachments = await getSalesOfferDocuments(featureKinds, salesOffer);

    const { introTitle, contentText, subject, updatedParams } = await constructSalesOfferEmailContentVariables(
        t,
        language,
        emailContent,
        dealer._id,
        vehicle,
        model,
        make,
        salesOffer,
        params,
        attachments,
        loaders
    );

    // get router/endpoint when provided
    const origins = await validateEndpoint(endpointId, launchpadModule);

    const signLink = await getSignLink(origins, featureKinds, salesOffer, { loaders, collections });
    const transporter = await createCompanySMTPTransports(company);
    if (featureKinds.length > 1) {
        // sending out combined email to customer
        await sendSalesOfferCombinedEmail(
            {
                data: {
                    salesOffer,
                    featureKinds,
                    url: signLink,
                    dealerEmailContext,
                    contentText,
                    dealerId: dealer._id,
                    emailContext,
                    introTitle,
                    edmEmailFooterContext,
                },
                to: {
                    name: customerName,
                    address: customerEmail,
                },
                subject,
                ...(!!attachments.length && { attachments }),
                i18n,
            },
            transporter,
            emailContext.sender
        );
    } else if (featureKinds.length === 1) {
        // sending out combined email to customer
        await sendPreOfferSingleEmail(
            {
                data: {
                    salesOffer,
                    featureKinds,
                    url: signLink,
                    dealerEmailContext,
                    contentText,
                    dealerId: dealer._id,
                    emailContext,
                    introTitle,
                    edmEmailFooterContext,
                },
                to: {
                    name: customerName,
                    address: customerEmail,
                },
                subject,
                ...(!!attachments.length && { attachments }),
                i18n,
            },
            transporter,
            emailContext.sender
        );
    }
};
