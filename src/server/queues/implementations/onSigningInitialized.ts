import { Document } from 'bson';
import { Job } from 'bull';
import type { RecipientViewRequest } from 'docusign-esign';
import { isEmpty, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationJourneySigningMode,
    ApplicationKind,
    ApplicationSigningStatus,
    ApplicationStage,
    ApplicationStatus,
    getKYCPresetsForCustomerModule,
    LocalCustomerFieldKey,
    ModuleType,
    SettingId,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getBankIdFromApplication, getApplicantIdFromApplication } from '../../database/helpers/applications';
import { getCustomerFullName, getLocalCustomerFieldValueFor } from '../../database/helpers/customers';
import { createSigning, generateAgreementPdf } from '../../journeys/helper';
import { generateInsuranceAgreementPdf } from '../../journeys/helper/generateInsuranceAgreement';
import { getSigningModule } from '../../journeys/helper/getSigningModule';
import { isValidApplicationModuleForAgreementPdf } from '../../journeys/helper/isValidApplicationForAgreement';
import DocusignSigning from '../../signing/docusign/DocusignSigning';
import { getSigningSettings } from '../../signing/helper';
import NamirialSigning from '../../signing/namirial/NamirialSigning';
import { getApplicationIdentifier, getStatusUpdates } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import { mainQueue } from '../mainQueue';
import { getApplicationPDFFilename } from './submitApplicationToBank/shared';

export type OnSigningInitializedMessage = {
    applicationId: ObjectId;
    clientAction: string;
    signerType: 'applicant' | 'guarantor';
    retries?: number;
};

export const onSigningInitializedHandler = async (message: OnSigningInitializedMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();
    const { applicationId, clientAction, retries = 0 } = message;
    const application = await collections.applications.findOne({ _id: applicationId });

    if (!application) {
        throw new Error('Application is missing');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    if (!applicationModule) {
        throw new Error('Application module is missing');
    }

    if (!isValidApplicationModuleForAgreementPdf(applicationModule)) {
        throw new Error('Application module is invalid for signing');
    }
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    const isResubmit = !isNil(journey.submission);

    const signingModule = await getSigningModule(applicationModule, application, isResubmit);
    const bankId = getBankIdFromApplication(application);
    const bank = bankId ? await collections.banks.findOne({ _id: bankId }) : undefined;

    if (!signingModule) {
        throw new Error('Signing module for namirial not found');
    }

    // Check also if insurance application has the same signing
    const insurerId =
        (application.kind === ApplicationKind.Standard ||
            application.kind === ApplicationKind.Configurator ||
            application.kind === ApplicationKind.Finder) &&
        application.insurancing?.insurerId;

    const insurer = insurerId
        ? await collections.insurers.findOne({ _id: insurerId, isActive: true, isDeleted: false })
        : undefined;
    const isResubmitInsurance = !isNil(journey.insuranceSubmission);
    const insuranceSigningModuleId = isResubmitInsurance
        ? insurer?.reSubmissionApprovalModuleId
        : insurer?.submissionApprovalModuleId;

    const hasSameSigning = insuranceSigningModuleId && insuranceSigningModuleId?.equals(signingModule._id);
    const hasSameSigningAndAllowedForInsurance =
        hasSameSigning &&
        (application.kind === ApplicationKind.Configurator ||
            application.kind === ApplicationKind.Finder ||
            application.kind === ApplicationKind.Standard);

    const setting = await getSigningSettings(signingModule._id);
    const customer = await collections.customers.findOne({
        _id: getApplicantIdFromApplication(application),
    });
    const guarantor = application.guarantorId?.[0]
        ? await collections.customers.findOne({ _id: application.guarantorId[0] })
        : null;

    // guarantor suppose to use the same customer module as customer
    const customerModule = await collections.modules.findOne({
        _id: customer.moduleId,
    });

    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('module type not supported');
    }

    const company = await collections.companies.findOne({ _id: signingModule.companyId });

    try {
        // create the signing envelope
        if (message.signerType === 'guarantor') {
            const envelopeId =
                (journey.applicantSigning.kind === ApplicationJourneySigningMode.Namirial ||
                    journey.applicantSigning.kind === ApplicationJourneySigningMode.Docusign) &&
                journey.hasGuarantorCustomer
                    ? journey.applicantSigning.envelopeId
                    : '';

            if (setting.settingId === SettingId.NamirialSetting) {
                // create namirial signing
                const namirialSigning = new NamirialSigning(setting);

                const getLinksResponse = await namirialSigning.getLinks(
                    journey.applicantSigning.kind === ApplicationJourneySigningMode.Namirial &&
                        journey.hasGuarantorCustomer
                        ? journey.applicantSigning.envelopeId
                        : ''
                );

                if (getLinksResponse?.ViewerLinks?.[1]?.ViewerLink !== '') {
                    await collections.applicationJourneys.findOneAndUpdate(
                        { applicationSuiteId: application._versioning.suiteId },
                        {
                            $set: {
                                guarantorSigning: {
                                    moduleId: signingModule._id,
                                    completed: false,
                                    kind: ApplicationJourneySigningMode.Namirial,
                                    status: ApplicationSigningStatus.Initialized,
                                    envelopeId,
                                    redirectionUrl: getLinksResponse?.ViewerLinks?.[1]?.ViewerLink,
                                },
                            },
                        }
                    );
                } else {
                    await mainQueue.add(
                        {
                            type: 'onSigningInitialized',
                            clientAction,
                            applicationId: application._id,
                            signerType: 'guarantor',
                            retries: retries + 1,
                        },
                        { delay: 1000 * 5 * (retries + 1) }
                    );
                }
            }

            if (setting.settingId === SettingId.Docusign) {
                // load translations
                const { i18n } = await createI18nInstance(application.languageId?.toHexString());
                await i18n.loadNamespaces(['emails', 'common']);
                const { t } = i18n;
                const email = getLocalCustomerFieldValueFor(guarantor, LocalCustomerFieldKey.Email);
                const kycPresets = getKYCPresetsForCustomerModule(customerModule, guarantor._kind);
                const fullName = getCustomerFullName(t, guarantor, company, kycPresets);

                const docusignSigning = new DocusignSigning(setting);

                const request: RecipientViewRequest = {
                    returnUrl: clientAction,
                    authenticationMethod: 'none',
                    email,
                    userName: fullName,
                    clientUserId: 'guarantor',
                };

                const view = await docusignSigning.createRecipientView(envelopeId, request);

                await collections.applicationJourneys.findOneAndUpdate(
                    { applicationSuiteId: application._versioning.suiteId },
                    {
                        $set: {
                            guarantorSigning: {
                                moduleId: signingModule._id,
                                completed: false,
                                kind: ApplicationJourneySigningMode.Docusign,
                                status: ApplicationSigningStatus.Initialized,
                                envelopeId,
                                redirectionUrl: view.url,
                            },
                        },
                    }
                );
            }
        } else {
            const agreementPdf = await generateAgreementPdf(application);
            const agreementIdentifier = getApplicationIdentifier(application, lead, [
                ApplicationStage.Financing,
                ApplicationStage.Reservation,
                ApplicationStage.Mobility,
            ]);

            const pdfs = [{ name: `${agreementIdentifier}.pdf`, pdf: agreementPdf }];

            if (hasSameSigningAndAllowedForInsurance) {
                const insuranceAgreementPdf = await generateInsuranceAgreementPdf(application);
                pdfs.push({
                    name: getApplicationPDFFilename(application.insuranceStage.identifier),
                    pdf: insuranceAgreementPdf,
                });
            }

            const { envelopeId, redirectionUrl, nonce, signingMode } = await createSigning(
                pdfs,
                {
                    application,
                    customer,
                    guarantor,
                    bank,
                    setting,
                    journeyIdentifier: 'namirial-signing',
                    company,
                    customerModule,
                },
                { clientAction }
            );

            await collections.applicationJourneys.findOneAndUpdate(
                { applicationSuiteId: application._versioning.suiteId },
                {
                    $set: {
                        applicantSigning: {
                            moduleId: signingModule._id,
                            completed: false,
                            kind: signingMode,
                            status: ApplicationSigningStatus.Initialized,
                            envelopeId,
                            redirectionUrl,
                            nonce,
                        },
                        ...(hasSameSigningAndAllowedForInsurance && {
                            insuranceApplicantSigning: {
                                ...journey.insuranceApplicantSigning,
                                status: ApplicationSigningStatus.Initialized,
                                moduleId: insuranceSigningModuleId,
                                kind: signingMode,
                                envelopeId,
                                redirectionUrl,
                                nonce,
                            },
                        }),
                    },
                }
            );
        }
    } catch (error) {
        const statusUpdates = getStatusUpdates(application, lead, ApplicationStatus.SigningCreationFailed);
        if (!isEmpty(statusUpdates)) {
            await collections.applications.findOneAndUpdate({ _id: application._id }, { $set: statusUpdates });
        }

        console.error(error);

        throw new Error('Failed to create signing ', error);
    }
};
