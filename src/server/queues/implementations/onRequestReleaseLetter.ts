import { Document } from 'bson';
import { Job } from 'bull';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { getFileStream } from '../../core/storage';
import {
    ApplicationDocument,
    AuditTrailKind,
    AuthorKind,
    LocalCustomerManagementModule,
    LocalVariant,
    PasswordConfiguration,
    RequestReleaseLetterAuditTrail,
    getKYCPresetsForCustomerModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getBankIdFromApplication } from '../../database/helpers/applications';
import { getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import { createCompanySMTPTransports, sendPasswordForAgreementToBank, sendRequestReleaseLetter } from '../../emails';
import getAgreementDocument from '../../journeys/helper/getAgreementDocument';
import createLoaders from '../../loaders';
import { getApplicationLogStages } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getEncryptedZip from '../../utils/getEncryptedZip';

export type onRequestReleaseLetterMessage = {
    applicationId: ObjectId;
    userId: ObjectId;
};

export const onRequestReleaseLetterHandler = async (message: onRequestReleaseLetterMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();
    const { applicationId, userId } = message;

    const application = await collections.applications.findOne({ _id: applicationId });
    const bankId = getBankIdFromApplication(application);
    const bank = bankId ? await collections.banks.findOne({ _id: bankId }) : null;

    if (!application._versioning.isLatest || !bank) {
        return;
    }

    const user = await collections.users.findOne({ _id: userId });

    if (!user) {
        throw new Error('User not found');
    }

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });

    const company = await collections.companies.findOne({ _id: applicationModule.companyId });

    const agreement = getAgreementDocument(application);

    await onRequestReleaseLetter(application, company, bank, user, agreement);
};

const onRequestReleaseLetter = async (application, company, bank, user, agreement: ApplicationDocument) => {
    const { collections } = await getDatabaseContext();
    const transporter = await createCompanySMTPTransports(company);
    const emailContext = await getCompanyEmailContext(company);

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails']);
    const { t } = i18n;

    const loaders = createLoaders();

    const [variant, customer, assignee, financeProduct] = await Promise.all([
        loaders.vehicleById.load(application.vehicleId) as Promise<LocalVariant>,
        loaders.customerById.load(application.applicantId),
        application.financingStage?.assigneeId ? loaders.userById.load(application.financingStage.assigneeId) : null,
        loaders.financeProductById.load(application.financing.financeProductId),
    ]);

    const customerModule = (await loaders.moduleById.load(customer.moduleId)) as LocalCustomerManagementModule;
    const randomPassword = nanoid();

    const files = agreement ? [{ source: await getFileStream(agreement), filename: agreement.filename }] : null;

    const isPasswordProtected = company.passwordConfiguration !== PasswordConfiguration.Off;

    const encryptedZip = files ? await getEncryptedZip(files, isPasswordProtected ? randomPassword : undefined) : null;

    const auditTrail: RequestReleaseLetterAuditTrail = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.RequestReleaseLetter,
        _date: new Date(),
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.RequestReleaseLetter),
        author: { kind: AuthorKind.User, id: user._id },
    };

    // create audit trail
    await collections.auditTrails.insertOne(auditTrail);

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);
    const hint = isPasswordProtected ? t('emails:requestReleaseLetter.hint') : '';

    // send the email
    await sendRequestReleaseLetter(
        {
            i18n,
            data: {
                emailContext,
                bank,
                application,
                variant,
                financeProduct,
                assignee,
                hint,
                module: applicationModule,
                dealerId: application.dealerId,
            },
            subject: t('emails:requestReleaseLetter.subject', {
                companyName: emailContext.companyName,
                identifier: application.financingStage?.identifier,
                customerName: customerFullName,
            }),
            to: { name: bank.displayName, address: bank.email },
            attachments: [
                encryptedZip && {
                    filename: 'Agreement.zip',
                    contentType: 'application/zip',
                    content: encryptedZip,
                },
            ].filter(Boolean),
        },
        transporter,
        emailContext.sender
    );

    if (isPasswordProtected) {
        await sendPasswordForAgreementToBank(
            {
                i18n,
                subject: t('emails:requestReleaseLetter.subject', {
                    companyName: emailContext.companyName,
                    identifier: application.financingStage?.identifier,
                    customerName: customerFullName,
                }),
                data: { emailContext, application, randomPassword },
                to: { name: bank.displayName, address: bank.integration.email },
            },
            transporter,
            emailContext.sender
        );
    }
};

export default onRequestReleaseLetter;
