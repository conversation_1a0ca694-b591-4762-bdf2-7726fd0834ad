import { TFunction } from 'i18next';
import {
    Company,
    Customer,
    CustomerKind,
    getKYCPresetsForCustomerModule,
    LocalCustomerFieldKey,
    LocalCustomerManagementModule,
} from '../../../database';
import { getCustomerFullName } from '../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import { UobSubmitRequest } from '../../../integrations/banks/uob/submit';

const prepareUobRequestApplicantPartial = async (
    applicant: Customer,
    company: Company,
    customerModule: LocalCustomerManagementModule,
    t: TFunction
): Promise<Pick<UobSubmitRequest, 'applicant'>> => {
    const kind = applicant._kind;

    switch (kind) {
        case CustomerKind.Local: {
            const customerFields = getLocalCustomerAggregatedFields(applicant);

            const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

            return Promise.resolve({
                applicant: {
                    basicInfo: {
                        principalName: getCustomerFullName(t, applicant, company, kycPresets),
                        mobileNumber: customerFields[LocalCustomerFieldKey.Phone]?.value,
                        emailAddress: customerFields[LocalCustomerFieldKey.Email],
                    },
                },
            });
        }

        default:
            throw new Error(`invalid customer kind to submit to uob: ${kind}`);
    }
};

export default prepareUobRequestApplicantPartial;
