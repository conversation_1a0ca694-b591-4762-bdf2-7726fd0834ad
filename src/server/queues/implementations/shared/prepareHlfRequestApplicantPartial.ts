import { TFunction } from 'i18next';
import {
    Company,
    Customer,
    CustomerKind,
    getKYCPresetsForCustomerModule,
    LocalCustomerFieldKey,
    LocalCustomerManagementModule,
} from '../../../database';
import { getCustomerFullName } from '../../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../../database/helpers/customers/shared';
import { HlfSubmitRequest } from '../../../integrations/banks/hlf/submit';

const prepareHlfRequestApplicantPartial = (
    applicant: Customer,
    company: Company,
    customerModule: LocalCustomerManagementModule,
    t: TFunction
): Promise<Pick<HlfSubmitRequest, 'name' | 'nric' | 'mobile' | 'email'>> => {
    const kind = applicant._kind;

    switch (kind) {
        case CustomerKind.Local: {
            const customerFields = getLocalCustomerAggregatedFields(applicant);

            const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

            return Promise.resolve({
                name: getCustomerFullName(t, applicant, company, kycPresets),
                nric: customerFields[LocalCustomerFieldKey.IdentityNumber],
                mobile: customerFields[LocalCustomerFieldKey.Phone]?.value,
                email: customerFields[LocalCustomerFieldKey.Email],
            });
        }

        default:
            throw new Error(`invalid customer kind to submit to hlf: ${kind}`);
    }
};

export default prepareHlfRequestApplicantPartial;
