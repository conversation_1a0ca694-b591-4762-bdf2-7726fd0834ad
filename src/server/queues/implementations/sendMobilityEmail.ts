import { Document } from 'bson';
import { Job } from 'bull';
import { TFunction } from 'i18next';
import { get, isEmpty } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../core/storage';
import {
    ApplicationJourney,
    ApplicationKind,
    AuditTrailKind,
    DealerTranslatedStringSetting,
    DealerTranslationText,
    LocalVariant,
    MobilityApplication,
    ModuleType,
    TranslatedString,
    UploadedFileWithPreview,
    PasswordConfiguration,
    ApplicationStage,
    LocalCustomerManagementModule,
    getKYCPresetsForCustomerModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { updateAuditTrailForMobilityEmailSent } from '../../database/helpers/auditTrails';
import { getCustomerEmail, getCustomerFullNameWithTitle, getCustomerName } from '../../database/helpers/customers';
import { AudienceMessage, createCompanySMTPTransports, sendMobilityEmail, sendPasswordToCustomer } from '../../emails';
import getTranslatedEmailContent from '../../emails/utils/getTranslatedEmailContent';
import createLoaders from '../../loaders';
import { MobilityAsset, MobilityRecipient } from '../../schema/resolvers/enums';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';
import { mainQueue } from '../mainQueue';
import {
    canSendPDFToCustomer,
    prepareApplicationSubmissionAttachment,
    prepareAttachmentsForEmail,
} from './sendApplicationSubmissionMail/shared';
import { getApplicationDataFromApplication } from './sendProceedWithCustomerEmail';
import getMobilityEmailActionLinks from './shared/getMobilityEmailActionLinks';
import getMobilityEmailContent, { MobilityEmailPath } from './shared/getMobilityEmailContent';

export const getRentalDisclaimer = (
    moduleRentalDisclaimer: DealerTranslatedStringSetting,
    dealerId: string
): TranslatedString | null => {
    const dealerRentalDisclaimer = moduleRentalDisclaimer.overrides.find(
        override => override.dealerId.toString() === dealerId
    );

    if (isEmpty(dealerRentalDisclaimer?.value.defaultValue)) {
        if (isEmpty(moduleRentalDisclaimer.defaultValue?.defaultValue)) {
            return null;
        }

        return moduleRentalDisclaimer.defaultValue;
    }

    return dealerRentalDisclaimer.value;
};

export type SendMobilityEmailHandlerMessage = {
    applicationId: ObjectId;
    mobilityRecipient: MobilityRecipient;
    mobilityAsset: MobilityAsset;
};

export const sendMobilityApplicationEmails = async (application: MobilityApplication, journey: ApplicationJourney) => {
    const { collections } = await getDatabaseContext();
    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    if (applicationModule._type !== ModuleType.MobilityModule) {
        throw new Error('ModuleType not support');
    }

    const canSendEmailToCustomer = get('sendEmailToCustomer', applicationModule);
    const getEmailContent = getMobilityEmailContent(application, applicationModule);

    const isResubmission = !!journey.resubmission?.stage;

    const mobilityAsset = isResubmission ? MobilityAsset.BookingAmendment : MobilityAsset.BookingConfirmation;

    const resubmissionData = journey.resubmission?.stage === ApplicationStage.Mobility ? journey.resubmission : null;

    const isSameLocation = resubmissionData?.previousLocationId?.equals(
        application.mobilityBookingDetails.location._id
    );
    const isEmailEnabledCustomers = getEmailContent<boolean>(
        `${MobilityRecipient.Customers}.${mobilityAsset}`,
        'isEmailEnabled'
    );
    const isEmailEnabledOperators = getEmailContent<boolean>(
        `${MobilityRecipient.Operators}.${mobilityAsset}`,
        'isEmailEnabled'
    );

    if (canSendEmailToCustomer && isEmailEnabledCustomers) {
        await Promise.resolve(
            mainQueue.add({
                type: 'sendMobilityEmail',
                applicationId: application._id,
                mobilityRecipient: MobilityRecipient.Customers,
                mobilityAsset,
            })
        );
    }

    // When location is changed, we need to send operators location update
    const shouldSendAssignmentEmail = isResubmission && !isSameLocation;
    if (shouldSendAssignmentEmail) {
        await Promise.resolve(
            mainQueue.add({
                type: 'sendOperatorLocationUpdate',
                previousApplicationId: resubmissionData?.previousApplicationId,
                nextApplicationId: application._id,
                author: resubmissionData?.author,
            })
        );
    } else if (isEmailEnabledOperators) {
        // When location is not changed, we need to send operators amendment email
        await Promise.resolve(
            mainQueue.add({
                type: 'sendMobilityEmail',
                applicationId: application._id,
                mobilityRecipient: MobilityRecipient.Operators,
                mobilityAsset,
            })
        );
    }
};
const getRecipient = (
    t: TFunction,
    message: SendMobilityEmailHandlerMessage,
    operatorName: string,
    operatorEmail: string,
    customerEmail: string,
    customerName: string
) => {
    switch (message.mobilityRecipient) {
        case MobilityRecipient.Operators:
            return { name: operatorName, address: operatorEmail };

        case MobilityRecipient.Customers:
            return { name: customerName, address: customerEmail };

        default:
            throw new Error('MobilityRecipient not found');
    }
};

export const sendMobilityEmailHandler = async (message: SendMobilityEmailHandlerMessage, job: Job<Document>) => {
    const { applicationId, mobilityAsset, mobilityRecipient } = message;

    const loaders = createLoaders();

    const { collections } = await getDatabaseContext();

    const application = await collections.applications.findOne({ _id: applicationId, kind: ApplicationKind.Mobility });
    if (!application || application.kind !== ApplicationKind.Mobility) {
        throw new Error('application not found');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });
    if (!lead) {
        throw new Error('Lead not found');
    }

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    if (applicationModule._type !== ModuleType.MobilityModule) {
        throw new Error('ModuleType not support');
    }

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    if (!journey) {
        throw new Error('Application Journey not found');
    }
    const [vehicleId, customerId] = getApplicationDataFromApplication(application);

    const [variant, applicant, company, dealer, assignee, promoCode, giftVoucher] = await Promise.all([
        loaders.vehicleById.load(vehicleId) as Promise<LocalVariant>,
        loaders.customerById.load(customerId),
        loaders.companyById.load(applicationModule.companyId),
        loaders.dealerById.load(application.dealerId),
        application.mobilityStage.assigneeId ? loaders.userById.load(application.mobilityStage.assigneeId) : null,
        application.promoCodeId ? loaders.promoCodeById.load(application.promoCodeId) : null,
        application.giftVoucherSuiteId ? loaders.giftVoucherBySuiteId.load(application.giftVoucherSuiteId) : null,
    ]);

    const customerModule = (await loaders.moduleById.load(applicant.moduleId)) as LocalCustomerManagementModule;

    const [emailContext, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getEdmEmailFooterContext({ company }),
    ]);

    const model = await loaders.loadModelById.load(variant.modelId);

    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails']);
    const { t } = i18n;
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerName = getCustomerName(t, applicant, company, kycPresets);
    const customerEmail = getCustomerEmail(t, applicant);
    const dealerName = dealer.displayName;
    const dealerEmailContext = await getDealerEmailContext(dealer);
    const transporter = await createCompanySMTPTransports(company);

    const getEmailContent = getMobilityEmailContent(application, applicationModule);

    const introImageFile = getEmailContent<UploadedFileWithPreview | null>(
        `${mobilityRecipient}.${mobilityAsset}` as MobilityEmailPath,
        'introImage'
    );

    const introImageUrl = await getUrlForUpload(introImageFile || variant.images?.[0]);

    const { amendLink, cancelLink } = await getMobilityEmailActionLinks(applicationModule._id, application);

    const attachmentInfo = await (async () => {
        switch (mobilityRecipient) {
            case MobilityRecipient.Customers: {
                const [canSendApplicationPDF] = canSendPDFToCustomer({ application, applicationModule });

                return prepareApplicationSubmissionAttachment(
                    application,
                    applicant,
                    company,
                    i18n,
                    canSendApplicationPDF,
                    false
                );
            }

            case MobilityRecipient.Operators:
            default: {
                return null;
            }
        }
    })();

    const operatorName = assignee?.displayName ?? dealerName;
    const operatorEmail = assignee?.email ?? dealer.contact?.email;

    if (!operatorEmail && message.mobilityRecipient === MobilityRecipient.Operators) {
        return;
    }

    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const recipientName = message.mobilityRecipient === MobilityRecipient.Operators ? operatorName : customerName;

    const attachments = prepareAttachmentsForEmail(application, attachmentInfo, emailContext);

    const rentalDisclaimer = getRentalDisclaimer(applicationModule.rentalDisclaimer, application.dealerId.toString());

    const subject: DealerTranslationText = getEmailContent(
        `${mobilityRecipient}.${mobilityAsset}` as MobilityEmailPath,
        'subject'
    );

    await sendMobilityEmail(
        {
            i18n,
            data: {
                emailContext,
                mobilityRecipient,
                mobilityAsset,
                dealer,

                customerName: recipientName.trim() !== '' ? recipientName : customerName,
                customerFullName,
                customerEmail,
                application,

                variant,
                modelName: model.name,
                edmEmailFooterContext,
                introImageUrl,
                applicant,
                introTitle: getEmailContent(`${mobilityRecipient}.${mobilityAsset}` as MobilityEmailPath, 'introTitle'),
                contentText: getEmailContent(
                    `${mobilityRecipient}.${mobilityAsset}` as MobilityEmailPath,
                    'contentText'
                ),
                payment: journey.deposit,
                amendLink,
                cancelLink,
                dealerEmailContext,
                rentalDisclaimer,
                promoCode,
                giftVoucher,
            },
            to: getRecipient(t, message, operatorName, operatorEmail, customerEmail, customerFullName),
            subject: getTranslatedEmailContent({
                dealerTranslationText: subject,
                preferredDealerId: dealer._id.toHexString(),
                i18nLanguage: i18n.language,
            }),
            ...(!!attachments.length && {
                attachments,
            }),
        },
        transporter,
        emailContext.sender
    );

    const auditTrailKind = (() => {
        switch (message.mobilityAsset) {
            case MobilityAsset.BookingAmendment:
                return AuditTrailKind.BookingAmendmentEmailSent;

            case MobilityAsset.BookingConfirmation:
                return AuditTrailKind.BookingConfirmationEmailSent;

            case MobilityAsset.BookingReminder:
                return AuditTrailKind.ReminderEmailSent;

            case MobilityAsset.BookingComplete:
                return AuditTrailKind.BookingCompletionEmailSent;

            // on cancelled case is on `onApplicationCancelled`
            // since it is not sent through the queue

            default:
                return null;
        }
    })();

    if (auditTrailKind) {
        // then update audit trail
        await updateAuditTrailForMobilityEmailSent(
            application,
            auditTrailKind,
            message.mobilityRecipient === MobilityRecipient.Operators
                ? AudienceMessage.Operator
                : AudienceMessage.Customer
        );
    }

    // Only send password to customer when configuration is random
    if (attachmentInfo?.password && company.passwordConfiguration === PasswordConfiguration.Random) {
        await sendPasswordToCustomer(
            {
                i18n,
                subject: t('emails:sendPasswordToCustomer.subject', {
                    companyName: emailContext.companyName,
                }),
                data: { emailContext, application, password: attachmentInfo.password, lead },
                to: getRecipient(t, message, operatorName, operatorEmail, customerEmail, customerFullName),
            },
            transporter,
            emailContext.sender
        );
    }
};

export default sendMobilityEmailHandler;
