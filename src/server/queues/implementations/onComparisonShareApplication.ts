import { Document } from 'bson';
import { Job } from 'bull';
import { ObjectId } from 'mongodb';
import {
    ApplicationFinancing,
    ApplicationKind,
    LocalCustomerManagementModule,
    LocalInsuranceProduct,
    LocalVariant,
    ModuleType,
    StandardApplication,
    StandardApplicationModule,
    User,
    getKYCPresetsForCustomerModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerFullNameWithTitle } from '../../database/helpers/customers';
import { getLocalCustomerAggregatedFields } from '../../database/helpers/customers/shared';
import { createCompanySMTPTransports, sendComparisonShareDetails } from '../../emails';
import generateComparisonSharePdf from '../../journeys/helper/generateComparisonSharePdf';
import { StandardApplicationModuleAsset } from '../../schema/resolvers/enums';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import { getStandardApplicationEmailContent } from './shared/getStandardApplicationEmailContents';

export type OnComparisonShareApplicationMessage = {
    applicationId: ObjectId;
    vehicleIds: ObjectId[];
    financings: ApplicationFinancing[];
    userId: ObjectId;
    insuranceProducts: LocalInsuranceProduct[];
};

export const onComparisonShareApplicationHandler = async (
    message: OnComparisonShareApplicationMessage,
    job: Job<Document>
) => {
    const { applicationId, vehicleIds, financings, userId, insuranceProducts } = message;
    const { collections } = await getDatabaseContext();
    const application = await collections.applications.findOne({ _id: applicationId });

    if (!application) {
        throw new Error('Application not found');
    }

    const user = userId ? await collections.users.findOne({ _id: userId }) : null;

    switch (application.kind) {
        case ApplicationKind.Standard:
            await onComparisonShareStandardApplication(application, vehicleIds, financings, user, insuranceProducts);

            return;

        default:
            throw new Error('Unsupported application kind');
    }
};

const onComparisonShareStandardApplication = async (
    application: StandardApplication,
    vehicleIds: ObjectId[],
    financings: ApplicationFinancing[],
    user: User | undefined,
    insuranceProducts: LocalInsuranceProduct[]
) => {
    const { collections } = await getDatabaseContext();
    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });

    if (applicationModule._type !== ModuleType.StandardApplicationModule) {
        throw new Error('Application module is not a standard application module');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['emails', 'common']);
    const { t } = i18n;

    const vehicle = (await collections.vehicles.findOne({ _id: application.vehicleId })) as LocalVariant;
    const company = await collections.companies.findOne({ _id: applicationModule.companyId });
    const dealer = await collections.dealers.findOne({ _id: application.dealerId });

    const emailContext = await getCompanyEmailContext(company);
    const transporter = await createCompanySMTPTransports(company);
    const customer = await collections.customers.findOne({ _id: application.applicantId });
    const customerModule = (await collections.modules.findOne({
        _id: customer.moduleId,
    })) as LocalCustomerManagementModule;

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const applicant = getLocalCustomerAggregatedFields(customer);
    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);

    const pdf = await generateComparisonSharePdf(application, vehicleIds, financings, insuranceProducts);

    const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
        _id: application.moduleId,
        _type: ModuleType.StandardApplicationModule,
    });

    const { introImageUrl, emailSubject, introTitle, contentText, fileName } = await getStandardApplicationEmailContent(
        {
            i18n,
            standardApplicationModule,
            standardApplicationAsset: StandardApplicationModuleAsset.CustomerComparisonShare,
            emailContext,
            vehicle,
            dealer,
            customer,
            customerModule,
            user,
            lead,
        }
    );

    await sendComparisonShareDetails(
        {
            i18n,
            subject: emailSubject,
            data: {
                emailContext,
                introImageUrl,
                introTitle,
                contentText,
            },
            to: { name: customerFullName, address: applicant.email },
            attachments: [
                {
                    filename: `${fileName}.pdf`,
                    contentType: 'application/pdf',
                    content: pdf,
                },
            ],
        },
        transporter,
        emailContext.sender
    );
};
