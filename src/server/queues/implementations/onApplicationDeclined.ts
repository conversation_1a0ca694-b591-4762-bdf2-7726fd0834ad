import { Document } from 'bson';
import { Job } from 'bull';
import { i18n, TFunction } from 'i18next';
import { isEmpty, isNil, last } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../core/storage';
import { Collections, getKYCPresetsForCustomerModule } from '../../database';
import {
    Application,
    ApplicationDeclinedAuditTrail,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    ConfiguratorApplication,
    EventApplication,
    FinderApplication,
    FinderVehicle,
    InventoryKind,
    LocalVariant,
    ModuleType,
    ReservationStockStatus,
    StandardApplication,
    StandardApplicationModule,
    StockInventoryKind,
    VehicleKind,
} from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerEmail, getCustomerFullNameWithTitle, getCustomerFullName } from '../../database/helpers/customers';
import { AudienceMessage, createCompanySMTPTransports, sendApplicationRejectConfirmation } from '../../emails';
import { DbsWebhookPayload } from '../../integrations/banks/dbs/types';
import { StandardApplicationModuleAsset } from '../../schema/resolvers/enums';
import { getApplicationIdentifierForAction, getApplicationLogStages, getStatusUpdates } from '../../utils/application';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';
import { canSendEmailToCustomer } from './sendApplicationSubmissionMail/shared';
import checkIsBankViewable from './shared/checkIsBankViewable';
import getApplicationAssignee from './shared/getApplicationAssignee';
import getApplicationContext from './shared/getApplicationContext';
import getApplicationDetailsLink from './shared/getApplicationDetailsLink';
import getApplicationStagesLink from './shared/getApplicationStagesLink';
import { getStandardApplicationEmailContent } from './shared/getStandardApplicationEmailContents';

export enum ApplicationDeclineSource {
    HLF = 'hlf',
    UOB = 'uob',
    User = 'user',
    DBS = 'dbs',
    Maybank = 'maybank',
}

export type OnUserApplicationDeclinedMessage = {
    source: ApplicationDeclineSource.User;
    applicationId: ObjectId;
    userId: ObjectId;
    stage: ApplicationStage;
};

export type OnHlfIntegrationApplicationDeclinedMessage = {
    source: ApplicationDeclineSource.HLF;
    bankId: ObjectId;
    applicationId: ObjectId;
    reference: string;
    statusRemark: string;
};

export type OnUobIntegrationApplicationDeclinedMessage = {
    source: ApplicationDeclineSource.UOB;
    bankId: ObjectId;
    applicationId: ObjectId;
    reference: string;
    subDescription: string;
};

export type OnDbsIntegrationApplicationDeclinedMessage = {
    source: ApplicationDeclineSource.DBS;
    bankId: ObjectId;
    applicationId: ObjectId;
    partnerCode: string;
    payload: DbsWebhookPayload;
};

export type OnMaybankIntegrationApplicationDeclinedMessage = {
    source: ApplicationDeclineSource.Maybank;
    bankId: ObjectId;
    applicationId: ObjectId;
    reference: string;
};

export type OnApplicationDeclinedMessage =
    | OnHlfIntegrationApplicationDeclinedMessage
    | OnUobIntegrationApplicationDeclinedMessage
    | OnUserApplicationDeclinedMessage
    | OnDbsIntegrationApplicationDeclinedMessage
    | OnMaybankIntegrationApplicationDeclinedMessage;

const generateAuditTrail = (
    message: OnApplicationDeclinedMessage,
    application: Application
): ApplicationDeclinedAuditTrail => {
    const auditTrail: Omit<ApplicationDeclinedAuditTrail, 'author'> = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationDeclined,
        _date: new Date(),
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(application, AuditTrailKind.ApplicationDeclined),
    };

    switch (message.source) {
        case ApplicationDeclineSource.UOB:
        case ApplicationDeclineSource.HLF:
        case ApplicationDeclineSource.DBS:
        case ApplicationDeclineSource.Maybank:
            return {
                ...auditTrail,
                author: { kind: AuthorKind.Bank, id: message.bankId },
            };

        case ApplicationDeclineSource.User:
            return {
                ...auditTrail,
                author: { kind: AuthorKind.User, id: message.userId },
            };

        default:
            return null;
    }
};

const getStateStatus = (message: OnApplicationDeclinedMessage) => {
    switch (message.source) {
        case ApplicationDeclineSource.HLF:
            return {
                statusRemark: message.statusRemark,
                bankReferenceNumber: message.reference,
            };

        case ApplicationDeclineSource.UOB:
            return {
                statusRemark: message.subDescription,
                bankReferenceNumber: message.reference,
            };

        case ApplicationDeclineSource.User:
        case ApplicationDeclineSource.DBS:
            return {
                statusRemark: '',
            };

        case ApplicationDeclineSource.Maybank: {
            return {
                bankReferenceNumber: message.reference,
            };
        }

        default:
            throw new Error('ApplicationDeclineSource not found');
    }
};

export const releaseStock = async (
    inventoryKind: InventoryKind,
    configuratorId: ObjectId,
    collections: Collections,
    suiteId: ObjectId
) => {
    const inventory = await collections.inventories.findOne({
        configuratorId,
        _kind: inventoryKind,
        'stocks.applicationId': suiteId,
    });

    const stock = inventory.stocks.find(
        stock => stock._kind === StockInventoryKind.ConfiguratorStock && stock.applicationId.equals(suiteId)
    );

    if (!stock || stock._kind !== StockInventoryKind.ConfiguratorStock) {
        return;
    }
    // remove the reserved stock when cancelling the application
    await collections.inventories.findOneAndUpdate(
        { _id: stock.inventoryId },
        {
            $unset: { 'stocks.$[stock].applicationId': '' },
            $set: { 'stocks.$[stock].reservationStatus': ReservationStockStatus.Available },
        },
        {
            arrayFilters: [{ 'stock._id': stock._id }],
        }
    );
};

const sendOutDeclineEmail = async (
    i18n: i18n,
    application: ConfiguratorApplication | StandardApplication | FinderApplication | EventApplication,
    t: TFunction,
    stage: ApplicationStage
) => {
    const { collections } = await getDatabaseContext();

    const context = await getApplicationContext<LocalVariant | FinderVehicle>(application._id);
    const assignee = await getApplicationAssignee(application, context.lead, stage);

    const { customer, company, variant, dealer, customerModule, lead } = context;
    // load mail context
    const emailContext = await getCompanyEmailContext(company);
    const edmEmailFooterContext = await getEdmEmailFooterContext({ company });
    const dealerEmailContext = await getDealerEmailContext(dealer);

    const defaultIntroImage =
        variant._kind === VehicleKind.FinderVehicle
            ? last(variant.listing.vehicle.images.edges[0].node.variants).url
            : await getUrlForUpload(variant?.images?.[0]);

    const transporter = await createCompanySMTPTransports(company);

    const link = await getApplicationDetailsLink(application, lead);

    const identifier = getApplicationIdentifierForAction(application, lead);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);
    const isBankViewable = await checkIsBankViewable(variant._versioning.suiteId, application.moduleId);
    const applicationStagesLink = await getApplicationStagesLink(application, lead);

    const getEmailData = (audience: AudienceMessage) => ({
        ...context,
        assignee,

        emailContext,

        edmEmailFooterContext,
        dealerEmailContext,
        introImageUrl: defaultIntroImage,

        link,

        audience,
        isBankViewable,

        applicationStagesLink,
    });

    if (application.kind === ApplicationKind.Standard) {
        const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
            _id: application.moduleId,
            _type: ModuleType.StandardApplicationModule,
        });

        const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
            await getStandardApplicationEmailContent({
                i18n,
                standardApplicationModule,
                standardApplicationAsset: StandardApplicationModuleAsset.SalesPersonRejected,
                emailContext,
                dealer,
                customer,
                customerModule,
                user: assignee,
                link,
                initiatorName: assignee.displayName,
                identifier: getApplicationIdentifierForAction(application, lead),
                lead,
            });
        // send the email to sales person
        await sendApplicationRejectConfirmation(
            {
                i18n,
                data: {
                    ...getEmailData(AudienceMessage.Salesperson),
                    introImageUrl: introImageUrl ?? defaultIntroImage,
                    introTitle,
                    contentText,
                    isSummaryVehicleVisible,
                },
                to: { name: assignee.displayName, address: assignee.email },
                subject: emailSubject,
            },
            transporter,
            emailContext.sender
        );

        if (canSendEmailToCustomer(application, context.bank, null, context.journey, false, customer, t)) {
            const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
                await getStandardApplicationEmailContent({
                    i18n,
                    standardApplicationModule,
                    standardApplicationAsset: StandardApplicationModuleAsset.CustomerRejected,
                    emailContext,
                    dealer,
                    customer,
                    customerModule,
                    user: assignee,
                    link,
                    identifier: getApplicationIdentifierForAction(application, lead),
                    lead,
                });
            await sendApplicationRejectConfirmation(
                {
                    i18n,
                    data: {
                        ...getEmailData(AudienceMessage.Customer),
                        introImageUrl: introImageUrl ?? defaultIntroImage,
                        introTitle,
                        contentText,
                        isSummaryVehicleVisible,
                    },
                    to: {
                        name: getCustomerFullName(t, customer, company, kycPresets),
                        address: getCustomerEmail(t, customer),
                    },
                    subject: emailSubject,
                },
                transporter,
                emailContext.sender
            );
        }

        return;
    }

    // send the email to sales person
    await sendApplicationRejectConfirmation(
        {
            i18n,
            data: getEmailData(AudienceMessage.Salesperson),
            to: { name: assignee.displayName, address: assignee.email },
            subject: t('emails:salesPersonRejectConfirmation.subject', {
                companyName: emailContext.companyName,
                identifier,
                customerName: customerFullName,
            }),
        },
        transporter,
        emailContext.sender
    );

    if (canSendEmailToCustomer(application, context.bank, null, context.journey, false, customer, t)) {
        await sendApplicationRejectConfirmation(
            {
                i18n,
                data: getEmailData(AudienceMessage.Customer),
                to: {
                    name: getCustomerFullName(t, customer, company, kycPresets),
                    address: getCustomerEmail(t, customer),
                },
                subject: t('emails:customerRejectConfirmation.subject', {
                    companyName: emailContext.companyName,
                    identifier,
                    customerName: customerFullName,
                }),
            },
            transporter,
            emailContext.sender
        );
    }
};

export const onApplicationDeclined = async (message: OnApplicationDeclinedMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();

    const { applicationId } = message;

    const application = await collections.applications.findOne({ _id: applicationId, '_versioning.isLatest': true });

    if (isNil(application)) {
        throw new Error('unable to locate application');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    const stage = message.source === ApplicationDeclineSource.User ? message.stage : ApplicationStage.Financing;

    // update application and journey
    if (message.source === ApplicationDeclineSource.HLF || message.source === ApplicationDeclineSource.Maybank) {
        const { reference } = message;
        await collections.applicationJourneys.findOneAndUpdate(
            { applicationSuiteId: application._versioning.suiteId },
            { $set: { isImmutable: false, 'submission.reference': reference } }
        );
    }

    if (message.source === ApplicationDeclineSource.UOB) {
        const { reference, subDescription } = message;
        await collections.applicationJourneys.updateOne(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    isImmutable: false,
                    'submission.reference': reference,
                    'submission.subDescription': subDescription,
                },
            }
        );
    }

    if (message.source === ApplicationDeclineSource.DBS) {
        const { partnerCode } = message;
        await collections.applicationJourneys.updateOne(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    isImmutable: false,
                    'submission.partnerCode': partnerCode,
                },
            }
        );
    }

    const statusUpdates = getStatusUpdates(application, lead, ApplicationStatus.Declined);
    if (!isEmpty(statusUpdates)) {
        await collections.applications.findOneAndUpdate({ _id: application._id }, { $set: statusUpdates });
    }

    // create audit trail
    await collections.auditTrails.insertOne(generateAuditTrail(message, application));

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails', 'calculators', 'variant']);

    const { t } = i18n;

    switch (application.kind) {
        case ApplicationKind.Configurator: {
            await sendOutDeclineEmail(i18n, application, t, stage);
            break;
        }

        case ApplicationKind.Event:
        case ApplicationKind.Standard:
        case ApplicationKind.Finder: {
            await sendOutDeclineEmail(i18n, application, t, stage);
            break;
        }

        default:
            throw new Error('ApplicationKind not support');
    }
};
