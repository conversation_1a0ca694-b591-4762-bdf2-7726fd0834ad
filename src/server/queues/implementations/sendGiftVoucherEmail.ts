import { ObjectId, Document } from 'bson';
import { Job } from 'bull';
import urljoin from 'url-join';
import config from '../../core/config';
import { getUrlForUpload } from '../../core/storage';
import {
    EndpointType,
    LocalCustomerManagementModule,
    LocalVariant,
    ModuleType,
    StockInventoryKind,
    getKYCPresetsForCustomerModule,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getCustomerEmail, getCustomerName } from '../../database/helpers/customers';
import { createCompanySMTPTransports, sendGiftVoucherConfirmation } from '../../emails';
import getTranslatedEmailContent from '../../emails/utils/getTranslatedEmailContent';
import createLoaders from '../../loaders';
import createI18nInstance from '../../utils/createI18nInstance';
import getCompanyEmailContext from '../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../utils/getEdmEmailFooterContext';

export type SendGiftVoucherHandlerMessage = {
    giftVoucherId: ObjectId;
};
export const sendGiftVoucherEmailHandler = async (message: SendGiftVoucherHandlerMessage, job: Job<Document>) => {
    const { giftVoucherId } = message;

    const loaders = createLoaders();

    const { collections } = await getDatabaseContext();

    const giftVoucher = await collections.giftVouchers.findOne({ _id: giftVoucherId });
    if (!giftVoucher) {
        throw new Error('gift voucher not found');
    }

    const giftVoucherModule = await collections.modules.findOne({ _id: giftVoucher.moduleId });
    if (giftVoucherModule._type !== ModuleType.GiftVoucherModule) {
        throw new Error('ModuleType not support');
    }

    const journey = await collections.giftVoucherJourney.findOne({
        giftVoucherSuiteId: giftVoucher._versioning.suiteId,
    });
    if (!journey) {
        throw new Error('Application Journey not found');
    }

    const { dealerId, purchaserId, vehicleId, stockId, endpointId, routerId } = giftVoucher;
    const purchaser = await collections.customers.findOne({
        _id: purchaserId,
    });

    const [variant, company, dealer, stock, router] = await Promise.all([
        loaders.vehicleById.load(vehicleId) as Promise<LocalVariant>,
        loaders.companyById.load(giftVoucherModule.companyId),
        loaders.dealerById.load(dealerId),
        loaders.stockById.load(stockId),
        loaders.routerById.load(routerId),
    ]);

    const customerModule = (await loaders.moduleById.load(purchaser.moduleId)) as LocalCustomerManagementModule;

    const endpoint = router.endpoints.find(endpoint => endpoint._id.equals(endpointId));
    if (endpoint._type !== EndpointType.MobilityApplicationEntrypoint) {
        throw new Error('unexpected endpoint registered ');
    }
    if (stock._kind !== StockInventoryKind.MobilityStock) {
        throw new Error('unexpected type stock registered');
    }

    const [emailContext, dealerEmailContext, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getDealerEmailContext(dealer),
        getEdmEmailFooterContext({ company }),
    ]);

    const transporter = await createCompanySMTPTransports(company);

    const { i18n } = await createI18nInstance(giftVoucher.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['emails', 'calculators', 'common']);
    const { t } = i18n;

    const kycPresets = getKYCPresetsForCustomerModule(customerModule, purchaser._kind);

    const purchaserName = getCustomerName(t, purchaser, company, kycPresets);

    const purchaserEmail = getCustomerEmail(t, purchaser);
    const introImageUrl = await getUrlForUpload(variant.images?.[0]);

    const { contentText, introTitle, subject } = giftVoucherModule.emailContents.customer.confirmation;
    const vehicleUrl = urljoin(
        `${config.protocol}://${router.hostname}`,
        router.pathname,
        endpoint.pathname,
        'details',
        stock._id.toHexString()
    );

    await sendGiftVoucherConfirmation(
        {
            i18n,
            data: {
                emailContext,
                dealerEmailContext,
                dealer,
                contentText,
                introTitle,
                customerName: purchaserName,
                voucher: giftVoucher,
                variant,
                edmEmailFooterContext,
                introImageUrl,
                vehicleUrl,
            },
            to: {
                address: purchaserEmail,
                name: purchaserName,
            },
            subject: getTranslatedEmailContent({
                dealerTranslationText: subject,
                preferredDealerId: dealer._id.toHexString(),
                i18nLanguage: i18n.language,
            }),
        },
        transporter,
        emailContext.sender
    );
};

export default sendGiftVoucherEmailHandler;
