import { Document } from 'bson';
import { Job } from 'bull';
import { isEmpty } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    ApplicationSigningStatus,
    ApplicationStage,
    ApplicationStatus,
    LocalCustomerManagementModule,
    ModuleType,
} from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getApplicantIdFromApplication } from '../../database/helpers/applications';
import { createSigning } from '../../journeys/helper';
import generateTestDriveApplicationPdf from '../../journeys/helper/generateTestDriveApplicationPdf';
import { isValidApplicationModuleForAgreementPdf } from '../../journeys/helper/isValidApplicationForAgreement';
import createLoaders from '../../loaders';
import { getSigningSettings } from '../../signing/helper';
import { getApplicationIdentifier, getStatusUpdates } from '../../utils/application';

export type OnTestDriveSigningInitializedMessage = {
    applicationId: ObjectId;
    clientAction: string;
    signerType: 'applicant' | 'guarantor';
    retries?: number;
};

export const onTestDriveSigningInitializedHandler = async (
    message: OnTestDriveSigningInitializedMessage,
    job: Job<Document>
) => {
    const { collections } = await getDatabaseContext();
    const { applicationId, clientAction } = message;
    const application = await collections.applications.findOne({ _id: applicationId });
    const loaders = createLoaders();
    if (!application) {
        throw new Error('Application is missing');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });
    if (!lead) {
        throw new Error('Lead is missing');
    }

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    if (!applicationModule) {
        throw new Error('Application module is missing');
    }

    if (!isValidApplicationModuleForAgreementPdf(applicationModule)) {
        throw new Error('Application module is invalid for signing');
    }

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });
    if (!journey || !journey.testDriveSetting?.signingModuleId) {
        throw new Error('Fail to retrieve signing for test drive');
    }

    const signingModule = await collections.modules.findOne({ _id: journey.testDriveSetting.signingModuleId });
    if (
        !signingModule ||
        (signingModule._type !== ModuleType.NamirialSigningModule && signingModule._type !== ModuleType.Docusign)
    ) {
        throw new Error('Signing module for namirial not found');
    }

    const company = await collections.companies.findOne({ _id: signingModule.companyId });
    const setting = await getSigningSettings(signingModule._id);
    const customer = await collections.customers.findOne({
        _id:
            message.signerType === 'applicant'
                ? getApplicantIdFromApplication(application)
                : application.guarantorId[0],
    });

    const customerModule = (await collections.modules.findOne({
        _id: customer.moduleId,
    })) as LocalCustomerManagementModule;

    try {
        const pdf = await generateTestDriveApplicationPdf(application, loaders);

        const identifier = getApplicationIdentifier(application, lead, [ApplicationStage.Appointment]);

        const pdfs = [{ name: `${identifier}.pdf`, pdf }];

        const { envelopeId, redirectionUrl, signingMode, nonce } = await createSigning(
            pdfs,
            {
                application,
                customer,
                setting,
                journeyIdentifier: 'test-drive-namirial-signing',
                company,
                customerModule,
            },
            { clientAction }
        );

        await collections.applicationJourneys.findOneAndUpdate(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    testDriveProcessSigning: {
                        moduleId: signingModule._id,
                        completed: false,
                        kind: signingMode,
                        status: ApplicationSigningStatus.Initialized,
                        envelopeId,
                        redirectionUrl,
                        nonce,
                    },
                },
            }
        );
    } catch (error) {
        const statusUpdates = getStatusUpdates(application, lead, ApplicationStatus.SigningCreationFailed);
        if (!isEmpty(statusUpdates)) {
            await collections.applications.findOneAndUpdate({ _id: application._id }, { $set: statusUpdates });
        }

        console.error(error);

        throw new Error('Failed to create signing ', error);
    }
};
