import { lazy } from '@loadable/component';
import { ErrorBoundary } from '@sentry/react';
import { useMemo } from 'react';
import { Routes, Route } from 'react-router-dom';
import { useRouter, useRouterRoutes } from '../components/contexts/shared';
import LicensePage from '../pages/LicensePage';
import usePathScriptObserver from '../pages/portal/EventApplicationEntrypoint/usePathScriptObserver';
import { useRuntimeConfig } from '../runtimeConfig';

const AdminRouter = lazy(() => import(/* webpackChunkName: "adminApplication" */ './AdminRouter'));
const PortalRouter = lazy(() => import(/* webpackChunkName: "publicApplication" */ './PortalRouter'));
const LoginRouter = lazy(() => import(/* webpackChunkName: "loginApplication" */ './LoginRouter'));
const ExternalLinkPage = lazy(() => import(/* webpackChunkName: "externalLinkPage" */ '../pages/ExternalLinkPage'));
const OIDCPage = lazy(() => import(/* webpackChunkName: "oidcPage" */ '../pages/login/OIDCPage'));

const useRouterPathScripts = () => {
    const router = useRouter(false);

    const specs = useMemo(() => router?.pathScripts ?? [], []);

    usePathScriptObserver(specs);
};

const MainRouter = () => {
    const { router } = useRuntimeConfig();
    const routes = useRouterRoutes();
    const hasRoutesWithAuthentication = routes.some(route => route.hasAuthentication);
    const hasLoginRouter = hasRoutesWithAuthentication || router.withAdmin;

    useRouterPathScripts();

    return (
        <ErrorBoundary>
            <Routes>
                {hasLoginRouter ? <Route element={<LoginRouter />} path="auth/*" /> : null}
                {router.withAdmin ? <Route element={<AdminRouter />} path="admin/*" /> : null}
                <Route element={<ExternalLinkPage />} path="l/:id" />
                <Route element={<LicensePage />} path="licenses" />
                <Route element={<OIDCPage />} path=".oidc/authorize" />
                <Route element={<PortalRouter />} path="*" />
            </Routes>
        </ErrorBoundary>
    );
};

export default MainRouter;
