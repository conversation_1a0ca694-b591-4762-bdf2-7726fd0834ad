import { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import { CompanyContextDataFragment } from '../api/fragments/CompanyContextData';
import PortalLoadingElement from '../components/PortalLoadingElement';
import ApplyCompanyTheming from '../components/contexts/ApplyCompanyTheming';
import { useCompany } from '../components/contexts/CompanyContextManager';
import { useRouterRoutes } from '../components/contexts/shared';
import MaintenanceResult from '../components/results/MaintenanceResult';
import DynamicPortalLayout from '../layouts/DynamicPortalLayout';
import ExternalLinkPage from '../pages/ExternalLinkPage';
import CalculationPage from '../pages/portal/CalculationPage';
import { MaintenanceSettings, MaintenanceWatcher } from '../pages/portal/Maintenance';
import NotFoundPage from '../pages/portal/NotFoundPage';

const PortalRouter = () => {
    const company = useCompany(true);
    const routes = useRouterRoutes();

    const childrenRoutes = [
        /* dummy content for debug purposes */
        <Route key="calculation" element={<CalculationPage />} path="calculation" />,
        /* routes computed by the router */
        ...routes.map(route => <Route {...route.routeProps} key={route.id} />),
        <Route key="externalLink" element={<ExternalLinkPage />} path="/:pathname/l/:id" />,
        /* fallback for 404 */
        <Route key="404" element={<NotFoundPage />} path="*" />,
    ];
    if (company) {
        return <CompanyPortalRouter company={company} routes={childrenRoutes} />;
    }

    return (
        <DynamicPortalLayout>
            <Routes>{childrenRoutes}</Routes>
        </DynamicPortalLayout>
    );
};

const CompanyRoutes = ({
    watcherMounted,
    maintenance,
    routes: normalRoutes,
}: {
    watcherMounted: boolean;
    maintenance: MaintenanceSettings | null | undefined;
    routes: JSX.Element[];
}) => {
    if (!watcherMounted) {
        return <PortalLoadingElement />;
    }

    const routes = maintenance ? (
        <Route key="maintenance" element={<MaintenanceResult maintenance={maintenance} />} path="*" />
    ) : (
        normalRoutes
    );

    return (
        <DynamicPortalLayout>
            <Routes>{routes}</Routes>
        </DynamicPortalLayout>
    );
};

const CompanyPortalRouter = ({ company, routes }: { company: CompanyContextDataFragment; routes: JSX.Element[] }) => {
    const [maintenance, setMaintenance] = useState<MaintenanceSettings | undefined>();
    const [watcherMounted, setWatcherMounted] = useState(false);

    return (
        <ApplyCompanyTheming>
            <>
                <MaintenanceWatcher
                    company={company}
                    setMaintenancePageContent={setMaintenance}
                    setWatcherMounted={setWatcherMounted}
                />
                <CompanyRoutes maintenance={maintenance} routes={routes} watcherMounted={watcherMounted} />
            </>
        </ApplyCompanyTheming>
    );
};

export default PortalRouter;
