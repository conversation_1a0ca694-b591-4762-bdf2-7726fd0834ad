import { useReducer } from 'react';
import { EventSortingField, EventSortingRule, SortingOrder, EventFilteringRule } from '../../../api/types';
import { ExtendedPageState, ExtendedPageAction, extendedReducer } from '../../../components/PaginatedTableWithContext';
import { TableSortingOrder, SortOrderValue } from '../../../utilities/useSortAndFilterCache';

type SortRule = EventSortingRule & SortOrderValue;
export type State = ExtendedPageState & {
    sort: SortRule;
    filter: EventFilteringRule;
};

export type SetSortAction = { type: 'setSort'; sortBy: SortRule };
export type SetFilterAction = { type: 'setFilter'; filterBy: EventFilteringRule };
export type SetPartialFilterAction = { type: 'setPartialFilter'; filterBy: EventFilteringRule };
export type Action = ExtendedPageAction | SetSortAction | SetFilterAction | SetPartialFilterAction;

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'setPage':
            return { ...state, page: action.page };

        case 'setPageSize':
            return { ...state, page: 1, pageSize: action.pageSize };

        case 'setSort':
            return { ...state, page: 1, sort: action.sortBy };

        case 'setFilter':
            return { ...state, page: 1, filter: action.filterBy };

        case 'setPartialFilter':
            return { ...state, page: 1, filter: { ...state.filter, ...action.filterBy } };

        default:
            return extendedReducer(state, action);
    }
};

const useListReducer = (cache?: State) =>
    useReducer(reducer, {
        companyId: cache?.companyId,
        // default pagination
        page: cache?.page || 1,
        pageSize: cache?.pageSize || 10,

        // default sorting
        sort: cache?.sort || {
            field: EventSortingField.EventId,
            order: SortingOrder.Desc,
            orderValue: TableSortingOrder.Desc,
        },

        filter: cache?.filter || { privateAccesses: [], actives: [] },
    });

export default useListReducer;
