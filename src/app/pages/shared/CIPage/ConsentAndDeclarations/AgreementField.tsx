import { Card, Row } from 'antd';
import { useFormikContext } from 'formik';
import { get, isEmpty, isNil, omit } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import styled, { css } from 'styled-components';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import { ConsentsAndDeclarationsType, LegalTextPosition } from '../../../../api/types';
import ConsentCheckBoxField from '../../../../components/fields/ConsentCheckBoxField';
import CheckboxField from '../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../themes/hooks';
import renderMarkdown from '../../../../utilities/renderMarkdown';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { CNDStyledTextSpan } from '../ui';
import PlatformCheckbox from './PlatformCheckbox';
import CheckboxContainer from './shared';

const Container = styled.div`
    display: flex;

    & .ant-form-item {
        margin-bottom: 0;
    }
`;

const Title = styled.h4<{ marginBottomSize?: 'default' | 'large'; $disabled?: boolean }>`
    font-weight: 900;
    font-size: 16px;
    ${({ marginBottomSize }) => marginBottomSize === 'large' && 'margin-bottom: 1rem;'}

    ${props =>
        props.$disabled &&
        css`
            cursor: not-allowed;
            pointer-events: none;
            opacity: 0.38;
        `}

    &.required::after {
        content: '*';
        margin-left: 4px;
        color: #ff4d4f;
        font-size: 14px;
        line-height: 1;
        font-weight: 400;
    }
`;

const MarketingContainer = styled(Container)`
    flex-direction: column;
`;

export const CndContainer = styled.div`
    line-height: 1.75;
`;

export const StyledRow = styled(Row)`
    justify-content: end;
`;

const StyledCard = styled(Card)`
    ${({ bordered = true }) =>
        bordered &&
        css`
            border: 1px solid #d9d9d9;
            border-radius: var(--card-border-radius, initial);
        `}

    & .ant-card-body {
        word-break: break-word;
        padding: ${({ bordered = true }) => (bordered ? '20px 24px' : '0')};
    }
`;

export type AgreementFieldProps = {
    prefix?: string;
    agreement: ApplicationAgreementDataFragment;
    disabled?: boolean;
    editable?: boolean;
    bordered?: boolean;
    // flag to indicate if the agreement is for display purposes only
    displayOnly?: boolean;
};

const initialPlatformsAgreed = {
    email: false,
    fax: false,
    phone: false,
    mail: false,
    sms: false,
};

const AgreementField = ({
    agreement,
    prefix,
    disabled = false,
    editable = true,
    bordered = true,
    displayOnly = false,
}: AgreementFieldProps) => {
    const fieldName = prefix ? `${prefix}.${agreement.id}` : agreement.id;
    const translatedString = useTranslatedString();
    const [visible, setVisible] = useState(false);
    const { ConsentModal, Checkbox, LegalText } = useThemeComponents();

    const { setFieldValue, values } = useFormikContext();

    const checkLegalTextPresent = useCallback(
        (agreement: ApplicationAgreementDataFragment) => {
            if (agreement.__typename === 'CheckboxApplicationAgreement') {
                return (
                    !isNil(translatedString(agreement.legalMarkup)) && !isEmpty(translatedString(agreement.legalMarkup))
                );
            }

            return false;
        },
        [translatedString]
    );

    const getCheckboxComponent = useCallback(
        (agreement: ApplicationAgreementDataFragment) => {
            if (agreement.__typename === 'CheckboxApplicationAgreement') {
                if (agreement.legalTextPosition === LegalTextPosition.Modal && checkLegalTextPresent(agreement)) {
                    return ConsentCheckBoxField;
                }

                return CheckboxField;
            }

            if (agreement.__typename === 'MarketingApplicationAgreement') {
                return ConsentCheckBoxField;
            }

            return CheckboxField;
        },
        [checkLegalTextPresent]
    );

    const actions = useMemo(
        () => ({
            onOK: () => {
                switch (agreement.__typename) {
                    case 'TextApplicationAgreement':
                        setFieldValue(`${fieldName}.isAgreed`, true);
                        break;

                    case 'CheckboxApplicationAgreement':
                        setFieldValue(`${fieldName}.isAgreed`, true);
                        setVisible(false);
                        break;

                    case 'MarketingApplicationAgreement':
                        setFieldValue(`${fieldName}.platformsAgreed`, omit('__typename', agreement.platform));
                        setFieldValue(`${fieldName}.isAgreed`, true);
                        break;

                    default:
                        throw new Error('Application agreement type is not supported');
                }
            },
            onCancel: () => {
                if (!editable) {
                    setVisible(false);

                    return;
                }

                if (agreement.type === ConsentsAndDeclarationsType.Marketing) {
                    setFieldValue(`${fieldName}.platformsAgreed`, initialPlatformsAgreed);
                    setFieldValue(`${fieldName}.isAgreed`, false);

                    return;
                }

                setFieldValue(`${fieldName}.isAgreed`, false);
                setVisible(false);
            },
            onCardClick:
                !disabled &&
                agreement.__typename === 'CheckboxApplicationAgreement' &&
                agreement.legalTextPosition === LegalTextPosition.Modal &&
                checkLegalTextPresent(agreement) &&
                !displayOnly
                    ? () => setVisible(true)
                    : undefined,
        }),
        [disabled, agreement, checkLegalTextPresent, displayOnly, setFieldValue, fieldName, editable]
    );

    const CheckboxComponent = getCheckboxComponent(agreement);

    const renderTextAgreement = (agreement: ApplicationAgreementDataFragment) => (
        <Container>
            <CndContainer>
                <CNDStyledTextSpan $disabled={disabled}>
                    <CNDStyledTextSpan $disabled={disabled}>
                        {renderMarkdown(translatedString(agreement.description))}
                    </CNDStyledTextSpan>
                </CNDStyledTextSpan>
            </CndContainer>
        </Container>
    );

    // Renders legal text in a text field
    const renderLegalText = (agreement: ApplicationAgreementDataFragment) => {
        if (agreement.__typename !== 'CheckboxApplicationAgreement') {
            return null;
        }

        return (
            <CNDStyledTextSpan $disabled={disabled}>
                {renderMarkdown(translatedString(agreement.legalMarkup))}
            </CNDStyledTextSpan>
        );
    };

    // Renders the description beside the checbox void of legal text
    // Also used when modal is enabled but legal text is empty
    const renderCheckbox = (agreement: ApplicationAgreementDataFragment) => {
        if (agreement.__typename !== 'CheckboxApplicationAgreement') {
            return null;
        }

        return (
            <CheckboxComponent
                checked={get(`${fieldName}.isAgreed`, values)}
                customComponent={Checkbox}
                disabled={disabled}
                name={`${fieldName}.isAgreed`}
                required={agreement.isMandatory}
            >
                <CNDStyledTextSpan $disabled={disabled}>
                    {renderMarkdown(translatedString(agreement.description))}
                </CNDStyledTextSpan>
            </CheckboxComponent>
        );
    };

    const renderLegalTextBeforeCheckbox = (agreement: ApplicationAgreementDataFragment) => {
        if (agreement.__typename !== 'CheckboxApplicationAgreement') {
            return null;
        }

        return (
            <>
                <LegalText
                    $disabled={disabled}
                    onClick={get(`${fieldName}.isAgreed`, values) ? actions.onCancel : actions.onOK}
                    isBefore
                >
                    {renderMarkdown(translatedString(agreement.legalMarkup))}
                </LegalText>
                {renderCheckbox(agreement)}
            </>
        );
    };

    const renderLegalTextAfterCheckbox = (agreement: ApplicationAgreementDataFragment) => {
        if (agreement.__typename !== 'CheckboxApplicationAgreement') {
            return null;
        }

        return (
            <>
                {renderCheckbox(agreement)}
                <LegalText
                    $disabled={disabled}
                    isBefore={false}
                    onClick={get(`${fieldName}.isAgreed`, values) ? actions.onCancel : actions.onOK}
                >
                    {renderMarkdown(translatedString(agreement.legalMarkup))}
                </LegalText>
            </>
        );
    };

    // Renders the legal text before or after the checkbox component
    const renderCheckboxWithLegalTextPosition = (agreement: ApplicationAgreementDataFragment) => {
        if (
            agreement.__typename === 'CheckboxApplicationAgreement' &&
            agreement.legalTextPosition === LegalTextPosition.Before
        ) {
            return renderLegalTextBeforeCheckbox(agreement);
        }
        if (
            agreement.__typename === 'CheckboxApplicationAgreement' &&
            agreement.legalTextPosition === LegalTextPosition.After
        ) {
            return renderLegalTextAfterCheckbox(agreement);
        }

        return null;
    };

    const renderCheckboxWithModal = (agreement: ApplicationAgreementDataFragment, disabled?: boolean) => {
        if (agreement.__typename !== 'CheckboxApplicationAgreement') {
            return null;
        }
        // Disabled in admin. Always show legal text after description in admin
        if (disabled) {
            return renderLegalTextAfterCheckbox(agreement);
        }

        return renderCheckbox(agreement);
    };

    // Decides if legal text should appear before or after the checkbox
    // If there does not exist legal text, it will not be displayed at all
    const renderCheckboxWithoutModal = (agreement: ApplicationAgreementDataFragment) => {
        if (agreement.__typename !== 'CheckboxApplicationAgreement') {
            return null;
        }
        // If no legal text, then it should just display the description
        if (checkLegalTextPresent(agreement)) {
            return renderCheckboxWithLegalTextPosition(agreement);
        }

        return renderCheckbox(agreement);
    };

    const renderCheckboxAgreement = (agreement: ApplicationAgreementDataFragment) => {
        if (agreement.__typename !== 'CheckboxApplicationAgreement') {
            return null;
        }

        return (
            <Container>
                <CheckboxContainer>
                    {agreement.legalTextPosition === LegalTextPosition.Modal
                        ? renderCheckboxWithModal(agreement, disabled)
                        : renderCheckboxWithoutModal(agreement)}
                </CheckboxContainer>
            </Container>
        );
    };

    const renderMarketingAgreement = (agreement: ApplicationAgreementDataFragment) => {
        if (agreement.__typename !== 'MarketingApplicationAgreement') {
            return null;
        }

        return (
            <MarketingContainer>
                <CheckboxContainer>
                    <CheckboxComponent
                        checked={get(`${fieldName}.isAgreed`, values)}
                        customComponent={Checkbox}
                        disabled={disabled}
                        name={`${fieldName}.isAgreed`}
                        onClick={get(`${fieldName}.isAgreed`, values) ? actions.onCancel : actions.onOK}
                        required={agreement.isMandatory}
                    >
                        <CNDStyledTextSpan $disabled={disabled}>
                            {renderMarkdown(translatedString(agreement.description))}
                        </CNDStyledTextSpan>
                    </CheckboxComponent>
                </CheckboxContainer>
                <CndContainer>
                    <PlatformCheckbox agreement={agreement} disabled={disabled} prefix={fieldName} />
                </CndContainer>
            </MarketingContainer>
        );
    };

    const renderAgreementCard = (agreement: ApplicationAgreementDataFragment) => {
        switch (agreement.type) {
            case ConsentsAndDeclarationsType.Text:
                return renderTextAgreement(agreement);

            case ConsentsAndDeclarationsType.Checkbox:
                return renderCheckboxAgreement(agreement);

            case ConsentsAndDeclarationsType.Marketing:
                return renderMarketingAgreement(agreement);

            default:
                throw new Error('Agreement type not supported');
        }
    };

    return (
        <>
            {agreement.__typename === 'CheckboxApplicationAgreement' &&
                agreement.legalTextPosition === LegalTextPosition.Modal && (
                    <ConsentModal
                        key={agreement.id}
                        closable={false}
                        data-cy="consent-modal"
                        footer={!editable ? null : undefined}
                        onCancel={actions.onCancel}
                        onOk={actions.onOK}
                        open={visible}
                        title={translatedString(agreement.title)}
                        centered
                    >
                        {checkLegalTextPresent(agreement) && renderLegalText(agreement)}
                    </ConsentModal>
                )}
            <StyledCard bordered={bordered} onClick={actions.onCardClick}>
                {translatedString(agreement.title) && (
                    <Title
                        $disabled={disabled}
                        className={
                            agreement.__typename !== 'TextApplicationAgreement' && agreement.isMandatory === true
                                ? 'required'
                                : ''
                        }
                        marginBottomSize={agreement.__typename === 'TextApplicationAgreement' ? 'default' : 'large'}
                    >
                        {translatedString(agreement.title)}
                    </Title>
                )}
                {renderAgreementCard(agreement)}
            </StyledCard>
        </>
    );
};

export default AgreementField;
