import { Row, Col } from 'antd';
import { useFormikContext } from 'formik';
import { get, set } from 'lodash/fp';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import ConsentCheckBoxField from '../../../../components/fields/ConsentCheckBoxField';
import { useThemeComponents } from '../../../../themes/hooks';
import { CNDStyledTextSpan } from '../ui';
import CheckboxContainer from './shared';

const PlatformContainer = styled(Col).attrs(() => ({
    span: 12,
}))`
    display: flex;
    margin-top: 1rem;
    height: 2rem;
`;

const StyledRow = styled(Row)`
    margin-left: 2.5rem;
`;

export type PlatformCheckboxProps = {
    prefix?: string;
    agreement: Extract<ApplicationAgreementDataFragment, { __typename: 'MarketingApplicationAgreement' }>;
    disabled?: boolean;
};

const PlatformCheckbox = ({ prefix, agreement, disabled }: PlatformCheckboxProps) => {
    const { t } = useTranslation('consent');
    const { Checkbox } = useThemeComponents();
    const { setFieldValue, values } = useFormikContext();

    const getFieldName = (field: string) => `${prefix}.platformsAgreed.${field}`;

    const togglePlatformCheck = useCallback(
        (field: string) => {
            const value = !get(`${prefix}.platformsAgreed.${field}`, values);
            setFieldValue(`${prefix}.platformsAgreed.${field}`, value);

            const platformsAgreed = get(`${prefix}.platformsAgreed`, values);
            const platformsAgreedValues = Array.from(Object.values(set(field, value, platformsAgreed)));

            setFieldValue(
                `${prefix}.isAgreed`,
                platformsAgreedValues.some(value => value === true)
            );
        },
        [prefix, setFieldValue, values]
    );

    const renderPlatformField = (platform: string) => (
        <PlatformContainer key={platform}>
            <CheckboxContainer>
                <ConsentCheckBoxField
                    customComponent={Checkbox}
                    disabled={disabled}
                    name={getFieldName(platform)}
                    onClick={() => togglePlatformCheck(platform)}
                >
                    <CNDStyledTextSpan $disabled={disabled}>
                        {t(`consent:marketingPlatforms.${platform}`)}
                    </CNDStyledTextSpan>
                </ConsentCheckBoxField>
            </CheckboxContainer>
        </PlatformContainer>
    );

    return (
        <StyledRow>
            {Object.keys(agreement.platform).map(marketingPlatform => {
                if (marketingPlatform !== '__typename' && agreement.platform[marketingPlatform]) {
                    return renderPlatformField(marketingPlatform);
                }

                return null;
            })}
        </StyledRow>
    );
};

export default PlatformCheckbox;
