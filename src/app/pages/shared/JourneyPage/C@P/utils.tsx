import { useMemo } from 'react';
import { LeadStatus, ModuleType } from '../../../../api/types';
import { LeadFormApplication } from '../../../admin/LeadDetailsPage/shared';
import { GenericFormApplication } from '../../ApplicationDetailsPage/generic/shared';

// TODO: might need to deprecate cap application functions

export const hasCapPrequalificationCheck = (application: GenericFormApplication) => {
    switch (application.__typename) {
        case 'EventApplication':
            return (
                application.module.__typename === 'EventApplicationModule' &&
                application.module.capModuleId &&
                application.event?.isCapEnabled &&
                application.event?.capPrequalification
            );

        default: {
            switch (application.module.__typename) {
                case 'ConfiguratorModule':
                case 'FinderApplicationPrivateModule':
                case 'FinderApplicationPublicModule':
                case 'StandardApplicationModule':
                    return application.module.capPrequalification;

                default:
                    return false;
            }
        }
    }
};

export const hasCapPrequalificationCheckForLead = (lead: LeadFormApplication) => {
    if (lead.__typename === 'EventLead') {
        return (
            lead.module.__typename === 'EventApplicationModule' &&
            lead.module.capModuleId &&
            lead.event?.isCapEnabled &&
            lead.event?.capPrequalification
        );
    }

    switch (lead.module.__typename) {
        default: {
            switch (lead.module.__typename) {
                case 'ConfiguratorModule':
                case 'FinderApplicationPrivateModule':
                case 'FinderApplicationPublicModule':
                case 'StandardApplicationModule':
                    return lead.module.capPrequalification;

                default:
                    return false;
            }
        }
    }
};

export const useMayPrequalifyLead = (lead: LeadFormApplication) =>
    useMemo(() => {
        const hasCapPrequalification = hasCapPrequalificationCheckForLead(lead);
        if (!hasCapPrequalification || lead.status === LeadStatus.Unqualified) {
            return false;
        }

        return lead.status === LeadStatus.PendingQualify || lead.status === LeadStatus.Contacted;
    }, [lead]);

export const useMayResubmitLeadToCap = (lead: LeadFormApplication) =>
    useMemo(() => {
        const capSubmissionStatusMayResubmit =
            lead.status === LeadStatus.SubmissionFailed || lead.status === LeadStatus.SubmittedWithError;

        if (lead.__typename === 'EventLead') {
            switch (lead.module.__typename) {
                case ModuleType.EventApplicationModule:
                    return lead.event.isCapEnabled && lead.module.capModuleId && capSubmissionStatusMayResubmit;

                default:
                    return false;
            }
        }

        switch (lead.module.__typename) {
            case ModuleType.ConfiguratorModule:
            case ModuleType.FinderApplicationPrivateModule:
            case ModuleType.FinderApplicationPublicModule:
            case ModuleType.StandardApplicationModule:
            case ModuleType.LaunchPadModule: {
                return lead.module.capModuleId && capSubmissionStatusMayResubmit;
            }

            default:
                return null;
        }
    }, [lead]);

export const useMayPrequalifyApplication = (application: GenericFormApplication) =>
    useMemo(() => {
        const hasCapPrequalification = hasCapPrequalificationCheck(application);
        if (!hasCapPrequalification || application.lead.status === LeadStatus.Unqualified) {
            return false;
        }

        return (
            application.lead.status === LeadStatus.PendingQualify || application.lead.status === LeadStatus.Contacted
        );
    }, [application]);

export const useMayResubmitApplicationToCap = (application: GenericFormApplication) =>
    useMemo(() => {
        const capSubmissionStatusMayResubmit =
            application.lead.status === LeadStatus.SubmissionFailed ||
            application.lead.status === LeadStatus.SubmittedWithError;

        if (application.__typename === 'EventApplication') {
            switch (application.module.__typename) {
                case ModuleType.EventApplicationModule:
                    return (
                        application.event?.isCapEnabled &&
                        application.module.capModuleId &&
                        capSubmissionStatusMayResubmit
                    );

                default:
                    return false;
            }
        }

        switch (application.module.__typename) {
            case ModuleType.ConfiguratorModule:
            case ModuleType.FinderApplicationPrivateModule:
            case ModuleType.FinderApplicationPublicModule:
            case ModuleType.StandardApplicationModule: {
                return application.module.capModuleId && capSubmissionStatusMayResubmit;
            }

            default:
                return null;
        }
    }, [application]);

export const isCapDisabledForApplication = (application: GenericFormApplication): boolean => {
    if (application.__typename === 'EventApplication') {
        if (application.module.__typename === 'EventApplicationModule') {
            return !application.module?.capModuleId || !application.event?.isCapEnabled;
        }
    }

    // For other application types, only check if they have a capModuleId
    switch (application.module.__typename) {
        case 'ConfiguratorModule':
        case 'FinderApplicationPrivateModule':
        case 'FinderApplicationPublicModule':
        case 'StandardApplicationModule':
            return !application.module?.capModuleId;

        default:
            return true;
    }
};
