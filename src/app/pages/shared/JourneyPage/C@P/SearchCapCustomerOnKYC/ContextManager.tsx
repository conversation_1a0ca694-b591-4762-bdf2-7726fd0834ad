import React, { useCallback, useMemo, useState } from 'react';
import type { LeadDataFragment } from '../../../../../api/fragments/LeadData';
import {
    ButtonClickedAction,
    type ButtonClickedData,
    type SelectedCapValues,
    ViewState,
} from '../../../../../components/cap/searchCustomersAndLeads/types';
import useReducer from '../../../../../components/cap/searchCustomersAndLeads/useReducer';
import { SearchCapCustomerContext } from './shared';
import SearchCapCustomerKYC from '.';

type SearchCapCustomerContextManagerProps = {
    lead?: LeadDataFragment;
    kycHasCompleted: boolean;
    applicationRequireLogin: boolean;
    applicationModuleId: string;
    eventId?: string;
    capModuleId: string;
    dealerId: string;
    children: React.ReactNode;
    onCapValuesChanged?: (capValues: SelectedCapValues) => void;
    onCapModalErrorConfirmed?: () => void;
};

export const SearchCapCustomerContextManager: React.FC<SearchCapCustomerContextManagerProps> = ({
    lead,
    kycHasCompleted,
    applicationRequireLogin,
    applicationModuleId,
    capModuleId,
    eventId,
    dealerId,
    children,
    onCapValuesChanged,
    onCapModalErrorConfirmed,
}: SearchCapCustomerContextManagerProps) => {
    const [state, dispatch] = useReducer();
    const { visible, selectedLead, selectedBusinessPartner, userChooseCreateNew, viewState, searchedCustomer } = state;
    const [componentClosable, setComponentCloseable] = useState<boolean>(true);

    const showSearchComponent = useCallback(
        (closable?: boolean) => {
            dispatch({
                type: 'setStateValues',
                values: {
                    visible: true,
                    lead,
                    applicationModuleId,
                    eventId,
                    capModuleId,
                    dealerId,
                    viewState:
                        !selectedBusinessPartner && !searchedCustomer?.email && !searchedCustomer?.phone
                            ? ViewState.SearchForm
                            : viewState,
                },
            });
            setComponentCloseable(closable);
        },
        [
            lead,
            applicationModuleId,
            eventId,
            capModuleId,
            dealerId,
            selectedBusinessPartner,
            searchedCustomer,
            viewState,
        ]
    );

    const hideSearchComponent = useCallback(() => {
        dispatch({
            type: 'setStateValues',
            values: { visible: false, searchedCustomer: { email: null, phone: null } },
        });
    }, [dispatch]);

    const context = useMemo(
        () => ({
            componentState: {
                open: visible,
                closeable: componentClosable,
            },
            showSearchComponent,
            hideSearchComponent,
            selectedLead,
            selectedBusinessPartner,
            userChooseCreateNew,
            searchedCustomer,
        }),
        [
            visible,
            componentClosable,
            showSearchComponent,
            hideSearchComponent,
            selectedLead,
            selectedBusinessPartner,
            userChooseCreateNew,
            searchedCustomer,
        ]
    );

    const onButtonClicked = useCallback(
        (data: ButtonClickedData) => {
            const { action } = data;
            switch (action) {
                case ButtonClickedAction.SelectBP: {
                    return onCapValuesChanged({
                        selectedBusinessPartner: data.selectedBusinessPartner,
                        selectedValue: ViewState.BusinessPartner,
                    });
                }
                case ButtonClickedAction.SelectLead: {
                    return onCapValuesChanged({
                        selectedBusinessPartner: data.selectedBusinessPartner,
                        selectedLead: data.selectedLead,
                        selectedValue: ViewState.Lead,
                    });
                }
                case ButtonClickedAction.ErrorSearchConfirmed: {
                    return onCapModalErrorConfirmed();
                }
                default: {
                    return null;
                }
            }
        },
        [onCapModalErrorConfirmed, onCapValuesChanged]
    );

    return (
        <SearchCapCustomerContext.Provider value={context}>
            {children}
            {!kycHasCompleted && capModuleId && applicationRequireLogin && dealerId && (
                <SearchCapCustomerKYC
                    closable={componentClosable}
                    dispatch={dispatch}
                    onButtonClicked={onButtonClicked}
                    onClose={hideSearchComponent}
                    state={state}
                />
            )}
        </SearchCapCustomerContext.Provider>
    );
};
