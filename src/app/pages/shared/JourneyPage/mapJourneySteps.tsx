import { DebugJourneyDataFragment } from '../../../api';
import { JourneyStage } from '../../portal/StandardApplicationEntrypoint/Journey/shared';

const mapJourneySteps = (stepIdentifiers: string[] = []) => {
    // map of step identifiers from journey context (backend) to journey stages (frontend)
    const StepToJourneyStageMap: Record<string, JourneyStage> = {
        'applicant-kyc': JourneyStage.ApplicantKYC,
        'guarantor-kyc': JourneyStage.GuarantorKYC,

        appointment: JourneyStage.Appointment,

        'adyen-payment': JourneyStage.Deposit,
        'fiserv-payment': JourneyStage.Deposit,
        'paygate-payment': JourneyStage.Deposit,
        'porsche-payment': JourneyStage.Deposit,
        'ttb-payment': JourneyStage.Deposit,

        'namirial-signing': JourneyStage.Namirial,
        'guarantor-namirial-signing': JourneyStage.GuarantorNamirial,
        'test-drive-namirial-signing': JourneyStage.TestDriveNamirial,
        'insurance-namirial-signing': JourneyStage.InsuranceNamirial,
        'finance-otp-signing': JourneyStage.Otp,
        'insurance-otp-signing': JourneyStage.InsuranceOtp,
        'test-drive-otp-signing': JourneyStage.TestDriveOtp,

        unknown: JourneyStage.Unknown,
    };

    return stepIdentifiers.map(stepIdentifier => StepToJourneyStageMap[stepIdentifier]).filter(Boolean);
};

const mergeKycAndAppointmentStages = (stages: JourneyStage[]) => {
    const hasKycAndAppointment =
        stages.includes(JourneyStage.ApplicantKYC) && stages.includes(JourneyStage.Appointment);

    if (!hasKycAndAppointment) {
        return stages;
    }

    const filteredStages = stages.filter(
        stage => stage !== JourneyStage.ApplicantKYC && stage !== JourneyStage.Appointment
    );

    return [JourneyStage.KYCAndAppointment, ...filteredStages];
};

const getCurrentStageIndex = (stages: JourneyStage[], currentStage: JourneyStage) => {
    const hasKycAndAppointment =
        (stages.includes(JourneyStage.ApplicantKYC) && stages.includes(JourneyStage.Appointment)) ||
        stages.includes(JourneyStage.KYCAndAppointment);

    return stages.findIndex(stage => {
        if (
            hasKycAndAppointment &&
            (currentStage === JourneyStage.ApplicantKYC || currentStage === JourneyStage.Appointment)
        ) {
            return stage === JourneyStage.KYCAndAppointment;
        }

        return stage === currentStage;
    });
};

type Application =
    | Extract<DebugJourneyDataFragment['application'], { __typename: 'StandardApplication' }>
    | Extract<DebugJourneyDataFragment['application'], { __typename: 'TestDriveApplication' }>
    | Extract<DebugJourneyDataFragment['application'], { __typename: 'FinderApplication' }>
    | Extract<DebugJourneyDataFragment['application'], { __typename: 'EventApplication' }>
    | Extract<DebugJourneyDataFragment['application'], { __typename: 'ConfiguratorApplication' }>
    | Extract<DebugJourneyDataFragment['application'], { __typename: 'MobilityApplication' }>
    | Extract<DebugJourneyDataFragment['application'], { __typename: 'LaunchpadApplication' }>;

const getApplicationJourneyStages = (application: Application) => {
    const { journeySteps } = application;

    if (!journeySteps) {
        return [];
    }

    return mergeKycAndAppointmentStages(mapJourneySteps(journeySteps));
};

export { mapJourneySteps, getApplicationJourneyStages, getCurrentStageIndex };
