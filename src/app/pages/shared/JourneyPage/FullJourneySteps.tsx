import React, { useMemo, CSSProperties } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useThemeComponents } from '../../../themes/hooks';
import { JourneyStage } from '../../portal/StandardApplicationEntrypoint/Journey/shared';
import { getCurrentStageIndex } from './mapJourneySteps';

type FullJourneyStepsProps = {
    stages: JourneyStage[];
    appendBottomSpacing?: boolean;
    style?: CSSProperties;
};

const StepWrapper = styled.div<{ appendBottomSpacing?: boolean }>`
    padding-top: 16px;
    padding-bottom: ${({ appendBottomSpacing }) => (appendBottomSpacing ? '50px' : '0')};
    display: flex;
    justify-content: center;
`;

const FullJourneySteps = ({ stages = [], appendBottomSpacing = true, style }: FullJourneyStepsProps) => {
    const { Steps } = useThemeComponents();
    const { t } = useTranslation('journey');

    const allStages = useMemo(() => [...stages, JourneyStage.Confirmation], [stages]);
    const current = useMemo(() => getCurrentStageIndex(allStages, JourneyStage.Confirmation), [allStages]);

    return (
        <StepWrapper appendBottomSpacing={appendBottomSpacing} style={style}>
            <Steps
                current={current}
                items={allStages.map((stage, index) => ({
                    index,
                    title: t(`journey:steps.${stage}`),
                }))}
            />
        </StepWrapper>
    );
};

export default FullJourneySteps;
