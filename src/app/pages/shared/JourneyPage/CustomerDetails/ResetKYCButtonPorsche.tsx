import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';

type ResetKYCButtonProps = {
    onConfirm: () => void;
};

const ResetKYCButtonPorsche = ({ onConfirm }: ResetKYCButtonProps) => {
    const { t } = useTranslation('customerDetails');
    const { Button, Popconfirm } = useThemeComponents();
    const { layout } = useRouter();

    return (
        <Popconfirm
            cancelText={t('customerDetails:resetKYC.cancelText')}
            okButtonProps={{ htmlType: 'reset', form: 'applicantForm' }}
            okText={t('customerDetails:resetKYC.okText')}
            onConfirm={onConfirm}
            title={t('customerDetails:resetKYC.confirmMessage')}
        >
            <Button
                className="reset-form-button"
                onClick={e => e.preventDefault()}
                porscheTheme={layout?.__typename === 'PorscheV3Layout' ? 'dark' : undefined}
            >
                {t('customerDetails:buttons.resetForm')}
            </Button>
        </Popconfirm>
    );
};

export default ResetKYCButtonPorsche;
