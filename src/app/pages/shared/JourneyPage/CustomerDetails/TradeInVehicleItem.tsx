import { Col, ColProps, Row } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import dayjs from 'dayjs';
import { useField, useFormikContext } from 'formik';
import { get, isEmpty, set } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { KycFieldSpecsFragment } from '../../../../api';
import {
    CurrentVehicleEquipmentLine,
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
    TradeInVehiclePayload,
} from '../../../../api/types';
import { defaultFilterOption } from '../../../../components/fields/SelectField';
import { useThemeComponents } from '../../../../themes/hooks';
import { getKycFieldLabel } from '../../../../utilities/kycPresets/shared';
import { useTradeInRequiredStatus } from '../../../../utilities/kycPresets/useTradeInRequiredStatus';
import { withCommasForNumberString } from '../../../../utilities/useFormats';
import useSystemOptions from '../../../../utilities/useSystemOptions';
import { mileageFormatAmount } from '../../../admin/AddVehiclePage/AddVariantPage/helper';
import type { ApplicantFormValues } from '../../../portal/ConfiguratorApplicationEntrypoint/ApplicantKYCPage/shared';
import { Title } from '../../../portal/EventApplicationEntrypoint/ApplicantForm/shared';

const HeaderContainer = styled.div`
    display: flex;
    align-items: center;
    margin-top: 16px;
    gap: 16px;
`;

const RadioContainer = styled.div`
    display: flex;
    align-items: center;
    height: fit-content;
    margin-bottom: 18px;
`;

const TradeInContainer = styled.div`
    width: 100%;
`;

type TradeInVehicleDetailsProps = {
    withMyInfo: boolean;
    kycPresets: KycFieldSpecsFragment[];
};

export const TradeInVehicleDetails = ({ withMyInfo, kycPresets }: TradeInVehicleDetailsProps) => {
    const { values, setFieldValue } = useFormikContext<ApplicantFormValues>();

    const tradeInKeyMapper = useCallback(
        (vehicle: TradeInVehiclePayload) =>
            vehicle.source === LocalCustomerFieldSource.MyInfo ? vehicle.registrationNumber : 'tradeInVehicle',
        []
    );

    const tradeInVehicleValues = useMemo(() => values.tradeInVehicle || [], [values.tradeInVehicle]);

    const hasSelectedVehicle = useMemo(
        () => tradeInVehicleValues.some(vehicle => vehicle.isSelected),
        [tradeInVehicleValues]
    );

    useEffect(() => {
        if (withMyInfo && !hasSelectedVehicle && tradeInVehicleValues?.length > 0) {
            setFieldValue('tradeInVehicle', set('[0].isSelected', true, tradeInVehicleValues));
        }
    }, [withMyInfo, tradeInVehicleValues, hasSelectedVehicle, setFieldValue]);

    return (
        <>
            {tradeInVehicleValues.map((value: TradeInVehiclePayload, index: number) => (
                <TradeInContainer key={tradeInKeyMapper(value)}>
                    <Header
                        index={index}
                        name={`tradeInVehicle.[${index}]`}
                        withMyInfo={value.source === LocalCustomerFieldSource.MyInfo}
                    />
                    <Row gutter={24}>
                        <TradeInVehicleItem
                            colSpan={{ md: 12, xs: 24 }}
                            kycPresets={kycPresets}
                            markMyinfo={false}
                            name={`tradeInVehicle.[${index}]`}
                            value={value}
                        />
                    </Row>
                </TradeInContainer>
            ))}
        </>
    );
};

export const Header = ({
    withMyInfo,
    index: indexProps,
    name,
}: {
    withMyInfo: boolean;
    index: number;
    name: string;
}) => {
    const { t } = useTranslation('eventApplicantForm');
    const { values, setFieldValue } = useFormikContext<ApplicantFormValues>();
    const { Radio } = useThemeComponents();

    const title = withMyInfo
        ? t('eventApplicantForm:panelTitles.tradeInVehicleDetailsWithMyInfo', { index: indexProps + 1 })
        : t('eventApplicantForm:panelTitles.tradeInVehicleDetails');

    const tradeInVehicleValues = useMemo(() => values.tradeInVehicle || [], [values.tradeInVehicle]);

    const onChange = useCallback(
        (e: CheckboxChangeEvent) => {
            const tradeInVehicle = tradeInVehicleValues.map((vehicle, index) => ({
                ...vehicle,
                isSelected: indexProps === index ? e.target.checked : false,
            }));
            setFieldValue('tradeInVehicle', tradeInVehicle);
        },
        [indexProps, setFieldValue, tradeInVehicleValues]
    );

    return (
        <HeaderContainer>
            <Title>{title}</Title>
            {withMyInfo && (
                <RadioContainer onClick={event => event.stopPropagation()}>
                    <Radio checked={get(`${name}.isSelected`, values)} name={`${name}.isSelected`} onChange={onChange}>
                        {t('eventApplicantForm:fields.tradeInVehicle.selectTradeIn.label')}
                    </Radio>
                </RadioContainer>
            )}
        </HeaderContainer>
    );
};

export type TradeInVehicleItemProps = {
    name: string;
    colSpan: ColProps;
    value: TradeInVehiclePayload;
    kycPresets: KycFieldSpecsFragment[];
    disabled?: boolean;
    markMyinfo?: boolean;
};

const TradeInVehicleItem = ({
    name,
    colSpan,
    value,
    disabled = false,
    markMyinfo = true,
    kycPresets,
}: TradeInVehicleItemProps) => {
    const { t } = useTranslation('configuratorJourney');
    const [field] = useField<TradeInVehiclePayload>({ name });
    const { values, setFieldValue } = useFormikContext<ApplicantFormValues>();

    const withTradeIn = useMemo(() => values?.configuration?.tradeIn, [values?.configuration?.tradeIn]);

    const selectedMake = useMemo(() => field?.value?.make, [field]);

    const { FormFields } = useThemeComponents();
    const {
        currentVehicleSourceOptions,
        currentVehicleOwnership,
        currentVehicleEquipmentLine,
        currentVehicleEngineType,
    } = useSystemOptions();

    const isMyInfoData = useMemo(() => value.source === LocalCustomerFieldSource.MyInfo, [value.source]);
    const markWithMyinfo = useMemo(() => markMyinfo && isMyInfoData, [markMyinfo, isMyInfoData]);

    const isRequiredMap = useTradeInRequiredStatus(kycPresets, value);

    const { modelYear, equipmentLine, ownership } = value;

    const kycHasEquipmentLine = useMemo(
        () => kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.CurrentVehicleEquipmentLine),
        [kycPresets]
    );

    const kycHasOwnership = useMemo(
        () => kycPresets.some(kyc => kyc.key === LocalCustomerFieldKey.CurrentVehicleOwnership),
        [kycPresets]
    );

    const isSetDefaultOwnerShip = useRef(false);
    const isSetDefaultEquipmentline = useRef(false);

    useEffect(() => {
        if (modelYear >= dayjs().year() + 1) {
            setFieldValue(`${name}.modelYear`, dayjs().year() + 1);
        }
        if (isEmpty(equipmentLine) && kycHasEquipmentLine && !isSetDefaultEquipmentline.current) {
            isSetDefaultEquipmentline.current = true;
            setFieldValue(`${name}.equipmentLine`, CurrentVehicleEquipmentLine.Basic);
        }
        if ((ownership === null || ownership === undefined) && kycHasOwnership && !isSetDefaultOwnerShip.current) {
            isSetDefaultOwnerShip.current = true;
            setFieldValue(`${name}.ownership`, true);
        }
    }, [equipmentLine, kycHasEquipmentLine, kycHasOwnership, kycPresets, modelYear, name, ownership, setFieldValue]);

    const sortedFields = useMemo(
        () => Object.entries(isRequiredMap).sort(([, a], [, b]) => a.index - b.index),
        [isRequiredMap]
    );

    const mileageFormatter = useCallback((value, { userTyping }) => {
        if (!value) {
            return null;
        }

        return userTyping ? withCommasForNumberString(value) : mileageFormatAmount(value);
    }, []);

    const renderField = useCallback(
        (fieldKey: LocalCustomerFieldKey, labelKey: string, fieldType: 'input' | 'number', formatter?) => (
            <Col {...colSpan}>
                {fieldType === 'input' ? (
                    <FormFields.InputField
                        disabled={isMyInfoData || disabled}
                        label={getKycFieldLabel(
                            markWithMyinfo,
                            t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.${labelKey}.label`)
                        )}
                        name={`${name}.${labelKey}`}
                        required={isRequiredMap[fieldKey]?.isRequired || false}
                    />
                ) : (
                    <FormFields.InputNumberField
                        formatter={formatter}
                        {...t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.${labelKey}`, {
                            returnObjects: true,
                        })}
                        label={getKycFieldLabel(
                            markWithMyinfo,
                            t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.${labelKey}.label`)
                        )}
                        name={`${name}.${labelKey}`}
                        required={isRequiredMap[fieldKey]?.isRequired || false}
                    />
                )}
            </Col>
        ),
        [colSpan, FormFields, isMyInfoData, disabled, markWithMyinfo, t, name, isRequiredMap]
    );

    return (
        <>
            {/* Render fields based on sortedFields */}
            {sortedFields.map(([fieldKey]) => {
                switch (fieldKey) {
                    case LocalCustomerFieldKey.CurrentVehicleSource:
                        return (
                            <Col {...colSpan}>
                                <FormFields.SelectField
                                    disabled={disabled}
                                    filterOption={defaultFilterOption}
                                    label={t(
                                        `configuratorJourney:applicantkyc.fields.tradeInVehicle.vehicleSource.label`
                                    )}
                                    name={`${name}.vehicleSource`}
                                    options={currentVehicleSourceOptions}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleSource]?.isRequired || false
                                    }
                                    showSearch
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehicleOwnership:
                        return (
                            <Col {...colSpan}>
                                <FormFields.SelectField
                                    disabled={disabled}
                                    filterOption={defaultFilterOption}
                                    label={t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.ownership.label`)}
                                    name={`${name}.ownership`}
                                    options={currentVehicleOwnership}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleOwnership]?.isRequired ||
                                        false
                                    }
                                    showSearch
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehicleMake:
                        return (
                            <Col {...colSpan}>
                                <FormFields.VehicleMakeSelectField
                                    disabled={isMyInfoData || disabled}
                                    filterOption={defaultFilterOption}
                                    label={getKycFieldLabel(
                                        markWithMyinfo,
                                        t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.make.label`)
                                    )}
                                    name={`${name}.make`}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleMake]?.isRequired || false
                                    }
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehicleModel:
                        return (
                            <Col {...colSpan}>
                                <FormFields.VehicleModelSelectField
                                    disabled={isMyInfoData || disabled}
                                    filterOption={defaultFilterOption}
                                    label={getKycFieldLabel(
                                        markWithMyinfo,
                                        t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.model.label`)
                                    )}
                                    name={`${name}.model`}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleModel]?.isRequired || false
                                    }
                                    selectedMake={selectedMake}
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehicleEquipmentLine:
                        return (
                            <Col {...colSpan}>
                                <FormFields.SelectField
                                    disabled={disabled}
                                    filterOption={defaultFilterOption}
                                    label={t(
                                        `configuratorJourney:applicantkyc.fields.tradeInVehicle.equipmentLine.label`
                                    )}
                                    name={`${name}.equipmentLine`}
                                    options={currentVehicleEquipmentLine}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleEquipmentLine]?.isRequired ||
                                        false
                                    }
                                    showSearch
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehicleModelYear:
                        return (
                            <Col {...colSpan}>
                                <FormFields.InputNumberField
                                    disabled={disabled}
                                    label={t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.modelYear.label`)}
                                    max={dayjs().year() + 1}
                                    maxLength={4}
                                    min={1900}
                                    name={`${name}.modelYear`}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleModelYear]?.isRequired ||
                                        false
                                    }
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehiclePurchaseYear:
                        return (
                            <Col {...colSpan}>
                                <FormFields.InputNumberField
                                    disabled={disabled}
                                    label={t(
                                        `configuratorJourney:applicantkyc.fields.tradeInVehicle.purchaseYear.label`
                                    )}
                                    max={dayjs().year()}
                                    maxLength={4}
                                    min={1900}
                                    name={`${name}.purchaseYear`}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleModelYear]?.isRequired ||
                                        false
                                    }
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehicleEngineType:
                        return (
                            <Col {...colSpan}>
                                <FormFields.SelectField
                                    disabled={disabled}
                                    filterOption={defaultFilterOption}
                                    label={t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.engineType.label`)}
                                    name={`${name}.engineType`}
                                    options={currentVehicleEngineType}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleEngineType]?.isRequired ||
                                        false
                                    }
                                    showSearch
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehicleRegistrationNumber:
                        return renderField(
                            LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
                            'registrationNumber',
                            'input'
                        );

                    case LocalCustomerFieldKey.CurrentVehicleVin:
                        return renderField(LocalCustomerFieldKey.CurrentVehicleVin, 'vin', 'input');

                    case LocalCustomerFieldKey.CurrentVehicleMileage:
                        return (
                            <Col {...colSpan}>
                                <FormFields.InputNumberField
                                    disabled={disabled}
                                    formatter={mileageFormatter}
                                    label={t('configuratorJourney:applicantkyc.fields.tradeInVehicle.mileage.label')}
                                    name={`${name}.mileage`}
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleMileage]?.isRequired || false
                                    }
                                />
                            </Col>
                        );
                    case LocalCustomerFieldKey.CurrentVehicleContractEnd:
                        return (
                            <Col {...colSpan}>
                                <FormFields.DatePickerField
                                    disabled={disabled}
                                    format="YYYY/MM"
                                    label={t(
                                        // eslint-disable-next-line max-len
                                        `configuratorJourney:applicantkyc.fields.tradeInVehicle.vehicleContractEnd.label`
                                    )}
                                    name={`${name}.vehicleContractEnd`}
                                    picker="month"
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehicleContractEnd]?.isRequired ||
                                        false
                                    }
                                />
                            </Col>
                        );

                    case LocalCustomerFieldKey.CurrentVehiclePotentialReplacement:
                        return (
                            <Col {...colSpan}>
                                <FormFields.DatePickerField
                                    disabled={disabled}
                                    label={t(
                                        // eslint-disable-next-line max-len
                                        `configuratorJourney:applicantkyc.fields.tradeInVehicle.potentialReplacement.label`
                                    )}
                                    name={`${name}.potentialReplacement`}
                                    picker="date"
                                    required={
                                        isRequiredMap[LocalCustomerFieldKey.CurrentVehiclePotentialReplacement]
                                            ?.isRequired || false
                                    }
                                />
                            </Col>
                        );

                    default:
                        return null;
                }
            })}

            {isMyInfoData && (
                <>
                    {withTradeIn && !isRequiredMap[LocalCustomerFieldKey.CurrentVehicleMake] && (
                        <Col {...colSpan}>
                            <FormFields.VehicleMakeSelectField
                                disabled={isMyInfoData || disabled}
                                filterOption={defaultFilterOption}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.make.label`)
                                )}
                                name={`${name}.make`}
                                required={isRequiredMap[LocalCustomerFieldKey.CurrentVehicleMake]?.isRequired || false}
                            />
                        </Col>
                    )}
                    {withTradeIn && !isRequiredMap[LocalCustomerFieldKey.CurrentVehicleModel] && (
                        <Col {...colSpan}>
                            <FormFields.VehicleModelSelectField
                                disabled={isMyInfoData || disabled}
                                filterOption={defaultFilterOption}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.model.label`)
                                )}
                                name={`${name}.model`}
                                required={isRequiredMap[LocalCustomerFieldKey.CurrentVehicleModel]?.isRequired || false}
                                selectedMake={selectedMake}
                            />
                        </Col>
                    )}
                    {withTradeIn && !isRequiredMap[LocalCustomerFieldKey.CurrentVehicleModelYear] && (
                        <Col {...colSpan}>
                            <FormFields.InputNumberField
                                disabled={disabled}
                                label={t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.modelYear.label`)}
                                max={dayjs().year() + 1}
                                maxLength={4}
                                name={`${name}.modelYear`}
                                required={
                                    isRequiredMap[LocalCustomerFieldKey.CurrentVehicleModelYear]?.isRequired || false
                                }
                            />
                        </Col>
                    )}
                    {withTradeIn &&
                        !isRequiredMap[LocalCustomerFieldKey.CurrentVehicleRegistrationNumber] &&
                        renderField(
                            LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
                            'registrationNumber',
                            'input'
                        )}

                    {withTradeIn &&
                        !isRequiredMap[LocalCustomerFieldKey.CurrentVehicleVin] &&
                        renderField(LocalCustomerFieldKey.CurrentVehicleVin, 'vin', 'input')}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputNumberField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.yearOfManufacture.label`)
                                )}
                                name={`${name}.yearOfManufacture`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.DatePickerField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(
                                        // eslint-disable-next-line max-len
                                        `configuratorJourney:applicantkyc.fields.tradeInVehicle.firstRegistrationDate.label`
                                    )
                                )}
                                name={`${name}.firstRegistrationDate`}
                                picker="date"
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.DatePickerField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.roadTaxExpiryDate.label`)
                                )}
                                name={`${name}.roadTaxExpiryDate`}
                                picker="date"
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputNumberField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.engineCapacity.label`)
                                )}
                                name={`${name}.engineCapacity`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.propellant.label`)
                                )}
                                name={`${name}.propellant`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.primaryColour.label`)
                                )}
                                name={`${name}.primaryColour`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.secondaryColour.label`)
                                )}
                                name={`${name}.secondaryColour`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.status.label`)
                                )}
                                name={`${name}.status`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.scheme.label`)
                                )}
                                name={`${name}.scheme`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.coeCategory.label`)
                                )}
                                name={`${name}.coeCategory`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.DatePickerField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.coeExpiryDate.label`)
                                )}
                                name={`${name}.coeExpiryDate`}
                                picker="date"
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputNumberField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.openMarketValue.label`)
                                )}
                                name={`${name}.openMarketValue`}
                            />
                        </Col>
                    )}

                    {withTradeIn && (
                        <Col {...colSpan}>
                            <FormFields.InputNumberField
                                disabled={isMyInfoData || disabled}
                                label={getKycFieldLabel(
                                    markWithMyinfo,
                                    t(`configuratorJourney:applicantkyc.fields.tradeInVehicle.noOfTransfers.label`)
                                )}
                                name={`${name}.noOfTransfers`}
                            />
                        </Col>
                    )}
                    {withTradeIn &&
                        !isRequiredMap[LocalCustomerFieldKey.CurrentVehicleMileage] &&
                        renderField(LocalCustomerFieldKey.CurrentVehicleMileage, 'mileage', 'number', mileageFormatter)}
                    {withTradeIn && !isRequiredMap[LocalCustomerFieldKey.CurrentVehicleContractEnd] && (
                        <Col {...colSpan}>
                            <FormFields.DatePickerField
                                disabled={disabled}
                                format="YYYY/MM"
                                label={t(
                                    `configuratorJourney:applicantkyc.fields.tradeInVehicle.vehicleContractEnd.label`
                                )}
                                name={`${name}.vehicleContractEnd`}
                                picker="month"
                                required={
                                    isRequiredMap[LocalCustomerFieldKey.CurrentVehicleContractEnd]?.isRequired || false
                                }
                            />
                        </Col>
                    )}
                </>
            )}
        </>
    );
};

export default TradeInVehicleItem;
