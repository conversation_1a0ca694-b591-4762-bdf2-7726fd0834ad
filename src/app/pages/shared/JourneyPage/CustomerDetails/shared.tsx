import { Tabs } from 'antd';
import styled from 'styled-components';
import { AffinAutoFinanceCentre } from '../../../../api';
import { ApplicationDocumentDataFragment } from '../../../../api/fragments/ApplicationDocumentData';
import { ApplicationDocumentKind, CustomerKind } from '../../../../api/types';
import { Panel } from '../../../../components/wrappers/CollapsibleWrapper';

export const allowedCountryForSections = ['TW', 'AE', 'SA'];

export const StyledPanel = styled(Panel)`
    & .ant-collapse-header > span.ant-collapse-header-text {
        font-size: 20px;
        font-weight: 900;
    }
`;

export default StyledPanel;

export const StyledTab = styled(Tabs)`
    margin-bottom: 1rem;

    & .ant-tabs-nav {
        margin: 0;

        &::before {
            border-bottom: 0;
        }
    }

    & .ant-tabs-tab {
        padding-bottom: 0;
    }
`;

export const StyledTabPane = styled.span`
    font-size: 20px;
`;

export const HeaderContainer = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
`;

export const OccupySpace = styled.div`
    margin-bottom: 24px;
`;

export const AFFIN_BANK_OPTIONS: Readonly<AffinAutoFinanceCentre[]> = [
    AffinAutoFinanceCentre.JalanIpoh,
    AffinAutoFinanceCentre.PjSs2,
    AffinAutoFinanceCentre.PjState,
    AffinAutoFinanceCentre.SeriKembangan,
    AffinAutoFinanceCentre.TamanMaluri,
    AffinAutoFinanceCentre.AlorSetar,
    AffinAutoFinanceCentre.Ipoh,
    AffinAutoFinanceCentre.Penang,
    AffinAutoFinanceCentre.Juru,
    AffinAutoFinanceCentre.JohorBahru,
    AffinAutoFinanceCentre.BatuPahat,
    AffinAutoFinanceCentre.Melaka,
    AffinAutoFinanceCentre.Seremban,
    AffinAutoFinanceCentre.JohorJaya,
    AffinAutoFinanceCentre.KotaBharu,
    AffinAutoFinanceCentre.KualaTerengganu,
    AffinAutoFinanceCentre.Kuantan,
    AffinAutoFinanceCentre.KotaKinabalu,
    AffinAutoFinanceCentre.Kuching,
    AffinAutoFinanceCentre.BandarBaruBangi,
    AffinAutoFinanceCentre.Sandakan,
    AffinAutoFinanceCentre.Sibu,
];

export const ProceedWithCustomerWrapper = styled.div`
    padding-top: 32px;
    padding-bottom: 32px;
`;

export const getInitUploadDocuments = (
    documents: ApplicationDocumentDataFragment[],
    showUploadDocument: boolean,
    customerKind: CustomerKind
) => {
    const uploadDocumentKind = (() => {
        switch (customerKind) {
            case CustomerKind.Corporate:
                return ApplicationDocumentKind.CorporateIdentity;

            case CustomerKind.Guarantor:
                return ApplicationDocumentKind.GuarantorIdentity;

            case CustomerKind.Local:
                return ApplicationDocumentKind.CustomerIdentity;

            default:
                return null;
        }
    })();
    const identityDocuments = documents.filter(i => i.kind === uploadDocumentKind);
    const hasApplicantIdentityDocuments =
        identityDocuments.length > 0 && showUploadDocument && customerKind && uploadDocumentKind;

    const uploadDocuments = hasApplicantIdentityDocuments
        ? {
              [customerKind]: identityDocuments,
          }
        : undefined;

    return uploadDocuments;
};
