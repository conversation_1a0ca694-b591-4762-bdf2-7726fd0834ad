import Icon from '@ant-design/icons';
import React, { PropsWithChildren, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { LayoutType } from '../../../api/types';
import SearchCapCustomerButton from '../../../components/cap/searchCustomersAndLeads/SearchCapCustomerButton';
import type { ConfiguratorApplicationState } from '../../portal/ConfiguratorApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../../portal/EventApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from '../../portal/FinderApplicationPublicAccessEntrypoint/shared';
import { LaunchpadApplicationState } from '../../portal/LaunchPadApplicationEntrypoint/utils/types';
import type { MobilityApplicationState } from '../../portal/MobilityApplicationEntrypoint/Journey/shared';
import type { StandardApplicationState } from '../../portal/StandardApplicationEntrypoint/Journey/shared';
import { JourneyStage } from '../../portal/StandardApplicationEntrypoint/Journey/shared';
import { useJourneyStepsContext } from './JourneyStepsContext';
import { getCurrentStageIndex } from './mapJourneySteps';
import LockIcon from '../../../../../public/icons/lock.svg';

const Container = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 24px;

    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3 &&
        css`
            margin-bottom: 120px;
        `}
`;

const MainContentCard = styled.div`
    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3 &&
        css`
            display: flex;
            flex-direction: column;
            row-gap: 16px;
        `}
`;

const PriorStagesWrapper = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 4.25rem;
    width: 100%;
`;

const TitleContainer = styled.div`
    display: flex;
    flex-wrap: wrap;
    row-gap: 16px;
    column-gap: 16px;
    align-items: center;
`;

const BottomWrapper = styled(PriorStagesWrapper)`
    margin-top: 40px;
`;

const TitleWrapper = styled.div<{
    active: boolean;
    isPrior: boolean;
    allowClick: boolean;
}>`
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    color: ${({ active, isPrior }) => (active || isPrior ? '#000' : '#949598')};
    ${({ allowClick }) => allowClick && 'cursor: pointer;'};
    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3 &&
        css`
            border-radius: 12px;
            padding: ${props.active ? 'initial' : '32px'};
            background-color: #fff;
        `}
`;

const Title = styled.p`
    font-size: 20px;
    font-weight: bold;
    margin: 0;
`;

const RightIcon = styled(Icon)`
    font-size: 24px;
`;

type Applications =
    | StandardApplicationState
    | ConfiguratorApplicationState
    | EventApplicationState
    | FinderApplicationState
    | MobilityApplicationState
    | LaunchpadApplicationState;

type JourneySectionTitleProps = {
    active: boolean; // is in current stage
    isPrior: boolean; // is in prior stage
    index: number;
    title: string;
    extra?: React.ReactNode;
    allowClick?: boolean;
    onClick?: () => void;
    withCapSearchButton?: boolean;
};

const JourneySectionTitle = ({
    active,
    index,
    title,
    isPrior,
    extra,
    allowClick = false,
    onClick,
    withCapSearchButton = false,
}: JourneySectionTitleProps) => {
    const rightElement = useMemo(() => {
        if (active) {
            return extra;
        }

        if (isPrior) {
            return null;
        }

        return <RightIcon component={LockIcon} />;
    }, [active, extra, isPrior]);

    return (
        <TitleWrapper
            active={active}
            allowClick={allowClick}
            isPrior={isPrior}
            onClick={allowClick ? onClick : undefined}
        >
            <TitleContainer>
                <Title>
                    {index}. {title}
                </Title>
                <span>{withCapSearchButton && <SearchCapCustomerButton />}</span>
            </TitleContainer>
            <span>{rightElement}</span>
        </TitleWrapper>
    );
};

type JourneyStepsProps = {
    stage: JourneyStage;
    stages: JourneyStage[];
    applicationType: Applications['__typename'];
    extra?: React.ReactNode;
    withCapSearchButton?: boolean;
    isPorscheIdLoginMandatory?: boolean;
} & PropsWithChildren;

const JourneySectionWrapper = ({
    stage: currentStage,
    stages,
    applicationType,
    children,
    extra,
    withCapSearchButton = false,
    isPorscheIdLoginMandatory = false,
}: JourneyStepsProps) => {
    const { t } = useTranslation('journey');

    const { onBack, shouldAllowBack } = useJourneyStepsContext();

    const currentStageIndex = useMemo(() => getCurrentStageIndex(stages, currentStage), [stages, currentStage]);

    const [priorStages, currentStageFromList, laterStages] = useMemo(() => {
        const prior = stages?.slice(0, currentStageIndex);
        const current = stages?.[currentStageIndex];
        const later = stages?.slice(currentStageIndex + 1);

        return [prior, current, later];
    }, [stages, currentStageIndex]);

    const priorAndCurrentStages = useMemo(
        () => [...(priorStages || []), currentStageFromList].filter(Boolean),
        [priorStages, currentStageFromList]
    );

    if (applicationType === 'MobilityApplication') {
        // eslint-disable-next-line react/jsx-no-useless-fragment
        return <>{children}</>;
    }

    const hasCompleteMandatoryPorscheIdLogin = useMemo(
        () => isPorscheIdLoginMandatory && currentStage !== JourneyStage.PorscheIdLoginRegister,
        [currentStage, isPorscheIdLoginMandatory]
    );

    return (
        <Container>
            {priorStages.length > 0 && (
                <PriorStagesWrapper>
                    {priorStages.map((stage, index) => (
                        <JourneySectionTitle
                            key={stage}
                            active={index === currentStageIndex}
                            allowClick={
                                !(
                                    hasCompleteMandatoryPorscheIdLogin && stage === JourneyStage.PorscheIdLoginRegister
                                ) &&
                                shouldAllowBack &&
                                index < currentStageIndex &&
                                currentStageIndex - index === 1
                            }
                            extra={extra}
                            index={index + 1}
                            isPrior={stage !== currentStage}
                            onClick={onBack}
                            title={t(`journey:steps.${stage}`)}
                            withCapSearchButton={withCapSearchButton && currentStage === JourneyStage.ApplicantKYC}
                        />
                    ))}
                </PriorStagesWrapper>
            )}

            <MainContentCard className="v3-layout-card">
                <JourneySectionTitle
                    key={currentStage}
                    allowClick={false}
                    extra={extra}
                    index={currentStageIndex + 1}
                    isPrior={false}
                    onClick={onBack}
                    title={t(`journey:steps.${currentStage}`)}
                    withCapSearchButton={withCapSearchButton && currentStage === JourneyStage.ApplicantKYC}
                    active
                />
                {children}
            </MainContentCard>

            {laterStages && laterStages.length > 0 && (
                <BottomWrapper>
                    {laterStages.map((stage, index) => (
                        <JourneySectionTitle
                            key={stage}
                            active={false}
                            index={priorAndCurrentStages.length + index + 1}
                            isPrior={false}
                            title={t(`journey:steps.${stage}`)}
                        />
                    ))}
                </BottomWrapper>
            )}
        </Container>
    );
};

export default JourneySectionWrapper;
