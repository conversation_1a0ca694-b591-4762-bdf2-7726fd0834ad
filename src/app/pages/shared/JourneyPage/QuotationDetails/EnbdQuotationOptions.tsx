import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Typography } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useField } from 'formik';
import { isNil } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useReducer } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ApplicationQuotationOptionSetting } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import ArrayField from '../../../../components/fields/ArrayField';
import type { PageState } from '../../../../components/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import useFormats, { withCommasForNumberString } from '../../../../utilities/useFormats';
import useSystemOptions from '../../../../utilities/useSystemOptions';
import { QuotationFormValues } from './types';
import { useQuotationUtilities } from './utils';

type ExtendedApplicationQuotationOptionSetting = ApplicationQuotationOptionSetting & {
    // amount displayed based on the isVatIncluded
    amount: number;
};

const reducer = (state: PageState): PageState => state;

export type EnbdQuotationOptionsFormProps = {
    push: (initialValue: ExtendedApplicationQuotationOptionSetting) => void;
    remove: (index: number) => void;
    replace: (index: number, value: ExtendedApplicationQuotationOptionSetting) => void;
    options: ExtendedApplicationQuotationOptionSetting[];
    disabled?: boolean;
    allowVatChange?: boolean;
    prefix: string;
};

// Override the table style, with bold header
const OverrideHeaderStyle = styled.div`
    .ant-table-thead > tr > th {
        font-weight: bold;
    }
    tbody.ant-table-tbody > tr > td {
        vertical-align: top;
    }
    tbody.ant-table-tbody > tr:hover > td {
        background-color: white !important; // need to set it as important
    }
    .ant-form-item {
        margin-bottom: 0;
    }
`;

const AmountField = ({ index, prefix, disabled }: { index: number; prefix: string; disabled?: boolean }) => {
    const { FormFields } = useThemeComponents();
    const [field, meta, { setValue }] = useField<ExtendedApplicationQuotationOptionSetting>(`${prefix}[${index}]`);

    const company = useCompany(true);
    const { formatAmount, currencySymbol } = useFormats(company?.currency ?? '', 2, 0);
    const { getGrossAmountFromNet } = useQuotationUtilities();

    useEffect(() => {
        const { amount: currentAmount, amountIncludingVat: currentAmountIncludingVat, isVatIncluded } = field.value;

        if (isNil(currentAmount) || !meta.touched) {
            return;
        }

        const expectedAmountIncludingVat = isVatIncluded ? currentAmount : getGrossAmountFromNet(currentAmount, 2);

        if (expectedAmountIncludingVat !== currentAmountIncludingVat) {
            setValue({
                ...field.value,
                amountIncludingVat: expectedAmountIncludingVat,
            });
        }

        // purposely omit the dependency of field.value
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [field.value.amount, meta.touched, getGrossAmountFromNet, setValue]);

    const formatter = useCallback(
        (value: number, { userTyping }) => {
            if (!value) {
                return value?.toString();
            }

            return userTyping ? withCommasForNumberString(value?.toString()) : formatAmount(value);
        },
        [formatAmount]
    );

    return (
        <FormFields.InputNumberField
            disabled={disabled}
            formatter={formatter}
            name={`${prefix}[${index}].amount`}
            precision={2}
            prefix={currencySymbol}
            required
        />
    );
};

const EnbdQuotationOptionsForm = ({
    push,
    remove,
    options,
    replace,
    disabled,
    allowVatChange,
    prefix,
}: EnbdQuotationOptionsFormProps) => {
    const { t } = useTranslation(['quotationDetails']);

    const { PaginatedTableWithContext, FormFields, Button } = useThemeComponents();

    const { vatInclusionOptions } = useSystemOptions();

    const { getNetAmountFromGross } = useQuotationUtilities();

    // Reducer only to satisfy the PaginatedTableWithContext
    const [state, dispatch] = useReducer(reducer, {
        page: 1,
        pageSize: 100,
    });

    const handleInclusionChange = useCallback(
        (newIsVatIncluded: boolean, record: ApplicationQuotationOptionSetting, index: number) => {
            const { amountIncludingVat, isVatIncluded } = record;

            if (newIsVatIncluded === isVatIncluded) {
                return;
            }

            const newAmount = newIsVatIncluded ? amountIncludingVat : getNetAmountFromGross(amountIncludingVat);

            replace(index, {
                amount: newAmount,
                amountIncludingVat,
                description: record.description,
                isVatIncluded: newIsVatIncluded,
            });
        },
        [getNetAmountFromGross, replace]
    );

    const columns = useMemo(() => {
        const definedColumns: ColumnsType<ApplicationQuotationOptionSetting> = [
            {
                key: 'index',
                title: t('quotationDetails:options.columns.index'),
                render: (value, record, index) => (
                    <span
                        style={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            height: 'var(--input-height, 32px)',
                        }}
                    >
                        {index + 1}
                    </span>
                ),
                width: 36,
                align: 'center',
            },
            {
                key: 'description',
                title: t('quotationDetails:options.columns.description'),
                render: (value, record, index) => (
                    <FormFields.InputField disabled={disabled} name={`${prefix}[${index}].description`} />
                ),
            },
            {
                key: 'vatInclusion',
                title: t('quotationDetails:options.columns.vatInclusion'),
                render: (value, record, index) => (
                    <FormFields.SelectField
                        disabled={isNil(allowVatChange) ? disabled : !allowVatChange}
                        name={`${prefix}[${index}].isVatIncluded`}
                        onChange={(newValue: boolean) => handleInclusionChange(newValue, record, index)}
                        options={vatInclusionOptions}
                        showSearch
                    />
                ),
                width: 159,
            },
            {
                key: 'amount',
                title: t('quotationDetails:options.columns.price'),
                render: (value, record, index) => <AmountField disabled={disabled} index={index} prefix={prefix} />,
                width: 159,
            },
            !disabled && {
                key: 'actions',
                title: t('quotationDetails:options.columns.actions'),
                render: (value, record, index) => (
                    <Button
                        icon={<DeleteOutlined />}
                        onClick={() => remove(index)}
                        size="middle"
                        style={{ height: 'var(--input-height, 32px)' }}
                        type="link"
                    />
                ),
                width: 72,
                align: 'center',
            },
        ];

        return definedColumns.filter(Boolean);
    }, [Button, FormFields, disabled, handleInclusionChange, prefix, remove, t, vatInclusionOptions, allowVatChange]);

    return (
        <>
            <Typography.Title level={5}>{t('quotationDetails:options.title')}</Typography.Title>
            <OverrideHeaderStyle>
                <PaginatedTableWithContext
                    columns={columns}
                    dataSource={options}
                    dispatch={dispatch}
                    size="small"
                    state={state}
                    total={options?.length}
                />
            </OverrideHeaderStyle>
            {!disabled && (
                <Button
                    icon={<PlusOutlined />}
                    onClick={() =>
                        push({
                            amount: 0,
                            amountIncludingVat: 0,
                            description: '',
                            isVatIncluded: true,
                        })
                    }
                    style={{ marginTop: 16 }}
                    type="link"
                >
                    {t('quotationDetails:options.action.add')}
                </Button>
            )}
        </>
    );
};

type EnbdQuotationOptionsProps = {
    disabled?: boolean;
    name?: string;
    allowVatChange?: boolean;
};

const EnbdQuotationOptions = ({ disabled, name, allowVatChange }: EnbdQuotationOptionsProps) => (
    <ArrayField<ExtendedApplicationQuotationOptionSetting, QuotationFormValues>
        name={name}
        render={({ push, remove, replace }, options) => (
            <EnbdQuotationOptionsForm
                allowVatChange={allowVatChange}
                disabled={disabled}
                options={options}
                prefix={name}
                push={push}
                remove={remove}
                replace={replace}
            />
        )}
    />
);

export default EnbdQuotationOptions;
