import { Tag } from 'antd';
import { TFunction } from 'i18next';
import { SetStateAction, useCallback } from 'react';
import { ModuleListDataFragment } from '../../../api/fragments/ModuleListData';
import { LeadSortingField, LeadStageOption, LeadStatus } from '../../../api/types';
import { DownloadState } from '../../../components/fields/DownloadModal/DownloadModal';
import { ExportFormat } from '../../../utilities/export';
import exportLeads from '../../../utilities/export/leads';
import streamExportLeads from '../../../utilities/export/streamLeads';
import makeGetSortingRule from '../../../utilities/makeGetSortingRule';
import { ApplicationColumns, useAdditionalColumns } from '../ApplicationList/helpers';

export const getSortField = makeGetSortingRule((field): LeadSortingField => {
    switch (field) {
        case 'module.company.displayName':
            return LeadSortingField.Company;

        case 'identifier':
            return LeadSortingField.Identifier;

        case 'vehicle.name':
            return LeadSortingField.VehicleName;

        case 'status':
            return LeadSortingField.LeadStatus;

        case 'versioning.createdAt':
            return LeadSortingField.CreatedAt;

        case 'assignee.displayName':
            return LeadSortingField.Assignee;

        case 'module.displayName':
            return LeadSortingField.Module;

        case 'customer.fullName':
            return LeadSortingField.Customer;

        case 'campaignValues.capCampaignId':
            return LeadSortingField.LeadGenFormCampaignId;

        case 'event.displayName':
            return LeadSortingField.LeadGenFormName;

        default:
            throw new Error(`Such Field is not supported: ${field}`);
    }
});

export const getStatusColor = (status: LeadStatus) => {
    switch (status) {
        case LeadStatus.SubmissionFailed:
            return { background: '#FFF1F0', font: '#CF1322', border: '#CF1322' };

        case LeadStatus.Completed:
            return { background: '#f9f0ff', font: '#531dab', border: '#d3adf7' };

        case LeadStatus.PendingQualify:
            return { background: '#E6FFFB', font: '#08979C', border: '#08979C' };

        case LeadStatus.SubmittedToCap:
            return { background: '#E6FFFB', font: '#08979C', border: '#08979C' };

        case LeadStatus.SubmittedWithError:
            return { background: '#FFF1F0', font: '#CF1322', border: '#CF1322' };

        case LeadStatus.Unqualified:
            return { background: '#FAFAFA', font: '#000000', border: '262626' };

        default:
            return { background: '#0000000a', font: '#000000', border: '#0505050f' };
    }
};

export const renderStatusTag = (value: LeadStatus, t: TFunction) => {
    const { background, font, border } = getStatusColor(value);

    return (
        <Tag key={value} style={{ backgroundColor: background, color: font, borderColor: border }}>
            {t(`leadListPage:status.${value}`)}
        </Tag>
    );
};

// to later update to have stage for lead, and contact
export const useLeadColumns = (options?: { hasModule?: boolean }) => {
    const { hasModule = true } = options || {};

    const { hasEventModule } = useAdditionalColumns();

    return [
        ApplicationColumns.AppDate,
        hasEventModule && ApplicationColumns.LeadGenFormName,
        hasEventModule && ApplicationColumns.LeadGenFormCampaignId,
        ApplicationColumns.Identifier,
        ApplicationColumns.Vehicle,
        ApplicationColumns.Assignee,
        ApplicationColumns.Applicant,
        hasModule && ApplicationColumns.Module,
        ApplicationColumns.Status,
    ];
};

export const allowedCapFormatTypes: ModuleListDataFragment['__typename'][] = [
    'FinderApplicationPublicModule',
    'FinderApplicationPrivateModule',
    'EventApplicationModule',
    'ConfiguratorModule',
    'StandardApplicationModule',
    'LaunchPadModule',
];

export const initialDownloadParamsValues: DownloadState = {
    module: null,
    period: { start: undefined, end: undefined },
    format: ExportFormat.DefaultFormat,
};

export const allOption = { label: 'All', value: 'all' };

type DownloadLeadParams = {
    token: string;
    stage: LeadStageOption;
    currentLanguageId: string;
    modules: ModuleListDataFragment[];
    dealerIds: string[];
    channelModuleOption: { label: string; value: string }[];
    setDownloading: (value: SetStateAction<boolean>) => void;
    notifyUser: (value: string) => void;
    t: TFunction;
    setDefault: () => void;
    passwordModal?: {
        open: (password: string) => void;
    };
    isLargeExport?: boolean;
};
export const useDownloadLeads = ({
    token,
    stage,
    currentLanguageId,
    modules,
    dealerIds,
    channelModuleOption,
    setDownloading,
    notifyUser,
    t,
    setDefault,
    passwordModal,
    isLargeExport = true,
}: DownloadLeadParams) =>
    useCallback(
        async (values, actions) => {
            if (!values.module) {
                return;
            }
            try {
                actions.setFieldError('period', undefined);

                setDownloading(true);

                const moduleIds =
                    values.module === allOption.value
                        ? channelModuleOption
                              .filter(option => option.value !== allOption.value)
                              .map(({ value }) => value)
                        : [values.module];

                // all modules are on the same company
                const selectedModule = modules.find(data => data.id === moduleIds[0]);

                if (isLargeExport) {
                    const response = await streamExportLeads({
                        dealerIds,
                        moduleIds,
                        period: values.period,
                        companyName: selectedModule.company.displayName,
                        token,
                        stage,
                        format: values.format,
                        languageId: currentLanguageId,
                    });

                    if (response?.status === 200) {
                        // Pre-select module if there is no other choice
                        if (channelModuleOption.length !== 1) {
                            actions.resetForm();
                        } else {
                            actions.resetForm({
                                values: {
                                    module: values.module,
                                    period: initialDownloadParamsValues.period,
                                    format: initialDownloadParamsValues.format,
                                },
                            });
                        }
                        setDefault();

                        notifyUser(t('leadListPage:download.initiated'));
                    } else {
                        actions.setFieldError(
                            'period',
                            response?.status === 400
                                ? t('leadListPage:download.downloadNotCompleted')
                                : t('leadListPage:download.noRecordToDownload')
                        );
                    }
                } else {
                    const password = await exportLeads({
                        dealerIds,
                        moduleIds,
                        period: values.period,
                        companyName: selectedModule.company.displayName,
                        token,
                        stage,
                        format: values.format,
                        languageId: currentLanguageId,
                    });

                    if (password && passwordModal) {
                        passwordModal.open(password);
                    }
                    // Pre-select module if there is no other choice
                    if (channelModuleOption.length !== 1) {
                        actions.resetForm();
                    } else {
                        actions.resetForm({
                            values: {
                                module: values.module,
                                period: initialDownloadParamsValues.period,
                                format: initialDownloadParamsValues.format,
                            },
                        });
                    }
                    setDefault();
                }
            } catch (error) {
                actions.setFieldError('period', t('leadListPage:download.noRecordToDownload'));
            } finally {
                setDownloading(false);
            }
        },
        [
            setDownloading,
            channelModuleOption,
            modules,
            isLargeExport,
            dealerIds,
            token,
            stage,
            currentLanguageId,
            setDefault,
            notifyUser,
            t,
            passwordModal,
        ]
    );
