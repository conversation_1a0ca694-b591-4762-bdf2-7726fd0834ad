import { useApolloClient } from '@apollo/client';
import { message, Modal } from 'antd';
import { Formik } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { CustomerDetailSpecsFragment } from '../../../api/fragments';
import {
    UpdateCustomerDocument,
    UpdateCustomerMutation,
    UpdateCustomerMutationVariables,
} from '../../../api/mutations/updateCustomer';
import { CustomerKind, KycField, KycFieldPurpose, LocalCustomerFieldSource } from '../../../api/types';
import Form from '../../../components/fields/Form';
import CollapsibleWrapper from '../../../components/wrappers/CollapsibleWrapper';
import { getInitialValues, prepareKYCFieldPayload } from '../../../utilities/kycPresets';
import useKYCFormValidator from '../../../utilities/kycPresets/useKYCValidators';
import useFormattedSimpleVersioning from '../../../utilities/useFormattedSimpleVersioning';
import useHandleError from '../../../utilities/useHandleError';
import useValidator from '../../../utilities/useValidator';
import ApplicantPanel, { FeatureInUsed } from '../ApplicationDetailsPage/standard/ApplicationTab/ApplicantPanel';
import type { ApplicationFormValues } from '../ApplicationDetailsPage/standard/shared';
import { hasCustomerUpdate, useDialCode } from '../ApplicationDetailsPage/standard/useUpdates';

type CustomerDetailsFormProps = {
    customer: CustomerDetailSpecsFragment;
    isMask: boolean;
    forCI?: boolean;
};

const CustomerDetailsForm = ({ customer, isMask, forCI }: CustomerDetailsFormProps) => {
    const { t } = useTranslation('customerDetails');
    const navigate = useNavigate();
    const apolloClient = useApolloClient();
    const dialCode = useDialCode();

    const { updated, offset } = useFormattedSimpleVersioning({
        versioning: customer.versioning,
        timeZone: customer.module.company.timeZone,
    });

    const kycPresetFromCustomerFields = useMemo(
        () =>
            customer.fields.map(
                field =>
                    ({
                        key: field.key,
                        purpose: [KycFieldPurpose.Kyc],
                        isRequired: false,
                    }) as KycField
            ),
        [customer.fields]
    );

    const kycPresetsWithCreationDate = useMemo(
        () => [
            { key: 'CreationDate', purpose: [KycFieldPurpose.Kyc], isRequired: false },
            ...kycPresetFromCustomerFields,
        ],
        [kycPresetFromCustomerFields]
    );

    const kycExtraSettings = useMemo(() => {
        if (customer?.module?.__typename === 'LocalCustomerManagementModule') {
            return customer.module.extraSettings;
        }

        return null;
    }, [customer]);
    // Validation for KYC should be based on `customer` instead of `applicant.fields`
    // since `applicant.fields` from server, the values stored is only the non-empty one

    const applicantValidator = useKYCFormValidator({
        field: kycPresetFromCustomerFields,
        extraSettings: kycExtraSettings,
        moduleCountryCode: customer.module.company.countryCode,
        prefix: 'customer.fields',
        saveDraft: false,
    });
    const validate = useValidator(applicantValidator);

    const initialValues = useMemo(
        () => ({
            applicantKYC: kycPresetsWithCreationDate,
            versioning: customer.versioning,
            customer: {
                ...customer,
                fields: {
                    ...getInitialValues(customer.fields, kycPresetFromCustomerFields),
                    CreationDate: {
                        source: LocalCustomerFieldSource.UserInput,
                        value: new Date(customer?.versioning?.createdAt),
                    },
                },
            },
        }),
        [customer, kycPresetFromCustomerFields, kycPresetsWithCreationDate]
    );

    const onSubmit = useHandleError(
        async values => {
            const { CreationDate, ...otherValues } = values.customer.fields;
            const updates = prepareKYCFieldPayload(otherValues);
            const isNeedUpdate = hasCustomerUpdate(updates, values.customer.fields, initialValues.customer.fields, {
                dialCode,
            });
            if (!isNeedUpdate) {
                message.warn(t('customerDetails:messages.noUpdated'));

                return;
            }

            Modal.confirm({
                className: 'static-modal',
                title: t(`customerDetails:confirmModal.title`),
                okText: t('customerDetails:buttons.ok'),
                cancelText: t('customerDetails:buttons.cancel'),
                onOk: async () => {
                    try {
                        message.loading({
                            content: t('customerDetails:messages.submittingChanges'),
                            key: 'primary',
                            duration: 0,
                        });
                        const params = {
                            customerId: values.customer.id,
                            updates,
                        };
                        await apolloClient.mutate<UpdateCustomerMutation, UpdateCustomerMutationVariables>({
                            mutation: UpdateCustomerDocument,
                            variables: params,
                        });

                        message.success({
                            content: t('customerDetails:messages.submittedChanges'),
                            key: 'primary',
                        });

                        if (forCI) {
                            navigate(-1);
                        }
                    } catch (error) {
                        console.error(error);
                        message.destroy('primary');
                    }
                },
            });
        },
        [apolloClient, forCI, initialValues.customer.fields, navigate, t, dialCode]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form name="customerForm" onSubmitCapture={handleSubmit}>
                    <CollapsibleWrapper
                        defaultActiveKey={['customerDetails']}
                        destroyInactivePanel={false}
                        useTransparentBackground
                    >
                        <ApplicantPanel<ApplicationFormValues>
                            applicantVersioning={{ updated, offset }}
                            customer={customer}
                            customerKind={CustomerKind.Local}
                            featureInUsed={FeatureInUsed.Customer}
                            forCI={forCI}
                            isMask={isMask}
                            showCustomerLink={false}
                            defaultExpanded
                        />
                    </CollapsibleWrapper>
                </Form>
            )}
        </Formik>
    );
};

export default CustomerDetailsForm;
