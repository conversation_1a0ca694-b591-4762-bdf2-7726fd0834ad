/* eslint-disable max-len */
import { StepProps } from 'antd';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import * as permissionKind from '../../../../../shared/permissions';
import { ApplicationStage } from '../../../../api/types';
import { useRouter } from '../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../themes/hooks';
import hasPermissions from '../../../../utilities/hasPermissions';
import { GetEventDetailPathFunction } from '../../ApplicationDetailsPage/standard/ApplicationTab/ReferenceApplicationField';
import ApplicationStepDescription, {
    ApplicationStepData,
    useGetInitialModuleStepTitle,
} from './ApplicationStepDescription';
import ModuleStepDescription from './ModuleStepDescription';
import type {
    Application,
    GetApplicationReferenceLinkCIType,
    GetApplicationReferenceLinkType,
    GetLeadReferenceLinkType,
    GetLeadReferenceLinkCIType,
} from './shared';

const StyledVehicleNameContainer = styled.div`
    margin-bottom: 24px;
    font-size: 16px;
`;

type InnerProps = {
    application: Application;
    getApplicationReferenceLink?: GetApplicationReferenceLinkType;
    getApplicationReferenceLinkCI?: GetApplicationReferenceLinkCIType;
    getLeadReferenceLink?: GetLeadReferenceLinkType;
    getLeadReferenceLinkCI?: GetLeadReferenceLinkCIType;
    getEventDetail?: GetEventDetailPathFunction;
};

type StepPropsAndStage = StepProps & { stage: ApplicationStage | null };

const Inner = ({
    application,
    getApplicationReferenceLink,
    getApplicationReferenceLinkCI,
    getLeadReferenceLink,
    getLeadReferenceLinkCI,
    getEventDetail,
}: InnerProps) => {
    const router = useRouter();
    const { CardWithHeader, JourneySteps } = useThemeComponents();

    const getReferenceLinkWrapper = useCallback(
        (applicationStage: ApplicationStage, applicationStageIdentifier: string) => {
            switch (applicationStage) {
                case ApplicationStage.Lead: {
                    if (getLeadReferenceLink) {
                        return getLeadReferenceLink(
                            applicationStageIdentifier,
                            application.lead.versioning.suiteId,
                            application.lead.isLead
                                ? hasPermissions(application.lead.permissions, [permissionKind.viewLeads])
                                : hasPermissions(application.lead.permissions, [permissionKind.viewContact]),
                            application.lead.isLead,
                            hasPermissions(application.lead.permissions, [
                                permissionKind.viewLeads,
                                permissionKind.viewContact,
                            ])
                        );
                    }

                    return getLeadReferenceLinkCI(
                        applicationStageIdentifier,
                        application.lead.versioning.suiteId,
                        application.lead.isLead,
                        application.lead.isLead
                            ? hasPermissions(application.lead.permissions, [permissionKind.viewLeads])
                            : hasPermissions(application.lead.permissions, [permissionKind.viewContact]),
                        router?.endpoints
                    );
                }

                default: {
                    if (getApplicationReferenceLink) {
                        return getApplicationReferenceLink(
                            applicationStage,
                            applicationStageIdentifier,
                            application?.versioning?.suiteId,
                            hasPermissions(application.permissions, [permissionKind.viewApplications]),
                            hasPermissions(application.lead.permissions, [
                                permissionKind.viewLeads,
                                permissionKind.viewContact,
                            ])
                        );
                    }

                    return getApplicationReferenceLinkCI(
                        applicationStage,
                        applicationStageIdentifier,
                        application?.moduleId,
                        hasPermissions(application.permissions, [permissionKind.viewApplications]),
                        application?.versioning?.suiteId,
                        router?.endpoints
                    );
                }
            }
        },
        [
            getApplicationReferenceLink,
            getApplicationReferenceLinkCI,
            getLeadReferenceLink,
            getLeadReferenceLinkCI,
            application,
            router?.endpoints,
        ]
    );

    const { t } = useTranslation(['customerDetails', 'common']);
    const stagesInvolved = application?.stages;

    const getInitialModuleStepTitle = useGetInitialModuleStepTitle();

    const initialSteps: StepPropsAndStage[] = useMemo(() => {
        const stepItems: StepPropsAndStage[] = [];
        const stagesMap: Record<ApplicationStage, string> = {
            [ApplicationStage.Mobility]: t('customerDetails:steps.stage.mobilityBooking'),
            [ApplicationStage.Lead]: t('customerDetails:steps.stage.lead'),
            [ApplicationStage.Reservation]: t('customerDetails:steps.stage.reservation'),
            [ApplicationStage.Appointment]: t('customerDetails:steps.stage.appointment'),
            [ApplicationStage.VisitAppointment]: t('customerDetails:steps.stage.visitAppointment'),
            [ApplicationStage.Financing]: t('customerDetails:steps.stage.financing'),
            [ApplicationStage.Insurance]: t('customerDetails:steps.stage.insurance'),
            [ApplicationStage.TradeIn]: t('customerDetails:steps.stage.tradeIn'),
        };

        switch (application.module?.__typename) {
            case 'ConfiguratorModule':
            case 'StandardApplicationModule':
            case 'FinderApplicationPublicModule':
            case 'FinderApplicationPrivateModule':
            case 'EventApplicationModule':
            case 'MobilityModule':
            case 'LaunchPadModule':
                stepItems.push({
                    title: getInitialModuleStepTitle(application.module?.__typename),
                    stage: null,
                    description: (
                        <ModuleStepDescription
                            application={application as unknown as ApplicationStepData}
                            paths={{
                                references: {
                                    getEventDetail,
                                },
                            }}
                        />
                    ),
                });

                if (stagesInvolved.includes(ApplicationStage.Mobility)) {
                    stepItems.push({
                        title: stagesMap[ApplicationStage.Mobility],
                        stage: ApplicationStage.Mobility,
                    });

                    return stepItems;
                }

                if (stagesInvolved.includes(ApplicationStage.Lead)) {
                    stepItems.push({ title: stagesMap[ApplicationStage.Lead], stage: ApplicationStage.Lead });

                    return stepItems;
                }

                stagesInvolved.forEach(stage => {
                    if (stagesMap[stage]) {
                        stepItems.push({
                            title: stagesMap[stage],
                            stage,
                        });
                    }
                });

                return stepItems;

            default:
                throw new Error('Invalid application module.');
        }
    }, [application, getInitialModuleStepTitle, stagesInvolved, t, getEventDetail]);

    const fillStepsDetails = useCallback(
        (currentSteps: StepPropsAndStage[]) =>
            currentSteps.map((step, index) => {
                if (index === 0) {
                    return step;
                }

                return {
                    ...step,
                    description: (
                        <ApplicationStepDescription
                            application={application as unknown as ApplicationStepData}
                            applicationStage={step.stage}
                            getReferenceLinkWrapper={getReferenceLinkWrapper}
                        />
                    ),
                };
            }),
        [application, getReferenceLinkWrapper]
    );

    const stepItems = useMemo(() => {
        if (!application.draftFlow.isReceived) {
            return initialSteps;
        }

        return fillStepsDetails(initialSteps);
    }, [application.draftFlow.isReceived, fillStepsDetails, initialSteps]);

    return (
        <CardWithHeader key={application.id} title={application.dealer.displayName} variant="journey">
            <StyledVehicleNameContainer>{application?.vehicle?.name?.defaultValue}</StyledVehicleNameContainer>
            <JourneySteps current={application?.draftFlow?.isReceived ? stepItems.length : 0} items={stepItems} />
        </CardWithHeader>
    );
};

export default Inner;
