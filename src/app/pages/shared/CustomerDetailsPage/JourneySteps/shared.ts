import { ApplicationStage } from '../../../../api';
import { EndpointContextDataFragment } from '../../../../api/fragments';
import { ApplicationInCustomerDetailsFragment } from '../../../../api/fragments/ApplicationInCustomerDetails';

export type Application = Exclude<
    ApplicationInCustomerDetailsFragment,
    { __typename: 'BmwApplication' | 'SDMApplication' | 'AudiFinancingApplication' }
>;

export type GetApplicationReferenceLinkCIType = (
    applicationStage: ApplicationStage,
    applicationStageIdentifier: string,
    applicationId: string,
    isClickable: boolean,
    applicationModuleId?: string,
    routerEndpoints?: EndpointContextDataFragment[]
) => JSX.Element | string;

export type GetApplicationReferenceLinkType = (
    applicationStage: ApplicationStage,
    applicationStageIdentifier: string,
    applicationId: string,
    isClickable: boolean,
    hasLeadsAndContactsPermission: boolean
) => JSX.Element | string;

export type GetLeadReferenceLinkType = (
    identifier: string,
    leadId: string,
    isClickable: boolean,
    isLead: boolean,
    hasLeadsAndContactsPermission: boolean
) => JSX.Element | string;

export type GetLeadReferenceLinkCIType = (
    identifier: string,
    leadId: string,
    isClickable: boolean,
    isLead: boolean,
    routerEndpoints: EndpointContextDataFragment[]
) => JSX.Element | string;
