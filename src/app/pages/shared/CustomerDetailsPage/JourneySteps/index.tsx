/* eslint-disable max-len */
import { ApplicationInCustomerDetailsFragment } from '../../../../api/fragments';
import { GetEventDetailPathFunction } from '../../ApplicationDetailsPage/standard/ApplicationTab/ReferenceApplicationField';
import Inner from './Inner';
import type {
    GetApplicationReferenceLinkCIType,
    GetApplicationReferenceLinkType,
    GetLeadReferenceLinkCIType,
    GetLeadReferenceLinkType,
} from './shared';

type JourneyStepsProps = {
    application: ApplicationInCustomerDetailsFragment;
    getApplicationReferenceLink?: GetApplicationReferenceLinkType;
    getApplicationReferenceLinkCI?: GetApplicationReferenceLinkCIType;
    getLeadReferenceLink?: GetLeadReferenceLinkType;
    getLeadReferenceLinkCI?: GetLeadReferenceLinkCIType;
    getEventDetail?: GetEventDetailPathFunction;
};

const JourneySteps = ({
    application,
    getApplicationReferenceLink,
    getApplicationReferenceLinkCI,
    getLeadReferenceLink,
    getLeadReferenceLinkCI,
    getEventDetail,
}: JourneyStepsProps) => (
    <Inner
        application={application}
        getApplicationReferenceLink={getApplicationReferenceLink}
        getApplicationReferenceLinkCI={getApplicationReferenceLinkCI}
        getEventDetail={getEventDetail}
        getLeadReferenceLink={getLeadReferenceLink}
        getLeadReferenceLinkCI={getLeadReferenceLinkCI}
    />
);

export default JourneySteps;
