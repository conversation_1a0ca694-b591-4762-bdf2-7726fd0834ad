import { TFunction } from 'i18next';
import { SetStateAction, useCallback } from 'react';
import { EventDataFragment } from '../../../api/fragments/EventData';
import type { ModuleListDataFragment } from '../../../api/fragments/ModuleListData';
import { ApplicationStage } from '../../../api/types';
import { DownloadState } from '../../../components/fields/DownloadModal/DownloadModal';
import { ExportFormat, exportApplications, streamExportApplications } from '../../../utilities/export';

export const allOption = { label: 'All', value: 'all' };

export const initialDownloadParamsValues: DownloadState = {
    module: null,
    period: { start: undefined, end: undefined },
    format: ExportFormat.DefaultFormat,
};

type DownloadApplicationParams = {
    token: string;
    stage: ApplicationStage;
    currentLanguageId: string;
    modules: ModuleListDataFragment[];
    event?: EventDataFragment;
    dealerIds: string[];
    channelModuleOption: { label: string; value: string }[];
    setDownloading: (value: SetStateAction<boolean>) => void;
    notifyUser: (value: string) => void;
    t: TFunction;
    setDefault: () => void;
    passwordModal?: {
        open: (password: string) => void;
    };
    namespace?: string;
    isLargeExport?: boolean;
};

export const useDownloadApplications = ({
    token,
    stage,
    currentLanguageId,
    modules,
    event,
    dealerIds,
    channelModuleOption,
    setDownloading,
    notifyUser,
    t,
    setDefault,
    passwordModal,
    isLargeExport = true,
}: DownloadApplicationParams) =>
    useCallback(
        async (values, actions) => {
            if (!values.module && !event) {
                return;
            }

            try {
                actions.setFieldError('period', undefined);
                setDownloading(true);
                const moduleIds =
                    values.module === allOption.value
                        ? channelModuleOption
                              .filter(option => option.value !== allOption.value)
                              .map(({ value }) => value)
                        : [values.module];

                // all modules are on the same company
                const selectedModule = event?.module ?? modules.find(data => data.id === moduleIds[0]);

                if (isLargeExport) {
                    // Use streaming endpoint for large exports
                    const response = await streamExportApplications({
                        dealerIds,
                        stage,
                        moduleIds: event?.module.id ? [event?.module.id] : moduleIds,
                        eventId: event?.id,
                        period: values.period,
                        companyName: selectedModule.company.displayName,
                        token,
                        format: values.format,
                        languageId: currentLanguageId,
                    });

                    if (response?.status === 200) {
                        // Pre-select module if there is no other choice
                        if (channelModuleOption.length !== 1) {
                            actions.resetForm();
                        } else {
                            actions.resetForm({
                                values: {
                                    module: values.module,
                                    period: initialDownloadParamsValues.period,
                                    format: initialDownloadParamsValues.format,
                                },
                            });
                        }
                        setDefault();

                        notifyUser(t('applicationList:download.initiated'));
                    } else {
                        actions.setFieldError(
                            'period',
                            response?.status === 400
                                ? t('applicationList:download.downloadNotCompleted')
                                : t('applicationList:download.noRecordToDownload')
                        );
                    }
                } else {
                    // Use direct download for smaller exports
                    const password = await exportApplications({
                        dealerIds,
                        stage,
                        moduleIds,
                        period: values.period,
                        companyName: selectedModule.company.displayName,
                        token,
                        format: values.format,
                        languageId: currentLanguageId,
                    });

                    if (password && passwordModal) {
                        passwordModal.open(password);
                    }

                    // Pre-select module if there is no other choice
                    if (channelModuleOption.length !== 1) {
                        actions.resetForm();
                    } else {
                        actions.resetForm({
                            values: {
                                module: values.module,
                                period: initialDownloadParamsValues.period,
                                format: initialDownloadParamsValues.format,
                            },
                        });
                    }
                    setDefault();
                }
            } catch (error) {
                actions.setFieldError('period', t('applicationList:download.noRecordToDownload'));
            } finally {
                setDownloading(false);
            }
        },
        [
            setDownloading,
            channelModuleOption,
            modules,
            t,
            notifyUser,
            setDefault,
            token,
            dealerIds,
            stage,
            currentLanguageId,
            passwordModal,
            isLargeExport,
        ]
    );
