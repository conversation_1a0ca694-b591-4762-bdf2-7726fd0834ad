import { PText } from '@porsche-design-system/components-react';
import { Typography } from 'antd';
import { isFinite } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useRouter } from '../../../components/contexts/shared';
import useCompanyFormats from '../../../utilities/useCompanyFormats';

const DepositContainer = styled.div`
    width: 100%;
    height: 54px;
    background-color: #eeeff2;
    border-radius: var(--card-border-radius, 6px);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 16px;
    align-items: center;
`;

export type BookingDepositProps = {
    depositAmount: number;
};

const BookingDeposit = ({ depositAmount }: BookingDepositProps) => {
    const { t } = useTranslation('paymentDetails');
    const formats = useCompanyFormats();
    const { layout } = useRouter();
    const v3LayoutType = layout?.__typename === 'PorscheV3Layout';

    const label = useMemo(() => t('paymentDetails:labels.bookingDeposit.label'), [t]);

    if (!isFinite(depositAmount)) {
        return null;
    }

    const depositAmountWithCurrency = formats.formatAmountWithCurrency(depositAmount);

    if (v3LayoutType) {
        return (
            <DepositContainer className="v3-layout-card-small-booking-deposit">
                <PText size="medium" weight="bold">
                    {label}
                </PText>
                <PText size="medium" weight="bold">
                    {depositAmountWithCurrency}
                </PText>
            </DepositContainer>
        );
    }

    return (
        <DepositContainer>
            <Typography.Title level={4} style={{ margin: 0 }}>
                {label}
            </Typography.Title>
            <Typography.Title level={4} style={{ margin: 0 }}>
                {depositAmountWithCurrency}
            </Typography.Title>
        </DepositContainer>
    );
};

export default BookingDeposit;
