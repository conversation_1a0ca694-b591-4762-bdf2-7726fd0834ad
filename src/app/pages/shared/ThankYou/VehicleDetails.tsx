import { Typography, Space } from 'antd';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { AllowedApplicationForPayment } from '../../../utilities/journeys/payment';
import type { State } from '../../portal/StandardApplicationEntrypoint/Journey/shared';
import VehicleInfo from './VehicleInfo';

type VehicleDetailsProps = {
    state?: State<AllowedApplicationForPayment>;
    vehicleSection?: ReactNode;
};

const VehicleDetails = ({ state, vehicleSection }: VehicleDetailsProps) => {
    const { t } = useTranslation(['paymentDetails']);

    return (
        <Space className="v3-layout-card" direction="vertical" size={24} style={{ width: '100%' }}>
            <Typography.Title level={4} style={{ marginBottom: 0 }}>
                {t('paymentDetails:titles.selectedVehicle')}
            </Typography.Title>
            {!vehicleSection && state && <VehicleInfo state={state} />}
            {vehicleSection}
        </Space>
    );
};

export default VehicleDetails;
