import { Row as AntdRow, Col, Typography } from 'antd';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import styled from 'styled-components';
import { AllowedApplicationForPayment } from '../../../utilities/journeys/payment';
import useCompanyFormats from '../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../utilities/useTranslatedString';
import Media from '../../portal/ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/components/Media';
import calculateTotalPrice from '../../portal/ConfiguratorApplicationEntrypoint/helper';
import { GiftVoucherState } from '../../portal/MobilityApplicationEntrypoint/GiftCodeJourney/reducer';
import type { State } from '../../portal/StandardApplicationEntrypoint/Journey/shared';

const Row = styled(AntdRow)`
    border: 1px solid #dfdfdf;
    align-items: center;
    border-radius: var(--card-border-radius, initial);
`;

const StyledCol = styled(Col)`
    display: flex;
    justify-content: center;
    align-items: start;
    flex-direction: column;
`;

const VehiclePrice = styled(Typography)`
    &.ant-typography {
        font-size: 16px;
        color: #0e0e0e;
        padding-left: 15px;
    }
`;

const VehicleName = styled(VehiclePrice)`
    &.ant-typography {
        font-weight: 900;
    }
`;

const MediaContainer = styled.div`
    & .ant-image {
        width: 100%;
        margin: auto;
        & > img {
            aspect-ratio: 16/9;
            object-fit: cover;
        }
    }
`;

export type VehicleInfoProps = {
    state: State<AllowedApplicationForPayment> | GiftVoucherState;
};

const VehicleInfo = ({ state }: VehicleInfoProps) => {
    const formats = useCompanyFormats();
    const translatedString = useTranslatedString();

    const application = useMemo(
        () => (state as State<AllowedApplicationForPayment>)?.application || (state as GiftVoucherState)?.giftVoucher,
        [state]
    );
    const { variant, totalPrice, filename, source } = useMemo(() => {
        switch (application.vehicle?.__typename) {
            case 'LocalVariant': {
                switch (application.__typename) {
                    case 'EventApplication': {
                        return {
                            variant: application.vehicle ? translatedString(application.vehicle.name) : null,
                            totalPrice: null,
                            filename: application.vehicle?.images?.[0]?.filename,
                            source: application.vehicle?.images?.[0]?.url,
                        };
                    }

                    case 'StandardApplication': {
                        return {
                            variant: translatedString(application.vehicle.name),
                            totalPrice: application.financing?.totalPrice ?? application.vehicle.vehiclePrice,
                            filename: application.vehicle?.images?.[0]?.filename,
                            source: application.vehicle?.images?.[0]?.url,
                        };
                    }

                    case 'ConfiguratorApplication': {
                        return {
                            variant: translatedString(application.vehicle.name),
                            totalPrice: calculateTotalPrice(application),
                            filename: application.vehicleImage?.filename ?? application.vehicle?.images?.[0]?.filename,
                            source: application.vehicleImage?.url ?? application.vehicle?.images?.[0]?.url,
                        };
                    }

                    case 'GiftVoucher': {
                        const vehicleImage =
                            application?.stock?.__typename === 'MobilityStockInventory' &&
                            application.stock?.images?.length
                                ? application.stock.images[0]
                                : application.vehicle?.images?.[0];

                        return {
                            variant: translatedString(application.vehicle.name),
                            totalPrice: application.value * application.numberOfBookingReferenceDays,
                            filename: vehicleImage?.filename,
                            source: vehicleImage?.url,
                        };
                    }

                    default:
                        throw new Error('not implemented');
                }
            }

            default:
                throw new Error('not implemented');
        }
    }, [application, translatedString]);

    return (
        <Row>
            <Col span={9}>
                <MediaContainer>
                    <Media fileName={filename} source={source} />
                </MediaContainer>
            </Col>
            <StyledCol span={15}>
                <VehicleName>{variant}</VehicleName>
                {!isNil(totalPrice) && <VehiclePrice>{formats.formatAmountWithCurrency(totalPrice)}</VehiclePrice>}
            </StyledCol>
        </Row>
    );
};

export default VehicleInfo;
