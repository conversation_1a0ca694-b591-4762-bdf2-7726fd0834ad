import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { ApplicationDataFragment } from '../../../api/fragments/ApplicationData';
import {
    CancelApplicationDocument,
    CancelApplicationMutation,
    CancelApplicationMutationVariables,
} from '../../../api/mutations/cancelApplication';
import { ApplicationStage, ApplicationStatus } from '../../../api/types';
import { useApplicationDetailsExtraContext } from '../../../components/contexts/ApplicationDetailsExtraContext';
import { useThemeComponents } from '../../../themes/hooks';
import ActionButton from '../ApplicationDetailsPage/generic/ActionButton';
import { Container, SubActionContainer } from '../ApplicationDetailsPage/standard/Actions';
import type { MobilityBookingFormValues } from './shared';

type MobilityBookingFooterProps = {
    application: Extract<ApplicationDataFragment, { __typename: 'MobilityApplication' }>;
};

const MobilityBookingFooter = ({ application }: MobilityBookingFooterProps) => {
    const { t } = useTranslation('applicationDetails');
    const { forCI } = useApplicationDetailsExtraContext();
    const { Modal } = useThemeComponents();

    const canAmend = useMemo(() => {
        const {
            mobilityBookingDetails: { period },
            module,
        } = application;
        if (module.__typename === 'MobilityModule') {
            const { amendmentCutOff } = module;

            return dayjs().add(amendmentCutOff, 'days').isBefore(period.start);
        }

        return false;
    }, [application]);
    const apolloClient = useApolloClient();
    const navigate = useNavigate();

    const { values } = useFormikContext<MobilityBookingFormValues>();
    const [openCancelModal, setOpenCancelModal] = useState<boolean>(false);

    const onSubmitCancel = useCallback(async () => {
        message.loading({
            content: t('applicationDetails:messages.cancellingBooking'),
            key: 'primary',
            duration: 0,
        });

        await apolloClient.mutate<CancelApplicationMutation, CancelApplicationMutationVariables>({
            mutation: CancelApplicationDocument,
            variables: {
                applicationId: values.id,
                stage: ApplicationStage.Mobility,
            },
        });

        message.success({
            content: t('applicationDetails:messages.cancelledBooking'),
            key: 'primary',
        });

        navigate('/admin/mobilitybookings');
    }, [apolloClient, navigate, t, values.id]);

    const hasFooter = useMemo(
        () => values.mobilityStage?.status !== ApplicationStatus.Cancelled && canAmend,
        [canAmend, values.mobilityStage?.status]
    );

    if (!hasFooter) {
        return null;
    }

    return (
        <Container forCI={forCI}>
            <>
                <Modal
                    onCancel={() => setOpenCancelModal(false)}
                    onOk={onSubmitCancel}
                    open={openCancelModal}
                    title={t('applicationDetails:cancelModal.title')}
                >
                    {t('applicationDetails:cancelModal.body')}
                </Modal>
                <SubActionContainer forCI={forCI}>
                    <ActionButton forCI={forCI} onClick={() => setOpenCancelModal(true)} size="large">
                        {t('applicationDetails:buttons.void')}
                    </ActionButton>
                    <ActionButton
                        key="submit"
                        forCI={forCI}
                        form="mobilityBookingForm"
                        htmlType="submit"
                        size="large"
                        type="primary"
                    >
                        {t('applicationDetails:buttons.amend')}
                    </ActionButton>
                </SubActionContainer>
            </>
        </Container>
    );
};

export default MobilityBookingFooter;
