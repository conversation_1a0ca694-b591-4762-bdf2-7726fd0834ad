import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled from 'styled-components';
import * as permissionKind from '../../../../shared/permissions';
import { KycFieldSpecsFragment } from '../../../api/fragments';
import { useGetPublicStockQuery } from '../../../api/queries';
import { ApplicationStage, KycFieldPurpose, LocalCustomerFieldKey } from '../../../api/types';
import { useApplicationDetailsExtraContext } from '../../../components/contexts/ApplicationDetailsExtraContext';
import breakpoints from '../../../utilities/breakpoints';
import hasPermissions from '../../../utilities/hasPermissions';
import { getInitialValues } from '../../../utilities/kycPresets';
import useDebounce from '../../../utilities/useDebounce';
import useHandleError from '../../../utilities/useHandleError';
import { StyledForm } from '../ApplicationDetailsPage/standard/ApplicationForm';
import useSubmitChanges from '../ApplicationDetailsPage/standard/useSubmitChanges';
import useAgreementsValues from '../CIPage/ConsentAndDeclarations/useAgreementsValues';
import { formatToHomeLocationPayload, formatToPickupLocationPayload } from './ApplicationTab/shared';
import MobilityBookingAction from './MobilityBookingAction';
import MobilityBookingFooter from './MobilityBookingFooter';
import type { MobilityBookingFormProps, MobilityBookingFormValues } from './shared';

export const Form = styled(StyledForm)`
    margin-left: 0px;
    margin-right: 0px;
    @media screen and (min-width: ${breakpoints.md}) {
        margin-left: 0px;
        margin-right: 0px;
    }
`;

const MobilityBookingForm = ({ application }: MobilityBookingFormProps) => {
    const agreements = useAgreementsValues(application.applicantAgreements);
    const { t } = useTranslation('applicationDetails');
    const { forCI } = useApplicationDetailsExtraContext();

    const { data, loading } = useGetPublicStockQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            id: application.mobilityBookingDetails.inventoryStockId,
            omitApplicationId: application?.versioning.suiteId,
        },
    });

    const kycPresets = useMemo((): KycFieldSpecsFragment[] => {
        const uaeKycPresetIndex = application.applicantKYC.findIndex(
            kyc => kyc.key === LocalCustomerFieldKey.UaeDrivingLicense
        );

        const birthdayKYCPreset: KycFieldSpecsFragment = {
            __typename: 'KYCField',
            isRequired: true,
            key: LocalCustomerFieldKey.Birthday,
            purpose: [KycFieldPurpose.Kyc],
        };

        return uaeKycPresetIndex < 0
            ? application.applicantKYC
            : [
                  ...application.applicantKYC.slice(0, uaeKycPresetIndex),
                  birthdayKYCPreset,
                  ...application.applicantKYC.slice(uaeKycPresetIndex),
              ];
    }, [application.applicantKYC]);

    const guarantorKycPresets = useMemo((): KycFieldSpecsFragment[] => {
        const uaeKycPresetIndex = application.guarantorKYC.findIndex(
            kyc => kyc.key === LocalCustomerFieldKey.UaeDrivingLicense
        );

        const birthdayKYCPreset: KycFieldSpecsFragment = {
            __typename: 'KYCField',
            isRequired: true,
            key: LocalCustomerFieldKey.Birthday,
            purpose: [KycFieldPurpose.Kyc],
        };

        return uaeKycPresetIndex < 0
            ? application.guarantorKYC
            : [
                  ...application.guarantorKYC.slice(0, uaeKycPresetIndex),
                  birthdayKYCPreset,
                  ...application.guarantorKYC.slice(uaeKycPresetIndex),
              ];
    }, [application.guarantorKYC]);

    const initialValues = useMemo(
        (): MobilityBookingFormValues => ({
            ...application,
            customer: {
                ...application.applicant,
                fields: getInitialValues(application.applicant.fields, kycPresets),
            },
            ...(application.guarantor && {
                guarantor: {
                    ...application.guarantor,
                    fields: getInitialValues(application.guarantor?.fields, guarantorKycPresets),
                },
            }),
            applicantKYC: kycPresets,
            agreements,
            rentalBookingPeriod: application.mobilityBookingDetails.period,
            startTime: dayjs(application.mobilityBookingDetails.period.start).tz(application.module.company.timeZone),
            endTime: dayjs(application.mobilityBookingDetails.period.end).tz(application.module.company.timeZone),
            ...(application.deposit && {
                deposit: {
                    ...application.deposit,
                },
            }),
            location: JSON.stringify(
                application.mobilityBookingDetails.location.__typename === 'MobilityBookingLocationHome'
                    ? formatToHomeLocationPayload(application.mobilityBookingDetails.location)
                    : formatToPickupLocationPayload(application.mobilityBookingDetails.location)
            ),
        }),
        [application, kycPresets, guarantorKycPresets, agreements]
    );

    const client = useApolloClient();
    const navigate = useNavigate();

    const onSubmit = useDebounce(
        useHandleError(
            async (values: MobilityBookingFormValues) => {
                message.loading({
                    content: t('applicationDetails:messages.submittingChanges'),
                    key: 'primary',
                    duration: 0,
                });
                const startDate = dayjs(values.rentalBookingPeriod.start)
                    .set('hour', values.startTime.hour())
                    .set('minute', values.startTime.minute())
                    .set('second', 0)
                    .tz(application.module.company.timeZone, true)
                    .toDate();

                const endDate = dayjs(values.rentalBookingPeriod.end)
                    .set('hour', values.endTime.hour())
                    .set('minute', values.endTime.minute())
                    .set('second', 0)
                    .tz(application.module.company.timeZone, true)
                    .toDate();

                const concatValues: MobilityBookingFormValues = {
                    ...values,
                    mobilityBookingDetails: {
                        ...values.mobilityBookingDetails,
                        period: {
                            __typename: 'Period',
                            start: startDate,
                            end: endDate,
                        },
                    },
                };

                const { redirectionLink } = await useSubmitChanges(
                    client,
                    initialValues,
                    concatValues,
                    false,
                    ApplicationStage.Mobility,
                    hasPermissions(application.permissions, [permissionKind.allowRecalculateApplication])
                );

                // inform about success
                message.success({
                    content: t('applicationDetails:messages.submittedChanges'),
                    key: 'primary',
                });

                if (redirectionLink) {
                    window.open(redirectionLink, '_blank');
                } else if (forCI) {
                    navigate(-1);
                }
            },
            [t, application.module.company.timeZone, application.permissions, forCI, client, initialValues, navigate]
        )
    );

    const stock = useMemo(() => {
        if (data?.stock?.__typename === 'ConfiguratorStockInventory') {
            throw new Error('ConfiguratorStockInventory not support');
        }

        return data?.stock;
    }, [data?.stock]);

    if (loading) {
        return null;
    }

    return (
        <Formik<MobilityBookingFormValues> initialValues={initialValues} onSubmit={onSubmit}>
            {({ handleSubmit }) => (
                <Form id="mobilityBookingForm" name="mobilityBookingForm" onSubmitCapture={handleSubmit}>
                    <MobilityBookingAction application={application} stock={stock} />
                    <MobilityBookingFooter application={application} />
                </Form>
            )}
        </Formik>
    );
};

export default MobilityBookingForm;
