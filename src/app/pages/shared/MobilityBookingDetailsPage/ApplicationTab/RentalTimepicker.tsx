import { Col } from 'antd';
import { PickerTimeProps } from 'antd/es/date-picker/generatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useFormikContext } from 'formik';
import { head, isNil, xor } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../../../themes/hooks';
import {
    calculateAvailaleHoursForNextBooking,
    defaultDisabledHoursAndMinutesOfTheDay,
    getDisabledHoursAndMinutesOfDate,
} from '../../../portal/MobilityApplicationEntrypoint/CarDetailsPage/BookingForm/RentalFields/shared';
import DatePickerType from '../../../portal/MobilityApplicationEntrypoint/shared';
import type { MobilityBookingFormValues } from '../shared';
import { RentalFieldProps } from './shared';

const RentalTimePicker = ({
    module,
    stock,
    disabledTimeSlot,
    colSpan,
    typeTimePicker,
    application,
    name,
    disabled,
    offset,
}: RentalFieldProps) => {
    const {
        FormFields: { TimePickerField },
    } = useThemeComponents();
    const { t } = useTranslation('applicationDetails');
    const { values, setFieldValue } = useFormikContext<MobilityBookingFormValues>();

    const { minimumAdvancedBooking: minimumBooking, durationBeforeNextBooking: durationNextBooking } = module;

    const reservedTimeSlotRange = calculateAvailaleHoursForNextBooking(
        stock?.reservations ?? [],
        minimumBooking,
        durationNextBooking,
        disabledTimeSlot
    );

    // by default it should be disabled
    const [disabledTimePicker, setDisabledTimePicker] = useState<boolean>(
        (!isNil(values.rentalBookingPeriod?.end) && !isNil(values.rentalBookingPeriod?.start)) ?? true
    );

    useEffect(() => {
        if (application && typeTimePicker === DatePickerType.End) {
            setDisabledTimePicker(true);
        }
    }, [application, typeTimePicker]);

    const disabledTimeValue = useMemo(() => {
        if (application) {
            const date =
                typeTimePicker === DatePickerType.Start
                    ? dayjs(values.rentalBookingPeriod?.start).format('YYYY-MM-DD')
                    : dayjs(values.rentalBookingPeriod?.end).format('YYYY-MM-DD');

            const disabledArrayByDate = reservedTimeSlotRange[date];

            return getDisabledHoursAndMinutesOfDate(
                typeTimePicker,
                disabledTimeSlot,
                disabledArrayByDate,
                {},
                {},
                !isNil(values?.startTime) ? dayjs(values.startTime).hour() : null,
                !isNil(values?.startTime) ? dayjs(values.startTime).minute() : null,
                values.rentalBookingPeriod,
                stock?.reservations ?? [],
                durationNextBooking
            );
        }
        // on load when neither of dates chosen
        if (isNil(values?.rentalBookingPeriod)) {
            return defaultDisabledHoursAndMinutesOfTheDay(typeTimePicker, values.rentalBookingPeriod, disabledTimeSlot);
        }

        let date = null;
        date =
            typeTimePicker === DatePickerType.Start
                ? dayjs(values.rentalBookingPeriod?.start).format('YYYY-MM-DD')
                : dayjs(values.rentalBookingPeriod?.end).format('YYYY-MM-DD');

        const disabledArrayByDate = reservedTimeSlotRange[date];
        if (!isNil(disabledArrayByDate)) {
            return getDisabledHoursAndMinutesOfDate(
                typeTimePicker,
                disabledTimeSlot,
                disabledArrayByDate,
                {},
                {},
                !isNil(values?.startTime) ? dayjs(values.startTime).hour() : null,
                !isNil(values?.startTime) ? dayjs(values.startTime).minute() : null,
                values.rentalBookingPeriod,
                stock?.reservations ?? [],
                durationNextBooking
            );
        }

        return typeTimePicker === DatePickerType.Start
            ? defaultDisabledHoursAndMinutesOfTheDay(typeTimePicker, values.rentalBookingPeriod, disabledTimeSlot)
            : defaultDisabledHoursAndMinutesOfTheDay(
                  typeTimePicker,
                  values.rentalBookingPeriod,
                  disabledTimeSlot,
                  {}, // for admin no need blocking period , no need to add maximum allowable days or hours into checker
                  !isNil(values?.startTime) ? dayjs(values.startTime).hour() : null,
                  !isNil(values?.startTime) ? dayjs(values.startTime).minute() : null
              );
    }, [
        application,
        disabledTimeSlot,
        durationNextBooking,
        reservedTimeSlotRange,
        stock?.reservations,
        typeTimePicker,
        values.rentalBookingPeriod,
        values.startTime,
    ]);

    const onSelect = useCallback(
        (time: Dayjs) => {
            if (typeTimePicker === DatePickerType.Start) {
                if (isNil(values.startTime)) {
                    const disabledMinutes = disabledTimeValue.disabledMinutes(time.hour());
                    const remainderMinutes = head(xor(disabledMinutes, [0, 15, 30, 45]));
                    const updatedTime = time.set('minute', remainderMinutes);
                    setFieldValue('startTime', updatedTime);
                } else {
                    setFieldValue('endTime', undefined);
                }
            } else if (typeTimePicker === DatePickerType.End) {
                if (isNil(values.endTime)) {
                    const disabledMinutes = disabledTimeValue.disabledMinutes(time.hour());
                    const remainderMinutes = head(xor(disabledMinutes, [0, 15, 30, 45]));
                    const updatedTime = time.set('minute', remainderMinutes);

                    setFieldValue('endTime', updatedTime);
                }
            }
        },
        [disabledTimeValue, setFieldValue, typeTimePicker, values.endTime, values.startTime]
    );

    useEffect(() => {
        setDisabledTimePicker(isNil(values.rentalBookingPeriod?.end) && isNil(values.rentalBookingPeriod?.start));
    }, [values.rentalBookingPeriod?.end, values.rentalBookingPeriod?.start]);

    const disabledTime: PickerTimeProps<Dayjs>['disabledTime'] = useCallback(
        (time: Dayjs) => disabledTimeValue,

        [disabledTimeValue]
    );

    return (
        <Col {...colSpan}>
            <TimePickerField
                {...t(
                    `applicationDetails:fields.mobilityBooking.${
                        typeTimePicker === DatePickerType.Start ? 'startDateTime' : 'endDateTime'
                    }`,
                    { returnObjects: true }
                )}
                disabled={disabledTimePicker || disabled}
                disabledTime={disabledTime}
                format={t('common:formats.mobilityTimePicker')}
                minuteStep={15}
                name={name}
                offset={offset}
                onSelect={onSelect}
                showNow={false}
                hideDisabledOptions
                required
            />
        </Col>
    );
};

export default RentalTimePicker;
