import { Space } from 'antd';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import * as permissionKind from '../../../../../shared/permissions';
import { StockPublicDataFragment } from '../../../../api/fragments/StockPublicData';
import { CustomerKind } from '../../../../api/types';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import hasPermissions from '../../../../utilities/hasPermissions';
import ApplicantPanel from '../../ApplicationDetailsPage/standard/ApplicationTab/ApplicantPanel';
import PaymentPanel from '../../ApplicationDetailsPage/standard/ApplicationTab/PaymentPanel';
import type { MobilityBookingFormValues, MobilityBookingFormProps } from '../shared';
import AddOnsPanel from './AddOnsPanel';
import AdditionalInfoPanel from './AdditionalInfoPanel';
import AmendmentsPanel from './AmendmentsPanel';
import ConsentsPanel from './ConsentsPanel';
import MainPanel from './MainPanel';

export type ApplicationTabProps = {
    application: MobilityBookingFormProps['application'];
    isMask: boolean;
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>;
};

const MobilityBookingApplicationTab = ({ application, isMask, stock }: ApplicationTabProps) => {
    const { forCI } = useApplicationDetailsExtraContext();

    const { addOns, additionalInfos, mobilitySnapshots } = useMemo(() => {
        const { mobilitySnapshots } = application;
        const addOns = mobilitySnapshots.filter(snapshot => snapshot.__typename === 'MobilityAddonSnapshot');
        const additionalInfos = mobilitySnapshots.filter(
            snapshot => snapshot.__typename === 'MobilityAdditionalInfoSnapshot'
        );

        return { addOns, additionalInfos, mobilitySnapshots };
    }, [application]);

    const { permissions: accountPermissions } = useAccount();
    const hasViewCustomerPermissions = hasPermissions(accountPermissions, [permissionKind.viewCustomers]);

    const { module } = application;
    if (module.__typename !== 'MobilityModule') {
        throw new Error('Mobility Module not found');
    }

    return (
        <Space direction="vertical" size={16} style={{ width: '100%' }}>
            <MainPanel application={application} module={module} stock={stock} />
            {addOns?.length > 0 && <AddOnsPanel mobilitySnapshots={mobilitySnapshots} />}
            {additionalInfos?.length > 0 && <AdditionalInfoPanel mobilitySnapshots={mobilitySnapshots} />}

            {hasViewCustomerPermissions && (
                <ApplicantPanel<MobilityBookingFormValues>
                    application={application}
                    customerKind={application.applicant.kind}
                    forCI={forCI}
                    isMask={isMask}
                    defaultExpanded
                    showCustomerLink
                />
            )}

            <ConsentsPanel application={application} />

            {hasViewCustomerPermissions && !isNil(application.guarantor) && (
                <ApplicantPanel<MobilityBookingFormValues>
                    customerKind={CustomerKind.Guarantor}
                    forCI={forCI}
                    isMask={isMask}
                    showCustomerLink={false}
                />
            )}

            {!isNil(application.guarantor) && <ConsentsPanel application={application} type="guarantor" />}

            {application?.__typename === 'MobilityApplication' &&
                ((application?.deposit && application.deposit.amount !== 0) || application.giftVoucher) && (
                    <PaymentPanel application={application} skipped={application?.deposit?.skipped} defaultExpanded />
                )}
            <AmendmentsPanel />
        </Space>
    );
};

export default MobilityBookingApplicationTab;
