import { Col } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import ApplicationDetailsPanel from '../../ApplicationDetailsPage/standard/Panel';
import colSpan from '../../ApplicationDetailsPage/standard/colSpan';
import SectionKey from '../SectionKey';
import type { MobilityBookingFormValues, MobilityBookingFormProps } from '../shared';
import { AmendmentCard, AmendmentTitle, Subtitle } from '../ui';

export type AmendmentContentProps = {
    title: string;
    mobilityBookingDetails: MobilityBookingFormProps['application']['mobilityBookingDetails'];
    formatDate: (date: string | Date) => string;
};

const AmendmentContent = ({ title, mobilityBookingDetails, formatDate }: AmendmentContentProps) => {
    const { t } = useTranslation(['applicationDetails', 'mobilityOrderSummary']);

    const locationName = useMemo(() => {
        switch (mobilityBookingDetails.location.__typename) {
            case 'MobilityBookingLocationHome':
                return t('mobilityOrderSummary:homeDelivery.label');

            case 'MobilityBookingLocationPickup':
                return mobilityBookingDetails.location.name;

            default:
                throw new Error(`Unknown location type for amendment panel`);
        }
    }, [mobilityBookingDetails.location, t]);

    return (
        <AmendmentCard bordered>
            {title && <AmendmentTitle>{title}</AmendmentTitle>}
            <Subtitle>
                {t('applicationDetails:panels.application.amendments.startDateTime', {
                    startDateTime: formatDate(mobilityBookingDetails.period.start),
                })}
            </Subtitle>
            <Subtitle>
                {t('applicationDetails:panels.application.amendments.endDateTime', {
                    endDateTime: formatDate(mobilityBookingDetails.period.end),
                })}
            </Subtitle>
            <Subtitle>
                {t('applicationDetails:panels.application.amendments.location', {
                    location: locationName,
                })}
            </Subtitle>
        </AmendmentCard>
    );
};

const AmendmentsPanel = () => {
    const { t } = useTranslation(['applicationDetails', 'common']);
    const { values } = useFormikContext<MobilityBookingFormValues>();
    const { forCI } = useApplicationDetailsExtraContext();

    const formatDate = useCallback(
        (value: string | Date) => dayjs(value).format(t('common:formats.dateTimeFormat')),
        [t]
    );

    const { id: currentId, amendments, mobilityBookingDetails: currentBookingDetails } = values;
    const contents = useMemo(() => {
        const currentVersion = (
            <Col {...colSpan} key={currentId}>
                <AmendmentContent
                    key={currentId}
                    formatDate={formatDate}
                    mobilityBookingDetails={currentBookingDetails}
                    title={t('applicationDetails:panels.application.amendments.currentAmendment')}
                />
            </Col>
        );

        const previousVersions = amendments?.map(({ id, mobilityBookingDetails, versioning }) => (
            <Col {...colSpan} key={id}>
                <AmendmentContent
                    key={id}
                    formatDate={formatDate}
                    mobilityBookingDetails={mobilityBookingDetails}
                    title={t('applicationDetails:panels.application.amendments.previousAmendment', {
                        dateTime: formatDate(versioning.updatedAt),
                    })}
                />
            </Col>
        ));

        return [currentVersion, ...previousVersions];
    }, [currentId, formatDate, currentBookingDetails, t, amendments]);

    return (
        <ApplicationDetailsPanel
            forCI={forCI}
            gutter={[24, 12]}
            header={t('applicationDetails:panels.application.amendments.header')}
            name={SectionKey.Amendments}
            defaultExpanded
        >
            {contents}
        </ApplicationDetailsPanel>
    );
};

export default AmendmentsPanel;
