import { Col, message } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import { MobilityApplicationModuleDataFragment } from '../../../../api/fragments/MobilityApplicationModuleData';
import { StockPublicDataFragment } from '../../../../api/fragments/StockPublicData';
import { useUpdateAssigneeOnApplicationMutation } from '../../../../api/mutations/updateAssigneeOnApplication';
import { ApplicationStage } from '../../../../api/types';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import { useLanguage } from '../../../../components/contexts/LanguageContextManager';
import { useThemeComponents } from '../../../../themes/hooks';
import hasPermissions from '../../../../utilities/hasPermissions';
import useFormattedSimpleVersioning from '../../../../utilities/useFormattedSimpleVersioning';
// eslint-disable-next-line max-len
import { calculateDisabledTimeRange } from '../../../portal/MobilityApplicationEntrypoint/CarDetailsPage/BookingForm/RentalFields/shared';
import DatePickerType from '../../../portal/MobilityApplicationEntrypoint/shared';
import useAssigneeOptions from '../../ApplicationDetailsPage/standard/ApplicationTab/useAssigneeOptions';
import ApplicationDetailsPanel from '../../ApplicationDetailsPage/standard/Panel';
import colSpan from '../../ApplicationDetailsPage/standard/colSpan';
import SectionKey from '../SectionKey';
import type { MobilityBookingFormValues, MobilityBookingFormProps } from '../shared';
import RentalDatepicker from './RentalDatepicker';
import RentalTimePicker from './RentalTimepicker';
import { formatToHomeLocationPayload, formatToPickupLocationPayload } from './shared';

type MainPanelProps = {
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>;
    module: MobilityApplicationModuleDataFragment;
    application: MobilityBookingFormProps['application'];
};

const MainPanel = ({ stock, module, application }: MainPanelProps) => {
    const { currentLanguageId } = useLanguage();
    const { t } = useTranslation(['applicationDetails', 'applicationList', 'common']);
    const { values, setFieldValue } = useFormikContext<MobilityBookingFormValues>();
    const { FormFields } = useThemeComponents();
    const { forCI } = useApplicationDetailsExtraContext();

    const { updated, offset } = useFormattedSimpleVersioning({
        versioning: values.versioning,
        timeZone: application.module.company.timeZone,
    });

    const assigneeOptions = useAssigneeOptions(application.availableAssignees);

    const { unavailableTimeRange } = module;
    const disabledTimeSlot = calculateDisabledTimeRange(unavailableTimeRange);

    const [internalRentalDates, setInternalRentalDates] = useState<{ start: string | Date; end: string | Date }>(
        values.mobilityBookingDetails.period
    );

    const [updateAssigneeMutation] = useUpdateAssigneeOnApplicationMutation();

    useEffect(() => {
        if (
            isNil(internalRentalDates) ||
            !dayjs(internalRentalDates.start).isSame(dayjs(values.rentalBookingPeriod?.start), 'second') ||
            !dayjs(internalRentalDates.end).isSame(dayjs(values.rentalBookingPeriod?.end), 'second')
        ) {
            setInternalRentalDates(values.rentalBookingPeriod);
            setFieldValue('startTime', undefined);
            setFieldValue('endTime', undefined);
        }

        if (
            !dayjs(internalRentalDates?.start).isSame(dayjs(values.rentalBookingPeriod?.start), 'day') ||
            !dayjs(internalRentalDates?.end).isSame(dayjs(values.rentalBookingPeriod?.end), 'day')
        ) {
            setFieldValue('startTime', undefined);
            setFieldValue('endTime', undefined);
        }
    }, [internalRentalDates, setFieldValue, values.mobilityBookingDetails, values.rentalBookingPeriod]);

    useEffect(() => {
        if (!isNil(values?.startTime) && !isNil(values?.endTime)) {
            setFieldValue('startTime', values?.startTime);
            setFieldValue('endTime', values?.endTime);
        }
    }, [setFieldValue, values?.endTime, values?.startTime]);

    // Need to map location options
    // Should cover old data that doesn't have the current module location id
    // The value is in "string", combined with onChange, we parse it back to object
    const locationOptions = useMemo(() => {
        const options: Array<{ label: string; value: string }> = [];
        let hasExistingOption: boolean = false;

        // Check from location module
        module.locations.forEach(location => {
            if (
                application.mobilityBookingDetails.location.__typename === 'MobilityBookingLocationPickup' &&
                location.id === application.mobilityBookingDetails.location.id
            ) {
                hasExistingOption = true;
            }

            options.push({
                label: location.name,
                value: JSON.stringify(formatToPickupLocationPayload(location)),
            });
        });

        // Check from home delivery
        if (module.homeDelivery.isEnable) {
            if (
                application.mobilityBookingDetails.location.__typename === 'MobilityBookingLocationHome' &&
                module.homeDelivery.id === application.mobilityBookingDetails.location.id
            ) {
                hasExistingOption = true;
            }

            options.push({
                label: t('common:homeDelivery'),
                value: JSON.stringify(formatToHomeLocationPayload(module.homeDelivery)),
            });
        }

        // Push it as first options
        if (!hasExistingOption) {
            options.unshift(
                application.mobilityBookingDetails.location.__typename === 'MobilityBookingLocationHome'
                    ? {
                          label: t('common:homeDelivery'),
                          value: JSON.stringify(formatToHomeLocationPayload(module.homeDelivery)),
                      }
                    : {
                          label: application.mobilityBookingDetails.location.name,
                          value: JSON.stringify(
                              formatToPickupLocationPayload(application.mobilityBookingDetails.location)
                          ),
                      }
            );
        }

        return options;
    }, [module.locations, module.homeDelivery, application.mobilityBookingDetails.location, t]);

    const onAssigneeChange = useCallback(
        async assigneeId => {
            try {
                message.loading({
                    content: t('applicationDetails:messages.updatingAssignee'),
                    key: 'primary',
                    duration: 0,
                });

                await updateAssigneeMutation({
                    variables: {
                        applicationId: values.id,
                        assigneeId,
                        stage: ApplicationStage.Mobility,
                        languageId: currentLanguageId,
                    },
                });

                message.success({
                    content: t('applicationDetails:messages.updatedAssignee'),
                    key: 'primary',
                });
            } catch (error) {
                message.destroy('primary');
                message.error(t('applicationDetails:messages.updatingAssigneeFailed'));
            }
        },
        [currentLanguageId, t, updateAssigneeMutation, values.id]
    );

    return (
        <ApplicationDetailsPanel
            forCI={forCI}
            header={t('applicationDetails:panels.application.main.header')}
            name={SectionKey.MainDetails}
            defaultExpanded
        >
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.dealer`, { returnObjects: true })}
                    value={application.dealer.displayName}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.identifier`, { returnObjects: true })}
                    value={values.mobilityStage?.identifier}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.TranslatedInputField
                    {...t('applicationDetails:fields.vehicle.name', { returnObjects: true })}
                    name="vehicle.name"
                    disabled
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('applicationDetails:fields.vehicle.identifier', { returnObjects: true })}
                    name="vehicle.identifier"
                    disabled
                    required
                />
            </Col>
            <RentalDatepicker
                application={application}
                colSpan={colSpan}
                disabledTimeSlot={disabledTimeSlot}
                module={module}
                stock={stock}
            />
            <RentalTimePicker
                application={application}
                colSpan={colSpan}
                disabledTimeSlot={disabledTimeSlot}
                module={module}
                name="startTime"
                offset={offset}
                stock={stock}
                typeTimePicker={DatePickerType.Start}
            />
            <RentalTimePicker
                application={application}
                colSpan={colSpan}
                disabledTimeSlot={disabledTimeSlot}
                module={module}
                name="endTime"
                offset={offset}
                stock={stock}
                typeTimePicker={DatePickerType.End}
            />

            <Col {...colSpan}>
                <FormFields.SelectField
                    {...t('applicationDetails:fields.mobilityBooking.location', { returnObjects: true })}
                    name="location"
                    options={locationOptions}
                    required
                    showSearch
                />
            </Col>

            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.mobilityStatus`, { returnObjects: true })}
                    value={t(`applicationList:status.${values.mobilityStage?.status}`)}
                />
            </Col>

            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.mobilityBooking.vin`, { returnObjects: true })}
                    value={stock?.vin}
                />
            </Col>

            <Col {...colSpan}>
                <FormFields.SelectField
                    {...t(`applicationDetails:fields.assignee`, { returnObjects: true })}
                    disabled={!hasPermissions(application.permissions, [permissionKind.updateApplication])}
                    name="mobilityStage.assigneeId"
                    onChange={onAssigneeChange}
                    options={assigneeOptions}
                    showSearch={!forCI}
                    style={{ padding: '0 0 5px' }}
                    required
                />
            </Col>

            <Col {...colSpan}>
                <FormFields.DisplayFieldWithUTCOffset
                    {...t('applicationDetails:fields.updatedAt', { returnObjects: true, offset })}
                    value={updated}
                />
            </Col>
        </ApplicationDetailsPanel>
    );
};

export default MainPanel;
