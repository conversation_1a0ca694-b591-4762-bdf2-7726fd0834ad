import { Space } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import useCompanyFormats from '../../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import ApplicationDetailsPanel from '../../ApplicationDetailsPage/standard/Panel';
import SectionKey from '../SectionKey';
import type { MobilityBookingFormProps } from '../shared';
import { Title, Subtitle, StyledCard } from '../ui';

export type AddOnsPanelProps = {
    mobilitySnapshots: MobilityBookingFormProps['application']['mobilitySnapshots'];
};

const AddOnsPanel = ({ mobilitySnapshots }: AddOnsPanelProps) => {
    const { t } = useTranslation(['applicationDetails']);
    const translate = useTranslatedString();
    const { formatAmountWithCurrency } = useCompanyFormats();
    const { forCI } = useApplicationDetailsExtraContext();

    const content = useMemo(
        () =>
            mobilitySnapshots
                .filter(snapshot => snapshot.__typename === 'MobilityAddonSnapshot')
                .map(addOn => (
                    <StyledCard>
                        <Title>{translate(addOn.title)}</Title>
                        {addOn.__typename === 'MobilityAddonSnapshot' && (
                            <Subtitle>
                                {translate(addOn.option.name)}: {formatAmountWithCurrency(addOn.option.price)}
                            </Subtitle>
                        )}
                    </StyledCard>
                )),
        [mobilitySnapshots]
    );

    return (
        <ApplicationDetailsPanel
            forCI={forCI}
            gutter={0}
            header={t('applicationDetails:panels.application.addOns.header')}
            name={SectionKey.AddOns}
            defaultExpanded
        >
            <Space direction="vertical" style={{ width: '100%' }}>
                {content}
            </Space>
        </ApplicationDetailsPanel>
    );
};

export default AddOnsPanel;
