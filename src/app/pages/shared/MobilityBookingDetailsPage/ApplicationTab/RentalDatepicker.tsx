import { CalendarOutlined } from '@ant-design/icons';
import { Col } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker/generatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useFormikContext } from 'formik';
import { isEmpty, isNil } from 'lodash/fp';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../../../themes/hooks';
import {
    blockerFromStartDateForEndDateOptions,
    calculateAvailaleHoursForNextBooking,
    calculateBookingEntireDay,
    computeClosestMaxDate,
} from '../../../portal/MobilityApplicationEntrypoint/CarDetailsPage/BookingForm/RentalFields/shared';
import { getAvailableDateRange, getUnavailableDayofWeek } from '../../../portal/MobilityApplicationEntrypoint/helper';
import DatePickerType from '../../../portal/MobilityApplicationEntrypoint/shared';
import type { MobilityBookingFormValues } from '../shared';
import { getFocusToStartDateInput, RentalFieldProps } from './shared';

const RentalDatepicker = ({
    module,
    stock,
    disabledTimeSlot,
    application,
    typeTimePicker,
    colSpan,
}: RentalFieldProps) => {
    const {
        minimumAdvancedBooking: minimumBooking,
        durationBeforeNextBooking: durationNextBooking,
        availableNumberOfBookingRange: availableBookingRange,
        unavailableDayOfWeek,
        unavailableTimeRange,
    } = module;

    const reservedTimeSlotRange = calculateAvailaleHoursForNextBooking(
        stock?.reservations ?? [],
        minimumBooking,
        durationNextBooking,
        disabledTimeSlot
    );
    const { values, setFieldValue } = useFormikContext<MobilityBookingFormValues>();
    const { FormFields } = useThemeComponents();
    const { t } = useTranslation('applicationDetails');

    const onCalendarChange = useCallback(
        (value, dateStrings, info) => {
            const [start, end] = value ?? [];

            const rentalPeriod = {
                start: start ? dayjs(start).toDate() : undefined,
                end: end ? dayjs(end).toDate() : undefined,
            };

            setFieldValue('rentalBookingPeriod', rentalPeriod);
        },
        [setFieldValue]
    );

    const onChange = useCallback(
        value => {
            const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');

            if (!isNil(inputs) && inputs.length > 0) {
                getFocusToStartDateInput(inputs);
                const [start, end] = value ?? [];

                const rentalPeriod = {
                    start: start ? dayjs(start).toDate() : undefined,
                    end: end ? dayjs(end).toDate() : undefined,
                };
                setFieldValue('rentalBookingPeriod', rentalPeriod);
            }
        },
        [setFieldValue]
    );

    const onOpenChange = useCallback(
        open => {
            const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');
            // 0 refers start and 1 refers endDate
            if (open && !isNil(inputs) && isNil(values.rentalBookingPeriod?.start) && inputs.length > 0) {
                getFocusToStartDateInput(inputs);
            }

            if (!isNil(inputs) && inputs.length > 0 && !open) {
                inputs[1].classList.remove('.ant-picker-input-active');
            }

            if (!isNil(inputs) && inputs.length > 0 && open) {
                inputs[1].click();
                getFocusToStartDateInput(inputs);
                if (!isNil(values.rentalBookingPeriod?.end)) {
                    setFieldValue('rentalBookingPeriod', {
                        start: undefined,
                        end: undefined,
                    });
                }
            }
        },
        [setFieldValue, values.rentalBookingPeriod]
    );

    const onClick = useCallback(() => {
        const inputs: NodeListOf<HTMLElement> = document.querySelectorAll('.ant-picker-range .ant-picker-input');
        if (!isNil(inputs) && inputs.length > 0) {
            // 0 refers start and 1 refers endDate
            getFocusToStartDateInput(inputs);
            if (!isNil(values.rentalBookingPeriod?.end)) {
                setFieldValue('rentalBookingPeriod', {
                    start: undefined,
                    end: undefined,
                });
            }
        }
    }, [setFieldValue, values.rentalBookingPeriod?.end]);

    const disabledDate: RangePickerProps<Dayjs>['disabledDate'] = useCallback(
        value => {
            // value each date of the calendar
            const date = dayjs(value);
            const dateString = date.format('YYYY-MM-DD');
            const { start, end } = values.rentalBookingPeriod || {};

            // current date
            const todayDate = dayjs();

            // in `disabledDate` API, it does not able to use `type` suchlike `disabledTime` feature
            // availableBookingRange = max range of booking
            const [datepickerType, lastAvailableBookingDate] = getAvailableDateRange(availableBookingRange, start, end);

            const unavailableDayofWeek = getUnavailableDayofWeek(unavailableDayOfWeek);
            let checker = false;

            if (!isNil(lastAvailableBookingDate)) {
                checker =
                    datepickerType === DatePickerType.Start
                        ? !date.isBetween(
                              dayjs(values.rentalBookingPeriod?.start),
                              lastAvailableBookingDate,
                              'day',
                              '[]'
                          )
                        : !date.isBetween(
                              lastAvailableBookingDate,
                              dayjs(values.rentalBookingPeriod?.end),
                              'day',
                              '[]'
                          );
            }

            const { isBlock } = calculateBookingEntireDay(
                value,
                unavailableTimeRange,
                datepickerType,
                durationNextBooking,
                stock?.reservations ?? [],
                values.rentalBookingPeriod
            );

            const maxDate = computeClosestMaxDate(start, stock?.reservations ?? [], stock?.blockPeriod ?? []);

            const blockerFromStartDate = start
                ? blockerFromStartDateForEndDateOptions(
                      values.rentalBookingPeriod,
                      durationNextBooking,
                      stock?.reservations ?? [],
                      unavailableTimeRange
                  )
                : false;

            if (value) {
                // check for available period, prefer settings on stocks over inventories
                const inventory = stock?.inventory.__typename === 'MobilityInventory' ? stock.inventory : null;
                const period = stock?.period || inventory?.period;
                const isWithinAvailablePeriod =
                    isEmpty(period) ||
                    (date.isSameOrAfter(period?.start, 'day') && date.isSameOrBefore(period?.end, 'day'));

                return (
                    // firstly we block dates before today
                    date.isBefore(todayDate, 'day') ||
                    // not within valid period
                    !isWithinAvailablePeriod ||
                    // block the dates which already fully block ( blocked entire day)
                    (!isNil(reservedTimeSlotRange[dateString]) &&
                        Object.entries(reservedTimeSlotRange[dateString]).length > 23) ||
                    // only allow available date range based on user selected date ( start date or end date first) and
                    // `mobilityModule.availableDateRange`
                    // availableDateRange = 0 :: means datepicker do not block any dates
                    // availableDateRange > 0 :: means show limited available dates based on user choose date
                    checker ||
                    /**
                     * we check whether the reservation listing has any calendar date chosen overlapping with
                     * existing booking, we will take the closest start datetime of the reservation based on the
                     * user selected date , vice versa to end date
                     *  */
                    (isBlock && !date.isSame(dayjs(values.rentalBookingPeriod?.start), 'day')) ||
                    (maxDate && date.isAfter(maxDate, 'day')) ||
                    (blockerFromStartDate && date.isAfter(dayjs(values.rentalBookingPeriod?.start), 'day')) ||
                    /**
                     * block if the user choose the end date and the date is all later than
                     * all existing reservation end date, system will block the minDate as
                     * the closest reservation end date
                     * :: to prevent overlapping booking on existing booking
                     */
                    unavailableDayofWeek.includes(date.day())
                );
            }

            return false;
        },
        [
            availableBookingRange,
            durationNextBooking,
            reservedTimeSlotRange,
            stock?.blockPeriod,
            stock?.inventory,
            stock?.period,
            stock?.reservations,
            unavailableDayOfWeek,
            unavailableTimeRange,
            values.rentalBookingPeriod,
        ]
    );

    return (
        <Col {...colSpan}>
            <FormFields.RangePickerField
                disabled={[false, false]}
                disabledDate={disabledDate}
                format={t('common:formats.dateTimePicker')}
                label={t('applicationDetails:fields.mobilityBooking.period.label')}
                name="rentalBookingPeriod"
                onCalendarChange={onCalendarChange}
                onChange={onChange}
                onClick={onClick}
                onOpenChange={onOpenChange}
                showTime={false}
                suffixIcon={<CalendarOutlined />}
                readOnly
                required
            />
        </Col>
    );
};

export default RentalDatepicker;
