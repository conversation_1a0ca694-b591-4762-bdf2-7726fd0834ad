import { Space } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import ApplicationDetailsPanel from '../../ApplicationDetailsPage/standard/Panel';
import SectionKey from '../SectionKey';
import type { MobilityBookingFormProps } from '../shared';
import { Title, Subtitle, Description, StyledCard } from '../ui';

export type AdditionalInfoPanelProps = {
    mobilitySnapshots: MobilityBookingFormProps['application']['mobilitySnapshots'];
};

const AdditionalInfoPanel = ({ mobilitySnapshots }: AdditionalInfoPanelProps) => {
    const { t } = useTranslation(['applicationDetails']);
    const translate = useTranslatedString();
    const { forCI } = useApplicationDetailsExtraContext();

    const additionalInfos = useMemo(
        () => mobilitySnapshots.filter(snapshot => snapshot.__typename === 'MobilityAdditionalInfoSnapshot'),
        [mobilitySnapshots]
    );

    const content = useMemo(
        () =>
            additionalInfos.map(info => {
                const details =
                    info.__typename === 'MobilityAdditionalInfoSnapshot' &&
                    info.detail.map(detail => (
                        <>
                            <Subtitle>{translate(detail.subTitle)}</Subtitle>
                            <Description>{translate(detail.description)}</Description>
                        </>
                    ));

                return (
                    <StyledCard>
                        <Title>{translate(info.title)}</Title>
                        {details}
                    </StyledCard>
                );
            }),
        [additionalInfos]
    );

    return (
        <ApplicationDetailsPanel
            forCI={forCI}
            gutter={0}
            header={t('applicationDetails:panels.application.additionalInfo.header')}
            name={SectionKey.AdditionalInfo}
            defaultExpanded
        >
            <Space direction="vertical" style={{ width: '100%' }}>
                {content}
            </Space>
        </ApplicationDetailsPanel>
    );
};

export default AdditionalInfoPanel;
