import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { uniqBy } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';
import { ApplicationStage } from '../../../../api/types';
import { useApplicationDetailsExtraContext } from '../../../../components/contexts/ApplicationDetailsExtraContext';
import ApplicationDetailsPanel from '../../ApplicationDetailsPage/standard/Panel';
import AgreementsGrid from '../../CIPage/ConsentAndDeclarations/AgreementsGrid';
import SectionKey from '../SectionKey';
import type { MobilityBookingFormValues } from '../shared';
import { hasIsApplyingFinancingCondition, hasIsApplyingInsuranceCondition } from './shared';

type ConsentPanelProps = {
    defaultExpanded?: boolean;
    stage?: ApplicationStage;
    application?: ApplicationDataFragment;
    type?: 'applicant' | 'guarantor';
};

const useGetAgreementsFromApplication = (
    stage?: ApplicationStage,
    application?: ApplicationDataFragment,
    type = 'applicant'
) => {
    const { values } = useFormikContext<MobilityBookingFormValues>();

    return useMemo(() => {
        const allAgreements = type === 'applicant' ? values.applicantAgreements : values.guarantorAgreements;

        if (!allAgreements) {
            return [];
        }

        switch (stage) {
            case ApplicationStage.Insurance:
                return allAgreements.filter(agreement => !hasIsApplyingFinancingCondition(agreement.conditions));
            case ApplicationStage.Financing:
                return allAgreements.filter(agreement => !hasIsApplyingInsuranceCondition(agreement.conditions));

            case ApplicationStage.Appointment: {
                if (
                    application.__typename !== 'ConfiguratorApplication' &&
                    application.__typename !== 'EventApplication' &&
                    application.__typename !== 'FinderApplication' &&
                    application.__typename !== 'StandardApplication'
                ) {
                    return allAgreements;
                }

                if (application.draftFlow.isTestDriveProcessStarted) {
                    return uniqBy('id', [...application.testDriveAgreements, ...allAgreements]);
                }

                return allAgreements;
            }

            default:
                return allAgreements;
        }
    }, [application, stage, type, values]);
};

const ConsentsPanel = ({ defaultExpanded, stage, application, type = 'applicant' }: ConsentPanelProps) => {
    const { t } = useTranslation(['applicationDetails']);
    const { forCI } = useApplicationDetailsExtraContext();

    const agreements = useGetAgreementsFromApplication(stage, application, type);

    return (
        <ApplicationDetailsPanel
            defaultExpanded={defaultExpanded}
            forCI={forCI}
            gutter={[24, 12]}
            header={
                type === 'applicant'
                    ? t('applicationDetails:panels.application.consents.header')
                    : t('applicationDetails:panels.application.guarantorConsents.header')
            }
            name={SectionKey.Consents}
        >
            <Col span={24}>
                <AgreementsGrid
                    agreements={agreements}
                    editable={false}
                    prefix={type === 'applicant' ? 'agreements' : 'guarantorAgreementValues'}
                    disabled
                />
            </Col>
        </ApplicationDetailsPanel>
    );
};

export default ConsentsPanel;
