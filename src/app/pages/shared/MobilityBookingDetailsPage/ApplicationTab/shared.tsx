import { isNil, pick } from 'lodash/fp';
import { MobilityApplicationModuleDataFragment } from '../../../../api/fragments/MobilityApplicationModuleData';
import { MobilityBookingLocationHomeDataFragment } from '../../../../api/fragments/MobilityBookingLocationHomeData';
import { MobilityBookingLocationPickupDataFragment } from '../../../../api/fragments/MobilityBookingLocationPickupData';
import { StockPublicDataFragment } from '../../../../api/fragments/StockPublicData';
import { ConditionType, MobilityBookingLocationPayload } from '../../../../api/types';
// eslint-disable-next-line max-len
import { HourMinutesMapping } from '../../../portal/MobilityApplicationEntrypoint/CarDetailsPage/BookingForm/RentalFields/shared';
import DatePickerType from '../../../portal/MobilityApplicationEntrypoint/shared';
import type { MobilityBookingFormValues, MobilityBookingFormProps } from '../shared';

export type RentalFieldProps = {
    module: MobilityApplicationModuleDataFragment;
    application: MobilityBookingFormProps['application'];
    stock: Extract<StockPublicDataFragment, { __typename: 'MobilityStockInventory' }>;
    disabledTimeSlot?: HourMinutesMapping;
    typeTimePicker?: DatePickerType;
    name?: string;
    disabled?: boolean;
    colSpan?: { lg: number; md: number; xs: number };
    offset?: string;
};

export const getFocusToStartDateInput = (inputs: NodeListOf<HTMLElement>) => {
    if (!isNil(inputs) && inputs.length > 0) {
        inputs[1].classList.remove('.ant-picker-input-active');
        inputs[0].classList.add('ant-picker-input-active');
        inputs[0].focus();
    }
};

export type Conditions = MobilityBookingFormValues['applicantAgreements'][0]['conditions'][0];

export const hasIsApplyingInsuranceCondition = (conditions?: Conditions[]) =>
    conditions?.some(condition => condition.type === ConditionType.IsApplyingForInsurance);

export const hasIsApplyingFinancingCondition = (conditions: Conditions[]) =>
    conditions?.some(condition => condition.type === ConditionType.IsApplyingForFinancing);

export const formatToHomeLocationPayload = (
    location: Omit<MobilityBookingLocationHomeDataFragment, 'type' | '__typename'>
): MobilityBookingLocationPayload => ({ isHomeDelivery: true, homeDelivery: { id: location.id } });

export const formatToPickupLocationPayload = (
    location: Omit<MobilityBookingLocationPickupDataFragment, 'type' | '__typename'>
): MobilityBookingLocationPayload => ({
    isHomeDelivery: false,
    pickup: pick(['id', 'name', 'address', 'url', 'phone', 'email'], location),
});
