import dayjs from 'dayjs';
import isEqual from 'fast-deep-equal';
import { isNil, isArray, get } from 'lodash/fp';

import { SubmitChangesMutationVariables } from '../../../../api/mutations/submitChanges';
import { UpdateApplicationMutationVariables } from '../../../../api/mutations/updateApplication';
import {
    ApplicationStage,
    ApplicationUpdate,
    ApplicationUpdateConfiguration,
    LocalCustomerFieldKey,
} from '../../../../api/types';
import getFinancingFromCalculatorValues from '../../../../calculator/getFinancingFromCalculatorValues';
import getInsuranceValueFromCalculator from '../../../../calculator/getInsuranceValueFromCalculator';
import { GenericCalculatorValues } from '../../../../calculator/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import combineDateAndTime from '../../../../utilities/combineDateAndTime';
import { KYCPresetFormFields, prepareKYCFieldPayload } from '../../../../utilities/kycPresets';
import useDialCodeByCountry from '../../../admin/CompanyDetailsPage/useDialCodeByCountry';
import type { MobilityBookingFormValues } from '../../MobilityBookingDetailsPage/shared';
import type { ApplicationFormValues } from './shared';

const getTestDriveValues = (value: ApplicationFormValues) => {
    if (!value.appointmentStage) {
        return {};
    }

    const { mileage, registrationNumber } = value.appointmentStage;

    return {
        mileage,
        registrationNumber,
    };
};

export type AppointmentDateTime = {
    appointmentDate: string | Date | dayjs.Dayjs;
    appointmentTime: string | Date | dayjs.Dayjs;
};

export type PrepareAppointmentChangesValues = ApplicationFormValues & AppointmentDateTime;

export const prepareAppointmentChanges = (
    values: ApplicationFormValues & AppointmentDateTime,
    stage: ApplicationStage,
    timeZone?: string
) => {
    if (stage !== ApplicationStage.Appointment && stage !== ApplicationStage.VisitAppointment) {
        return values;
    }

    const { appointmentDate, appointmentTime, ...otherValues } = values;

    const bookingTimeSlot = combineDateAndTime(appointmentDate, appointmentTime?.toString(), timeZone);
    if (!bookingTimeSlot) {
        return values;
    }

    const result = { ...otherValues };

    if (stage === ApplicationStage.Appointment) {
        result.appointmentStage = {
            ...values.appointmentStage,
            bookingTimeSlot: {
                ...values.appointmentStage.bookingTimeSlot,
                slot: bookingTimeSlot.toDate(),
            },
        };
    }

    if (stage === ApplicationStage.VisitAppointment) {
        result.visitAppointmentStage = {
            ...values.visitAppointmentStage,
            bookingTimeSlot: {
                ...values.visitAppointmentStage.bookingTimeSlot,
                slot: bookingTimeSlot.toDate(),
            },
        };
    }

    return result;
};

const getApplicationUpdates = (
    initialValues: ApplicationFormValues | MobilityBookingFormValues,
    values: ApplicationFormValues | MobilityBookingFormValues,
    stage: ApplicationStage,
    allowRecalculateApplication?: boolean
): SubmitChangesMutationVariables['updates'] | UpdateApplicationMutationVariables['updates'] => {
    const updates: SubmitChangesMutationVariables['updates'] = [];

    switch (stage) {
        case ApplicationStage.Mobility: {
            const { location, mobilityBookingDetails } = values as MobilityBookingFormValues;

            updates.push({
                updateMobilityBooking: {
                    location: JSON.parse(location),
                    period: mobilityBookingDetails.period,
                    userTimezone: dayjs()
                        .local()
                        .format()
                        .substring(dayjs().local().format().length - 6),
                },
            });

            break;
        }

        default: {
            const formikValues = values as ApplicationFormValues;
            const initialGenericValues = initialValues as ApplicationFormValues;

            const applyingForFinance =
                formikValues.withFinancing && formikValues.withFinancing !== initialGenericValues.withFinancing;

            if (
                (allowRecalculateApplication || applyingForFinance) &&
                (!isEqual(initialGenericValues.financing, formikValues.financing) ||
                    !isEqual(initialGenericValues.vehicle.id, formikValues.vehicle.id))
            ) {
                updates.push({
                    updateFinancing: {
                        financing: getFinancingFromCalculatorValues(formikValues.financing),
                        variantId: formikValues.vehicle.id,
                        promoCodeId: formikValues.promoCode?.id,
                    },
                });
            }

            if (
                (formikValues.insurancing && !isEqual(initialGenericValues.insurancing, formikValues.insurancing)) ||
                !isEqual(initialValues.vehicle.id, formikValues.vehicle.id)
            ) {
                updates.push({
                    updateInsurance: {
                        insurancing: getInsuranceValueFromCalculator(
                            formikValues.insurancing as GenericCalculatorValues
                        ),
                        variantId: formikValues.vehicle.id,
                    },
                });
            }

            updates.push({ otherVehicleInformation: formikValues.otherVehicleInformation });

            const newConfiguration: Partial<ApplicationUpdateConfiguration> = {};
            if (applyingForFinance) {
                newConfiguration.withFinancing = true;
            }

            if (formikValues.withInsurance && formikValues.withInsurance !== initialGenericValues.withInsurance) {
                newConfiguration.withInsurance = true;
            }

            if (formikValues.tradeIn && formikValues.tradeIn !== initialGenericValues.tradeIn) {
                newConfiguration.tradeIn = true;
            }

            if (Object.keys(newConfiguration).length) {
                updates.push({ configuration: newConfiguration });
            }

            if (
                ((!isNil(initialGenericValues.appointmentStage?.bookingTimeSlot?.slot) ||
                    !isNil(formikValues.appointmentStage?.bookingTimeSlot?.slot)) &&
                    !isEqual(
                        dayjs(initialGenericValues.appointmentStage?.bookingTimeSlot?.slot),
                        dayjs(formikValues.appointmentStage?.bookingTimeSlot?.slot)
                    )) ||
                !isEqual(getTestDriveValues(initialGenericValues), getTestDriveValues(formikValues))
            ) {
                updates.push({
                    updateAppointmentDetails: {
                        ...getTestDriveValues(formikValues),
                        bookingTimeSlot: formikValues.appointmentStage?.bookingTimeSlot.slot,
                    },
                });
            }

            if (
                (!isNil(initialGenericValues.visitAppointmentStage?.bookingTimeSlot?.slot) ||
                    !isNil(formikValues.visitAppointmentStage?.bookingTimeSlot?.slot)) &&
                !isEqual(
                    dayjs(initialGenericValues.visitAppointmentStage?.bookingTimeSlot?.slot),
                    dayjs(formikValues.visitAppointmentStage?.bookingTimeSlot?.slot)
                )
            ) {
                updates.push({
                    updateVisitAppointmentDetails: {
                        bookingTimeSlot: formikValues.visitAppointmentStage?.bookingTimeSlot.slot,
                    },
                });
            }

            if (!isNil(formikValues.quotation) && !isEqual(initialGenericValues.quotation, formikValues.quotation)) {
                updates.push({
                    updateQuotationDetails: formikValues.quotation,
                });
            }

            break;
        }
    }

    updates.push({ updateLocalCustomer: prepareKYCFieldPayload(values.customer.fields) });

    return updates;
};

type HasUpdateOption = { dialCode?: number };

// do not need to consider mobility
export const hasUpdates = (
    updates: UpdateApplicationMutationVariables['updates'],
    values: ApplicationFormValues,
    initialValues: ApplicationFormValues,
    options?: HasUpdateOption
) => {
    if (isNil(updates) || (isArray(updates) && !updates?.length)) {
        return false;
    }

    if (!isEqual(values.remarks, initialValues.remarks)) {
        return true;
    }

    if (!isEqual(values.commentsToInsurer, initialValues.commentsToInsurer)) {
        return true;
    }

    let hasChanges = false;
    isArray(updates) &&
        updates.forEach(update => {
            if (
                update?.updateFinancing ||
                update?.updateInsurance ||
                update?.configuration ||
                update?.updateAppointmentDetails ||
                update?.updateVisitAppointmentDetails ||
                update?.updateQuotationDetails
            ) {
                hasChanges = true;
            }

            if (update?.otherVehicleInformation) {
                Object.keys(values.otherVehicleInformation).forEach(key => {
                    if (
                        !isEqual(
                            get(key, values.otherVehicleInformation),
                            get(key, initialValues?.otherVehicleInformation)
                        )
                    ) {
                        hasChanges = true;
                    }
                });
            }
            if (update.updateLocalCustomer?.length) {
                if (
                    hasCustomerUpdate(
                        update.updateLocalCustomer,
                        values.customer.fields,
                        initialValues?.customer.fields,
                        options
                    )
                ) {
                    hasChanges = true;
                }
            }
        });

    return hasChanges;
};

const phoneNumberKycFields = [
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CorporatePhone,
];

export const useDialCode = () => {
    const dialCodeByCountry = useDialCodeByCountry();
    const company = useCompany(true);
    const countryCodeOrName = company?.countryCode;
    const dialCode = dialCodeByCountry(countryCodeOrName);

    return dialCode;
};

export const hasCustomerUpdate = (
    updates: ApplicationUpdate['updateLocalCustomer'],
    current: KYCPresetFormFields,
    initial: KYCPresetFormFields,
    options?: HasUpdateOption
) => {
    let hasChanges = false;
    updates
        .map(i => i.key)
        .forEach(key => {
            const { dialCode } = options;
            // phone field compare specially
            if (
                phoneNumberKycFields.includes(key) &&
                isNil(get(key, initial)) &&
                (!!get([key, 'value', 'value'], current) || !isEqual(get([key, 'value', 'prefix'], current), dialCode))
            ) {
                hasChanges = true;
            } else if (
                key === LocalCustomerFieldKey.ReferenceDetailSet &&
                isNil(get(key, initial)) &&
                (!!get([key, 'value', 'contactNumber', 'value'], current) ||
                    !isEqual(get([key, 'value', 'contactNumber', 'prefix'], current), dialCode))
            ) {
                hasChanges = true;
            } else if (!isEqual(get([key, 'value'], current), get([key, 'value'], initial))) {
                hasChanges = true;
            }
        });

    return hasChanges;
};

export default getApplicationUpdates;
