import { ApolloClient } from '@apollo/client';
import { ApplicationDataFragment } from '../../../../api/fragments';

import {
    SubmitChangesDocument,
    SubmitChangesMutation,
    SubmitChangesMutationVariables,
} from '../../../../api/mutations/submitChanges';
import {
    UpdateMobilityApplicationDocument,
    UpdateMobilityApplicationMutation,
    UpdateMobilityApplicationMutationVariables,
} from '../../../../api/mutations/updateMobilityApplication';
import { ApplicationStage } from '../../../../api/types';
import type { MobilityBookingFormValues } from '../../MobilityBookingDetailsPage/shared';
import type { ApplicationFormValues } from './shared';
import getApplicationUpdates from './useUpdates';

type SubmitChangeProps = {
    application: ApplicationDataFragment;
    redirectionLink: string;
};

const useSubmitChanges = async (
    apolloClient: ApolloClient<object>,
    initialValues: ApplicationFormValues | MobilityBookingFormValues,
    values: ApplicationFormValues | MobilityBookingFormValues,
    withRedirectionLink: boolean,
    stage: ApplicationStage,
    allowRecalculateApplication?: boolean
): Promise<SubmitChangeProps> => {
    const updates: SubmitChangesMutationVariables['updates'] = getApplicationUpdates(
        initialValues,
        values,
        stage,
        allowRecalculateApplication
    );

    if (stage === ApplicationStage.Mobility) {
        const { data } = await apolloClient.mutate<
            UpdateMobilityApplicationMutation,
            UpdateMobilityApplicationMutationVariables
        >({
            mutation: UpdateMobilityApplicationDocument,
            variables: {
                applicationId: values.id,
                updates,
                stage,
            },
        });

        return { application: data?.result?.application, redirectionLink: data?.result?.redirectionLink };
    }

    const { data } = await apolloClient.mutate<SubmitChangesMutation, SubmitChangesMutationVariables>({
        mutation: SubmitChangesDocument,
        variables: {
            applicationId: values.id,
            updates,
            withRedirectionLink,
            stage,
            remarks: values.remarks,
            commentsToInsurer:
                values.__typename !== 'MobilityApplication' && values.commentsToInsurer
                    ? values.commentsToInsurer
                    : undefined,
        },
    });

    return { application: data?.result?.application, redirectionLink: data?.result?.redirectionLink };
};

export default useSubmitChanges;
