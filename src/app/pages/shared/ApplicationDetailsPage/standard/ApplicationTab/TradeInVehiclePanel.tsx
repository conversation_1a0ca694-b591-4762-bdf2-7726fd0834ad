import { Col } from 'antd';
import { FieldArray, useFormikContext } from 'formik';
import { getOr } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { LocalCustomerFieldKey, TradeInVehiclePayload } from '../../../../../api/types';
import { useApplicationDetailsExtraContext } from '../../../../../components/contexts/ApplicationDetailsExtraContext';
import { useThemeComponents } from '../../../../../themes/hooks';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import TradeInVehicleItem from '../../../JourneyPage/CustomerDetails/TradeInVehicleItem';
import ApplicationDetailsPanel from '../Panel';
import SectionKey from '../SectionKey';
import colSpan from '../colSpan';
import type { ApplicationFormValues } from '../shared';

const TradeInVehiclePanel = () => {
    const { t } = useTranslation('applicationDetails');
    const { values } = useFormikContext<ApplicationFormValues>();
    const { forCI } = useApplicationDetailsExtraContext();

    const { formatAmountWithCurrency } = useCompanyFormats();
    const { FormFields } = useThemeComponents();

    return (
        <ApplicationDetailsPanel
            forCI={forCI}
            header={t('applicationDetails:panels.application.tradeInVehicle.header')}
            name={SectionKey.TradeInVehicles}
        >
            <Col {...colSpan}>
                <FormFields.CheckboxField
                    {...t('applicationDetails:fields.tradeInVehicle', { returnObjects: true })}
                    name="configuration.tradeIn"
                    disabled
                />
            </Col>
            {values.applicantKYC?.some(item =>
                [
                    LocalCustomerFieldKey.CurrentVehicleSource,
                    LocalCustomerFieldKey.CurrentVehicleOwnership,
                    LocalCustomerFieldKey.CurrentVehicleMake,
                    LocalCustomerFieldKey.CurrentVehicleModel,
                    LocalCustomerFieldKey.CurrentVehicleEquipmentLine,
                    LocalCustomerFieldKey.CurrentVehicleModelYear,
                    LocalCustomerFieldKey.CurrentVehiclePurchaseYear,
                    LocalCustomerFieldKey.CurrentVehicleEngineType,
                    LocalCustomerFieldKey.CurrentVehicleMileage,
                    LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
                    LocalCustomerFieldKey.CurrentVehicleContractEnd,
                    LocalCustomerFieldKey.CurrentVehiclePotentialReplacement,
                    LocalCustomerFieldKey.CurrentVehicleVin,
                ].includes(item.key)
            ) && (
                <FieldArray
                    name="tradeInVehicle"
                    render={() =>
                        getOr([], 'tradeInVehicle', values).map((value: TradeInVehiclePayload, index: number) => (
                            <TradeInVehicleItem
                                key={`tradeInVehicle-${index.toString()}`}
                                colSpan={colSpan}
                                kycPresets={values.applicantKYC}
                                name={`tradeInVehicle[${index}]`}
                                value={value}
                                disabled
                            />
                        ))
                    }
                />
            )}
            {/* Event doesn't have this */}
            {values.configuration.tradeIn && values.financing && (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.tradeInAmount', { returnObjects: true })}
                            value={formatAmountWithCurrency(values.financing.tradeInAmount)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.priceAfterTradeIn', { returnObjects: true })}
                            value={formatAmountWithCurrency(
                                values.financing.downPayment.amount - values.financing.tradeInAmount
                            )}
                        />
                    </Col>
                </>
            )}
        </ApplicationDetailsPanel>
    );
};

export default TradeInVehiclePanel;
