/* eslint-disable max-len */
import { useFormikContext } from 'formik';
import { isEmpty } from 'lodash/fp';
import { useMemo, useRef, useState } from 'react';
import * as permissionKind from '../../../../../../../shared/permissions';
import { ApplicationMarketTypeFragmentFragment } from '../../../../../../api/fragments/ApplicationMarketTypeFragment';
import { PromoCodeDataFragment } from '../../../../../../api/fragments/PromoCodeData';
import { useGetFinderVehicleQuery } from '../../../../../../api/queries/getFinderVehicle';
import {
    ApplicationMarket,
    DefaultApplicationMarket,
    FinanceProductSortingField,
    NewZealandApplicationMarket,
    SingaporeApplicationMarket,
    SortingOrder,
} from '../../../../../../api/types';
import { GenericCalculatorProvider } from '../../../../../../calculator/CalculatorProvider';
import getCalculatorValuesFromApplication from '../../../../../../calculator/getCalculatorValuesFromApplication';
import { GenericCalculatorValues } from '../../../../../../calculator/types';
import hasPermissions from '../../../../../../utilities/hasPermissions';
import useMarketSpecificCalculatorValues from '../../../../../../utilities/useMarketSpecificCalculatorValues';
import useLocalModuleFinanceProducts from '../../../../../portal/StandardApplicationEntrypoint/useLocalModuleFinanceProducts';
import useLocalVariants from '../../../../../portal/StandardApplicationEntrypoint/useLocalVariants';
import getIsDealerOptionsVisible, { getIncludeDealerOptionsForFinancing } from '../../../../getIsDealerOptionsVisible';
import getIsDisplayFinanceCalculator from '../../../../getIsDisplayFinanceCalculator';
import getIsFlexibleDiscountEnabled from '../../../../getIsFlexibleDiscountEnabled';
import type { ApplicationFormValues } from '../../shared';
import CalculatorFinanceForm from './CalculatorFinanceForm';
import CalculatorFinanceInnerModal from './CalculatorFinanceInnerModal';
import {
    CompatibleApplication,
    CompatibleModule,
    GenericCalculatorModalProps,
    isApplicationTypeCompatible,
    isModuleTypeCompatible,
} from './helpers';

const getApplicationFinancingMarketType = (application: CompatibleApplication): ApplicationMarket => {
    switch (application.financing.__typename) {
        case 'DefaultApplicationFinancing':
            return ApplicationMarket.Default;

        case 'SingaporeApplicationFinancing':
            return ApplicationMarket.Singapore;

        case 'NewZealandApplicationFinancing':
            return ApplicationMarket.NewZealand;

        default:
            throw new Error('ApplicationMarket not supported');
    }
};

export const getApplicationMarketType = (
    application: CompatibleApplication,
    module: CompatibleModule
): ApplicationMarketTypeFragmentFragment => {
    if (!application.financing) {
        return module.marketType;
    }

    if (module.market === getApplicationFinancingMarketType(application)) {
        return module.marketType;
    }

    switch (application.financing.__typename) {
        case 'DefaultApplicationFinancing': {
            const defaultFinancing: DefaultApplicationMarket = {
                type: ApplicationMarket.Default,
                __typename: 'DefaultApplicationMarket',
            };

            return defaultFinancing;
        }

        case 'SingaporeApplicationFinancing': {
            const singaporeFinancing: SingaporeApplicationMarket = {
                coe: {
                    __typename: 'DealershipMarket',
                    defaultValue: application.financing.coe,
                    editable: false,
                    overrides: [],
                },
                type: ApplicationMarket.Singapore,
                __typename: 'SingaporeApplicationMarket',
            };

            return singaporeFinancing;
        }

        case 'NewZealandApplicationFinancing': {
            const newZealandFinancing: NewZealandApplicationMarket = {
                type: ApplicationMarket.NewZealand,
                ppsr: {
                    __typename: 'DealershipMarket',
                    defaultValue: application.financing.ppsr,
                    editable: false,
                    overrides: [],
                },

                estFee: {
                    __typename: 'DealershipMarket',
                    defaultValue: application.financing.estFee,
                    editable: false,
                    overrides: [],
                },
                estFeeEditable: false,
                ppsrEditable: false,
                __typename: 'NewZealandApplicationMarket',
                bankEstFee: {
                    __typename: 'BankDealershipMarket',
                    editable: false,
                    overrides: [],
                },
                nzFees: {
                    __typename: 'NzFeesDealershipMarket',
                    viewable: false,
                    overrides: [],
                },
            };

            return newZealandFinancing;
        }

        default:
            throw new Error('ApplicationFinancing not supported');
    }
};

export const useAvailableFinanceProducts = application => {
    if (!isApplicationTypeCompatible(application)) {
        return [];
    }

    const { module } = application;

    if (!isModuleTypeCompatible(module)) {
        return [];
    }

    const { financeProducts } = useLocalModuleFinanceProducts({
        module,
        dealerIds: [application.dealer.id],
        sort: { field: FinanceProductSortingField.Order, order: SortingOrder.Asc },
    });

    return financeProducts;
};

// TODO: Investigate why is this triggering a page refresh
const CalculatorFinanceModal = ({ application, open, revision, close }: GenericCalculatorModalProps) => {
    const { values } = useFormikContext<ApplicationFormValues>();
    if (!isApplicationTypeCompatible(application)) {
        throw new Error('Application type not supported for financing');
    }

    const { module } = application;

    if (!isModuleTypeCompatible(module)) {
        throw new Error('Application module type not supported for financing');
    }

    const { loading: loadingVariants, variants } = useLocalVariants({
        module,
        dealerIds: [application.dealer.id],
        skip: application.__typename === 'FinderApplication',
    });

    const { loading: loadingFinderVehicle, data: finderVehicleData } = useGetFinderVehicleQuery({
        variables: {
            id: application.vehicleId,
        },
        skip: !module.company.id || application.__typename !== 'FinderApplication',
    });

    const { loading: loadingFinanceProducts, financeProducts } = useLocalModuleFinanceProducts({
        module,
        dealerIds: [application.dealer.id],
        sort: { field: FinanceProductSortingField.Order, order: SortingOrder.Asc },
    });

    const marketSpecificCalculatorValues = useMarketSpecificCalculatorValues(module.marketType, {
        coe: application.dealer.coe || application.dealer.company.coe,
        ppsr: application.dealer.ppsr || application.dealer.company.ppsr,
        estFee: application.dealer.estFee || application.dealer.company.estFee,
        bankEstFee: application.bank?.estFee ?? 0,
    });

    const variantId = values.vehicle.id;

    const initialValues = useMemo((): Partial<GenericCalculatorValues> => {
        if (values.financing && values.bank) {
            return {
                ...getCalculatorValuesFromApplication(application),
                // Aside from application, need to use the initial values from the modal
                // To prevent loss of form data when modal is closed
                ...values.financing,
                isFinancingEnabled:
                    values.configuration.__typename === 'EventApplicationConfiguration'
                        ? false
                        : (values.configuration?.withFinancing ?? false),
            };
        }

        return {
            ...marketSpecificCalculatorValues,
            vehicle: variantId,
            isFinancingEnabled:
                values.configuration.__typename === 'EventApplicationConfiguration'
                    ? false
                    : (values.configuration?.withFinancing ?? false),
        };
    }, [values.financing, values.bank, values.configuration, marketSpecificCalculatorValues, variantId, application]);

    const [promoCode, setPromoCode] = useState<PromoCodeDataFragment>(application.promoCode);

    const initialValuesReferences = useRef({ revision, initialValues });

    const bankDisplayPreference = useMemo(() => module.bankDisplayPreference, [module]);

    const usedVehicles = useMemo(() => {
        if (application.__typename === 'FinderApplication') {
            return finderVehicleData?.vehicle?.__typename === 'FinderVehicle' ? [finderVehicleData.vehicle] : [];
        }

        return variants;
    }, [application.__typename, finderVehicleData, variants]);

    if (initialValuesReferences.current.revision !== revision) {
        initialValuesReferences.current = { revision, initialValues };
    }

    if (loadingVariants || loadingFinanceProducts || loadingFinderVehicle) {
        return null;
    }

    if (isEmpty(application.financing)) {
        return null;
    }

    return (
        <GenericCalculatorProvider
            key={revision}
            bankDisplayPreference={bankDisplayPreference}
            companyId={application.module.company.id}
            dealerId={application.dealer.id}
            financeProducts={financeProducts}
            hasFinancingCalculator={application.configuration.withFinancing || getIsDisplayFinanceCalculator(module)}
            includeDealerOptionsForFinancing={getIncludeDealerOptionsForFinancing(module)}
            initialValues={initialValuesReferences.current.initialValues}
            insuranceProducts={[]}
            isDealerOptionsVisible={getIsDealerOptionsVisible(module)}
            isFinancingOptional={false}
            isFlexibleDiscountEnabled={getIsFlexibleDiscountEnabled(module)}
            marketType={getApplicationMarketType(application, module)}
            promoCode={promoCode}
            promoCodeViewable={!!module.promoCodeModule}
            snapshot={application}
            vehicles={usedVehicles}
            allowOutdated
        >
            {calculatorContext => (
                <CalculatorFinanceInnerModal
                    calculatorContext={calculatorContext}
                    close={close}
                    open={open}
                    promoCode={promoCode}
                >
                    <CalculatorFinanceForm
                        application={application}
                        calculatorContext={calculatorContext}
                        dealerId={application.dealer.id}
                        isEditableField={hasPermissions(application.permissions, [
                            permissionKind.allowRecalculateEditable,
                        ])}
                        module={module}
                        promoCode={promoCode}
                        setPromoCode={setPromoCode}
                    />
                </CalculatorFinanceInnerModal>
            )}
        </GenericCalculatorProvider>
    );
};

export default CalculatorFinanceModal;
