import { useFormikContext } from 'formik';
import { ReactNode, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { CalculatorContext } from '../../../../../../calculator/computing';
import { VehicleFieldContext } from '../../../../../../calculator/computing/defaultFields/vehicleField';
import { GenericCalculatorValues } from '../../../../../../calculator/types';
import { useThemeComponents } from '../../../../../../themes/hooks';
import type { ApplicationFormValues } from '../../shared';

type CalculatorInsuranceInnerModalProps = {
    calculatorContext?: CalculatorContext<GenericCalculatorValues>;
    children: JSX.Element | ReactNode;
    close: () => void;
    open: boolean;
};

const CalculatorInsuranceInnerModal = ({
    calculatorContext,
    children,
    close,
    open,
}: CalculatorInsuranceInnerModalProps) => {
    const { t } = useTranslation('applicationDetails');
    const { Modal } = useThemeComponents();
    const { setFieldValue } = useFormikContext<ApplicationFormValues>();

    const saveCalculator = useCallback(async () => {
        const insurerId = calculatorContext.values?.insurerId;

        const { selectedVehicle } = calculatorContext.getFieldContext<VehicleFieldContext>('vehicle');

        // we only set values to formik when finance product is not null
        if (insurerId) {
            setFieldValue('insurancing', calculatorContext.values);
            setFieldValue('vehicle', selectedVehicle);

            close();
        }
        // if no insurer, should not update the formik
        // close the modal
        close();
    }, [calculatorContext, close, setFieldValue]);

    return (
        <Modal
            cancelText={t('applicationDetails:buttons.cancel')}
            okText={t('applicationDetails:buttons.save')}
            onCancel={close}
            onOk={saveCalculator}
            open={open}
            title={t('applicationDetails:buttons.save')}
            width={540}
        >
            {children}
        </Modal>
    );
};

export default CalculatorInsuranceInnerModal;
