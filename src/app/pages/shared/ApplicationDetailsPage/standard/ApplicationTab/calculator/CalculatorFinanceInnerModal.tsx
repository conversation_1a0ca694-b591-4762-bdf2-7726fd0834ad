import { useFormikContext } from 'formik';
import { ReactNode, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { PromoCodeDataFragment } from '../../../../../../api/fragments/PromoCodeData';
import { CalculatorContext } from '../../../../../../calculator/computing';
import { FinanceProductFieldContext } from '../../../../../../calculator/computing/defaultFields/financeProductField';
import { VehicleFieldContext } from '../../../../../../calculator/computing/defaultFields/vehicleField';
import { GenericCalculatorValues } from '../../../../../../calculator/types';
import { useThemeComponents } from '../../../../../../themes/hooks';
import type { ApplicationFormValues } from '../../shared';

type CalculatorFinanceInnerModalProps = {
    calculatorContext?: CalculatorContext<GenericCalculatorValues>;
    children: JSX.Element | ReactNode;
    close: () => void;
    open: boolean;
    promoCode: PromoCodeDataFragment;
};

const CalculatorFinanceInnerModal = ({
    calculatorContext,
    children,
    close,
    open,
    promoCode,
}: CalculatorFinanceInnerModalProps) => {
    const { Modal } = useThemeComponents();
    const { t } = useTranslation('applicationDetails');
    const { setFieldValue } = useFormikContext<ApplicationFormValues>();

    const saveCalculator = useCallback(async () => {
        const { selectedFinanceProduct } =
            calculatorContext.getFieldContext<FinanceProductFieldContext>('financeProduct');

        const { selectedVehicle } = calculatorContext.getFieldContext<VehicleFieldContext>('vehicle');

        // we only set values to formik when finance product is not null
        if (selectedFinanceProduct) {
            setFieldValue('financing', calculatorContext.values);
            setFieldValue('vehicle', selectedVehicle);
            setFieldValue('financeProduct', selectedFinanceProduct);
            setFieldValue('promoCode', promoCode);
        }
        // if no finance product, should not update the formik
        // close the modal
        close();
    }, [calculatorContext, close, setFieldValue, promoCode]);

    return (
        <Modal
            cancelText={t('applicationDetails:buttons.cancel')}
            okText={t('applicationDetails:buttons.save')}
            onCancel={close}
            onOk={saveCalculator}
            open={open}
            title={t('applicationDetails:buttons.save')}
            width={572}
        >
            {children}
        </Modal>
    );
};

export default CalculatorFinanceInnerModal;
