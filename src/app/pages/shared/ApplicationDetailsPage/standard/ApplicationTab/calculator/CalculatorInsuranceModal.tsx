/* eslint-disable max-len */
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { isEmpty } from 'lodash/fp';
import { useMemo, useRef } from 'react';
import { ApplicationInsurancingDataFragment } from '../../../../../../api/fragments/ApplicationInsurancingData';
import { useGetFinderVehicleQuery } from '../../../../../../api/queries/getFinderVehicle';
import { DisplayPreference, InsuranceProductSortingField, SortingOrder } from '../../../../../../api/types';
import { GenericCalculatorProvider } from '../../../../../../calculator/CalculatorProvider';
import { InsuranceGridCalculator } from '../../../../../../calculator/Implementations';
import { InsuranceGridCalculatorProps } from '../../../../../../calculator/Implementations/InsuranceGridCalculator';
import getInsuranceCalculatorValueFromApplication from '../../../../../../calculator/getInsuranceCalculatorValueFromApplication';
import { GenericCalculatorValues } from '../../../../../../calculator/types';
import Form from '../../../../../../components/fields/Form';
import useMarketSpecificCalculatorValues from '../../../../../../utilities/useMarketSpecificCalculatorValues';
import useMinDateOfBirth from '../../../../../../utilities/useMinDateOfBirth';
import useLocalVariants from '../../../../../portal/StandardApplicationEntrypoint/useLocalVariants';
import useLocalInsuranceProducts from '../../../../CIPage/useLocalInsuranceProduct';
import getIsDisplayFinanceCalculator from '../../../../getIsDisplayFinanceCalculator';
import type { ApplicationFormValues } from '../../shared';
import CalculatorInsuranceInnerModal from './CalculatorInsuranceInnerModal';
import { GenericCalculatorModalProps, isApplicationTypeCompatible, isModuleTypeCompatible } from './helpers';

const CalculatorInsuranceModal = ({ application, open, revision, close }: GenericCalculatorModalProps) => {
    const { values } = useFormikContext<ApplicationFormValues>();
    if (!isApplicationTypeCompatible(application)) {
        throw new Error('Application type not supported for insurance');
    }

    const { module } = application;
    if (!isModuleTypeCompatible(module)) {
        throw new Error('Application module type not supported for insurance');
    }

    const { loading: loadingVariants, variants } = useLocalVariants({
        module,
        dealerIds: [application.dealer.id],
        skip: application.__typename === 'FinderApplication',
    });

    const { loading: loadingFinderVehicle, data: finderVehicleData } = useGetFinderVehicleQuery({
        variables: {
            id: application.vehicleId,
        },
        skip: !module.company.id || application.__typename !== 'FinderApplication',
    });

    const marketSpecificCalculatorValues = useMarketSpecificCalculatorValues(module.marketType, {
        coe: application.dealer.coe || application.dealer.company.coe,
        ppsr: application.dealer.ppsr || application.dealer.company.ppsr,
        estFee: application.dealer.estFee || application.dealer.company.estFee,
        bankEstFee: application.bank?.estFee ?? 0,
    });

    const minDateOfBirth = useMinDateOfBirth();
    const usedDateOfBirth = useMemo(
        () =>
            values.customer.fields?.Birthday?.value ? dayjs(values.customer.fields?.Birthday?.value) : minDateOfBirth,
        [minDateOfBirth, values.customer.fields]
    );

    const { insuranceProducts } = useLocalInsuranceProducts({
        module,
        sort: { field: InsuranceProductSortingField.Order, order: SortingOrder.Asc },
        dealerIds: [application.dealer.id],
    });

    const initialValues = useMemo((): Partial<GenericCalculatorValues> => {
        if (values.insurancing) {
            const fromInsurancing = getInsuranceCalculatorValueFromApplication(
                values.insurancing as unknown as ApplicationInsurancingDataFragment
            );

            return {
                ...marketSpecificCalculatorValues,
                ...fromInsurancing,
                // we need update the carPrice which should follow from `Vehicle of Interest` Panel
                carPrice: values.insurancing.carPrice,
            };
        }

        return {
            ...marketSpecificCalculatorValues,
            vehicle: values.vehicle?.id,
            dateOfBirth: usedDateOfBirth,
            dateOfRegistration: dayjs(),
            noClaimDiscount: 50,
            yearsOfDriving: 8,
            isInsuranceEnabled: true,
        };
    }, [marketSpecificCalculatorValues, usedDateOfBirth, values.insurancing, values.vehicle?.id]);

    const initialValuesReferences = useRef({ revision, initialValues });

    const usedVehicles = useMemo(() => {
        if (application.__typename === 'FinderApplication') {
            return finderVehicleData?.vehicle?.__typename === 'FinderVehicle' ? [finderVehicleData.vehicle] : [];
        }

        return variants;
    }, [application.__typename, finderVehicleData, variants]);

    if (loadingVariants || loadingFinderVehicle) {
        return null;
    }

    // When insurance data empty, and rendering calculator only
    if (isEmpty(application.insurancing)) {
        return null;
    }

    return (
        <GenericCalculatorProvider
            key={revision}
            bankDisplayPreference={DisplayPreference.Hidden}
            companyId={application.module.company.id}
            dealerId={application.dealer.id}
            financeProducts={[]}
            hasFinancingCalculator={application.configuration.withFinancing || getIsDisplayFinanceCalculator(module)}
            initialValues={initialValuesReferences.current.initialValues}
            insuranceProducts={insuranceProducts}
            insurerDisplayPreference={module.insurerDisplayPreference}
            isDealerOptionsVisible={false}
            marketType={module.marketType}
            promoCodeViewable={false}
            vehicles={usedVehicles}
            includeDealerOptionsForFinancing
            isFinancingOptional
        >
            {calculatorContext => (
                <CalculatorInsuranceInnerModal calculatorContext={calculatorContext} close={close} open={open}>
                    <Form>
                        <InsuranceGridCalculator
                            applicationModule={module as unknown as InsuranceGridCalculatorProps['applicationModule']}
                            calculatorContext={calculatorContext}
                            dealerId={values.dealer.id}
                            hideCheckbox
                            hideHeader
                            initialInsuranceConfiguration
                        />
                    </Form>
                </CalculatorInsuranceInnerModal>
            )}
        </GenericCalculatorProvider>
    );
};

export default CalculatorInsuranceModal;
