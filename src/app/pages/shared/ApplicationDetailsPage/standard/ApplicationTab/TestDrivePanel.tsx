import { Col, ColProps } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments';
import { useThemeComponents } from '../../../../../themes/hooks';
import { getOffset } from '../../../../../utilities/date';
import { usePrepareChangedVehicleLocal } from '../../generic/TestDriveModal/TestDriveChangedVehicle';
import { mileageFormatter, useMileageSuffix } from '../../generic/TestDriveModal/shared';
import { TestDriveModalProps } from '../../generic/TestDriveModal/types';
import ApplicationDetailsPanel from '../Panel';
import SectionKey from '../SectionKey';
import defaultColSpan from '../colSpan';
import type { ApplicationFormValues } from '../shared';

type TestDrivePanelProps = {
    application: ApplicationDataFragment;
    forCI: boolean;
    timeZone?: string;
    ParentComponent?: React.ComponentType<any>;
    parentProps?: Record<string, any>;
    colSpan?: ColProps;
};

type ChangeVehicleSectionProps = {
    application: TestDriveModalProps['application'];
    changedVehicle: ApplicationFormValues['appointmentStage']['changedVehicle'];
    colSpan?: ColProps;
};

const ChangedVehicleLocal = ({ application, changedVehicle, colSpan = defaultColSpan }: ChangeVehicleSectionProps) => {
    const { t } = useTranslation('applicationDetails');
    const { FormFields } = useThemeComponents();

    const { values, initialValues } = useFormikContext<ApplicationFormValues>();

    const { loadingVariant, modelOptions, subModelOptions, vehicleOptions, innerState } = usePrepareChangedVehicleLocal(
        application,
        vehicleId => () => {},
        values?.appointmentStage?.changedVehicle?.__typename === 'AppointmentChangedVehicleLocal'
            ? values?.appointmentStage?.changedVehicle?.vehicleId
            : undefined,
        initialValues?.appointmentStage?.changedVehicle?.__typename === 'AppointmentChangedVehicleLocal'
            ? initialValues?.appointmentStage?.changedVehicle?.vehicleId
            : undefined
    );

    if (loadingVariant) {
        return null;
    }

    const modelName = modelOptions.find(item => item.value === innerState.model)?.label;
    const subModelName = subModelOptions.find(item => item.value === innerState.subModel)?.label;
    const variantName = vehicleOptions.find(
        item =>
            values?.appointmentStage?.changedVehicle?.__typename === 'AppointmentChangedVehicleLocal' &&
            item.value === values?.appointmentStage?.changedVehicle?.vehicleId
    )?.label;

    return (
        <>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.testDrive.changedVehicle.model', { returnObjects: true })}
                    value={modelName}
                />
            </Col>
            {subModelOptions.length > 0 && (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.testDrive.changedVehicle.subModel', {
                            returnObjects: true,
                        })}
                        value={subModelName}
                    />
                </Col>
            )}
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t('applicationDetails:fields.testDrive.changedVehicle.variant', {
                        returnObjects: true,
                    })}
                    value={variantName}
                />
            </Col>
        </>
    );
};

const ChangedVehicleSection = ({
    application,
    changedVehicle,
    colSpan = defaultColSpan,
}: ChangeVehicleSectionProps) => {
    const { t } = useTranslation('applicationDetails');
    const { FormFields } = useThemeComponents();
    const { values } = useFormikContext<ApplicationFormValues>();

    switch (changedVehicle.__typename) {
        case 'AppointmentChangedVehicleText': {
            return (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t(`applicationDetails:fields.testDrive.changedVehicle.model`, {
                                returnObjects: true,
                            })}
                            value={
                                values.appointmentStage.changedVehicle.__typename === 'AppointmentChangedVehicleText' &&
                                values.appointmentStage.changedVehicle.model
                            }
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t(`applicationDetails:fields.testDrive.changedVehicle.subModel`, {
                                returnObjects: true,
                            })}
                            value={
                                values.appointmentStage.changedVehicle.__typename === 'AppointmentChangedVehicleText' &&
                                values.appointmentStage.changedVehicle.subModel
                            }
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t(`applicationDetails:fields.testDrive.changedVehicle.variant`, {
                                returnObjects: true,
                            })}
                            value={
                                values.appointmentStage.changedVehicle.__typename === 'AppointmentChangedVehicleText' &&
                                values.appointmentStage.changedVehicle.variant
                            }
                        />
                    </Col>
                </>
            );
        }

        case 'AppointmentChangedVehicleLocal': {
            return <ChangedVehicleLocal application={application} changedVehicle={changedVehicle} />;
        }

        default: {
            return null;
        }
    }
};

const TestDrivePanel = ({
    application,
    ParentComponent = ApplicationDetailsPanel,
    parentProps = {},
    forCI,
    colSpan = defaultColSpan,
}: TestDrivePanelProps) => {
    const { t } = useTranslation('applicationDetails');

    const { FormFields } = useThemeComponents();
    const { values } = useFormikContext<ApplicationFormValues>();
    const mileageSuffix = useMileageSuffix(values.vehicle);

    const timeZone = useMemo(() => application.module.company.timeZone, [application.module.company.timeZone]);

    const { checkIn, checkOut } = useMemo(
        () => ({
            checkOut: values?.appointmentStage?.checkOutTime
                ? dayjs(values.appointmentStage.checkOutTime).tz(timeZone).format('HH:mm:ss')
                : undefined,
            checkIn: values?.appointmentStage?.checkInTime
                ? dayjs(values.appointmentStage.checkInTime).tz(timeZone).format('HH:mm:ss')
                : undefined,
        }),
        [timeZone, values.appointmentStage?.checkInTime, values.appointmentStage?.checkOutTime]
    );

    const combinedParentProps = useMemo(
        () => ({
            forCI,
            header: t('applicationDetails:panels.application.testDrive.header'),
            name: SectionKey.TestDrive,
            ...parentProps,
        }),
        [forCI, t, parentProps]
    );

    return (
        <ParentComponent {...combinedParentProps}>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.testDrive.checkOutTime`, {
                        returnObjects: true,
                        offset: getOffset(t, timeZone),
                    })}
                    value={checkOut}
                    disabled
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.testDrive.checkInTime`, {
                        returnObjects: true,
                        offset: getOffset(t, timeZone),
                    })}
                    value={checkIn}
                    disabled
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputNumberField
                    {...t(`applicationDetails:fields.testDrive.mileageStart`, { returnObjects: true })}
                    addonAfter={mileageSuffix}
                    formatter={mileageFormatter}
                    name="appointmentStage.mileage.start"
                    disabled
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputNumberField
                    {...t(`applicationDetails:fields.testDrive.mileageEnd`, { returnObjects: true })}
                    addonAfter={mileageSuffix}
                    formatter={mileageFormatter}
                    name="appointmentStage.mileage.end"
                    disabled
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t(`applicationDetails:fields.testDrive.registrationNumber`, { returnObjects: true })}
                    name="appointmentStage.registrationNumber"
                    disabled
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.CheckboxField
                    {...t(`applicationDetails:fields.testDrive.hasChangedVehicle`, { returnObjects: true })}
                    name="appointmentStage.hasChangedVehicle"
                    disabled
                />
            </Col>
            {values.appointmentStage?.hasChangedVehicle &&
                !!values.appointmentStage?.changedVehicle &&
                (application.__typename === 'ConfiguratorApplication' ||
                    application.__typename === 'EventApplication' ||
                    application.__typename === 'FinderApplication' ||
                    application.__typename === 'StandardApplication') && (
                    <ChangedVehicleSection
                        application={application}
                        changedVehicle={values.appointmentStage?.changedVehicle}
                    />
                )}
        </ParentComponent>
    );
};

export default TestDrivePanel;
