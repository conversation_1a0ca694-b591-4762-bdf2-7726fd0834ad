import { Space } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import * as permissionKind from '../../../../../../shared/permissions';
import { KycFieldSpecsFragment } from '../../../../../api/fragments';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { ProductionFinanceProductDetailsFragment } from '../../../../../api/fragments/ProductionFinanceProductDetails';
import { ApplicationStage, CustomerKind } from '../../../../../api/types';
import { useApplicationStageWithCap } from '../../../../../components/cap/searchCustomersAndLeads/utils';
import { useAccount } from '../../../../../components/contexts/AccountContextManager';
import { useApplicationDetailsExtraContext } from '../../../../../components/contexts/ApplicationDetailsExtraContext';
import hasPermissions from '../../../../../utilities/hasPermissions';
import isKYCPresetHasCurrentVehicleFields from '../../../../../utilities/kycPresets/isKYCPresetHasCurrentVehicleFields';
import ConsentsPanel from '../../../MobilityBookingDetailsPage/ApplicationTab/ConsentsPanel';
import type { ApplicationFormValues } from '../shared';
import AdditionalInformationPanel from './AdditionalInformationPanel';
import ApplicantPanel from './ApplicantPanel';
import FinancingPanel from './FinancingPanel';
import InsurancePanel from './InsurancePanel';
import MainPanel from './MainPanel';
import PaymentPanel from './PaymentPanel';
import QuotationPanel from './QuotationPanel';
import RelatedApplicationPanel from './RelatedApplicationPanel';
import TestDrivePanel from './TestDrivePanel';
import TradeInVehiclePanel from './TradeInVehiclePanel';
import VehiclePanel from './VehiclePanel';
import { useAvailableFinanceProducts } from './calculator/CalculatorFinanceModal';

export type ApplicationTabProps = {
    application: ApplicationDataFragment;
    stage: ApplicationStage;
    isMask: boolean;
};

export const hasFinancingModule = (
    application: ApplicationTabProps['application'],
    availableFinanceProducts: ProductionFinanceProductDetailsFragment[] = []
) => {
    if (application.__typename === 'MobilityApplication') {
        return false;
    }

    switch (application?.module.__typename) {
        case 'StandardApplicationModule':
        case 'ConfiguratorModule':
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
            return !!application?.module.bankModuleId && availableFinanceProducts.length > 0;

        default:
            return false;
    }
};

export const hasFinancingValue = (application: ApplicationTabProps['application']) => {
    if (application.__typename === 'MobilityApplication') {
        return false;
    }

    switch (application?.module.__typename) {
        case 'StandardApplicationModule':
        case 'ConfiguratorModule':
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
            return application?.financing && application.financeProduct;

        default:
            return false;
    }
};

export const hasTradeInModule = (application: ApplicationTabProps['application']) => {
    if (application.__typename === 'MobilityApplication') {
        return false;
    }

    switch (application?.configuration.__typename) {
        case 'ApplicationConfiguration':
        case 'ConfiguratorApplicationConfiguration':
        case 'EventApplicationConfiguration':
        case 'FinderApplicationConfiguration':
            return application.configuration.tradeIn;

        default:
            return false;
    }
};

export const hasTradeInKYC = (applicantKYC: KycFieldSpecsFragment[]) =>
    isKYCPresetHasCurrentVehicleFields(applicantKYC);

export const hasTradeInPanel = (
    application: ApplicationTabProps['application'],
    stage: ApplicationStage,
    applicantKYC: KycFieldSpecsFragment[]
) => {
    const hasCurrentVehicleFields = hasTradeInKYC(applicantKYC);

    return (
        (hasCurrentVehicleFields || hasTradeInModule(application)) &&
        [
            ApplicationStage.Financing,
            ApplicationStage.Reservation,
            ApplicationStage.Lead,
            ApplicationStage.Appointment,
            ApplicationStage.Insurance,
        ].includes(stage)
    );
};

export const hasRequestForFinancing = (application: ApplicationDataFragment) => {
    if (application?.__typename !== 'FinderApplication' && application?.__typename !== 'ConfiguratorApplication') {
        return false;
    }

    switch (application?.module.__typename) {
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
        case 'ConfiguratorModule':
            return (
                application?.configuration.requestForFinancing &&
                !application?.configuration.withFinancing &&
                !!application?.module.bankModuleId &&
                application?.financing &&
                application.financeProduct
            );

        default:
            return false;
    }
};

const hasFinancingPanel = (
    application: ApplicationDataFragment,
    stage: ApplicationStage,
    availableFinanceProducts: ProductionFinanceProductDetailsFragment[] = []
) =>
    ((hasFinancingModule(application, availableFinanceProducts) && hasFinancingValue(application)) ||
        hasRequestForFinancing(application)) &&
    stage === ApplicationStage.Financing;

const hasQuotationPanel = (
    application: ApplicationTabProps['application'],
    stage: ApplicationStage,
    availableFinanceProducts: ProductionFinanceProductDetailsFragment[] = []
) =>
    hasFinancingPanel(application, stage, availableFinanceProducts) &&
    (application.__typename === 'StandardApplication' ||
        application.__typename === 'ConfiguratorApplication' ||
        application.__typename === 'FinderApplication') &&
    application.bank?.integration?.__typename === 'EnbdBankIntegration';

export const hasAppointmentModule = (application: ApplicationTabProps['application']) => {
    switch (application?.module.__typename) {
        case 'StandardApplicationModule':
        case 'ConfiguratorModule':
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
        case 'EventApplicationModule':
            return !!application?.module.appointmentModule;

        default:
            return false;
    }
};

export const hasInsuranceModule = (application: ApplicationTabProps['application']) => {
    if (application.__typename === 'MobilityApplication' || application.__typename === 'EventApplication') {
        return false;
    }

    switch (application?.module.__typename) {
        case 'StandardApplicationModule':
        case 'ConfiguratorModule':
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
            return !!application?.module.insuranceModuleId && !!application?.module.insurers?.length;

        default:
            return false;
    }
};

export const hasInsuranceValue = (application: ApplicationTabProps['application']) => {
    if (
        application.__typename === 'MobilityApplication' ||
        application.__typename === 'EventApplication' ||
        application.__typename === 'LaunchpadApplication'
    ) {
        return false;
    }

    switch (application?.configuration.__typename) {
        case 'ApplicationConfiguration':
        case 'ConfiguratorApplicationConfiguration':
        case 'FinderApplicationConfiguration':
            return !isNil(application?.insurancing);

        default:
            return false;
    }
};

export const hasTestDrivePanel = (application: ApplicationTabProps['application'], stage: ApplicationStage) => {
    if (application.__typename === 'MobilityApplication') {
        return false;
    }

    return stage === ApplicationStage.Appointment && !isNil(application.draftFlow.isTestDriveProcessStarted);
};

export const hasInsurancePanel = (application: ApplicationTabProps['application'], stage: ApplicationStage) =>
    hasInsuranceModule(application) && hasInsuranceValue(application) && stage === ApplicationStage.Insurance;

const ApplicationTab = ({ application, stage, isMask }: ApplicationTabProps) => {
    const { forCI } = useApplicationDetailsExtraContext();
    const { values } = useFormikContext<ApplicationFormValues>();

    const applicantKind = useMemo(
        () => (application.applicant.__typename === 'CorporateCustomer' ? CustomerKind.Corporate : CustomerKind.Local),
        [application]
    );

    const { applicantKYC } = values;

    const availableFinanceProducts = useAvailableFinanceProducts(application);

    const { permissions: accountPermissions } = useAccount();
    const hasViewCustomerPermission = hasPermissions(accountPermissions, [permissionKind.viewCustomers]);

    const hasGuarantor = useMemo(
        () => (application.__typename !== 'LaunchpadApplication' ? !isNil(application.guarantor) : false),
        [application]
    );

    const applicationStageWithCap = useApplicationStageWithCap(stage);
    const capValues = useMemo(() => {
        if (applicationStageWithCap) {
            switch (application.__typename) {
                case 'ConfiguratorApplication':
                case 'FinderApplication':
                case 'EventApplication':
                case 'StandardApplication': {
                    return application.lead.capValues;
                }

                default:
                    return null;
            }
        }

        return null;
    }, [application, applicationStageWithCap]);

    const hasVehiclePanel = useMemo(() => {
        if (application.__typename !== 'EventApplication') {
            return true;
        }

        return !isNil(application.vehicle);
    }, [application]);

    return (
        <Space direction="vertical" size={forCI ? 48 : 16} style={{ display: 'flex' }}>
            <MainPanel application={application} stage={stage} />
            <RelatedApplicationPanel applications={[application]} forCI={forCI} stage={stage} />
            {hasViewCustomerPermission && (
                <ApplicantPanel<ApplicationFormValues>
                    application={application}
                    capValues={capValues}
                    customer={application.applicant}
                    customerKind={applicantKind}
                    forCI={forCI}
                    isMask={isMask}
                    stage={stage}
                    showCustomerLink
                />
            )}
            <ConsentsPanel application={application} stage={stage} />

            {hasViewCustomerPermission && hasGuarantor && (
                <ApplicantPanel<ApplicationFormValues>
                    customerKind={CustomerKind.Guarantor}
                    forCI={forCI}
                    isMask={isMask}
                    showCustomerLink={false}
                />
            )}
            {hasGuarantor && <ConsentsPanel application={application} stage={stage} type="guarantor" />}

            {hasTradeInPanel(application, stage, applicantKYC) && <TradeInVehiclePanel />}
            {application.__typename === 'EventApplication' && (
                <AdditionalInformationPanel customizedFields={application.customizedFields} forCi={forCI} />
            )}
            {hasQuotationPanel(application, stage, availableFinanceProducts) && (
                <QuotationPanel application={application} />
            )}
            {hasVehiclePanel && <VehiclePanel application={application} forCI={forCI} />}
            {application.__typename !== 'MobilityApplication' &&
                application.__typename !== 'LaunchpadApplication' &&
                application?.deposit &&
                [ApplicationStage.Financing, ApplicationStage.Reservation, ApplicationStage.Mobility].includes(
                    stage
                ) && <PaymentPanel application={application} skipped={application?.deposit?.skipped} />}
            {hasFinancingPanel(application, stage, availableFinanceProducts) &&
                application.__typename !== 'LaunchpadApplication' && <FinancingPanel application={application} />}
            {hasInsurancePanel(application, stage) && <InsurancePanel application={application} />}
            {hasTestDrivePanel(application, stage) && <TestDrivePanel application={application} forCI={forCI} />}
        </Space>
    );
};

export default ApplicationTab;
