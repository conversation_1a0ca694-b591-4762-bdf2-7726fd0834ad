import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { ApplicationMarket } from '../../../../../api/types';
import { useThemeComponents } from '../../../../../themes/hooks';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import colSpan from '../colSpan';
import type { ApplicationFormValues } from '../shared';

type MarketFieldDisplayProps = {
    application: Extract<
        ApplicationDataFragment,
        {
            __typename:
                | 'StandardApplication'
                | 'ConfiguratorApplication'
                | 'EventApplication'
                | 'FinderApplication'
                | 'LaunchpadApplication'
                | 'SalesOfferApplication';
        }
    >;
};

const MarketFieldDisplay = ({ application }: MarketFieldDisplayProps) => {
    const { t } = useTranslation('applicationDetails');

    const translate = useTranslatedString();

    const { formatAmountWithCurrency } = useCompanyFormats(application.module.company.id);

    const { values } = useFormikContext<ApplicationFormValues>();
    const { FormFields } = useThemeComponents();

    switch (values.financing.market) {
        case ApplicationMarket.Singapore:
            return (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('applicationDetails:fields.financing.coe', { returnObjects: true })}
                        value={formatAmountWithCurrency(values.financing.coe)}
                    />
                </Col>
            );

        case ApplicationMarket.NewZealand: {
            return (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.totalAmountPayable', {
                                returnObjects: true,
                            })}
                            value={formatAmountWithCurrency(values.financing.totalAmountPayable)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.ppsr', { returnObjects: true })}
                            value={formatAmountWithCurrency(values.financing.ppsr)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.estFee', { returnObjects: true })}
                            value={formatAmountWithCurrency(values.financing.estFee)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.financing.bankEstFee', {
                                returnObjects: true,
                                bankLegalName: translate(values.financeProduct.bank.legalName),
                            })}
                            value={formatAmountWithCurrency(values.financing.bankEstFee)}
                        />
                    </Col>
                </>
            );
        }

        default:
            return null;
    }
};

export default MarketFieldDisplay;
