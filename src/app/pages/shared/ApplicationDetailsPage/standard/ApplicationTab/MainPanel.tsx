/* eslint-disable max-len */
import { Col, message } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { useUpdateAssigneeOnApplicationMutation } from '../../../../../api/mutations/updateAssigneeOnApplication';
import { ApplicationStage } from '../../../../../api/types';
import { useApplicationStageWithCap } from '../../../../../components/cap/searchCustomersAndLeads/utils';
import { useApplicationDetailsExtraContext } from '../../../../../components/contexts/ApplicationDetailsExtraContext';
import { useLanguage } from '../../../../../components/contexts/LanguageContextManager';
import { useThemeComponents } from '../../../../../themes/hooks';
import { getApplicationStatus, getPathByStage } from '../../../../../utilities/application';
import useFormattedSimpleVersioning from '../../../../../utilities/useFormattedSimpleVersioning';
import ApplicationDetailsPanel from '../Panel';
import SectionKey from '../SectionKey';
import colSpan from '../colSpan';
import type { ApplicationFormValues } from '../shared';
import { AppointmentDateTime } from '../useUpdates';
import AppointmentFields from './AppointmentFields';
import useAssigneeOptions from './useAssigneeOptions';

export type MainPanelProps = {
    application?: ApplicationDataFragment;
    stage: ApplicationStage;
};

const MainPanel = ({ application, stage }: MainPanelProps) => {
    const { currentLanguageId } = useLanguage();
    const { t } = useTranslation(['applicationDetails', 'applicationList', 'common']);

    const assigneeOptions = useAssigneeOptions(application.availableAssignees);
    const { values } = useFormikContext<ApplicationFormValues & AppointmentDateTime>();
    const { FormFields } = useThemeComponents();
    const { forCI } = useApplicationDetailsExtraContext();

    const { updated, createdBy, offset, createdDate } = useFormattedSimpleVersioning({
        versioning: values.versioning,
        timeZone: application.module.company.timeZone,
    });

    const [updateAssigneeMutation] = useUpdateAssigneeOnApplicationMutation();

    const labels = useMemo(() => {
        switch (stage) {
            case ApplicationStage.Financing:
                return { identifier: 'applicationIdentifier', status: 'applicationStatus' };

            case ApplicationStage.Lead:
                return { identifier: 'leadIdentifier', status: 'leadStatus' };

            case ApplicationStage.Reservation:
                return { identifier: 'reservationIdentifier', status: 'reservationStatus' };

            case ApplicationStage.Appointment:
                return { identifier: 'appointmentIdentifier', status: 'appointmentStatus' };

            case ApplicationStage.Insurance:
                return { identifier: 'insuranceIdentifier', status: 'insuranceStatus' };

            case ApplicationStage.VisitAppointment:
                return { identifier: 'visitAppointmentIdentifier', status: 'visitAppointmentStatus' };

            default:
                throw new Error('Invalid application stage');
        }
    }, [stage]);

    const status = useMemo(() => getApplicationStatus(values, stage), [values, stage]);
    const applicationStageWithCap = useApplicationStageWithCap(stage);

    const stagePath = useMemo(() => getPathByStage(stage), [stage]);

    const onAssigneeChange = useCallback(
        async assigneeId => {
            try {
                message.loading({
                    content: t('applicationDetails:messages.updatingAssignee'),
                    key: 'primary',
                    duration: 0,
                });

                await updateAssigneeMutation({
                    variables: { applicationId: values.id, assigneeId, stage, languageId: currentLanguageId },
                });

                message.success({
                    content: t('applicationDetails:messages.updatedAssignee'),
                    key: 'primary',
                });
            } catch (error) {
                message.destroy('primary');
                message.error(t('applicationDetails:messages.updatingAssigneeFailed'));
            }
        },
        [currentLanguageId, stage, t, updateAssigneeMutation, values.id]
    );

    return (
        <ApplicationDetailsPanel
            forCI={forCI}
            header={t('applicationDetails:panels.application.main.header')}
            name={SectionKey.MainDetails}
            defaultExpanded
        >
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.${labels.identifier}`, { returnObjects: true })}
                    value={`${values[stagePath].identifier}`}
                />
            </Col>
            {applicationStageWithCap && values.lead.capValues && (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t(`applicationDetails:fields.capLeadId`, { returnObjects: true })}
                        value={values.lead.capValues.leadId}
                    />
                </Col>
            )}
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.${labels.status}`, { returnObjects: true })}
                    value={t(`applicationList:status.${status}`)}
                />
            </Col>
            {[ApplicationStage.Appointment].includes(stage) && (
                <AppointmentFields application={application} forCI={forCI} status={status} isTestDrive />
            )}
            {[ApplicationStage.VisitAppointment].includes(stage) && (
                <AppointmentFields application={application} forCI={forCI} status={status} />
            )}
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.createdAt`, { returnObjects: true })}
                    value={createdDate}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.dealer`, { returnObjects: true })}
                    value={values.dealer.displayName}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.createdBy`, { returnObjects: true })}
                    value={createdBy}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.SelectField
                    {...t(`applicationDetails:fields.assignee`, { returnObjects: true })}
                    name={`${stagePath}.assigneeId`}
                    onChange={onAssigneeChange}
                    options={assigneeOptions}
                    showSearch={!forCI}
                    style={{ padding: '0 0 5px' }}
                    required
                />
            </Col>

            <Col {...colSpan}>
                <FormFields.DisplayField
                    {...t(`applicationDetails:fields.module`, { returnObjects: true })}
                    value={values.module.displayName}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.DisplayFieldWithUTCOffset
                    {...t('applicationDetails:fields.updatedAt', { returnObjects: true, offset })}
                    value={updated}
                />
            </Col>
        </ApplicationDetailsPanel>
    );
};

export default MainPanel;
