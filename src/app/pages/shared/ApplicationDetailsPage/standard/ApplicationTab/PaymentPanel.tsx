import { Col } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments';
import { useApplicationDetailsExtraContext } from '../../../../../components/contexts/ApplicationDetailsExtraContext';
import FormFields from '../../../../../themes/admin/Fields/FormFields';
import useFormats, { withCommasForNumberString } from '../../../../../utilities/useFormats';
import usePromoCodeInfo from '../../../../../utilities/usePromoCodeInfo';
import type { MobilityBookingFormValues } from '../../../MobilityBookingDetailsPage/shared';
import { AFCApplicationFormValues } from '../ApplicationFormContext';
import ApplicationDetailsPanel from '../Panel';
import SectionKey from '../SectionKey';
import colSpan from '../colSpan';
import { SubTitle } from '../sharedUI';

export type PaymentPanelProps = {
    skipped: boolean;
    defaultExpanded?: boolean;
    application: ApplicationDataFragment;
};

const PaymentPanel = ({ skipped, application, defaultExpanded = false }: PaymentPanelProps) => {
    const { t } = useTranslation('applicationDetails');

    const { values } = useFormikContext<MobilityBookingFormValues | AFCApplicationFormValues>();
    const { forCI } = useApplicationDetailsExtraContext();

    const { formatAmount, formatAmountWithCurrency } = useFormats(
        application.module.company.currency ?? '',
        application.module.company.roundings.amount.decimals ?? 2,
        application.module.company.roundings.percentage.decimals ?? 0
    );

    const promoCodeInfo = usePromoCodeInfo(
        values.promoCode,
        application.module.company.id,
        values.promoCode?.promoType.__typename === 'GiftPromoType' ? null : values.promoCode?.promoType.amount
    );

    const giftVoucherAmount =
        application.__typename === 'MobilityApplication' && application.giftVoucher
            ? `${application.giftVoucher.giftCode} \n ${formatAmountWithCurrency(
                  application.giftVoucher.value * application.giftVoucher.numberOfBookingReferenceDays
              )}`
            : null;

    const formatter = useCallback(
        (value: number, { userTyping }) => {
            if (!value) {
                return value?.toString();
            }

            return userTyping ? withCommasForNumberString(value?.toString()) : formatAmount(value);
        },
        [formatAmount]
    );

    return (
        <ApplicationDetailsPanel
            defaultExpanded={defaultExpanded}
            forCI={forCI}
            header={t('applicationDetails:panels.application.payment.header')}
            name={SectionKey.PaymentDetails}
        >
            {!!skipped && (
                <Col span={24}>
                    <SubTitle>{t('applicationDetails:panels.application.payment.subHeaders.paymentSkipped')}</SubTitle>
                </Col>
            )}
            {!skipped && (
                <>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.payment.amount', { returnObjects: true })}
                            value={formatAmountWithCurrency(values.deposit?.amount)}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t(`applicationDetails:fields.payment.completedAt`, { returnObjects: true })}
                            value={dayjs(values.deposit?.completedAt).format('DD MMM YYYY')}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.payment.transactionId', { returnObjects: true })}
                            value={values.deposit?.transactionId}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.payment.status', { returnObjects: true })}
                            value={values.deposit?.status}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.DisplayField
                            {...t('applicationDetails:fields.payment.paymentMethod', { returnObjects: true })}
                            value={values.deposit?.paymentMethod}
                        />
                    </Col>

                    {application.__typename === 'MobilityApplication' && (
                        <>
                            <Col {...colSpan}>
                                <FormFields.DisplayField
                                    {...t('applicationDetails:fields.financing.promoCode', { returnObjects: true })}
                                    value={promoCodeInfo}
                                />
                            </Col>
                            {giftVoucherAmount && (
                                <Col {...colSpan}>
                                    <FormFields.DisplayField
                                        {...t('applicationDetails:fields.financing.giftVoucher', {
                                            returnObjects: true,
                                        })}
                                        value={giftVoucherAmount}
                                        disabled
                                    />
                                </Col>
                            )}
                        </>
                    )}
                </>
            )}
        </ApplicationDetailsPanel>
    );
};

export default PaymentPanel;
