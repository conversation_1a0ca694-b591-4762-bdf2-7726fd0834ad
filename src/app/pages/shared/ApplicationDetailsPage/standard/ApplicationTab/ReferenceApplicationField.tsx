import { Typography } from 'antd';
import { useFormikContext } from 'formik';
import { createContext, useMemo, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import * as permissionKind from '../../../../../../shared/permissions';
import { ReferenceApplicationDataFragment } from '../../../../../api';
import { ApplicationStage } from '../../../../../api/types';
import { DisplayFieldProps } from '../../../../../components/fields/DisplayField';
import { useThemeComponents } from '../../../../../themes/hooks';
import { TranslationFieldType } from '../../../../../utilities/common';
import hasPermissions from '../../../../../utilities/hasPermissions';
import type { ApplicationFormValues } from '../shared';

const ReferenceApplicationListContainer = styled.div`
    padding-top: 0;
    display: flex;
    white-space: nowrap;
    flex-wrap: wrap;

    & > *:not(:last-child):after {
        content: ', ';
        white-space: pre;
    }
`;

export type GetEventDetailPathFunction = ({ id, urlSlug }: { id: string; urlSlug: string }) => string | undefined;
export type GetFinderDetailPathFunction = ({ id }: { id: string }) => string | undefined;

export const eventAdminPath: GetEventDetailPathFunction = ({ id }) => `/admin/events/${id}`;
export const eventPortalPath =
    (prefix?: string): GetEventDetailPathFunction =>
    ({ urlSlug }) =>
        prefix ? `${prefix}/details/${urlSlug}` : undefined;

export const finderAdminPath: GetFinderDetailPathFunction = ({ id }) => `/admin/finderVehicles/${id}`;

export type ReferencePathPrefixes = {
    references?: {
        getEventDetail?: GetEventDetailPathFunction;
        getFinderDetail?: GetFinderDetailPathFunction;
    };
    stages?: {
        [key in ApplicationStage]?: string;
    } & {
        Contact?: string;
    };
};

type MinimumReferenceApplication = Pick<
    ApplicationFormValues,
    'id' | 'insuranceStage' | 'financingStage' | 'appointmentStage' | 'reservationStage' | 'stages'
> &
    Pick<ReferenceApplicationDataFragment, 'lead'> & {
        versioning: { suiteId: string };
        permissions: string[];
    };

type ReferenceApplicationListProps = {
    stage?: ApplicationStage;
    application: MinimumReferenceApplication & {
        referenceApplications?: MinimumReferenceApplication[];
    };
    style?: React.CSSProperties;
};

type ReferenceItem = { path: string; identifier: string; isClickable: boolean };

type ReferenceLink = {
    [key in ApplicationStage]?: ReferenceItem;
} & {
    Contact?: ReferenceItem;
};

export const ReferenceApplicationContext = createContext<ReferencePathPrefixes>(null);

export const useReferenceApplicationContext = () => {
    const context = useContext(ReferenceApplicationContext);

    if (!context) {
        throw new Error('ReferenceApplicationContext not found');
    }

    return context;
};

export const ReferenceApplicationProvider = ({
    referencePaths,
    children,
}: {
    referencePaths: ReferencePathPrefixes;
    children: React.ReactNode;
}) => {
    const value = useMemo(() => referencePaths, [referencePaths]);

    return <ReferenceApplicationContext.Provider value={value}>{children}</ReferenceApplicationContext.Provider>;
};

class ReferenceLinkGenerator {
    _links: ReferenceLink = {};

    _currentStage?: ApplicationStage;

    _paths: ReferencePathPrefixes;

    constructor(paths: ReferencePathPrefixes, currentStage?: ApplicationStage) {
        this._links = {};
        this._currentStage = currentStage;
        this._paths = paths;
    }

    getPath(checkStage: ApplicationStage, id: string) {
        return this._paths?.stages[checkStage] ? `${this._paths.stages[checkStage]}/${id}` : null;
    }

    getLeadPath<T extends ReferenceApplicationListProps['application']>(checkedApplication: T) {
        const { lead } = checkedApplication;

        if (lead.isLead) {
            return this._paths?.stages.Lead ? `${this._paths.stages.Lead}/${lead.versioning.suiteId}` : null;
        }

        return this._paths?.stages.Contact ? `${this._paths.stages.Contact}/${lead.versioning.suiteId}` : null;
    }

    canAddLink<T extends ReferenceApplicationListProps['application']>(
        checkedApplication: T,
        path: keyof Pick<T, 'financingStage' | 'insuranceStage' | 'appointmentStage' | 'reservationStage'>,
        checkStage: ApplicationStage
    ) {
        return (
            checkedApplication[path] &&
            this._currentStage !== checkStage &&
            !this._links[checkStage] &&
            checkedApplication.stages?.includes?.(checkStage)
        );
    }

    addStage<T extends ReferenceApplicationListProps['application']>(
        checkedApplication: T,
        path: keyof Pick<T, 'financingStage' | 'insuranceStage' | 'appointmentStage' | 'reservationStage'>,
        checkStage: ApplicationStage
    ) {
        if (this.canAddLink(checkedApplication, path, checkStage)) {
            this._links[checkStage] = {
                path: this.getPath(checkStage, checkedApplication.versioning.suiteId),
                identifier: checkedApplication[path].identifier,
                isClickable: hasPermissions(checkedApplication.permissions, [permissionKind.viewApplications]),
            };
        }
    }

    addLeadStage<T extends ReferenceApplicationListProps['application']>(
        checkedApplication: T,
        checkStage: ApplicationStage | 'Contact'
    ) {
        this._links[checkStage] = {
            path: this.getLeadPath(checkedApplication),
            identifier: checkedApplication.lead.identifier,
            isClickable: hasPermissions(checkedApplication.lead.permissions, [
                checkedApplication.lead.isLead ? permissionKind.viewLeads : permissionKind.viewContact,
            ]),
        };
    }

    getLinks() {
        return this._links;
    }
}

export const ReferenceApplicationList = ({ stage, application, style }: ReferenceApplicationListProps) => {
    const paths = useReferenceApplicationContext();

    const references = useMemo(() => {
        const linkGenerator = new ReferenceLinkGenerator(paths, stage);

        // Look from stages information first
        linkGenerator.addLeadStage(application, application.lead.isLead ? ApplicationStage.Lead : 'Contact');
        linkGenerator.addStage(application, 'financingStage', ApplicationStage.Financing);
        linkGenerator.addStage(application, 'insuranceStage', ApplicationStage.Insurance);
        linkGenerator.addStage(application, 'appointmentStage', ApplicationStage.Appointment);
        linkGenerator.addStage(application, 'reservationStage', ApplicationStage.Reservation);

        // Reference applications is the one with apply for financing / insurance
        if (application.referenceApplications.length > 0) {
            application.referenceApplications.forEach(referenceApplication => {
                linkGenerator.addStage(referenceApplication, 'financingStage', ApplicationStage.Financing);
                linkGenerator.addStage(referenceApplication, 'insuranceStage', ApplicationStage.Insurance);
                linkGenerator.addStage(referenceApplication, 'appointmentStage', ApplicationStage.Appointment);
                linkGenerator.addStage(referenceApplication, 'reservationStage', ApplicationStage.Reservation);
            });
        }

        const links = linkGenerator.getLinks();

        // Order it based on translation file
        return [
            links[ApplicationStage.Lead],
            links.Contact,
            links[ApplicationStage.Reservation],
            links[ApplicationStage.Financing],
            links[ApplicationStage.Insurance],
            links[ApplicationStage.Appointment],
        ].filter(Boolean);
    }, [application, stage, paths]);

    if (!references.length) {
        return null;
    }

    return (
        <ReferenceApplicationListContainer style={style}>
            {references.map(reference => (
                <div key={reference.identifier}>
                    {reference.path && reference.isClickable ? (
                        <Link className="ant-typography" onClick={ev => ev.stopPropagation()} to={reference.path}>
                            {reference.identifier}
                        </Link>
                    ) : (
                        <Typography.Text>{reference.identifier}</Typography.Text>
                    )}
                </div>
            ))}
        </ReferenceApplicationListContainer>
    );
};

const ReferenceApplicationField = ({ stage }: Omit<ReferenceApplicationListProps, 'application'>) => {
    const { t } = useTranslation(['applicationDetails']);
    const { values } = useFormikContext<ApplicationFormValues>();
    const { FormFields } = useThemeComponents();

    const displayProps = useMemo((): Omit<DisplayFieldProps, 'value'> => {
        switch (stage) {
            case ApplicationStage.Financing:
                return t<string, { returnObjects: true }, TranslationFieldType>(
                    'applicationDetails:fields.financeReference',
                    { returnObjects: true }
                );

            case ApplicationStage.Lead:
                return t<string, { returnObjects: true }, TranslationFieldType>(
                    'applicationDetails:fields.leadReference',
                    { returnObjects: true }
                );

            case ApplicationStage.Reservation:
                return t<string, { returnObjects: true }, TranslationFieldType>(
                    'applicationDetails:fields.reservationReference',
                    { returnObjects: true }
                );

            case ApplicationStage.Appointment:
                return t<string, { returnObjects: true }, TranslationFieldType>(
                    'applicationDetails:fields.appointmentReference',
                    { returnObjects: true }
                );

            case ApplicationStage.Insurance:
                return t<string, { returnObjects: true }, TranslationFieldType>(
                    'applicationDetails:fields.insuranceReference',
                    { returnObjects: true }
                );

            default:
                return {};
        }
    }, [stage, t]);

    return (
        <FormFields.DisplayField
            {...displayProps}
            value={<ReferenceApplicationList application={values} stage={stage} />}
        />
    );
};

export default ReferenceApplicationField;
