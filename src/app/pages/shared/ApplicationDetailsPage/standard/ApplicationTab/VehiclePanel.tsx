/* eslint-disable max-len */
import { ColProps } from 'antd';
import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import { getFinderVehicleCondition } from '../../../../portal/FinderApplicationPublicAccessEntrypoint/shared';
import ApplicationDetailsPanel from '../Panel';
import SectionKey from '../SectionKey';
import defaultColSpan from '../colSpan';
import type { ApplicationFormValues } from '../shared';
import VehicleFields, { VehicleResult } from './VehicleFields';

export type VehiclePanelProps = {
    application: ApplicationData;
    ParentComponent?: React.ComponentType<any>;
    parentProps?: Record<string, any>;
    forCI: boolean;
    colSpan?: ColProps;
};

export type ApplicationData = ApplicationDataFragment;

const getVehicle = (application: ApplicationData, name: string): VehicleResult => {
    switch (application.__typename) {
        case 'ConfiguratorApplication':
        case 'EventApplication':
        case 'StandardApplication':
            switch (application.vehicle.__typename) {
                case 'LocalVariant':
                    return {
                        price: application.financing?.carPrice,
                        makeName: application.vehicle?.model.make.name,
                        modelName: application.vehicle?.model.name,
                        submodelName: application.vehicle?.submodel?.name,
                        identifier: application.vehicle?.identifier,
                        __typename: application.vehicle?.__typename,
                        name,
                    };

                default:
                    throw new Error(`Vehicle type not supported`);
            }

        case 'FinderApplication': {
            switch (application.vehicle.__typename) {
                case 'FinderVehicle':
                    return {
                        price: application.financing?.carPrice,
                        vin: application.vehicle.listing?.vehicle?.vin,
                        engineNo: application.vehicle.lta?.engineNumber,
                        inventoryId: application.vehicle.listing?.id,
                        id: application.vehicle.id,
                        condition: getFinderVehicleCondition(application.vehicle),
                        exteriorColor: application.vehicle.listing?.vehicle?.exteriorColor?.name?.localize,
                        identifier: application.vehicle.listing?.vehicle?.orderTypeCode,
                        isClickable: !application.vehicle.isDeleted,
                        __typename: application.vehicle.__typename,
                        name,
                    };

                default:
                    throw new Error(`Vehicle type not supported`);
            }
        }

        case 'LaunchpadApplication':
            switch (application.vehicle.__typename) {
                case 'LocalVariant':
                    return {
                        price: application.vehicle?.vehiclePrice,
                        makeName: application.vehicle?.model.make.name,
                        modelName: application.vehicle?.model.name,
                        submodelName: application.vehicle?.submodel?.name,
                        identifier: application.vehicle?.identifier,
                        __typename: application.vehicle?.__typename,
                        name,
                    };

                default:
                    throw new Error(`Vehicle type not supported`);
            }

        default:
            throw new Error(`Application type not supported for getting vehicle details`);
    }
};

const VehiclePanel = ({
    application,
    ParentComponent = ApplicationDetailsPanel,
    parentProps = {},
    forCI,
    colSpan = defaultColSpan,
}: VehiclePanelProps) => {
    const { t } = useTranslation('applicationDetails');

    const translatedString = useTranslatedString();
    const { values } = useFormikContext<ApplicationFormValues>();
    const vehicle = getVehicle(application, translatedString(values.vehicle.name));

    const { configuratorBlocks, configurator } = useMemo(() => {
        if (application.__typename === 'ConfiguratorApplication') {
            const { configuratorBlocks, configurator } = application;

            return { configuratorBlocks, configurator };
        }

        return { configuratorBlocks: null, configurator: null };
    }, [application]);

    const showTestDrive = useMemo(() => {
        if (application.__typename === 'EventApplication') {
            return application.event.isAllowTestDrive;
        }

        if (
            application.module.__typename === 'StandardApplicationModule' ||
            application.module.__typename === 'ConfiguratorModule' ||
            application.module.__typename === 'FinderApplicationPublicModule' ||
            application.module.__typename === 'FinderApplicationPrivateModule'
        ) {
            return application.module.testDrive;
        }

        return false;
    }, [application]);

    const combinedParentProps = useMemo(
        () => ({
            forCI,
            header: t('applicationDetails:panels.application.vehicle.header'),
            name: SectionKey.VehicleOfInterest,
            ...parentProps,
        }),
        [forCI, t, parentProps]
    );

    return (
        <ParentComponent {...combinedParentProps}>
            <VehicleFields
                colSpan={colSpan}
                companyId={application.module.company.id}
                configurator={configurator}
                configuratorBlocks={configuratorBlocks}
                isFinderApplication={application.__typename === 'FinderApplication'}
                showTestDrive={showTestDrive}
                vehicle={vehicle}
            />
        </ParentComponent>
    );
};

export default VehiclePanel;
