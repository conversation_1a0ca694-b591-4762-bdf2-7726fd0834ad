import { Col } from 'antd';
import isEqual from 'fast-deep-equal';
import { useFormikContext } from 'formik';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { useApplicationDetailsExtraContext } from '../../../../../components/contexts/ApplicationDetailsExtraContext';
import { useThemeComponents } from '../../../../../themes/hooks';
import useSystemOptions from '../../../../../utilities/useSystemOptions';
import { useGetEnbdOptionsFromCalculator } from '../../../JourneyPage/QuotationDetails';
import EnbdQuotationOptions from '../../../JourneyPage/QuotationDetails/EnbdQuotationOptions';
import ApplicationDetailsPanel from '../Panel';
import SectionKey from '../SectionKey';
import colSpan from '../colSpan';
import type { ApplicationFormValues } from '../shared';

type QuotationPanelProps = {
    application: ApplicationDataFragment;
};

const QuotationPanel = ({ application }: QuotationPanelProps) => {
    const { t } = useTranslation(['quotationDetails']);
    const { FormFields } = useThemeComponents();
    const { values, setFieldValue } = useFormikContext<ApplicationFormValues>();
    const { forCI } = useApplicationDetailsExtraContext();

    const { downPaymentToOptions } = useSystemOptions();

    const getOptionsFromCalculator = useGetEnbdOptionsFromCalculator();

    const prevDealerOptions = useRef(values.financing.dealerOptions);

    useEffect(() => {
        if (!isEqual(prevDealerOptions.current, values.financing.dealerOptions)) {
            prevDealerOptions.current = values.financing.dealerOptions;

            const options = getOptionsFromCalculator(values.financing.dealerOptions, values.quotation.enbd.options);
            setFieldValue('quotation.enbd.options', options);
        }

        // Intentionally not including enbd options inside the dependency array
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [values.financing.dealerOptions, getOptionsFromCalculator, setFieldValue]);

    return (
        <ApplicationDetailsPanel forCI={forCI} header={t('quotationDetails:title')} name={SectionKey.QuotationDetails}>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('quotationDetails:fields.applicantName', { returnObjects: true })}
                    name="quotation.enbd.applicantName"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('quotationDetails:fields.commissionNumber', { returnObjects: true })}
                    name="quotation.enbd.commissionNumber"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('quotationDetails:fields.engineNumber', { returnObjects: true })}
                    name="quotation.enbd.engineNumber"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('quotationDetails:fields.chassisNumber', { returnObjects: true })}
                    name="quotation.enbd.chassisNumber"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('quotationDetails:fields.exteriorColor', { returnObjects: true })}
                    name="quotation.enbd.exteriorColor"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.SelectField
                    {...t('quotationDetails:fields.downPaymentTo', { returnObjects: true })}
                    name="quotation.enbd.downPaymentTo"
                    options={downPaymentToOptions}
                    showSearch={!forCI}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('quotationDetails:fields.companyName', { returnObjects: true })}
                    name="quotation.enbd.companyName"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('quotationDetails:fields.financeManager', { returnObjects: true })}
                    name="quotation.enbd.financeManagerName"
                    required
                />
            </Col>
            <Col span={24}>
                <EnbdQuotationOptions name="quotation.enbd.options" allowVatChange disabled />
            </Col>
        </ApplicationDetailsPanel>
    );
};

export default QuotationPanel;
