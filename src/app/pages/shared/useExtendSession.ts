import { useApolloClient } from '@apollo/client';
import { Dispatch, useCallback } from 'react';
import {
    ExtendConfiguratorStockExpiryMutation,
    ExtendConfiguratorStockExpiryMutationVariables,
    ExtendConfiguratorStockExpiryDocument,
    ExtendMobilityStockExpiryMutation,
    ExtendMobilityStockExpiryMutationVariables,
    ExtendMobilityStockExpiryDocument,
    ExtendEventJourneyExpiryMutation,
    ExtendEventJourneyExpiryMutationVariables,
    ExtendEventJourneyExpiryDocument,
} from '../../api/mutations';
import { ApplicationKind } from '../../api/types';
import type { ConfiguratorApplicationState } from '../portal/ConfiguratorApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../portal/EventApplicationEntrypoint/Journey/shared';
import type { MobilityApplicationState } from '../portal/MobilityApplicationEntrypoint/Journey/shared';
import type { Action } from '../portal/StandardApplicationEntrypoint/Journey/shared';

const useExtendSession = (
    dispatch: Dispatch<Action<ConfiguratorApplicationState | MobilityApplicationState | EventApplicationState>>
) => {
    const apolloClient = useApolloClient();

    return useCallback(
        async (token: string, applicationKind: ApplicationKind) => {
            switch (applicationKind) {
                case ApplicationKind.Configurator: {
                    const { data } = await apolloClient.mutate<
                        ExtendConfiguratorStockExpiryMutation,
                        ExtendConfiguratorStockExpiryMutationVariables
                    >({
                        mutation: ExtendConfiguratorStockExpiryDocument,
                        variables: {
                            token,
                        },
                    });

                    if (data.result.application.__typename !== 'ConfiguratorApplication') {
                        throw new Error('unexpected type');
                    }

                    dispatch({
                        type: 'refresh',
                        token: data.result.token,
                        application: data.result.application,
                    });

                    return data.result;
                }

                case ApplicationKind.Mobility: {
                    const { data } = await apolloClient.mutate<
                        ExtendMobilityStockExpiryMutation,
                        ExtendMobilityStockExpiryMutationVariables
                    >({
                        mutation: ExtendMobilityStockExpiryDocument,
                        variables: {
                            token,
                        },
                    });

                    if (data.result.application.__typename !== 'MobilityApplication') {
                        throw new Error('unexpected type');
                    }

                    dispatch({
                        type: 'refresh',
                        token: data.result.token,
                        application: data.result.application,
                    });

                    return data.result;
                }

                case ApplicationKind.Event: {
                    const { data } = await apolloClient.mutate<
                        ExtendEventJourneyExpiryMutation,
                        ExtendEventJourneyExpiryMutationVariables
                    >({
                        mutation: ExtendEventJourneyExpiryDocument,
                        variables: {
                            token,
                        },
                    });

                    if (data.result.application.__typename !== 'EventApplication') {
                        throw new Error('unexpected type');
                    }

                    dispatch({
                        type: 'refresh',
                        token: data.result.token,
                        application: data.result.application,
                    });

                    return data.result;
                }

                default:
                    return null;
            }
        },
        [apolloClient, dispatch]
    );
};

export default useExtendSession;
