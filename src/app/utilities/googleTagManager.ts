import dayjs from 'dayjs';
import { head, isEqual, isNil, omit, set } from 'lodash/fp';
import { useMemo } from 'react';
import { VariantConfiguratorDetailsFragment } from '../api/fragments/VariantConfiguratorDetails';
import { BlockType } from '../api/types';
import { useCompany } from '../components/contexts/CompanyContextManager';
import { useDealerSelectFieldOptions } from '../components/fields/DealerSelectField';
import {
    ConfiguratorFormValues,
    OptionFormValues,
    ConfiguratorBlock,
} from '../pages/portal/ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/shared';
import { TemporaryMobilityDateType } from '../pages/portal/MobilityApplicationEntrypoint/helper';
import useCompanyFormats from './useCompanyFormats';
import useTranslatedString, { TranslateValueInput } from './useTranslatedString';

declare global {
    interface Window {
        dataLayer: { [key: string]: any }[] | undefined;
        gtm;
    }
}

export enum GTMEvents {
    PlaceYourOrderSuccessfully = 'PlaceYourOrderSuccessfully',
    SaveYourOrderButtonClick = 'SaveYourOrderButtonClick',
    PlaceYourOrderButtonClick = 'PlaceYourOrderButtonClick',
    ProceedToSummaryButtonClick = 'ProceedToSummaryButtonClick',
    KycNextButtonClick = 'KycNextButtonClick',
}

export enum MobilityGTMEvents {
    VehicleSelectionView = 'VehicleSelectionView',
    BookingDetailsView = 'BookingDetailsView',
    CustomerDetails = 'CustomerDetails',
    ProceedToPayment = 'ProceedToPayment',
    PlacedOrderSuccessfully = 'PlacedOrderSuccessfully',
    SessionTimedOut = 'SessionTimedOut',
}

const getBlockData = (blockId: string, id: string, blocks: VariantConfiguratorDetailsFragment['blocks']) => {
    const block = blocks.find(({ id }) => id === blockId);

    if (!block) {
        return null;
    }

    switch (block.__typename) {
        case 'ColorBlock':
            return block.colorSettings.find(color => color.id === id);

        case 'TrimBlock':
            return block.trimSettings.find(color => color.id === id);

        case 'PackageBlock':
            return block.packageSettings.find(color => color.id === id);

        default:
            throw new Error('type not supported');
    }
};

type ConfiguratorGTMState = {
    color?: string;
    trim?: string;
    package?: string;
    options?: { type: string; value: string }[];
};

export const mapConfiguratorToGTMState = (
    configuration: ConfiguratorBlock[],
    optionValues: OptionFormValues[],
    blocks: VariantConfiguratorDetailsFragment['blocks'],
    translatedString: (value: TranslateValueInput) => string
): ConfiguratorGTMState => {
    const state = configuration.reduce((previous, current) => {
        // no id, we skip
        if (!current.ids.length) {
            return previous;
        }

        switch (current.blockType) {
            case BlockType.Color: {
                // there is only one for color, trim, and package
                const color = getBlockData(current.blockId, current.ids[0], blocks);

                if (color && color.__typename === 'ColorAndTrimSettings') {
                    return set('color', translatedString(color.name), previous);
                }

                return previous;
            }

            case BlockType.Trim: {
                // there is only one for color, trim, and package
                const trim = getBlockData(current.blockId, current.ids[0], blocks);

                if (trim && trim.__typename === 'ColorAndTrimSettings') {
                    return set('trim', translatedString(trim.name), previous);
                }

                return previous;
            }

            case BlockType.Package: {
                // there is only one for color, trim, and package
                const packageBlock = getBlockData(current.blockId, current.ids[0], blocks);

                if (packageBlock && packageBlock.__typename === 'PackageSettings') {
                    return set('package', translatedString(packageBlock.packageName), previous);
                }

                return previous;
            }

            default:
                return previous;
        }
    }, {} as ConfiguratorGTMState);

    // there are no additional options
    if (!optionValues || !optionValues.length) {
        return state;
    }

    const options = optionValues
        .map(option => {
            if (!option.values) {
                return null;
            }

            const optionBlock = blocks.find(({ id, __typename }) => id === option.id);

            if (!optionBlock || optionBlock.__typename !== 'OptionsBlock') {
                return null;
            }

            switch (optionBlock.optionSettings.__typename) {
                case 'SingleSelectOptionSettings': {
                    const setting = optionBlock.optionSettings.options.find(
                        singleSelectOption => singleSelectOption.id === option.values
                    );

                    if (setting) {
                        return {
                            type: translatedString(optionBlock.sectionTitle),
                            value:
                                setting.type === 'Price'
                                    ? `${setting.label.defaultValue} - ${setting.valuePrice}`
                                    : `${setting.label.defaultValue} - ${translatedString(setting.valueText)}`,
                        };
                    }

                    return null;
                }

                case 'MultiSelectOptionSettings': {
                    const settings = optionBlock.optionSettings.options.filter(multiSelectOption =>
                        (option.values as string[])?.includes(multiSelectOption.id)
                    );

                    if (settings) {
                        return {
                            type: translatedString(optionBlock.sectionTitle),
                            value: settings
                                .map(setting =>
                                    setting.type === 'Price'
                                        ? `${setting.label.defaultValue} - ${setting.valuePrice}`
                                        : `${setting.label.defaultValue} - ${translatedString(setting.valueText)}`
                                )
                                .join(', '),
                        };
                    }

                    return null;
                }

                case 'ComboOptionSettings': {
                    const optionValues = (option.values as { id: string; value: string }[]).filter(
                        ({ value }) => value
                    );

                    if (!optionValues?.length) {
                        return null;
                    }

                    const settings = optionBlock.optionSettings.options.filter(comboOption =>
                        optionValues?.map(({ id }) => id).includes(comboOption.id)
                    );

                    if (settings) {
                        return {
                            type: translatedString(optionBlock.sectionTitle),
                            value: settings
                                .map(
                                    setting =>
                                        `${setting.label.defaultValue} - ${
                                            optionValues.find(optionValue => optionValue.id === setting.id)?.value
                                        }`
                                )
                                .join(', '),
                        };
                    }

                    return null;
                }

                default:
                    return null;
            }
        })
        .filter(Boolean);

    return { ...state, options };
};

type ConfiguratorGTMData = {
    event: GTMEvents;
    configuratorDetails: ConfiguratorGTMState;
    dealer?: string;
    carPrice: string;
    totalPrice: string;
    monthlyPayment: string;
    promoCode?: string;
    variant: string;
    model: string;
};

type MobilityData = {
    variant: string;
    stockIdentifier: string;
    duration?: string;
    location?: string;
};

export type MobilityGTMData = {
    event: MobilityGTMEvents;
    data: MobilityData;
};

export const useConfiguratorTagManagerArguments = (
    values: Pick<ConfiguratorFormValues, 'calculator' | 'configuratorBlocks' | 'options' | 'dealerId'>,
    variantConfigurator: VariantConfiguratorDetailsFragment,
    model: string,
    event: GTMEvents
) => {
    const company = useCompany();
    const translatedString = useTranslatedString();
    const { options: dealerOptions } = useDealerSelectFieldOptions({
        companyId: company?.id,
        productionOnly: true,
        checkConfiguratorStock: true,
    });

    const companyFormats = useCompanyFormats();

    const variantName = useMemo(() => {
        switch (variantConfigurator.variant.__typename) {
            case 'LocalVariant':
                return variantConfigurator.variant.name.defaultValue;

            default:
                return '';
        }
    }, [variantConfigurator]);

    return useMemo(() => {
        const configuratorDetails = mapConfiguratorToGTMState(
            values.configuratorBlocks,
            values.options,
            variantConfigurator.blocks,
            translatedString
        );
        const dealer = dealerOptions.find(({ value }) => value === values.dealerId)?.label;
        const carPrice = companyFormats.formatAmountWithCurrency(values.calculator.carPrice);
        const totalPrice = companyFormats.formatAmountWithCurrency(values.calculator.totalPrice);
        const monthlyPayment = companyFormats.formatAmountWithCurrency(
            head(values.calculator.monthlyInstalments)?.amount
        );

        const result: ConfiguratorGTMData = {
            event,
            configuratorDetails,
            dealer,
            carPrice,
            totalPrice,
            monthlyPayment,
            variant: variantName,
            model,
        };

        // Promo code input is text from promo code's document
        if (values.calculator?.promoCodeInput) {
            result.promoCode = values.calculator.promoCodeInput;
        }

        return result;
    }, [
        values.configuratorBlocks,
        values.options,
        values.calculator.carPrice,
        values.calculator.totalPrice,
        values.calculator.monthlyInstalments,
        values.calculator.promoCodeInput,
        values.dealerId,
        variantConfigurator.blocks,
        translatedString,
        dealerOptions,
        companyFormats,
        event,
        variantName,
        model,
    ]);
};

export const computeMobilityGTMData = (
    type: MobilityGTMEvents,
    variant?: string,
    stockIdentifier?: string,
    duration?: string,
    location?: string,
    existedData?: MobilityGTMData
): MobilityGTMData => {
    const data: MobilityGTMData = {
        event: type,
        data: {
            variant,
            stockIdentifier,
            duration,
            location,
        },
    };
    if (!isNil(existedData) && isEqual(omit('event', data), omit('event', existedData))) {
        return { ...existedData, event: type };
    }

    switch (type) {
        case MobilityGTMEvents.VehicleSelectionView:
            return { event: type, data: null };

        case MobilityGTMEvents.BookingDetailsView:
            return { event: type, data: { stockIdentifier, variant } };

        case MobilityGTMEvents.CustomerDetails:
        case MobilityGTMEvents.ProceedToPayment:
        case MobilityGTMEvents.PlacedOrderSuccessfully:
        case MobilityGTMEvents.SessionTimedOut:
            return { event: type, data: { stockIdentifier, variant, duration, location } };

        default:
            throw new Error('MobilityGTMEvents not support');
    }
};

export const calculateMobilityPeriod = (
    startDate: TemporaryMobilityDateType,
    startTime: TemporaryMobilityDateType,
    endDate: TemporaryMobilityDateType,
    endTime: TemporaryMobilityDateType
) => {
    if (isNil(startDate) || isNil(startTime) || isNil(endDate) || isNil(endTime)) {
        return null;
    }

    const concatStart = dayjs(startDate)
        .set('hour', dayjs(startTime).hour())
        .set('minute', dayjs(startTime).minute())
        .set('second', dayjs(startTime).second());

    const concatEnd = dayjs(endDate)
        .set('hour', dayjs(endTime).hour())
        .set('minute', dayjs(endTime).minute())
        .set('second', dayjs(endTime).second());

    const differentInHour = concatEnd.diff(concatStart, 'hour');
    const differentInMinutes = concatEnd.diff(concatStart, 'minute');
    const days = Math.floor(differentInHour / 24);
    const hours = differentInHour % 24;
    const minutes = differentInMinutes % 60;

    return `${days} Days and ${hours} Hours ${minutes} Minutes`;
};
