import { lazy } from '@loadable/component';
import { RouterContextDataFragment } from '../api/fragments/RouterContextData';
import comparePaths from './comparePaths';
import type { RouteDefinition } from './shared';

const HomePage = lazy(() => import(/* webpackChunkName: "homePage" */ '../pages/portal/HomePage'));
const PrivatePage = lazy(() => import(/* webpackChunkName: "privatePage" */ '../pages/portal/PrivatePage'));

const StandardApplicationEntrypoint = lazy(
    () =>
        import(/* webpackChunkName: "standardApplicationEntrypoint" */ '../pages/portal/StandardApplicationEntrypoint')
);

const StandardApplicationPublicAccessEntrypoint = lazy(
    () =>
        import(
            // eslint-disable-next-line max-len
            /* webpackChunkName: "standardApplicationPublicAccessEntrypoint" */ '../pages/portal/StandardApplicationPublicAccessEntrypoint'
        )
);

const ApplicationListEndpoint = lazy(
    () => import(/* webpackChunkName: "applicationListEndpoint" */ '../pages/portal/ApplicationListEndpoint')
);

const LeadListEndpoint = lazy(
    () => import(/* webpackChunkName: "leadListEndpoint" */ '../pages/portal/LeadListEndpoint')
);

const EventApplicationEntrypoint = lazy(
    () => import(/* webpackChunkName: "eventApplicationEntrypoint" */ '../pages/portal/EventApplicationEntrypoint')
);

const LaunchPadApplicationEntrypoint = lazy(
    () =>
        import(
            /* webpackChunkName: "launchPadApplicationEntrypoint" */ '../pages/portal/LaunchPadApplicationEntrypoint'
        )
);

const ConfiguratorApplicationEntrypoint = lazy(
    () =>
        /* eslint-disable max-len */
        import(
            /* webpackChunkName: "configuratorApplicationEntrypoint" */ '../pages/portal/ConfiguratorApplicationEntrypoint'
        )
    /* eslint-enable max-len */
);

const MobilityWebPageEndpoint = lazy(
    () =>
        /* eslint-disable max-len */
        import(/* webpackChunkName: "webpageEntrypoint" */ '../pages/portal/MobilityWebpageEndpoint')
    /* eslint-enable max-len */
);

const MobilityApplicationEntrypoint = lazy(
    () =>
        import(/* webpackChunkName: "mobilityApplicationEntrypoint" */ '../pages/portal/MobilityApplicationEntrypoint')
);

const CustomerListEndpoint = lazy(
    () => import(/* webpackChunkName: "customerListEndpoint" */ '../pages/portal/CustomerListEndpoint')
);

const FinderApplicationPublicAccessEntrypoint = lazy(
    () =>
        import(
            // eslint-disable-next-line max-len
            /* webpackChunkName: "finderApplicationPublicAccessEntrypoint" */ '../pages/portal/FinderApplicationPublicAccessEntrypoint'
        )
);

const FinderApplicationEntrypoint = lazy(
    () => import(/* webpackChunkName: "finderApplicationEntrypoint" */ '../pages/portal/FinderApplicationEntrypoint')
);

const stripPathname = (pathname: string) => pathname.split('/').filter(Boolean).join('/');

type EndpointSpecs = RouterContextDataFragment['endpoints'][number];

const getRouteFromEndpoint = (endpoint: EndpointSpecs): RouteDefinition | RouteDefinition[] => {
    switch (endpoint.__typename) {
        case 'DummyWelcomePageEndpoint':
            return {
                id: endpoint.id,
                routeProps: { path: stripPathname(endpoint.pathname), element: <HomePage /> },
            };

        case 'DummyPrivatePageEndpoint':
            return {
                id: endpoint.id,
                routeProps: { path: stripPathname(endpoint.pathname), element: <PrivatePage endpoint={endpoint} /> },
                hasAuthentication: true,
            };

        case 'StandardApplicationEntrypoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <StandardApplicationEntrypoint endpoint={endpoint} />,
                },
                hasAuthentication: true,
            };

        case 'ApplicationListEndpoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <ApplicationListEndpoint endpoint={endpoint} />,
                },
            };

        case 'LeadListEndpoint': {
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <LeadListEndpoint endpoint={endpoint} />,
                },
            };
        }

        case 'EventApplicationEntrypoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <EventApplicationEntrypoint endpoint={endpoint} />,
                },
            };

        case 'LaunchPadApplicationEntrypoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <LaunchPadApplicationEntrypoint endpoint={endpoint} />,
                },
                hasAuthentication: true,
            };

        case 'ConfiguratorApplicationEntrypoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <ConfiguratorApplicationEntrypoint endpoint={endpoint} />,
                },
            };

        case 'CustomerListEndpoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <CustomerListEndpoint endpoint={endpoint} />,
                },
            };

        case 'WebPageEndpoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <MobilityWebPageEndpoint endpoint={endpoint} />,
                },
            };

        case 'MobilityApplicationEntrypoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <MobilityApplicationEntrypoint endpoint={endpoint} />,
                },
            };

        case 'StandardApplicationPublicAccessEntrypoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <StandardApplicationPublicAccessEntrypoint endpoint={endpoint} />,
                },
            };

        case 'FinderApplicationPublicAccessEntrypoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <FinderApplicationPublicAccessEntrypoint endpoint={endpoint} />,
                },
            };

        case 'FinderApplicationEntrypoint':
            return {
                id: endpoint.id,
                routeProps: {
                    path: `${stripPathname(endpoint.pathname)}/*`,
                    element: <FinderApplicationEntrypoint endpoint={endpoint} />,
                },
                hasAuthentication: true,
            };

        default:
            return [];
    }
};

const getRoutesFromRouter = (router: RouterContextDataFragment) =>
    // get routes from endpoints
    // endpoints are already sorted by pathname on the server side
    router.endpoints.flatMap(getRouteFromEndpoint).sort((a, b) => comparePaths(a.routeProps.path, b.routeProps.path));

export default getRoutesFromRouter;
