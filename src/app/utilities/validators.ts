import { validators } from '@amille/simple-validators';
import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { isValidNumber } from 'libphonenumber-js';
import { PropertyPath } from 'lodash';
import { get, isArray, isBoolean, isEmpty, isNil, last, trim, xor } from 'lodash/fp';
import urljoin from 'url-join';
import {
    AgeCalculationMethod,
    ApplicationScenario,
    BankIntegrationProvider,
    FinderVehicleCondition,
} from '../api/types';
import { NationalityCountryCode, SINGAPORE_CITIZEN } from '../components/fields/NationalityField';
import { CountriesDataSet } from '../datasets/countries';
import isValidEncodedURLParameters from './isValidEncodedURLParameters';
import { isValid as isValidObjectId } from './oid';
import { CitizenshipType } from './useSystemOptions';

export const emailRegex =
    // eslint-disable-next-line max-len
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{1,}))$/;

const passwordRegex =
    // eslint-disable-next-line max-len
    /^(?=.{14,}$)(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#^&*()={}[\]';,.?\-$%])(?:([\w\d!@#^&*()={}[\]';,.?\-$%]))+$/;
const allowedSpecialChars = "!@#^&*()={}[]';,.?-$%";
const invalidCharRegex = new RegExp(`[^a-zA-Z0-9${allowedSpecialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`);

const colorHexRegex = /^(?:[0-9a-fA-F]{3,4}){1,2}$/i; // without "#" part

const postalCodeRegex = /^[0-9]{6}$/;

const japanPostalCodeRegexWithoutDash = /^[0-9]{7}$/;

const southKorePpostalCodeRegex = /^[0-9]{5}$/;

const whiteSpace = /^\s*$/;

const empty = /^$/;

const uaeIdRegex = /^[0-9]{15}$/;

const urlRegexPattern = /^(https?:\/\/)?((localhost(:\d{1,5})?)|([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,6})(\/[^\s]*)?$/;

export const DELETE_CONFIRMATION_TEXT = 'DELETE';

const isValidSingaporeIdentity = ({
    identity,
    dateOfBirth,
    bankProvider,
    errorMessages,
}: {
    identity: string;
    dateOfBirth: string;
    bankProvider: string;
    errorMessages: { [key: string]: string };
}) => {
    const { invalidIdentityNumber, finSGNotSupported } = errorMessages;
    const match = identity.match(/^(?<prefix>[STFGM])(?<digits>\d{7})(?<checksum>[A-Z])$/);

    if (!match) {
        return { isValid: false, errorMessage: invalidIdentityNumber };
    }

    const { prefix, digits, checksum } = match.groups;

    // Forigners are not suppored by HLF
    if (
        (bankProvider === BankIntegrationProvider.Hlfv2 || bankProvider === BankIntegrationProvider.Hlf) &&
        prefix !== 'T' &&
        prefix !== 'S'
    ) {
        return { isValid: false, errorMessage: finSGNotSupported };
    }

    // validate only DoB of local singaporean
    if ((prefix === 'S' || prefix === 'T') && dateOfBirth) {
        if (new Date(dateOfBirth).getFullYear() < 2000 && prefix !== 'S') {
            return { isValid: false, errorMessage: invalidIdentityNumber };
        }

        if (new Date(dateOfBirth).getFullYear() > 2000 && prefix !== 'T') {
            return { isValid: false, errorMessage: invalidIdentityNumber };
        }
    }

    let offset = 0;

    if (prefix === 'T' || prefix === 'G') {
        offset = 4;
    } else if (prefix === 'M') {
        offset = 3;
    }

    const weights = [2, 7, 6, 5, 4, 3, 2];

    const sum = (digits as string)
        .split('')
        .reduce((accumulation, char, index) => accumulation + parseInt(char, 10) * weights[index], offset);

    let checksums = ['J', 'Z', 'I', 'H', 'G', 'F', 'E', 'D', 'C', 'B', 'A'];

    if (prefix === 'F' || prefix === 'G') {
        checksums = ['X', 'W', 'U', 'T', 'R', 'Q', 'P', 'N', 'M', 'L', 'K'];
    } else if (prefix === 'M') {
        checksums = ['K', 'L', 'J', 'N', 'P', 'Q', 'R', 'T', 'U', 'W', 'X'];
    }

    let index = sum % 11;

    if (prefix === 'M') {
        index = 10 - index;
    }

    const isValid = checksum === checksums[index];

    if (isValid) {
        return { isValid: true, errorMessage: '' };
    }

    return { isValid: false, errorMessage: invalidIdentityNumber };
};

const validIdentity = (
    citizenshipField: PropertyPath,
    nricField: PropertyPath,
    dobField: PropertyPath,
    nationalityField: PropertyPath,
    isRequired: boolean,
    defaultNationality?: string,
    bankProvider: string = ''
) =>
    validators.custom(nricField, (value, values, errors, context) => {
        const citizenship = get(citizenshipField, values);
        const dateOfBirth = get(dobField, values);
        const nationality = get(nationalityField, values);
        const errorMessages = context.defaultMessages;

        // If there's no value, check if it's required
        if (!value) {
            return isRequired ? context.defaultMessages.requiredValue : null;
        }

        if (citizenship === CitizenshipType.SingaporeanOrPr) {
            const result = isValidSingaporeIdentity({ identity: value, dateOfBirth, bankProvider, errorMessages });
            if (!result.isValid) {
                return result.errorMessage;
            }

            return null;
        }

        if (value && isNil(citizenship) && (isNil(nationality) || nationality?.toUpperCase() === SINGAPORE_CITIZEN)) {
            switch (defaultNationality) {
                // for singapore
                case NationalityCountryCode.Singapore: {
                    const result = isValidSingaporeIdentity({
                        identity: value,
                        dateOfBirth,
                        bankProvider,
                        errorMessages,
                    });
                    if (!result.isValid) {
                        return result.errorMessage;
                    }

                    break;
                }

                // for others
                default: {
                    if (value.length < 4 || value.toUpperCase() !== value) {
                        return errorMessages.invalidIdentityNumber;
                    }

                    break;
                }
            }
        }

        return null;
    });

const validUAEId = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const isInvalidUAEId = !uaeIdRegex.test(value);
        if (!value) {
            return null;
        }
        if (isInvalidUAEId) {
            return context.defaultMessages.invalidUAEId;
        }

        return null;
    });

const validDateOfBirthKYC = (
    field: PropertyPath,
    minimumAge: number,
    ageCalculationMethod: AgeCalculationMethod,
    isMobility: boolean,
    t: TFunction
) =>
    validators.custom(field, (value, values, errors, context) => {
        if (!isNil(value)) {
            if (ageCalculationMethod === AgeCalculationMethod.CalendarYearBased) {
                return dayjs().year() - dayjs(value).year() < minimumAge
                    ? t('formErrors.invalidDateOfBirth', { minAge: minimumAge })
                    : null;
            }

            if (dayjs().diff(value, 'year') < minimumAge) {
                return isMobility
                    ? context.defaultMessages.invalidDateOfBirthMobility
                    : t('formErrors.invalidDateOfBirth', { minAge: minimumAge });
            }

            return null;
        }

        return null;
    });

const validEmail = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value && !emailRegex.test(value)) {
            return context.defaultMessages.invalidEmail;
        }

        return null;
    });

const requiredNonEmptyString = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const input = trim(value);
        if (whiteSpace.test(input) || empty.test(input)) {
            return context.defaultMessages.requiredValue;
        }

        return null;
    });

const validPhone = (field: PropertyPath, prefixField: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const phone = ['+', get(prefixField, values), value].join('');
        const numberRegex = /^\d+$/;

        if (phone && (!isValidNumber(phone) || !numberRegex.test(value))) {
            return context.defaultMessages.invalidPhone;
        }

        return null;
    });

const verifiedPhone = (verifiedField: PropertyPath) =>
    validators.custom(verifiedField, (value, values, errors, context) =>
        get(verifiedField, values) === 'false' ? context.defaultMessages.verifyPhoneRequired : null
    );

const passwordLength = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value && (value.length < 8 || value.length > 40)) {
            return context.defaultMessages.invalidPasswordLength;
        }

        return null;
    });

const requiredObjectId = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (!value || !isValidObjectId(value)) {
            return context.defaultMessages.requiredValue;
        }

        return null;
    });

const requiredBoolean = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (isBoolean(value)) {
            return null;
        }

        return context.defaultMessages.requiredValue;
    });

const validSpecialCharacters = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (invalidCharRegex.test(value)) {
            const invalidChars = value.match(new RegExp(invalidCharRegex, 'g'));

            return context.outerContext.t?.('common:formErrors.invalidSpecialCharacters', {
                invalidChars: invalidChars?.join(', '),
            });
        }

        return null;
    });

const validPasswordFormat = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value && !passwordRegex.test(value)) {
            return context.defaultMessages.invalidPasswordFormat;
        }

        return null;
    });

const matchPassword = (field: PropertyPath, matchField: string) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value && !(value === values[matchField])) {
            return context.defaultMessages.invalidPasswordRepeat;
        }

        return null;
    });

const requiredStringEnum = (field: PropertyPath, enumeration: Record<string, string>) =>
    validators.custom(field, (value, values, errors, context) => {
        const enumerationValues = Array.from(Object.values(enumeration));
        if (!enumerationValues.includes(value)) {
            return context.defaultMessages.requiredValue;
        }

        return null;
    });

const requiresPeriod = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (isNil(value) || isEmpty(value)) {
            return context.defaultMessages.requiredValue;
        }

        const { start, end } = value;

        if (!isNil(start) && !isNil(end)) {
            const startPeriod = dayjs(start);
            const endPeriod = dayjs(end);
            if (startPeriod.isValid() && endPeriod.isValid() && endPeriod.isAfter(startPeriod)) {
                return null;
            }
        }

        return context.defaultMessages.requiredValue;
    });

const requiresTimePicker = (field: PropertyPath, isRequired: boolean) =>
    validators.custom(field, (value, values, errors, context) => {
        if (isEmpty(value) && isRequired) {
            return context.defaultMessages.requiredValue;
        }

        return null;
    });

const requiredTruthyBoolean = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value === true) {
            return null;
        }

        return context.defaultMessages.requiredValue;
    });

const requiredColorHex = (field: PropertyPath) =>
    validators.custom(field, (value: string, values, errors, context) => {
        if (value && colorHexRegex.test(value)) {
            return null;
        }

        return context.defaultMessages.invalidColor;
    });

const invalidAppointmentScenario = (field: PropertyPath) =>
    validators.custom(field, (value: string, values, errors, context) => {
        if (value.includes(ApplicationScenario.Appointment) && value.includes(ApplicationScenario.VisitAppointment)) {
            return context.defaultMessages.invalidColor;
        }

        return null;
    });

/**
 * Validates that a field is an array and optionally allows empty arrays.
 *
 * @param field - The property path of the field to validate.
 * @param allowEmptyArray - A boolean indicating whether empty arrays are allowed. Defaults to false.
 * @returns A validation function that checks if the field is a valid array.
 */
const requiredArray = (field: PropertyPath, allowEmptyArray = false) =>
    validators.custom(field, (value, values, errors, context) => {
        if (
            !isArray(value) ||
            (!allowEmptyArray && isEmpty(value)) ||
            value.some(item => isNil(item) || isEmpty(item))
        ) {
            return context.defaultMessages.requiredValue;
        }

        return null;
    });

const requiredUploadFile = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (isEmpty(value)) {
            return context.defaultMessages.requiredValue;
        }

        return null;
    });

const validPostalCode = (
    field: PropertyPath,
    countryField: PropertyPath,
    countryCode: string,
    countryList: CountriesDataSet,
    isRequired: boolean
) =>
    validators.custom(field, (value, values, errors, context) => {
        const countryInput = get(countryField, values);
        const country = countryList.find(country => country.name.common === countryInput);
        const countryValue = country?.cca2 || countryCode;

        if (isRequired && isEmpty(value)) {
            return context.defaultMessages.requiredValue;
        }

        switch (countryValue) {
            // for singapore
            case NationalityCountryCode.Singapore: {
                if (!isEmpty(value) && !postalCodeRegex.test(value)) {
                    return context.defaultMessages.invalidPostalCode;
                }

                break;
            }

            case NationalityCountryCode.Japan: {
                if (!isEmpty(value)) {
                    if (!japanPostalCodeRegexWithoutDash.test(value)) {
                        // for Japan, we reuse the same translation key. Therefore, just have to update the existing one
                        return context.defaultMessages.invalidPostalCode;
                    }
                    break;
                }

                break;
            }

            // for singapore
            case NationalityCountryCode.SouthKorea: {
                if (!isEmpty(value) && !southKorePpostalCodeRegex.test(value)) {
                    return context.defaultMessages.invalidPostalCode;
                }

                break;
            }

            // for others
            default:
                break;
        }

        return null;
    });

const urlIdentifier = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const urlIdentifierRegex = /^[a-z0-9]+[a-z0-9-]*[a-z0-9]+$/;
        if (!isEmpty(value) && !urlIdentifierRegex.test(value)) {
            return context.defaultMessages.urlIdentifier;
        }

        return null;
    });

const unavailableTimeRange = (timeRangePath: PropertyPath) =>
    validators.forEach(
        timeRangePath,
        validators.only(
            (values, error, { prefix }) => {
                const value = get(prefix, values);

                const { start, end } = value;

                return !(isNil(start) && isNil(end));
            },
            validators.custom('start', (value, values, errors, context) => {
                const { start, end } = get(context.prefix, values);
                let timeRangeIndex = null;
                if (Array.isArray(context.prefix)) {
                    timeRangeIndex = last(context.prefix);
                }
                const unavailableTimeRange = get(timeRangePath, values);
                if (isNil(start) || isNil(end)) {
                    return context.defaultMessages.missingTimeValue;
                }
                const startTime = dayjs(start);
                const endTime = dayjs(end);
                if (startTime.isSameOrAfter(endTime)) {
                    return context.defaultMessages.periodInvalid;
                }
                if (
                    unavailableTimeRange.some(
                        (slot, index) =>
                            timeRangeIndex !== index.toString() &&
                            dayjs(slot.end).isAfter(dayjs(startTime)) &&
                            dayjs(slot.start).isBefore(dayjs(endTime))
                    )
                ) {
                    return context.defaultMessages.existedTimeRange;
                }

                return null;
            })
        )
    );

const urlSlugChecker = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const urlIdentifierRegex = /^[a-zA-Z0-9-_]+$/;
        if (isEmpty(value) || !urlIdentifierRegex.test(value)) {
            return context.defaultMessages.urlIdentifier;
        }

        return null;
    });

const timeChecker = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const rentalPeriod = get(field, values);

        if (!isNil(rentalPeriod?.start) && !isNil(rentalPeriod?.end)) {
            if (dayjs(rentalPeriod?.end).isSameOrBefore(dayjs(rentalPeriod?.start), 'minute')) {
                return context.defaultMessages.periodInvalid;
            }
        }

        return null;
    });

const validTranslatedInput = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const translatedInput = get(field, values);

        if (!translatedInput.defaultValue) {
            return context.defaultMessages.requiredValue;
        }

        return null;
    });

const validTimeStringFormat = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        if (!value) {
            return context.defaultMessages.requiredValue;
        }

        if (!/^(\d+[wdhm]\s*)+$/.test(value)) {
            return context.defaultMessages.timeStringInvalid;
        }

        return null;
    });

const validNumberRange = (field: PropertyPath, min: number, max: number) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value) {
            if (value < min) {
                return context.defaultMessages.invalidNumberMin;
            }

            if (value > max) {
                return context.defaultMessages.invalidNumberMax;
            }
        }

        return null;
    });

const validNumberMin = (field: PropertyPath, min: number) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value && value < min) {
            return context.defaultMessages.invalidNumberMin;
        }

        return null;
    });

const validNumberMax = (field: PropertyPath, max: number) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value && value > max) {
            return context.defaultMessages.invalidNumberMax;
        }

        return null;
    });

const validMaxCharacter = (field: PropertyPath, isRequired: boolean, max: number) =>
    validators.custom(field, (value, values, errors, context) => {
        if (!value && isRequired) {
            return context.defaultMessages.requiredValue;
        }

        if (value && value.length > max) {
            return context.outerContext.t?.('common:formErrors.maxInputCount', { max });
        }

        return null;
    });

const validMaxDecimalPlaces = (field: PropertyPath, maxDecimalPlaces: number) =>
    validators.custom(field, (value, values, errors, context) => {
        if (value && String(value).includes('.') && String(value).split('.')[1].length > maxDecimalPlaces) {
            return context.outerContext.t?.('common:formErrors.maxDecimalPlaces', { maxDecimalPlaces });
        }

        return null;
    });

const validFinderApplicationModuleCondition = (
    field: PropertyPath,
    valueCondition: FinderVehicleCondition[],
    validatorCondition: FinderVehicleCondition[]
) =>
    validators.custom(field, (value, values, errors, context) => {
        const differentSelection = xor(valueCondition, validatorCondition);
        if (differentSelection.length > 0) {
            return context.defaultMessages.differentFinderApplicationModuleMessage;
        }

        return null;
    });

const requiresEventPeriod = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const { start, end } = value ?? {};
        const { startTime, endTime } = values;

        if (start && end && startTime && endTime) {
            const startPeriod = dayjs(start).hour(startTime.hour()).minute(startTime.minute());
            const endPeriod = dayjs(end).hour(endTime.hour()).minute(endTime.minute());
            if (startPeriod.isValid() && endPeriod.isValid() && endPeriod.isAfter(startPeriod)) {
                return null;
            }

            return context.defaultMessages.periodInvalid;
        }

        return context.defaultMessages.requiredValue;
    });

const requiresFutureDate = (field: PropertyPath, isTodayAllowed = false) =>
    validators.custom(field, (value, values, errors, context) => {
        const time = dayjs(value);

        if (!value || !time.isValid()) {
            return context.defaultMessages.requiredValue;
        }

        if (isTodayAllowed && time.isSameOrAfter(dayjs().startOf('day'))) {
            return null;
        }

        if (time.isAfter(dayjs().startOf('day'))) {
            return null;
        }

        return null;
    });

const matchDeleteConfirmation = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const confirmValue = get(field, values) ?? '';
        if (confirmValue === DELETE_CONFIRMATION_TEXT) {
            return null;
        }

        return context.defaultMessages.deleteConfirmationRequired;
    });

const validUTMParameter = (field: PropertyPath, leadGenFormUrl: string) =>
    validators.custom(field, (value, values, errors, context) => {
        const eventFormUrl =
            leadGenFormUrl && values.urlSlug ? urljoin(leadGenFormUrl, `/${values.urlSlug}`) : leadGenFormUrl;

        const utmUrl = get(context.prefix, values)?.utmUrl;
        if (utmUrl?.length > 2048) {
            return context.defaultMessages.utmUrlLengthy;
        }

        if (!/^(http|https)/.test(utmUrl)) {
            return context.defaultMessages.utmUrlInsecure;
        }

        if (utmUrl.includes(' ')) {
            return context.defaultMessages.utmUrlWhitespaces;
        }

        let url = null;
        try {
            url = new URL(utmUrl);
        } catch (error) {
            return context.defaultMessages.utmUrlInvalid;
        }

        const domainAndPath = url.href.split('?')[0];
        if (!isNil(eventFormUrl) && domainAndPath !== eventFormUrl) {
            return context.defaultMessages.utmUrlInvalidDomain;
        }

        const overrides = get('utmParametersSettings.overrides', values);

        // found identical utmUrl from the exact same lead gen form
        // validate all the UTM Parameter Urls
        const duplicateUrls = overrides.some((override, currentIndex) =>
            overrides.some(
                (currentOverride, index) => currentOverride.utmUrl === override.utmUrl && index !== currentIndex
            )
        );

        if (duplicateUrls) {
            return context.defaultMessages.utmUrlDuplicate;
        }

        if (isEmpty(url.searchParams.get('utm_campaign'))) {
            return context.defaultMessages.utmUrlMissingCampaignParameter;
        }

        if (isEmpty(url.searchParams.get('utm_medium'))) {
            return context.defaultMessages.utmUrlMissingMediumParameter;
        }

        if (isEmpty(url.searchParams.get('utm_source'))) {
            return context.defaultMessages.utmUrlMissingSourceParameter;
        }

        // check encoded of url parameters
        if (!isValidEncodedURLParameters(url.search, utmUrl)) {
            return context.defaultMessages.utmUrlNotEncoded;
        }

        return null;
    });

const validMyInfoSetting = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        // value = null is value of None option in dropdown
        if (value === null || value) {
            return null;
        }

        return context.defaultMessages.requiredValue;
    });

const validUrl = (field: PropertyPath, isRequired: boolean) =>
    validators.custom(field, (value, values, errors, context) => {
        if (isRequired && !value) {
            return context.defaultMessages.requiredValue;
        }

        if (!urlRegexPattern.test(value)) {
            return context.defaultMessages.urlValidate;
        }

        return null;
    });

export default {
    ...validators,
    matchPassword,
    validEmail,
    validPhone,
    verifiedPhone,
    validSpecialCharacters,
    validPasswordFormat,
    validIdentity,
    validPostalCode,
    passwordLength,
    requiredObjectId,
    requiredNonEmptyString,
    requiredBoolean,
    requiredStringEnum,
    requiresPeriod,
    requiredTruthyBoolean,
    requiredColorHex,
    requiredArray,
    requiredUploadFile,
    urlIdentifier,
    unavailableTimeRange,
    urlSlugChecker,
    timeChecker,
    validTranslatedInput,
    validTimeStringFormat,
    validNumberRange,
    validNumberMin,
    validNumberMax,
    validMaxCharacter,
    validMaxDecimalPlaces,
    validFinderApplicationModuleCondition,
    validUAEId,
    requiresEventPeriod,
    validDateOfBirthKYC,
    matchDeleteConfirmation,
    requiresTimePicker,
    invalidAppointmentScenario,
    validUTMParameter,
    validMyInfoSetting,
    requiresFutureDate,
    validUrl,
};
