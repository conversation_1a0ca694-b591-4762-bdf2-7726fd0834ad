import { isNil } from 'lodash/fp';
import type { UploadFileWithPreviewFormDataFragment } from '../api/fragments/UploadFileWithPreviewFormData';

export const downloadFile = (url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
};

/**
 * Type guard to check if a file is an UploadFileWithPreviewFormDataFragment
 */
export const isUploadFileWithPreviewFormDataFragment = (
    file: UploadFileWithPreviewFormDataFragment | File | null | undefined
): file is UploadFileWithPreviewFormDataFragment =>
    !isNil(file) && typeof file === 'object' && '__typename' in file && file.__typename === 'UploadedFileWithPreview';
