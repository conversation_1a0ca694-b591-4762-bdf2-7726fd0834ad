import * as Icon from '@ant-design/icons';
import { sortBy } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
    AddressType,
    ApplicationQuotationDownPaymentTarget,
    ApplicationStage,
    AutoplayApiVersion,
    BankIntegrationProvider,
    BannerTextPosition,
    CalculationRounding,
    ComboType,
    CompanyTheme,
    CurrentVehicleEquipmentLine,
    CurrentVehicleSource,
    DayOfWeek,
    DrivingLicenseType,
    Education,
    EmailProvider,
    Emirate,
    EmploymentStatus,
    EngineType,
    FinderVehicleCondition,
    IncomeType,
    InsuranceProductType,
    InsurerIntegrationProvider,
    IntentType,
    JobTitle,
    JobTitleTh,
    LayoutType,
    LeadStageOption,
    MaskDirection,
    MfaSettingsType,
    OptionKind,
    OptionType,
    PackageKind,
    PurchaseIntention,
    PurposeOfVisit,
    ReferenceDetailRelationship,
    RelationshipApplicant,
    ReservationStockStatus,
    ResidenceType,
    ResidentialStatus,
    SalesOfferAgreementKind,
    SmsProvider,
    TransmissionKind,
    TransportProtocolType,
    TtbPaymentFlag3ds,
    WebPageBlockBackground,
    WebPageBlockPosition,
    WebPageBlockType,
} from '../api/types';
import { useCompany } from '../components/contexts/CompanyContextManager';
import drivingLicenseClasses from '../datasets/drivingLicenseClasses';
import myDrivingLicenseClasses from '../datasets/myDrivingLicenseClasses';

export enum Title {
    MR = 'MR',
    MS = 'MS',
    MX = 'MX',
}

export enum Salutation {
    DR = 'DR',
    MR = 'MR',
    MDM = 'MDM',
    MRS = 'MRS',
    MS = 'MS',
}

// TODO: Need to handle this from graphql instead
export enum CitizenshipType {
    SingaporeanOrPr = 'Singapore Citizen/PR',
    Malaysian = 'Malaysian',
    Others = 'Other Nationality',
}

/**
 * @param includeMyInfoValues flag to indicate if values specific to MyInfo should be included
 */
const useSystemOptions = (includeMyInfoValues = false) => {
    const { t } = useTranslation('common');
    const company = useCompany(true);
    const countryCode = company?.countryCode;

    return useMemo(() => {
        const antdIconList = Object.entries(Icon).map(obj => obj[0]);
        antdIconList.splice(antdIconList.length - 4, 4);

        const antdIconOptions = antdIconList.map(list => ({
            label: list,
            value: list,
        }));

        const decimalsOptions = [
            {
                value: 0,
                label: t('common:options.decimals.zero'),
            },
            {
                value: 1,
                label: t('common:options.decimals.one'),
            },
            {
                value: 2,
                label: t('common:options.decimals.two'),
            },
        ];

        const calculationRoundingOptions = [
            {
                value: CalculationRounding.None,
                label: t('common:options.calculationRounding.none'),
            },
            {
                value: CalculationRounding.Tens,
                label: t('common:options.calculationRounding.tens'),
            },
            {
                value: CalculationRounding.Hundreds,
                label: t('common:options.calculationRounding.hundreds'),
            },
            {
                value: CalculationRounding.Thousands,
                label: t('common:options.calculationRounding.thousands'),
            },
        ];

        const emailProviderOptions = [
            {
                value: EmailProvider.System,
                label: t('common:options.emailProvider.system'),
            },
            {
                value: EmailProvider.Smtp,
                label: t('common:options.emailProvider.smtp'),
            },
        ];

        const smsProviderOptions = [
            {
                value: SmsProvider.System,
                label: t('common:options.smsProvider.system'),
            },
            {
                value: SmsProvider.Twilio,
                label: t('common:options.smsProvider.twilio'),
            },
        ];

        const bankIntegrationProviderOptions = [
            {
                value: BankIntegrationProvider.Email,
                label: t('common:options.bankIntegrationProvider.email'),
            },
            {
                value: BankIntegrationProvider.Hlf,
                label: t('common:options.bankIntegrationProvider.hlf'),
            },
            {
                value: BankIntegrationProvider.Hlfv2,
                label: t('common:options.bankIntegrationProvider.hlfV2'),
            },
            // temporary remove, until we offer this to client
            // {
            //     value: BankIntegrationProvider.Uob,
            //     label: t('common:options.bankIntegrationProvider.uob'),
            // },
            {
                value: BankIntegrationProvider.Dbs,
                label: t('common:options.bankIntegrationProvider.dbs'),
            },
            // temporary remove, until we offer this to client
            // {
            //     value: BankIntegrationProvider.Maybank,
            //     label: t('common:options.bankIntegrationProvider.maybank'),
            // },
            {
                value: BankIntegrationProvider.Enbd,
                label: t('common:options.bankIntegrationProvider.enbd'),
            },
        ];

        const insurerIntegrationProviderOptions = [
            {
                value: InsurerIntegrationProvider.Email,
                label: t('common:options.insurerIntegrationProvider.email'),
            },
            {
                value: InsurerIntegrationProvider.Eazy,
                label: t('common:options.insurerIntegrationProvider.eazy'),
            },
        ];

        const drivingLicenseTypeOptions = [
            {
                label: t('common:options.drivingLicenseType.qualified'),
                value: DrivingLicenseType.Qualified,
            },
            {
                label: t('common:options.drivingLicenseType.notApplicable'),
                value: DrivingLicenseType.NotApplicable,
            },
            ...(includeMyInfoValues
                ? [
                      {
                          label: t('common:options.drivingLicenseType.notHolding'),
                          value: DrivingLicenseType.NotHolding,
                      },
                  ]
                : []),
        ];

        const uaeDrivingLicenseTypeOptions = [
            {
                label: t('common:options.drivingLicenseType.foreigner'),
                value: DrivingLicenseType.Foreigner,
            },
            {
                label: t('common:options.drivingLicenseType.uae'),
                value: DrivingLicenseType.Uae,
            },
        ];

        const drivingLicenceValidityCodes = [
            {
                value: 'V',
                label: t('common:options.drivingLicenceValidityCodes.valid'),
            },
            {
                value: 'I',
                label: t('common:options.drivingLicenceValidityCodes.invalid'),
            },
            {
                value: 'E',
                label: t('common:options.drivingLicenceValidityCodes.expired'),
            },
        ];

        const mfaSettingsTypeOptions = [
            {
                value: MfaSettingsType.SmsOtp,
                label: t('common:options.mfaSettingType.smsOtp'),
            },
        ];

        const transportProtocolOptions = [
            {
                value: TransportProtocolType.Email,
                label: t('common:options.transportProtocol.email'),
            },
            {
                value: TransportProtocolType.Sms,
                label: t('common:options.transportProtocol.sms'),
            },
        ];

        const themeOptions = [
            {
                value: CompanyTheme.Default,
                label: t('common:options.theme.default'),
            },
            {
                value: CompanyTheme.Porsche,
                label: t('common:options.theme.porsche'),
            },
            {
                value: CompanyTheme.PorscheV3,
                label: t('common:options.theme.porscheV3'),
            },
            {
                value: CompanyTheme.Volkswagen,
                label: t('common:options.theme.volkswagen'),
            },
            {
                value: CompanyTheme.Skoda,
                label: t('common:options.theme.skoda'),
            },
        ];

        const ApplicationStageOptions = [
            {
                value: ApplicationStage.Financing,
                label: t('common:options.applicationStage.financing'),
            },
            {
                value: ApplicationStage.Reservation,
                label: t('common:options.applicationStage.reservation'),
            },
            {
                value: ApplicationStage.Appointment,
                label: t('common:options.applicationStage.appointment'),
            },
            {
                value: ApplicationStage.Insurance,
                label: t('common:options.applicationStage.insurance'),
            },
            {
                value: ApplicationStage.VisitAppointment,
                label: t('common:options.applicationStage.visitAppointment'),
            },
            {
                value: ApplicationStage.TradeIn,
                label: t('common:options.applicationStage.tradeIn'),
            },
        ];
        const stockStatuses = [
            {
                value: ReservationStockStatus.Available,
                label: t('common:options.stockStatuses.available'),
            },
            {
                value: ReservationStockStatus.ManuallyDeducted,
                label: t('common:options.stockStatuses.manuallyDeducted'),
            },
            {
                value: ReservationStockStatus.Reserved,
                label: t('common:options.stockStatuses.reserved'),
                disabled: true,
            },
            {
                value: ReservationStockStatus.SystemDeducted,
                label: t('common:options.stockStatuses.systemDeducted'),
                disabled: true,
            },
        ];

        const configuratorBannerTextPositionOptions = [
            {
                value: BannerTextPosition.Left,
                label: t('common:options.configurator.bannerTextPosition.left'),
            },
            {
                value: BannerTextPosition.Centre,
                label: t('common:options.configurator.bannerTextPosition.centre'),
            },
            {
                value: BannerTextPosition.Right,
                label: t('common:options.configurator.bannerTextPosition.right'),
            },
        ];

        const configuratorPackageOptions = [
            {
                value: PackageKind.Price,
                label: t('common:options.configurator.package.price'),
            },
            {
                value: PackageKind.Description,
                label: t('common:options.configurator.package.description'),
            },
        ];

        const configuratorOptionKinds = [
            {
                value: OptionKind.Combo,
                label: t('common:options.configurator.optionKinds.combo'),
            },
            {
                value: OptionKind.MultiSelect,
                label: t('common:options.configurator.optionKinds.multiSelect'),
            },
            {
                value: OptionKind.SingleSelect,
                label: t('common:options.configurator.optionKinds.singleSelect'),
            },
        ];

        const configuratorSelectOptionTypes = [
            {
                value: OptionType.Price,
                label: t('common:options.configurator.optionsTypes.price'),
            },
            {
                value: OptionType.Description,
                label: t('common:options.configurator.optionsTypes.description'),
            },
        ];

        const configuratorComboOptionTypes = [
            {
                value: ComboType.Dropdown,
                label: t('common:options.configurator.comboTypes.dropdown'),
            },
            {
                value: ComboType.TextField,
                label: t('common:options.configurator.comboTypes.textField'),
            },
        ];

        const titleOptions = [
            {
                label: t('common:options.titles.mr'),
                value: Title.MR,
            },
            {
                label: t('common:options.titles.ms'),
                value: Title.MS,
            },
        ];

        const nonBinaryTitleOptions = [
            ...titleOptions,
            {
                label: t('common:options.titles.mx'),
                value: Title.MX,
            },
        ];

        const salutationOptions = [
            {
                label: t('common:options.salutations.mr'),
                value: Salutation.MR,
            },
            {
                label: t('common:options.salutations.mrs'),
                value: Salutation.MRS,
            },
            {
                label: t('common:options.salutations.ms'),
                value: Salutation.MS,
            },
            {
                label: t('common:options.salutations.dr'),
                value: Salutation.DR,
            },
        ];

        const salutationBMWOptions = [
            {
                label: t('common:options.salutations.dr'),
                value: Salutation.DR,
            },
            {
                label: t('common:options.salutations.mr'),
                value: Salutation.MR,
            },
            {
                label: t('common:options.salutations.mdm'),
                value: Salutation.MDM,
            },
            {
                label: t('common:options.salutations.mrs'),
                value: Salutation.MRS,
            },
            {
                label: t('common:options.salutations.ms'),
                value: Salutation.MS,
            },
        ];

        const citizenshipOptions = [
            {
                label: t('common:options.citizenship.singaporeCitizenOrPr'),
                value: CitizenshipType.SingaporeanOrPr,
            },
            {
                label: t('common:options.citizenship.malaysian'),
                value: CitizenshipType.Malaysian,
            },
            {
                label: t('common:options.citizenship.others'),
                value: CitizenshipType.Others,
            },
        ];

        const maskDirectionOptions = [
            {
                value: MaskDirection.None,
                label: t('common:options.customerDataMasking.direction.none'),
            },
            {
                value: MaskDirection.Front,
                label: t('common:options.customerDataMasking.direction.front'),
            },
            {
                value: MaskDirection.Back,
                label: t('common:options.customerDataMasking.direction.back'),
            },
        ];

        const maskCountOptions = [
            {
                value: 1,
                label: t('common:options.customerDataMasking.counts.one'),
            },
            {
                value: 2,
                label: t('common:options.customerDataMasking.counts.two'),
            },
            {
                value: 3,
                label: t('common:options.customerDataMasking.counts.three'),
            },
            {
                value: 4,
                label: t('common:options.customerDataMasking.counts.four'),
            },
            {
                value: 5,
                label: t('common:options.customerDataMasking.counts.five'),
            },
        ];

        const webpageBlockPosition = [
            { value: WebPageBlockPosition.Left, label: t('common:options.webpageBlockPosition.left') },
            { value: WebPageBlockPosition.Right, label: t('common:options.webpageBlockPosition.right') },
        ];

        const webPageBlockBackgroundOptions = [
            { value: WebPageBlockBackground.White, label: t('common:options.webpageBlockBackground.white') },
            { value: WebPageBlockBackground.Black, label: t('common:options.webpageBlockBackground.black') },
        ];

        const webpageBlockType = [
            { value: WebPageBlockType.Column, label: t('common:options.webpageBlockType.column') },
            { value: WebPageBlockType.Custom, label: t('common:options.webpageBlockType.custom') },
            { value: WebPageBlockType.Image, label: t('common:options.webpageBlockType.image') },
            { value: WebPageBlockType.TextImage, label: t('common:options.webpageBlockType.textImage') },
            { value: WebPageBlockType.TextCarousel, label: t('common:options.webpageBlockType.textCarousel') },
        ];

        const transmissionType = [
            { value: TransmissionKind.Manual, label: t('common:options.transmissionKind.manual') },
            { value: TransmissionKind.Auto, label: t('common:options.transmissionKind.auto') },
        ];

        const configurator = {
            bannerTextPositionOptions: configuratorBannerTextPositionOptions,
            packageOptions: configuratorPackageOptions,
            optionKinds: configuratorOptionKinds,
            selectOptionTypes: configuratorSelectOptionTypes,
            comboOptionTypes: configuratorComboOptionTypes,
        };

        const yesNoDropdown = [
            { value: true, label: t('common:options.yesNo.yes') },
            { value: false, label: t('common:options.yesNo.no') },
        ];

        const dayOfWeek = [
            { value: DayOfWeek.Monday, label: t('common:options.dayofWeek.monday') },
            { value: DayOfWeek.Tuesday, label: t('common:options.dayofWeek.tuesday') },
            { value: DayOfWeek.Wednesday, label: t('common:options.dayofWeek.wednesday') },
            { value: DayOfWeek.Thursday, label: t('common:options.dayofWeek.thursday') },
            { value: DayOfWeek.Friday, label: t('common:options.dayofWeek.friday') },
            { value: DayOfWeek.Saturday, label: t('common:options.dayofWeek.saturday') },
            { value: DayOfWeek.Sunday, label: t('common:options.dayofWeek.sunday') },
        ];

        const jobTitleList = [
            { value: JobTitle.Staff, label: t('common:options.jobTitle.staff') },
            { value: JobTitle.Supervisor, label: t('common:options.jobTitle.supervisor') },
            { value: JobTitle.AssistantManager, label: t('common:options.jobTitle.assistantManager') },
            { value: JobTitle.Manager, label: t('common:options.jobTitle.manager') },
            { value: JobTitle.Director, label: t('common:options.jobTitle.director') },
            { value: JobTitle.VicePresident, label: t('common:options.jobTitle.vicePresident') },
            { value: JobTitle.President, label: t('common:options.jobTitle.president') },
            { value: JobTitle.Others, label: t('common:options.jobTitle.others') },
        ];

        let jobTitleThList = [
            { value: JobTitleTh.Employee, label: t('common:options.jobTitleTh.employee') },
            { value: JobTitleTh.CompanyOwner, label: t('common:options.jobTitleTh.companyOwner') },
            { value: JobTitleTh.ManagingDirector, label: t('common:options.jobTitleTh.managingDirector') },
            { value: JobTitleTh.ChiefExecutive, label: t('common:options.jobTitleTh.chiefExecutive') },
            { value: JobTitleTh.FreelanceSelfEmployed, label: t('common:options.jobTitleTh.freelanceSelfEmployed') },
            {
                value: JobTitleTh.RetiredHousewifeStudent,
                label: t('common:options.jobTitleTh.retiredHousewifeStudent'),
            },
            { value: JobTitleTh.Others, label: t('common:options.jobTitleTh.others') },
        ];

        if (countryCode === 'JP') {
            jobTitleThList = [
                { value: JobTitleTh.JpLawyer, label: t('common:options.jobTitleJp.jpLawyer') },
                { value: JobTitleTh.JpOfficeWorker, label: t('common:options.jobTitleJp.jpOfficeWorker') },
                {
                    value: JobTitleTh.JpOrganizationPersonnel,
                    label: t('common:options.jobTitleJp.jpOrganizationPersonnel'),
                },
                { value: JobTitleTh.JpBoardDirector, label: t('common:options.jobTitleJp.jpBoardDirector') },
                {
                    value: JobTitleTh.JpOrganizationOfficer,
                    label: t('common:options.jobTitleJp.jpOrganizationOfficer'),
                },
                { value: JobTitleTh.JpSelfEmployee, label: t('common:options.jobTitleJp.jpSelfEmployee') },
                { value: JobTitleTh.JpDoctor, label: t('common:options.jobTitleJp.jpDoctor') },
                { value: JobTitleTh.JpDentist, label: t('common:options.jobTitleJp.jpDentist') },
                {
                    value: JobTitleTh.JpNationalCivilServant,
                    label: t('common:options.jobTitleJp.jpNationalCivilServant'),
                },
                {
                    value: JobTitleTh.JpLocalCivilServant,
                    label: t('common:options.jobTitleJp.jpLocalCivilServant'),
                },
                {
                    value: JobTitleTh.JpCertifiedAccountant,
                    label: t('common:options.jobTitleJp.jpCertifiedAccountant'),
                },
                {
                    value: JobTitleTh.JpLicensedTaxAccountant,
                    label: t('common:options.jobTitleJp.jpLicensedTaxAccountant'),
                },
                { value: JobTitleTh.JpTeacher, label: t('common:options.jobTitleJp.jpTeacher') },
                { value: JobTitleTh.JpFreelance, label: t('common:options.jobTitleJp.jpFreelance') },
                { value: JobTitleTh.JpPartTimeJob, label: t('common:options.jobTitleJp.jpPartTimeJob') },
                { value: JobTitleTh.JpArtist, label: t('common:options.jobTitleJp.jpArtist') },
                {
                    value: JobTitleTh.JpAgricultureForestryFishing,
                    label: t('common:options.jobTitleJp.jpAgricultureForestryFishing'),
                },
                { value: JobTitleTh.JpHousewife, label: t('common:options.jobTitleJp.jpHousewife') },
                { value: JobTitleTh.JpStudent, label: t('common:options.jobTitleJp.jpStudent') },
                {
                    value: JobTitleTh.JpInoccupationHouseHelper,
                    label: t('common:options.jobTitleJp.jpInoccupationHouseHelper'),
                },
                { value: JobTitleTh.Others, label: t('common:options.jobTitleJp.others') },
            ];
        } else if (countryCode === 'KR' || countryCode === 'KO') {
            jobTitleThList = [
                { value: JobTitleTh.KoEmployee, label: t('common:options.jobTitleKo.koEmployee') },
                { value: JobTitleTh.KoBusinessOwner, label: t('common:options.jobTitleKo.koBusinessOwner') },
                { value: JobTitleTh.KoProfession, label: t('common:options.jobTitleKo.koProfession') },
                { value: JobTitleTh.KoCeo, label: t('common:options.jobTitleKo.koCeo') },
                { value: JobTitleTh.KoFreelancer, label: t('common:options.jobTitleKo.koFreelancer') },
                { value: JobTitleTh.Others, label: t('common:options.jobTitleKo.others') },
            ];
        }

        const employmentStatus = [
            { value: EmploymentStatus.BetweenJobs, label: t('common:options.employmentStatus.betweenJobs') },
            { value: EmploymentStatus.Contract, label: t('common:options.employmentStatus.contract') },
            { value: EmploymentStatus.FullTime, label: t('common:options.employmentStatus.fullTime') },
            { value: EmploymentStatus.PartTime, label: t('common:options.employmentStatus.partTime') },
            { value: EmploymentStatus.SelfEmployed, label: t('common:options.employmentStatus.selfEmployed') },
            { value: EmploymentStatus.Unemployed, label: t('common:options.employmentStatus.unemployed') },
        ];

        const relationshipApplicant = [
            { value: RelationshipApplicant.Children, label: t('common:options.relationshipApplicant.children') },
            { value: RelationshipApplicant.Colleague, label: t('common:options.relationshipApplicant.colleague') },
            {
                value: RelationshipApplicant.CorporatePrincipal,
                label: t('common:options.relationshipApplicant.corporatePrincipal'),
            },
            { value: RelationshipApplicant.Friend, label: t('common:options.relationshipApplicant.friend') },
            { value: RelationshipApplicant.Myself, label: t('common:options.relationshipApplicant.myself') },
            { value: RelationshipApplicant.Others, label: t('common:options.relationshipApplicant.others') },
            { value: RelationshipApplicant.Parent, label: t('common:options.relationshipApplicant.parent') },
            { value: RelationshipApplicant.Relative, label: t('common:options.relationshipApplicant.relative') },
            { value: RelationshipApplicant.Shareholder, label: t('common:options.relationshipApplicant.shareholder') },
            { value: RelationshipApplicant.Silbing, label: t('common:options.relationshipApplicant.silbing') },
            { value: RelationshipApplicant.Spouse, label: t('common:options.relationshipApplicant.spouse') },
        ];

        const residentialStatusList = [
            { value: ResidentialStatus.SelfOwned, label: t('common:options.residentialStatus.selfOwned') },
            { value: ResidentialStatus.RelativeOwned, label: t('common:options.residentialStatus.relativeOwned') },
            { value: ResidentialStatus.Rental, label: t('common:options.residentialStatus.rental') },
            { value: ResidentialStatus.Dormitory, label: t('common:options.residentialStatus.dormitory') },
            { value: ResidentialStatus.Others, label: t('common:options.residentialStatus.others') },
        ];

        const addressTypeOptions = [
            { value: AddressType.Residential, label: t('common:options.addressType.residential') },
            { value: AddressType.Office, label: t('common:options.addressType.office') },
        ];

        const educationOptions = [
            { value: Education.Primary, label: t('common:options.education.primary') },
            { value: Education.Preparatory, label: t('common:options.education.preparatory') },
            { value: Education.Secondary, label: t('common:options.education.secondary') },
            {
                value: Education.TechnicalSecondarySchool,
                label: t('common:options.education.technicalSecondarySchool'),
            },
            { value: Education.Bachelor, label: t('common:options.education.bachelor') },
            { value: Education.Master, label: t('common:options.education.master') },
            { value: Education.Doctorate, label: t('common:options.education.doctorate') },
        ];

        const incomeTypeOptions = [
            { value: IncomeType.Salaried, label: t('common:options.incomeType.salaried') },
            { value: IncomeType.SelfEmployed, label: t('common:options.incomeType.selfEmployed') },
        ];

        const residenceTypeOptions = [
            { value: ResidenceType.Owned, label: t('common:options.residenceType.owned') },
            { value: ResidenceType.Rented, label: t('common:options.residenceType.rented') },
        ];

        const emirateOptions = [
            { value: Emirate.AbuDhabi, label: t('common:options.emirate.abuDhabi') },
            { value: Emirate.Ajman, label: t('common:options.emirate.ajman') },
            { value: Emirate.Dubai, label: t('common:options.emirate.dubai') },
            { value: Emirate.Fujairah, label: t('common:options.emirate.fujairah') },
            { value: Emirate.RasAlKhaimah, label: t('common:options.emirate.rasAlKhaimah') },
            { value: Emirate.Sharjah, label: t('common:options.emirate.sharjah') },
        ];

        const referenceDetailRelationshipOptions = [
            {
                value: ReferenceDetailRelationship.Friend,
                label: t('common:options.referenceDetailRelationship.friend'),
            },
            {
                value: ReferenceDetailRelationship.Relative,
                label: t('common:options.referenceDetailRelationship.relative'),
            },
        ];

        const downPaymentToOptions = [
            { value: null, label: t('common:none') },
            { value: ApplicationQuotationDownPaymentTarget.Branch, label: t('common:options.downPaymentTo.branch') },
            { value: ApplicationQuotationDownPaymentTarget.Dealer, label: t('common:options.downPaymentTo.dealer') },
        ];

        const vatInclusionOptions = [
            { value: true, label: t('common:options.vatInclusion.include') },
            { value: false, label: t('common:options.vatInclusion.exclude') },
        ];

        const ttbFlag3dsOptions = [
            { value: TtbPaymentFlag3ds.Yes, label: t('common:options.yesNo.yes') },
            { value: TtbPaymentFlag3ds.No, label: t('common:options.yesNo.no') },
        ];

        const autoplayVersionOptions = [
            { value: AutoplayApiVersion.V3, label: t('common:options.autoplayApiVersion.v3') },
        ];

        const finderVehicleConditionOptions = [
            { value: FinderVehicleCondition.New, label: t('common:options.finderVehicleCondition.new') },
            { value: FinderVehicleCondition.Preowned, label: t('common:options.finderVehicleCondition.preowned') },
        ];

        const purchaseIntentionOptions = [
            { value: PurchaseIntention.WithinAMonth, label: t('common:options.purchaseIntention.withinAMonth') },
            {
                value: PurchaseIntention.InOneToThreeMonths,
                label: t('common:options.purchaseIntention.inOneToThreeMonths'),
            },
            {
                value: PurchaseIntention.InThreeToSixMonths,
                label: t('common:options.purchaseIntention.inThreeToSixMonths'),
            },
            {
                value: PurchaseIntention.InMoreThanSixMonths,
                label: t('common:options.purchaseIntention.inMoreThanSixMonths'),
            },
        ];

        const noClaimDiscountOptions = [
            { value: 0, label: '0%' },
            { value: 10, label: '10%' },
            { value: 20, label: '20%' },
            { value: 30, label: '30%' },
            { value: 40, label: '40%' },
            { value: 50, label: '50%' },
        ];

        const insuranceProductTypeOptions = [
            {
                value: InsuranceProductType.Eazy,
                label: t('common:options.insuranceProductType.eazy'),
            },
            {
                value: InsuranceProductType.ErgoLookupTable,
                label: t('common:options.insuranceProductType.ergoLookupTable'),
            },
        ];

        const currentVehicleSourceOptions = [
            {
                value: CurrentVehicleSource.SoldByDealer,
                label: t('common:options.currentVehicleSource.soldByDealer'),
            },
            {
                value: CurrentVehicleSource.VehicleInHouseHold,
                label: t('common:options.currentVehicleSource.vehicleInHouseHold'),
            },
            {
                value: CurrentVehicleSource.PreviousOwnedVehicle,
                label: t('common:options.currentVehicleSource.previousOwnedVehicle'),
            },
            {
                value: CurrentVehicleSource.PurchaseAlternative,
                label: t('common:options.currentVehicleSource.purchaseAlternative'),
            },
            {
                value: CurrentVehicleSource.TradeInByDealer,
                label: t('common:options.currentVehicleSource.tradeInByDealer'),
            },
        ];

        const currentVehicleOwnership = [
            { value: true, label: t('common:options.currentVehicleOwnership.yes') },
            { value: false, label: t('common:options.currentVehicleOwnership.no') },
        ];

        const currentVehicleEquipmentLine = [
            { value: CurrentVehicleEquipmentLine.Basic, label: t('common:options.currentVehicleEquipmentLine.basic') },
            { value: CurrentVehicleEquipmentLine.Sport, label: t('common:options.currentVehicleEquipmentLine.sport') },
            {
                value: CurrentVehicleEquipmentLine.Premium,
                label: t('common:options.currentVehicleEquipmentLine.premium'),
            },
            {
                value: CurrentVehicleEquipmentLine.FullyEquipped,
                label: t('common:options.currentVehicleEquipmentLine.fullyEquipped'),
            },
            { value: CurrentVehicleEquipmentLine.Other, label: t('common:options.currentVehicleEquipmentLine.other') },
        ];

        const currentVehicleEngineType = [
            { value: EngineType.Petrol, label: t('common:options.engineType.petrol') },
            { value: EngineType.Diesel, label: t('common:options.engineType.diesel') },
            { value: EngineType.Hybrid, label: t('common:options.engineType.hybrid') },
            { value: EngineType.Electric, label: t('common:options.engineType.electric') },
        ];

        const leadStageOptions = [
            { value: LeadStageOption.Lead, label: t('common:options.leadStage.Lead') },
            { value: LeadStageOption.Contact, label: t('common:options.leadStage.Contact') },
            { value: LeadStageOption.LeadAndContact, label: t('common:options.leadStage.LeadAndContact') },
        ];

        const purposeOfVisitOptions = [
            {
                value: PurposeOfVisit.NewVehicleEnquiryPurchase,
                label: t('common:options.purposeOfVisit.newVehicleEnquiryPurchase'),
            },
            {
                value: PurposeOfVisit.PreOwnedVehicleEnquiryPurchase,
                label: t('common:options.purposeOfVisit.preOwnedVehicleEnquiryPurchase'),
            },
            {
                value: PurposeOfVisit.VehicleConfigurationConsultation,
                label: t('common:options.purposeOfVisit.vehicleConfigurationConsultation'),
            },
            {
                value: PurposeOfVisit.InterestInTestDrive,
                label: t('common:options.purposeOfVisit.interestInTestDrive'),
            },
            {
                value: PurposeOfVisit.VehicleCollectionDelivery,
                label: t('common:options.purposeOfVisit.vehicleCollectionDelivery'),
            },
            {
                value: PurposeOfVisit.BrandExperienceEventAttendance,
                label: t('common:options.purposeOfVisit.brandExperienceEventAttendance'),
            },
            {
                value: PurposeOfVisit.AccessoriesMerchandiseShopping,
                label: t('common:options.purposeOfVisit.accessoriesMerchandiseShopping'),
            },
            { value: PurposeOfVisit.TradeInEvaluation, label: t('common:options.purposeOfVisit.tradeInEvaluation') },
        ];

        const intentTypeOptions = [
            { value: IntentType.ScheduledAppointment, label: t('common:options.intentType.scheduledAppointment') },
            { value: IntentType.WalkIn, label: t('common:options.intentType.walkIn') },
        ];

        const layoutOptions = [
            { value: LayoutType.BasicPro, label: t('common:options.layout.basicPro') },
            { value: LayoutType.PorscheV3, label: t('common:options.layout.porscheV3') },
        ];

        const salesOfferAgreementKinds = [
            { value: SalesOfferAgreementKind.Coe, label: t('common:options.salesOfferAgreement.coe') },
            {
                value: SalesOfferAgreementKind.Specification,
                label: t('common:options.salesOfferAgreement.specification'),
            },
            { value: SalesOfferAgreementKind.Vsa, label: t('common:options.salesOfferAgreement.vsa') },
        ];

        return {
            decimalsOptions,
            calculationRoundingOptions,
            emailProviderOptions,
            bankIntegrationProviderOptions,
            drivingLicenseTypeOptions,
            drivingLicenceValidityCodes,
            validDrivingLicenseClasses: drivingLicenseClasses.filter(({ value }) => value.startsWith('3')),
            drivingLicenseClasses,
            myDrivingLicenseClasses,
            smsProviderOptions,
            mfaSettingsTypeOptions,
            transportProtocolOptions,
            themeOptions: sortBy(theme => theme.label, themeOptions),
            ApplicationStageOptions,
            configurator,
            stockStatuses,
            titleOptions,
            nonBinaryTitleOptions,
            salutationOptions,
            salutationBMWOptions,
            antdIconOptions,
            maskDirectionOptions,
            maskCountOptions,
            webpageBlockPosition,
            webPageBlockBackgroundOptions,
            webpageBlockType,
            citizenshipOptions,
            yesNoDropdown,
            uaeDrivingLicenseTypeOptions,
            transmissionType,
            dayOfWeek,
            jobTitleList,
            jobTitleThList,
            employmentStatus,
            relationshipApplicant,
            residentialStatusList,
            insurerIntegrationProviderOptions,
            addressTypeOptions,
            educationOptions,
            incomeTypeOptions,
            residenceTypeOptions,
            emirateOptions,
            referenceDetailRelationshipOptions,
            downPaymentToOptions,
            vatInclusionOptions,
            ttbFlag3dsOptions,
            autoplayVersionOptions,
            finderVehicleConditionOptions,
            purchaseIntentionOptions,
            noClaimDiscountOptions,
            insuranceProductTypeOptions,
            currentVehicleSourceOptions,
            currentVehicleOwnership,
            currentVehicleEquipmentLine,
            currentVehicleEngineType,
            leadStageOptions,
            layoutOptions,
            purposeOfVisitOptions,
            intentTypeOptions,
            salesOfferAgreementKinds,
        };
    }, [includeMyInfoValues, t, countryCode]);
};

export default useSystemOptions;
