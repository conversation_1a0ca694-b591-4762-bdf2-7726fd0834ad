import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CoeCategory, SalesOfferDepositMethod } from '../api/types';

const useSalesOfferOptions = () => {
    const { t } = useTranslation('launchpadSalesOfferDetails');

    return useMemo(() => {
        const salesOfferDepositMethods = [
            {
                value: SalesOfferDepositMethod.Online,
                label: t('launchpadSalesOfferDetails:options.deposit.depositMethod.online'),
            },
            {
                value: SalesOfferDepositMethod.Offline,
                label: t('launchpadSalesOfferDetails:options.deposit.depositMethod.offline'),
            },
        ];

        const salesOfferCoeCategories = [
            {
                value: CoeCategory.B,
                label: t('launchpadSalesOfferDetails:options.mainDetails.coeCategory.b'),
            },
            {
                value: CoeCategory.E,
                label: t('launchpadSalesOfferDetails:options.mainDetails.coeCategory.e'),
            },
        ];

        return { salesOfferDepositMethods, salesOfferCoeCategories };
    }, [t]);
};

export default useSalesOfferOptions;
