/* eslint-disable max-len */
import { DebugJourneyDataFragment } from '../../api/fragments/DebugJourneyData';
import type { ConfiguratorApplicationState } from '../../pages/portal/ConfiguratorApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../../pages/portal/EventApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from '../../pages/portal/FinderApplicationPublicAccessEntrypoint/shared';
import { LaunchpadApplicationState } from '../../pages/portal/LaunchPadApplicationEntrypoint/utils/types';
import type { MobilityApplicationState } from '../../pages/portal/MobilityApplicationEntrypoint/Journey/shared';
import type { StandardApplicationState } from '../../pages/portal/StandardApplicationEntrypoint/Journey/shared';

export type AllowedApplicationForSigning =
    | StandardApplicationState
    | EventApplicationState
    | ConfiguratorApplicationState
    | FinderApplicationState
    | MobilityApplicationState
    | LaunchpadApplicationState;

export const ensureApplicationForSigning = (application: DebugJourneyDataFragment['application']) => {
    switch (application.__typename) {
        case 'StandardApplication':
            return application as StandardApplicationState;

        case 'FinderApplication':
            return application as FinderApplicationState;

        case 'ConfiguratorApplication':
            return application as ConfiguratorApplicationState;

        case 'EventApplication':
            return application as EventApplicationState;

        case 'MobilityApplication':
            return application as MobilityApplicationState;

        case 'LaunchpadApplication':
            return application as LaunchpadApplicationState;

        default:
            throw new Error('Application not supported for signing');
    }
};
