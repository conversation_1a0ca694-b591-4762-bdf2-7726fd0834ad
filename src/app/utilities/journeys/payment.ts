/* eslint-disable max-len */
import { DebugJourneyDataFragment } from '../../api/fragments/DebugJourneyData';
import type { ConfiguratorApplicationState } from '../../pages/portal/ConfiguratorApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../../pages/portal/EventApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from '../../pages/portal/FinderApplicationPublicAccessEntrypoint/shared';
import type { MobilityApplicationState } from '../../pages/portal/MobilityApplicationEntrypoint/Journey/shared';
import type { StandardApplicationState } from '../../pages/portal/StandardApplicationEntrypoint/Journey/shared';

export type AllowedApplicationForPayment =
    | StandardApplicationState
    | EventApplicationState
    | ConfiguratorApplicationState
    | MobilityApplicationState
    | FinderApplicationState;

export const ensureApplicationForPayment = (application: DebugJourneyDataFragment['application']) => {
    switch (application.__typename) {
        case 'StandardApplication':
            return application as StandardApplicationState;

        case 'FinderApplication':
            return application as FinderApplicationState;

        case 'MobilityApplication':
            return application as MobilityApplicationState;

        case 'ConfiguratorApplication':
            return application as ConfiguratorApplicationState;

        case 'EventApplication':
            return application as EventApplicationState;

        default:
            throw new Error('Application not supported for payment');
    }
};

export const getMobilityPromoCodeAmount = (application: AllowedApplicationForPayment) =>
    application.__typename === 'MobilityApplication' ? application.promoCodeAmount || 0 : 0;
