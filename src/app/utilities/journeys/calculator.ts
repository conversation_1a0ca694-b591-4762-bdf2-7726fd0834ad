/* eslint-disable max-len */
import dayjs from 'dayjs';
import replaceCurlyBracketString from '../../../server/utils/replaceCurlyBracketString';
import { ApplicationMarketTypeFragmentFragment } from '../../api/fragments/ApplicationMarketTypeFragment';
import { ApplicationMarket, ApplicationScenario } from '../../api/types';
import { GenericCalculatorValues } from '../../calculator/types';
import {
    hasFinancingScenario,
    hasInsuranceScenario,
} from '../../pages/admin/ModuleDetailsPage/modules/implementations/shared';
import type { ConfiguratorApplicationState } from '../../pages/portal/ConfiguratorApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from '../../pages/portal/FinderApplicationPublicAccessEntrypoint/shared';
import type { StandardApplicationState } from '../../pages/portal/StandardApplicationEntrypoint/Journey/shared';
import { DefaultMarketTypeValue } from '../useDefaultMarketTypeValue';

export type AllowedApplicationForCalculator =
    | StandardApplicationState
    | ConfiguratorApplicationState
    | FinderApplicationState;

export const getVehicleValuesForCalculator = (application: AllowedApplicationForCalculator) => {
    switch (application.__typename) {
        case 'StandardApplication':
        case 'ConfiguratorApplication': {
            if (application.vehicle.__typename !== 'LocalVariant') {
                throw new Error('unexpected vehicle type');
            }

            return { vehicle: application.vehicle.id, carPrice: application.vehicle.vehiclePrice };
        }

        case 'FinderApplication': {
            if (application.vehicle.__typename !== 'FinderVehicle') {
                throw new Error('unexpected vehicle type');
            }

            return { vehicle: application.vehicle.id, carPrice: application.vehicle.listing.price.value };
        }

        default:
            throw new Error('Application not supported for calculator');
    }
};

export const ensureApplicationForCalculator = (application: AllowedApplicationForCalculator) => {
    switch (application.__typename) {
        case 'StandardApplication':
        case 'ConfiguratorApplication':
        case 'FinderApplication':
            return application;

        default:
            throw new Error('Application not supported for calculator');
    }
};

// It's the same with getApplyForFinancing
// but it's inside server, so we can't use it here
export const getInitialWithFinancing = (scenarios: ApplicationScenario[]) => hasFinancingScenario(scenarios);

export const getInitialWithInsurancing = (scenarios: ApplicationScenario[]) => hasInsuranceScenario(scenarios);

export const getInitialCalculatorValues = (
    marketTypeValue: DefaultMarketTypeValue,
    marketType?: ApplicationMarketTypeFragmentFragment
) => {
    switch (marketType?.__typename) {
        case 'NewZealandApplicationMarket':
            return {
                market: ApplicationMarket.NewZealand,
                ppsr: marketTypeValue.ppsr,
                estFee: marketTypeValue.estFee,
                bankEstFee: marketTypeValue.bankEstFee,
            };

        case 'SingaporeApplicationMarket':
            return {
                market: ApplicationMarket.Singapore,
                coe: marketTypeValue.coe,
            };

        default:
            return {
                market: ApplicationMarket.Default,
            };
    }
};

export const getInitialInsuranceCalculatorValues = (withInsurance: boolean): Partial<GenericCalculatorValues> => {
    if (!withInsurance) {
        return {};
    }

    return {
        isInsuranceEnabled: true,
        noClaimDiscount: 50,
        dateOfRegistration: dayjs(),
        yearsOfDriving: 8,
        insurancePremium: undefined,
        dateOfBirth: dayjs().subtract(18, 'years'),
    };
};

export const getCalculatorValuesFromApplication = (
    marketType: ApplicationMarketTypeFragmentFragment,
    marketTypeValue: DefaultMarketTypeValue,
    application?: AllowedApplicationForCalculator
): Partial<GenericCalculatorValues> => {
    const ensuredApplication = ensureApplicationForCalculator(application);

    const { financing } = ensuredApplication;

    const result: Partial<GenericCalculatorValues> = {
        ...financing,
        ...getVehicleValuesForCalculator(application),
        financeProduct: financing?.financeProductId,
    };

    switch (financing?.__typename) {
        case 'NewZealandApplicationFinancing': {
            if (marketType.__typename !== 'NewZealandApplicationMarket') {
                throw new Error('Invalid market type');
            }

            return {
                ...result,
                market: ApplicationMarket.NewZealand,
                ppsr: financing.ppsr ?? marketTypeValue.ppsr,
                estFee: financing.estFee ?? marketTypeValue.estFee,
                bankEstFee: financing.bankEstFee ?? marketTypeValue.bankEstFee,
            };
        }

        case 'SingaporeApplicationFinancing': {
            if (marketType.__typename !== 'SingaporeApplicationMarket') {
                throw new Error('Invalid market type');
            }

            return {
                ...result,
                market: ApplicationMarket.Singapore,
                coe: financing.coe ?? marketTypeValue.coe,
            };
        }

        default:
            return {
                ...result,
                market: ApplicationMarket.Default,
            };
    }
};

export const getInsuranceValuesFromApplication = (
    withInsurance: boolean,
    application?: AllowedApplicationForCalculator
): Partial<GenericCalculatorValues> => {
    if (!withInsurance) {
        return {};
    }

    const ensuredApplication = ensureApplicationForCalculator(application);

    const { insurancing } = ensuredApplication;

    return {
        isInsuranceEnabled: true,
        noClaimDiscount: insurancing?.ncd ?? 50,
        dateOfRegistration: insurancing?.dateOfRegistration ? dayjs(insurancing.dateOfRegistration) : dayjs(),
        yearsOfDriving: insurancing?.yearsOfDriving ?? 8,
        insurancePremium: insurancing?.insurancePremium ?? 0,
        dateOfBirth: insurancing?.dateOfBirth ? dayjs(insurancing.dateOfBirth) : dayjs().subtract(18, 'years'),
    };
};

export const fillDynamicDisclaimerTextValues = (text: string, params?: { [key: string]: string | number }) =>
    replaceCurlyBracketString(text, params);
