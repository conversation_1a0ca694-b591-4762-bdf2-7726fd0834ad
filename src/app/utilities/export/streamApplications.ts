/**
 * Stream export applications excel
 *
 * This used in application list with timeframe filter for large exports
 */

import dayjs from 'dayjs';
import { nanoid } from 'nanoid';
import { ApplicationStage, PeriodPayload } from '../../api/types';
import getApplicationFileName from '../getApplicationFileName';
import { ExportFormat } from './types';

type StreamExportApplicationsType = {
    dealerIds: string[];
    stage: ApplicationStage;
    moduleIds: string[];
    eventId?: string;
    period: PeriodPayload;
    companyName: string;
    token: string | null;
    format: ExportFormat;
    languageId?: string;
};

const streamExportApplications = async ({
    dealerIds,
    stage,
    moduleIds,
    eventId,
    companyName,
    period,
    token,
    format,
    languageId,
}: StreamExportApplicationsType) => {
    const nonce = nanoid();
    const headers = {
        'Content-Type': 'application/json',
        'X-Timezone': dayjs.tz.guess(),
        Authorization: `Bearer ${token}`,
    };

    const body = {
        moduleIds,
        eventId,
        dealerIds,
        stage,
        period,
        format,
        nonce,
        languageId,
    };

    const runExport = async (capPurpose?: string[], filePurpose?: string[]) => {
        const generatedFilenames: string[] = [];

        if (Array.isArray(filePurpose) && filePurpose.length > 0) {
            filePurpose.forEach(purpose => {
                generatedFilenames.push(getApplicationFileName(companyName, period, stage, purpose));
            });
        } else {
            generatedFilenames.push(getApplicationFileName(companyName, period, stage));
        }

        const response = await fetch(`/api/export/applications/stream`, {
            method: 'POST',
            headers,
            body: JSON.stringify({
                ...body,
                ...(capPurpose && { capPurpose }),
                filename: generatedFilenames,
            }),
        });

        return response;
    };

    // For CAP format, send both purposes in a single request
    const result =
        format === 'cap'
            ? await runExport(['BP_UPLOAD', 'BP_LEAD_UPLOAD'], ['BP', 'BP_LEAD'])
            : await runExport([], []);

    return result;
};

export default streamExportApplications;
