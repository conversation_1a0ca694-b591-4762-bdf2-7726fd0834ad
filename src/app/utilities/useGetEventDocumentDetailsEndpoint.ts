import { useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import urlJoin from 'url-join';
import { LeadStageOption } from '../api';
import { useRouter } from '../components/contexts/shared';
import useConsolePermissions from '../layouts/ConsoleLayout/useConsolePermissions';
import { ApplicationStage } from './getApplicationFileName';

export const getDocType = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.Appointment:
            return 'appointments';

        case ApplicationStage.Lead:
            return 'leads';

        case ApplicationStage.Reservation:
            return 'reservations';

        case ApplicationStage.Financing:
            return 'applications';

        case ApplicationStage.Insurance:
            return 'insurances';

        case ApplicationStage.Mobility:
            return 'mobilitybookings';

        case ApplicationStage.VisitAppointment:
            return 'showroomvisits';

        case ApplicationStage.TradeIn:
            return 'tradeInStage';

        default:
            throw new Error('Application stage is not valid');
    }
};

const useGetEventDocumentDetailsEndpointPrefix = (stage: ApplicationStage, isLeadStage: boolean) => {
    const router = useRouter();
    const location = useLocation();
    const { hasLeadsAndContacts } = useConsolePermissions();

    return useCallback(
        (isLead?: boolean) => {
            let navigateToDocumentDetailsEndpoint: string;
            const getPath = () => {
                if (!isLeadStage) {
                    return getDocType(stage);
                }

                if (hasLeadsAndContacts) {
                    return 'leadsAndContacts';
                }

                return isLead ? 'leads' : 'contacts';
            };

            const docType = getPath();

            if (router) {
                // check first wether route is in admin and it has admin
                // if it's admin, need to redirect.
                if (router.withAdmin) {
                    const isAdminRoute = location.pathname.startsWith('/admin');

                    if (isAdminRoute) {
                        navigateToDocumentDetailsEndpoint = urlJoin('/admin', docType);

                        return navigateToDocumentDetailsEndpoint;
                    }
                }

                if (isLeadStage) {
                    const documentDetailsEndpoint = router.endpoints.find(endpoint => {
                        if (endpoint.__typename !== 'LeadListEndpoint') {
                            return false;
                        }

                        const stages = [
                            LeadStageOption.LeadAndContact,
                            isLead ? LeadStageOption.Lead : LeadStageOption.Contact,
                        ];

                        return stages.includes(endpoint.leadStage);
                    });

                    if (documentDetailsEndpoint) {
                        navigateToDocumentDetailsEndpoint = urlJoin('/', documentDetailsEndpoint.pathname);
                    }
                } else {
                    const documentDetailsEndpoint = router.endpoints.find(
                        endpoint =>
                            endpoint.__typename === 'ApplicationListEndpoint' && endpoint.applicationStage === stage
                    );

                    if (documentDetailsEndpoint) {
                        navigateToDocumentDetailsEndpoint = urlJoin('/', documentDetailsEndpoint.pathname);
                    }
                }
            } else {
                navigateToDocumentDetailsEndpoint = urlJoin('/admin', docType);
            }

            return navigateToDocumentDetailsEndpoint;
        },
        [hasLeadsAndContacts, isLeadStage, location.pathname, router, stage]
    );
};

export default useGetEventDocumentDetailsEndpointPrefix;
