import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ConditionType } from '../api/types';

const useConditionTypesOptions = (includeLogicConditions: boolean, conditionType?: ConditionType) => {
    const { t } = useTranslation('consentsAndDeclarations');

    return useMemo(() => {
        const logicConditionTypeOptions = [
            {
                value: ConditionType.Or,
                label: t('consentsAndDeclarations:options.conditionTypes.or'),
            },
        ];

        const applicationModuleConditionTypeOptions = [
            {
                value: ConditionType.IsApplicationModule,
                label: t('consentsAndDeclarations:options.conditionTypes.applicationModule'),
            },
        ];

        const bankConditionTypeOptions = [
            {
                value: ConditionType.IsBank,
                label: t('consentsAndDeclarations:options.conditionTypes.bank'),
            },
        ];

        const dealerConditionTypeOptions = [
            {
                value: ConditionType.IsDealer,
                label: t('consentsAndDeclarations:options.conditionTypes.dealer'),
            },
        ];

        const insurerConditionTypeOptions = [
            {
                value: ConditionType.IsInsurer,
                label: t('consentsAndDeclarations:options.conditionTypes.insurer'),
            },
        ];

        const locationConditionTypeOptions = [
            {
                value: ConditionType.IsLocation,
                label: t('consentsAndDeclarations:options.conditionTypes.location'),
            },
        ];

        const giftVoucherConditionTypeOptions = [
            {
                value: ConditionType.IsGiftVoucher,
                label: t('consentsAndDeclarations:options.conditionTypes.giftVoucher'),
            },
        ];

        const salesOfferAgreementsConditionTypeOptions = [
            {
                value: ConditionType.SalesOfferAgreements,
                label: t('consentsAndDeclarations:options.conditionTypes.salesOfferAgreements'),
            },
        ];

        const contextualConditionTypeOptions = [
            {
                value: ConditionType.IsApplicant,
                label: t('consentsAndDeclarations:options.conditionTypes.applicant'),
            },
            {
                value: ConditionType.IsGuarantor,
                label: t('consentsAndDeclarations:options.conditionTypes.guarantor'),
            },
            {
                value: ConditionType.IsCorporate,
                label: t('consentsAndDeclarations:options.conditionTypes.corporate'),
            },
            {
                value: ConditionType.IsTestDrive,
                label: t('consentsAndDeclarations:options.conditionTypes.testDrive'),
            },
            {
                value: ConditionType.IsShowroomVisit,
                label: t('consentsAndDeclarations:options.conditionTypes.showroomVisit'),
            },
            {
                value: ConditionType.IsTradeIn,
                label: t('consentsAndDeclarations:options.conditionTypes.tradeIn'),
            },
            {
                value: ConditionType.IsApplyingForFinancing,
                label: t('consentsAndDeclarations:options.conditionTypes.applyingForFinancing'),
            },
            {
                value: ConditionType.IsApplyingForInsurance,
                label: t('consentsAndDeclarations:options.conditionTypes.applyingForInsurance'),
            },
            {
                value: ConditionType.WithMyinfo,
                label: t('consentsAndDeclarations:options.conditionTypes.withMyinfo'),
            },
            {
                value: ConditionType.WithoutMyinfo,
                label: t('consentsAndDeclarations:options.conditionTypes.withoutMyinfo'),
            },
            {
                value: ConditionType.IsTestDriveProcess,
                label: t('consentsAndDeclarations:options.conditionTypes.isTestDriveProcess'),
            },
            {
                value: ConditionType.ForCapQualification,
                label: t('consentsAndDeclarations:options.conditionTypes.forCapQualification'),
            },
        ];

        let conditionTypeOptions = [];
        if (includeLogicConditions) {
            conditionTypeOptions = [
                ...logicConditionTypeOptions,
                ...applicationModuleConditionTypeOptions,
                ...bankConditionTypeOptions,
                ...dealerConditionTypeOptions,
                ...contextualConditionTypeOptions,
                ...insurerConditionTypeOptions,
                ...locationConditionTypeOptions,
                ...giftVoucherConditionTypeOptions,
                ...salesOfferAgreementsConditionTypeOptions,
            ];
        } else if (conditionType) {
            switch (conditionType) {
                case ConditionType.IsApplicationModule:
                    conditionTypeOptions = [...applicationModuleConditionTypeOptions];
                    break;

                case ConditionType.IsBank:
                    conditionTypeOptions = [...bankConditionTypeOptions];
                    break;

                case ConditionType.IsDealer:
                    conditionTypeOptions = [...dealerConditionTypeOptions];
                    break;

                case ConditionType.IsInsurer:
                    conditionTypeOptions = [...insurerConditionTypeOptions];
                    break;

                case ConditionType.IsLocation:
                    conditionTypeOptions = [...locationConditionTypeOptions];
                    break;

                case ConditionType.IsGiftVoucher:
                    conditionTypeOptions = [...giftVoucherConditionTypeOptions];
                    break;

                case ConditionType.SalesOfferAgreements:
                    conditionTypeOptions = [...salesOfferAgreementsConditionTypeOptions];
                    break;

                default:
                    conditionTypeOptions = [...contextualConditionTypeOptions];
                    break;
            }
        } else {
            conditionTypeOptions = [
                ...applicationModuleConditionTypeOptions,
                ...bankConditionTypeOptions,
                ...dealerConditionTypeOptions,
                ...contextualConditionTypeOptions,
                ...locationConditionTypeOptions,
                ...giftVoucherConditionTypeOptions,
                ...salesOfferAgreementsConditionTypeOptions,
            ];
        }

        return { conditionTypeOptions };
    }, [conditionType, includeLogicConditions, t]);
};

export default useConditionTypesOptions;
