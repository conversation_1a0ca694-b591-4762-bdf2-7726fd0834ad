import { Col } from 'antd';
import { defaultFilterOption } from '../../components/fields/SelectField';
import { useThemeComponents } from '../../themes/hooks';
import type { SelectFieldProps } from '../../themes/porsche/Fields/shared';
import type { KycPresetFieldProps } from './shared';

type StringDescriptionFieldProps = Omit<KycPresetFieldProps, 'customerType'> &
    Pick<SelectFieldProps, 'options'> & {
        disabled?: boolean;
        label: string;
        name: string;
        isOthers: boolean;
        othersLabel: string;
    };

const StringDescriptionField = ({
    field,
    colSpan,
    label,
    othersLabel,
    name,
    options,
    isOthers,
    disabled = false,
    fromJourney = true,
}: StringDescriptionFieldProps) => {
    const { FormFields } = useThemeComponents();

    return (
        <>
            <Col key={field.key} {...colSpan}>
                <FormFields.SelectField
                    disabled={disabled}
                    filterOption={defaultFilterOption}
                    label={label}
                    name={`${name}.value`}
                    options={options}
                    required={field.isRequired}
                    showSearch
                />
            </Col>

            {isOthers && (
                <Col key={`${field.key}_other`} {...colSpan}>
                    <FormFields.InputField
                        disabled={disabled}
                        label={othersLabel}
                        name={`${name}.description`}
                        required
                    />
                </Col>
            )}
        </>
    );
};

export default StringDescriptionField;
