import { Col, ColProps } from 'antd';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { TFunction } from 'i18next';
import { get } from 'lodash/fp';
import type { KycFieldSpecsFragment } from '../../../api/fragments/KYCFieldSpecs';
import { DrivingLicenseType, LocalCustomerFieldKey } from '../../../api/types';
import { useCountryPresetValue } from '../../../components/fields/NationalityField';
// eslint-disable-next-line max-len
import type { ApplicantFormValues } from '../../../pages/portal/ConfiguratorApplicationEntrypoint/ApplicantKYCPage/shared';
import { useThemeComponents } from '../../../themes/hooks';
import { allowedExtensions } from '../../extensions';
import useSystemOptions from '../../useSystemOptions';
import { getKycFieldLabel } from '../shared';
import {
    useCountryDisabledLabel,
    useDrivingLicenseDisabledLabel,
    useGenderDisabledLabel,
    useMaritalDisabledLabel,
    useNationalityDisabledLabel,
    useRaceDisabledLabel,
    useReferenceDetailSetDisabledLabel,
    useRegionDisabledLabel,
    useResidentDisabledLabel,
} from './shared';

type DisabledFieldKeyProps = {
    field: KycFieldSpecsFragment;
    prefixName: string;
    label: string;
    t: TFunction;
    colSpan: ColProps;
    fromMyinfo: boolean;
    country: string;
    values?: ApplicantFormValues;
    selectedFeatureLabel?: string;
};

const DrivingLicenseDisabledFieldKey = ({
    values,
    prefixName,
    country,
    fromMyinfo,
    colSpan,
    field,
    t,
    drivingLicenseVariables: {
        qualifiedExpiryDate,
        qualifiedIssueDate,
        selectedClass,
        selectedType,
        selectedValidity,
        type,
    },
}: DisabledFieldKeyProps & {
    drivingLicenseVariables: {
        qualifiedExpiryDate: string;
        qualifiedIssueDate: string;
        selectedClass: string;
        selectedType: string;
        selectedValidity: string;
        type: string;
    };
}) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <>
            <Col key={`${field.key}_type`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.drivingLicense.type.label`))}
                    value={selectedType}
                />
            </Col>
            {type === DrivingLicenseType.Qualified && (
                <>
                    <Col key={`${field.key}_validity`} {...colSpan}>
                        <DisplayField
                            label={getKycFieldLabel(
                                fromMyinfo,
                                t(`customerDetails:fields.drivingLicense.validity.label`)
                            )}
                            value={selectedValidity}
                        />
                    </Col>
                    <Col key={`${field.key}_expireDate`} {...colSpan}>
                        <DisplayField
                            label={getKycFieldLabel(
                                fromMyinfo,
                                t(`customerDetails:fields.drivingLicense.expireDate.label`)
                            )}
                            value={qualifiedExpiryDate}
                        />
                    </Col>
                    <Col key={`${field.key}_class`} {...colSpan}>
                        <DisplayField
                            label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.drivingLicense.class.label`))}
                            value={selectedClass}
                        />
                    </Col>
                    <Col key={`${field.key}_issueDate`} {...colSpan}>
                        <DisplayField
                            label={getKycFieldLabel(
                                fromMyinfo,
                                t(`customerDetails:fields.drivingLicense.issueDate.label`)
                            )}
                            value={qualifiedIssueDate}
                        />
                    </Col>
                </>
            )}
        </>
    );
};

const NationalityDisabledFieldKey = ({ field, colSpan, label, selectedFeatureLabel }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <Col key={field.key} {...colSpan}>
            <DisplayField label={label} value={selectedFeatureLabel} />
        </Col>
    );
};

const CountryDisabledFieldKey = ({ field, colSpan, label, selectedFeatureLabel }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <Col key={field.key} {...colSpan}>
            <DisplayField label={label} value={selectedFeatureLabel} />
        </Col>
    );
};

const RegionDisabledFieldKey = ({ field, colSpan, label, selectedFeatureLabel }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <Col key={field.key} {...colSpan}>
            <DisplayField label={label} value={selectedFeatureLabel} />
        </Col>
    );
};

const GenderDisabledFieldKey = ({ field, colSpan, label, values, selectedFeatureLabel }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <Col key={field.key} {...colSpan}>
            <DisplayField label={label} value={selectedFeatureLabel} />
        </Col>
    );
};

const UAEDrivingLicenseDisabledFieldKey = ({ field, colSpan, prefixName, values, t }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField, MultipleDraggerField },
    } = useThemeComponents();
    const fieldValue = get(`${prefixName}`, values);
    const isUAEResident = get('isUAEResident', fieldValue);
    const nationalList = useCountryPresetValue();

    return (
        <>
            {isUAEResident && (
                <Col key={`${field.key}_emiratesId`} {...colSpan}>
                    <DisplayField
                        {...t(`customerDetails:fields.drivingLicense.uaeResident.emiratesId`, {
                            returnObjects: true,
                        })}
                        value={fieldValue?.emiratesId}
                    />
                </Col>
            )}
            {!isUAEResident && (
                <>
                    <Col key={`${field.key}_uaeResident`} {...colSpan}>
                        <DisplayField
                            {...t(`customerDetails:fields.drivingLicense.uaeResident.passport`, {
                                returnObjects: true,
                            })}
                            value={fieldValue?.passport}
                        />
                    </Col>
                    <Col key={`${field.key}_issuedCountry`} {...colSpan}>
                        <DisplayField
                            {...t(`customerDetails:fields.drivingLicense.uaeResident.issuedCountry`, {
                                returnObjects: true,
                            })}
                            value={nationalList[fieldValue?.issuedCountry]?.label}
                        />
                    </Col>
                    <Col key={`${field.key}_issueDate`} {...colSpan}>
                        <DisplayField
                            {...t(
                                `customerDetails:fields.drivingLicense.uaeResident.${
                                    !isUAEResident ? 'foreignerDrivingLicenseIssueDate' : 'uaeDriverLicenseIssueDate'
                                }`,
                                { returnObjects: true }
                            )}
                            value={fieldValue?.issueDate ? dayjs(fieldValue.issueDate).format('DD MMM YYYY') : ''}
                        />
                    </Col>
                    <Col key={`${field.key}_expiryDate`} {...colSpan}>
                        <DisplayField
                            {...t(
                                `customerDetails:fields.drivingLicense.uaeResident.${
                                    !isUAEResident ? 'foreignerDrivingLicenseExpiryDate' : 'uaeDriverLicenseExpiryDate'
                                }`,
                                { returnObjects: true }
                            )}
                            value={fieldValue?.issueDate ? dayjs(fieldValue.expiryDate).format('DD MMM YYYY') : ''}
                        />
                    </Col>
                    <Col key={`${field.key}_uploadDLCopy`} {...colSpan}>
                        <MultipleDraggerField
                            {...t(`customerDetails:fields.drivingLicense.uaeResident.uploadDLCopy`, {
                                returnObjects: true,
                            })}
                            extensions={[...allowedExtensions.image, ...allowedExtensions.document]}
                            name={`${prefixName}.uploadDLCopy`}
                            disabled
                        />
                    </Col>
                    <Col key={`${field.key}_uploadEIDPassportCopy`} {...colSpan}>
                        <MultipleDraggerField
                            {...t(`customerDetails:fields.drivingLicense.uaeResident.uploadEIDPassportCopy`, {
                                returnObjects: true,
                            })}
                            extensions={[...allowedExtensions.image, ...allowedExtensions.document]}
                            name={`${prefixName}.uploadEIDPassportCopy`}
                            disabled
                        />
                    </Col>
                </>
            )}
        </>
    );
};

const UAEIdentitySetDisabledFieldKey = ({
    field,
    colSpan,
    prefixName,
    values,
    t,
    fromMyinfo,
}: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField, MultipleDraggerField },
    } = useThemeComponents();
    const fieldValue = get(`${prefixName}`, values);

    return (
        <>
            <Col key={`${field.key}_passportNumber`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.uaeIdSet.passportNumber.label`))}
                    value={fieldValue?.passportNumber}
                />
            </Col>
            <Col key={`${field.key}_passportExpiryDate`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.uaeIdSet.passportExpiryDate.label`))}
                    value={
                        fieldValue?.passportExpiryDate ? dayjs(fieldValue.passportExpiryDate).format('DD MMM YYYY') : ''
                    }
                />
            </Col>
            <Col key={`${field.key}_emiratesIdNumber`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.uaeIdSet.emiratesIdNumber.label`))}
                    value={fieldValue?.emiratesIdNumber}
                />
            </Col>
            <Col key={`${field.key}_emiratesIdExpiryDate`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(
                        fromMyinfo,
                        t(`customerDetails:fields.uaeIdSet.emiratesIdExpiryDate.label`)
                    )}
                    value={
                        fieldValue?.emiratesIdExpiryDate
                            ? dayjs(fieldValue.emiratesIdExpiryDate).format('DD MMM YYYY')
                            : ''
                    }
                />
            </Col>

            <Col key={`${field.key}_drivingLicenseNumber`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(
                        fromMyinfo,
                        t(`customerDetails:fields.uaeIdSet.drivingLicenseNumber.label`)
                    )}
                    value={fieldValue?.drivingLicenseNumber}
                />
            </Col>

            <Col key={`${field.key}_drivingLicenseExpiryDate`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(
                        fromMyinfo,
                        t(`customerDetails:fields.uaeIdSet.drivingLicenseExpiryDate.label`)
                    )}
                    value={
                        fieldValue?.drivingLicenseExpiryDate
                            ? dayjs(fieldValue.drivingLicenseExpiryDate).format('DD MMM YYYY')
                            : ''
                    }
                />
            </Col>
            <Col key={`${field.key}_uaeResidenceSince`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.uaeIdSet.uaeResidenceSince.label`))}
                    value={
                        fieldValue?.uaeResidenceSince ? dayjs(fieldValue.uaeResidenceSince).format('DD MMM YYYY') : ''
                    }
                />
            </Col>
            <Col key={`${field.key}_uaeVisaNumber`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.uaeIdSet.uaeVisaNumber.label`))}
                    value={fieldValue?.uaeVisaNumber}
                />
            </Col>
            <Col key={`${field.key}_uaeVisaIssueDate`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.uaeIdSet.uaeVisaIssueDate.label`))}
                    value={fieldValue?.uaeVisaIssueDate ? dayjs(fieldValue.uaeVisaIssueDate).format('DD MMM YYYY') : ''}
                />
            </Col>
            <Col key={`${field.key}_uaeVisaExpiryDate`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.uaeIdSet.uaeVisaExpiryDate.label`))}
                    value={
                        fieldValue?._uaeVisaExpiryDate ? dayjs(fieldValue._uaeVisaExpiryDate).format('DD MMM YYYY') : ''
                    }
                />
            </Col>
            <Col key={`${field.key}_supportingDocument`} {...colSpan}>
                <MultipleDraggerField
                    extensions={[...allowedExtensions.image, ...allowedExtensions.document]}
                    label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.uaeIdSet.supportingDocument.label`))}
                    name={`${prefixName}.supportingDocuments`}
                    disabled
                />
            </Col>
        </>
    );
};

const ResidentDisabledFieldKey = ({ colSpan, field, label, selectedFeatureLabel }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <Col key={field.key} {...colSpan}>
            <DisplayField label={label} value={selectedFeatureLabel} />
        </Col>
    );
};

const SalaryTransferredBankSetDisabledFieldKey = ({
    colSpan,
    field,
    fromMyinfo,
    prefixName,
    t,
    values,
}: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField, MultipleDraggerField },
    } = useThemeComponents();
    const fieldValue = get(`${prefixName}`, values);
    const { yesNoDropdown } = useSystemOptions(true);

    return (
        <>
            <Col key={`${field.key}_enabled`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(
                        fromMyinfo,
                        t(`customerDetails:fields.salaryTransferredBank.enabled.label`)
                    )}
                    value={yesNoDropdown.find(option => option.value === fieldValue?.enabled)?.label}
                />
            </Col>
            <Col key={`${field.key}_bankName`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(
                        fromMyinfo,
                        t(`customerDetails:fields.salaryTransferredBank.bankName.label`)
                    )}
                    value={fieldValue?.bankName}
                />
            </Col>
            <Col key={`${field.key}_bankAccountNumber`} {...colSpan}>
                <DisplayField
                    label={getKycFieldLabel(
                        fromMyinfo,
                        t(`customerDetails:fields.salaryTransferredBank.bankAccountNumber.label`)
                    )}
                    value={fieldValue?.bankAccountNumber}
                />
            </Col>
            <Col key={`${field.key}_supportingDocument`} {...colSpan}>
                <MultipleDraggerField
                    extensions={[...allowedExtensions.image, ...allowedExtensions.document]}
                    label={getKycFieldLabel(
                        fromMyinfo,
                        t(`customerDetails:fields.salaryTransferredBank.supportingDocument.label`)
                    )}
                    name={`${prefixName}.supportingDocuments`}
                    disabled
                />
            </Col>
        </>
    );
};

const MaritalDisabledFieldKey = ({ colSpan, field, label, selectedFeatureLabel }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <Col key={field.key} {...colSpan}>
            <DisplayField label={label} value={selectedFeatureLabel} />
        </Col>
    );
};

const RaceDisabledFieldKey = ({ colSpan, field, label, selectedFeatureLabel }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <Col key={field.key} {...colSpan}>
            <DisplayField label={label} value={selectedFeatureLabel} />
        </Col>
    );
};

const DisabledFieldKey = ({ colSpan, country, field, fromMyinfo, label, prefixName, t }: DisabledFieldKeyProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();
    const { values } = useFormikContext<ApplicantFormValues>();
    const { nonBinaryTitleOptions } = useSystemOptions();

    const fieldValue = get(`${prefixName}`, values);

    switch (field.key) {
        case LocalCustomerFieldKey.JobTitle:
        case LocalCustomerFieldKey.JobTitleTh:
        case LocalCustomerFieldKey.RelationshipWithApplicant:
        case LocalCustomerFieldKey.ResidentialStatusVwfs:
            return (
                <Col key={field.key} {...colSpan}>
                    <DisplayField label={label} value={fieldValue?.value?.value} />
                </Col>
            );

        case LocalCustomerFieldKey.Title:
        case LocalCustomerFieldKey.NonBinaryTitle: {
            const titleLabel = nonBinaryTitleOptions.find(title => title.value === fieldValue?.value)?.label;

            return (
                <Col key={field.key} {...colSpan}>
                    <DisplayField label={label} value={titleLabel} />
                </Col>
            );
        }

        case LocalCustomerFieldKey.CreationDate:
        case LocalCustomerFieldKey.Birthday:
        case LocalCustomerFieldKey.CorporateRegistrationDate:
        case LocalCustomerFieldKey.DriverLicensePassDate:
        case LocalCustomerFieldKey.DateOfJoining:
        case LocalCustomerFieldKey.PreferredFirstPaymentDate: {
            const updatedValue = fieldValue?.value ? dayjs(fieldValue?.value).format('DD MMM YYYY') : '';

            return (
                <Col key={field.key} {...colSpan}>
                    <DisplayField label={label} value={updatedValue} />
                </Col>
            );
        }

        case LocalCustomerFieldKey.Nationality: {
            const { selectedFeatureLabel } = useNationalityDisabledLabel(prefixName, values);

            return (
                <NationalityDisabledFieldKey
                    key={field.key}
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    selectedFeatureLabel={selectedFeatureLabel}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.Country: {
            const { selectedFeatureLabel } = useCountryDisabledLabel(prefixName, values);

            return (
                <CountryDisabledFieldKey
                    key={field.key}
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    selectedFeatureLabel={selectedFeatureLabel}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.Region: {
            const { selectedFeatureLabel } = useRegionDisabledLabel(prefixName, t, values);

            return (
                <RegionDisabledFieldKey
                    key={field.key}
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    selectedFeatureLabel={selectedFeatureLabel}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.Phone:
        case LocalCustomerFieldKey.Telephone:
        case LocalCustomerFieldKey.CompanyPhone:
        case LocalCustomerFieldKey.CorporatePhone: {
            const value = fieldValue?.value
                ? `+${fieldValue?.value?.prefix ?? ''} ${fieldValue?.value?.value ?? ''}`
                : '';

            return (
                <Col key={field.key} {...colSpan}>
                    <DisplayField label={label} value={value} />
                </Col>
            );
        }

        case LocalCustomerFieldKey.Gender: {
            const { selectedFeatureLabel } = useGenderDisabledLabel(prefixName, t, values);

            return (
                <GenderDisabledFieldKey
                    key={field.key}
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    selectedFeatureLabel={selectedFeatureLabel}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.MaritalStatus: {
            const { selectedFeatureLabel } = useMaritalDisabledLabel(prefixName, t, values);

            return (
                <MaritalDisabledFieldKey
                    key={field.key}
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    selectedFeatureLabel={selectedFeatureLabel}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.ResidentialStatus: {
            const { selectedFeatureLabel } = useResidentDisabledLabel(prefixName, t, values);

            return (
                <ResidentDisabledFieldKey
                    key={field.key}
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    selectedFeatureLabel={selectedFeatureLabel}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.Race: {
            const { selectedFeatureLabel } = useRaceDisabledLabel(prefixName, values);

            return (
                <RaceDisabledFieldKey
                    key={field.key}
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    selectedFeatureLabel={selectedFeatureLabel}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.DrivingLicense:
        case LocalCustomerFieldKey.DrivingLicenseTh:
        case LocalCustomerFieldKey.DrivingLicenseMy: {
            const drivingLicenseVariables = useDrivingLicenseDisabledLabel(prefixName, country, fromMyinfo, values);

            return (
                <DrivingLicenseDisabledFieldKey
                    colSpan={colSpan}
                    country={country}
                    drivingLicenseVariables={drivingLicenseVariables}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.UaeDrivingLicense: {
            return (
                <UAEDrivingLicenseDisabledFieldKey
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.ReferenceDetailSet: {
            const phoneValue = fieldValue?.contactNumber
                ? `+${fieldValue?.contactNumber?.prefix} ${fieldValue?.contactNumber?.value}`
                : '';

            const { selectedFeatureLabel } = useReferenceDetailSetDisabledLabel(prefixName, values);

            return (
                <>
                    <Col key={`${field.key}_name`} {...colSpan}>
                        <DisplayField
                            label={getKycFieldLabel(fromMyinfo, t(`customerDetails:fields.referenceDetail.name.label`))}
                            value={fieldValue?.name}
                        />
                    </Col>
                    <Col key={`${field.key}_relationship`} {...colSpan}>
                        <DisplayField
                            label={getKycFieldLabel(
                                fromMyinfo,
                                t(`customerDetails:fields.referenceDetail.relationship.label`)
                            )}
                            value={selectedFeatureLabel}
                        />
                    </Col>
                    <Col key={`${field.key}_contactNumber`} {...colSpan}>
                        <DisplayField
                            label={getKycFieldLabel(
                                fromMyinfo,
                                t(`customerDetails:fields.referenceDetail.contactNumber.label`)
                            )}
                            value={phoneValue}
                        />
                    </Col>
                </>
            );
        }

        case LocalCustomerFieldKey.SalaryTransferredBankSet: {
            return (
                <SalaryTransferredBankSetDisabledFieldKey
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    t={t}
                    values={values}
                />
            );
        }

        case LocalCustomerFieldKey.UaeIdentitySet: {
            return (
                <UAEIdentitySetDisabledFieldKey
                    colSpan={colSpan}
                    country={country}
                    field={field}
                    fromMyinfo={fromMyinfo}
                    label={label}
                    prefixName={prefixName}
                    t={t}
                    values={values}
                />
            );
        }

        default:
            return (
                <Col key={field.key} {...colSpan}>
                    <DisplayField label={label} value={fieldValue?.value} />
                </Col>
            );
    }
};

export default DisabledFieldKey;
