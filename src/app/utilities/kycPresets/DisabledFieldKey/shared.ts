import dayjs from 'dayjs';
import { TFunction } from 'i18next';
import { get, upperCase } from 'lodash/fp';
import { useMemo } from 'react';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { ApplicantFormValues } from '../../../pages/portal/ConfiguratorApplicationEntrypoint/ApplicantKYCPage/shared';
import useCountryList from '../../useCountryList';
import useGenderList from '../../useGenderList';
import useMaritalStatusList from '../../useMaritalStatus';
import useNationalityList from '../../useNationalityList';
import useRaceList from '../../useRaceList';
import useRegionList from '../../useRegionList';
import useResidentialStatusList from '../../useResidentialStatusList';
import useSystemOptions from '../../useSystemOptions';

export const useDrivingLicenseDisabledLabel = (
    prefixName: string,
    country: string,
    fromMyinfo: boolean,
    values?: ApplicantFormValues
) => {
    const prefixValue = get(`${prefixName}`, values);
    const fieldValue = prefixValue?.value[0];
    const type = get('type', fieldValue);

    const qualifiedExpiryDate = fieldValue?.expiryDate ? dayjs(fieldValue.expiryDate).format('DD MMM YYYY') : '';
    const qualifiedIssueDate = fieldValue?.issueDate ? dayjs(fieldValue.issueDate).format('DD MMM YYYY') : '';

    const {
        drivingLicenseTypeOptions,
        drivingLicenceValidityCodes,
        validDrivingLicenseClasses,
        drivingLicenseClasses: allDrivingLicenseClasses,
        myDrivingLicenseClasses,
    } = useSystemOptions(true);
    let drivingLicenseClasses = validDrivingLicenseClasses;

    return useMemo(() => {
        const selectedType = drivingLicenseTypeOptions.find(option => option.label === type)?.label;

        const selectedValidity = drivingLicenceValidityCodes.find(code => code.value === fieldValue?.validity)?.label;

        if (country === 'MY') {
            drivingLicenseClasses = myDrivingLicenseClasses;
        }

        if (fromMyinfo) {
            drivingLicenseClasses = allDrivingLicenseClasses;
        }
        const selectedClass = drivingLicenseClasses.find(option => option.value === fieldValue?.class)?.label;

        return {
            selectedType,
            selectedValidity,
            selectedClass,
            qualifiedExpiryDate,
            qualifiedIssueDate,
            type,
        };
    }, [drivingLicenseTypeOptions, drivingLicenceValidityCodes, drivingLicenseClasses, country, fromMyinfo]);
};

export const useNationalityDisabledLabel = (prefixName: string, values?: ApplicantFormValues) => {
    const fieldValue = get(`${prefixName}`, values);

    const { loading, data } = useNationalityList();

    return useMemo(() => {
        const selectedFeatureLabel = loading
            ? ''
            : data.find(nationality => nationality.value === upperCase(fieldValue?.value))?.label;

        return {
            selectedFeatureLabel,
        };
    }, [data, loading, fieldValue?.value]);
};

export const useCountryDisabledLabel = (prefixName: string, values?: ApplicantFormValues) => {
    const fieldValue = get(`${prefixName}`, values);

    const { data, loading } = useCountryList();

    return useMemo(() => {
        const selectedFeatureLabel = loading
            ? ''
            : data
                  .map(({ cca2, name: { common } }) => ({ value: cca2, label: common }))
                  .find(country => country.value === fieldValue?.value || country.label === fieldValue?.value)?.label;

        return {
            selectedFeatureLabel,
        };
    }, [data, loading, fieldValue?.value]);
};

export const useRegionDisabledLabel = (prefixName: string, t: TFunction, values?: ApplicantFormValues) => {
    const fieldValue = get(`${prefixName}`, values);
    const { data: countryListData, loading: countryListLoading } = useCountryList();
    const { data: regionListData, loading: regionListLoading } = useRegionList();
    const company = useCompany(true);

    return useMemo(() => {
        const selectedCountry = values?.customer?.fields?.Country;
        const selectedCountryCode = (() => {
            if (countryListLoading) {
                return null;
            }

            if (!selectedCountry?.value) {
                return company.countryCode;
            }

            return countryListData.find(({ name: { common: countryName } }) => countryName === selectedCountry?.value)
                ?.cca2;
        })();

        const regionsByCountry =
            !regionListLoading && selectedCountryCode
                ? regionListData.filter(({ country }) => country === selectedCountryCode)
                : [];

        if (regionsByCountry.length) {
            const selectedFeatureLabel = regionsByCountry
                .map(({ city }) => ({
                    value: city,
                    label: t(`regions:${city}`, { defaultValue: city }),
                }))
                .find(region => region.value === fieldValue?.value)?.label;

            return {
                selectedFeatureLabel,
            };
        }

        return {
            selectedFeatureLabel: fieldValue?.value,
        };
    }, [
        company?.countryCode,
        countryListData,
        countryListLoading,
        fieldValue?.value,
        regionListData,
        regionListLoading,
        t,
        values?.customer?.fields?.Country,
    ]);
};

export const useGenderDisabledLabel = (prefixName: string, t: TFunction, values?: ApplicantFormValues) => {
    const fieldValue = get(`${prefixName}`, values);
    const { data, loading } = useGenderList();

    return useMemo(() => {
        const selectedFeatureLabel = loading
            ? ''
            : data
                  .map(gender => ({
                      value: gender.value,
                      label: t(gender.label),
                  }))
                  .find(option => option.value === fieldValue?.value)?.label;

        return {
            selectedFeatureLabel,
        };
    }, [data, loading, fieldValue?.value, t]);
};

export const useResidentDisabledLabel = (prefixName: string, t: TFunction, values?: ApplicantFormValues) => {
    const fieldValue = get(`${prefixName}`, values);

    const { loading, data } = useResidentialStatusList();

    return useMemo(() => {
        const selectedFeatureLabel = loading
            ? ''
            : data
                  .map(status => ({
                      value: status.value,
                      label: t(status.label),
                  }))
                  .find(status => status.value === fieldValue?.value)?.label;

        return {
            selectedFeatureLabel,
        };
    }, [data, loading, fieldValue?.value, t]);
};

export const useMaritalDisabledLabel = (prefixName: string, t: TFunction, values?: ApplicantFormValues) => {
    const fieldValue = get(`${prefixName}`, values);
    const { loading, data } = useMaritalStatusList();

    return useMemo(() => {
        const selectedFeatureLabel = loading
            ? ''
            : data
                  .map(maritalStatus => ({
                      value: maritalStatus.value,
                      label: t(maritalStatus.label),
                  }))
                  .find(status => status.value === fieldValue?.value)?.label;

        return {
            selectedFeatureLabel,
        };
    }, [data, loading, fieldValue?.value, t]);
};

export const useRaceDisabledLabel = (prefixName: string, values?: ApplicantFormValues) => {
    const fieldValue = get(`${prefixName}`, values);

    const { loading, data } = useRaceList();

    return useMemo(() => {
        const selectedFeatureLabel = loading
            ? ''
            : data
                  .map(race => ({
                      value: race.value,
                      label: race.label,
                  }))
                  .find(race => race.value === fieldValue?.value)?.label;

        return {
            selectedFeatureLabel,
        };
    }, [data, loading, fieldValue?.value]);
};

export const useReferenceDetailSetDisabledLabel = (prefixName: string, values?: ApplicantFormValues) => {
    const fieldValue = get(`${prefixName}`, values);

    const { referenceDetailRelationshipOptions } = useSystemOptions(true);

    return useMemo(() => {
        const selectedFeatureLabel = referenceDetailRelationshipOptions.find(
            option => option.value === fieldValue?.relationship
        )?.label;

        return {
            selectedFeatureLabel,
        };
    }, [referenceDetailRelationshipOptions, fieldValue?.relationship]);
};
