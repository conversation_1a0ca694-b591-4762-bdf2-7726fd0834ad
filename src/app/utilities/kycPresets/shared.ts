import type { ColProps } from 'antd';
import type { KycFieldSpecsFragment } from '../../api/fragments/KYCFieldSpecs';
import type { UploadFileFormDataFragment } from '../../api/fragments/UploadFileFormData';
import type { ApplicationDocumentKind, CustomerKind, LocalCustomerFieldSource } from '../../api/types';

export type UploadDocumentCore<T extends { id: string }> = {
    uploadDocument: (kind: ApplicationDocumentKind, file: File) => Promise<T>;
    removeDocument: (uploadId: string) => Promise<boolean>;
};

export type UploadDocumentProp = UploadDocumentCore<UploadFileFormDataFragment>;

export type KycPresetFieldProps = {
    customerType: string;
    field: KycFieldSpecsFragment;
    colSpan: ColProps;
    prefix?: string;
    bankDisable?: boolean;
    createdAt?: string | Date;
    markMyinfo?: boolean;
    customerKind?: CustomerKind;
    isDisabled?: boolean;
    fromJourney?: boolean;
    enableMobileVerification?: boolean;
};

export const getKycFieldLabel = (markMyinfo: boolean, label: string) => (markMyinfo ? `${label} (Myinfo)` : label);

export type KYCPresetFormValueFields<Type> = { source: LocalCustomerFieldSource; value: Type };
