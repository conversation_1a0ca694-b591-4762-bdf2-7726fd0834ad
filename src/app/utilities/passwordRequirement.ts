import { TFunction } from 'i18next';
import zxcvbn from 'zxcvbn';

const requirements = (t: TFunction, userEmail?: string) => [
    {
        description: t('userSelf:passwordSettings.requirements.error.atLeastUpperCase', { min: '1', range: '[A-Z]' }),
        regex: /[A-Z]/,
    },
    {
        description: t('userSelf:passwordSettings.requirements.error.atLeastLowerCase', { min: '1', range: '[a-z]' }),
        regex: /[a-z]/,
    },
    {
        description: t('userSelf:passwordSettings.requirements.error.atLeastNumber', { min: '1', range: '[0-9]' }),
        regex: /[0-9]/,
    },
    {
        description: t('userSelf:passwordSettings.requirements.error.atLeastSpecialCharacter', {
            min: '1',
            range: `[!@#^&*()={}[]';,.?-$%]`,
        }),
        regex: /[!@#^&*()={}[\]';,.?\-$%]/,
    },
    {
        description: t('userSelf:passwordSettings.requirements.error.atLeastCharacter', {
            size: '14',
        }),
        regex: /.{14,}/,
    },
    {
        description: t('userSelf:passwordSettings.requirements.strongPassword'),
        tooltip: t('userSelf:passwordSettings.requirements.strongPasswordTooltip'),
        isChecked: (value: string) => zxcvbn(value, userEmail ? [userEmail] : []).score >= 3,
    },
];

export default requirements;
