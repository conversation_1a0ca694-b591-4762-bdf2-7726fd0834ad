import styled from 'styled-components';
import BasicProPageWithHeader, { PageWithHeaderProps } from '../../../layouts/BasicProLayout/BasicProPageWithHeader';
import { FooterGlobalStyle, FooterGlobalStyleProps } from '../../GlobalStyles';

const StyleLayoutWithHeader = styled(BasicProPageWithHeader)`
    .ant-page-header-heading {
        justify-content: center;
        position: relative;

        & .ant-page-header-heading-left {
            display: flex;

            & > .ant-page-header-back {
                position: absolute;
                left: 0;

                > .ant-page-header-back-button {
                    color: var(--ant-primary-color);
                    font-size: var(--button-font-size, 1rem);
                }

                > .ant-icon {
                    color: currentColor;
                    font-size: inherit;
                }
            }
        }
    }
`;

export type DefaultLayoutProps = PageWithHeaderProps & FooterGlobalStyleProps;
const FinderLayout = ({
    hasFooterBar = false,
    preferredMobileFooterHeight,
    footerType = 'default',
    footerMaxWidth,
    ...props
}: DefaultLayoutProps) => {
    // Destructure FooterGlobalStyleProps properties
    const footerProps: FooterGlobalStyleProps = {
        hasFooterBar,
        footerType,
        footerMaxWidth,
        preferredMobileFooterHeight,
    };

    return (
        <>
            <FooterGlobalStyle {...footerProps} />
            <StyleLayoutWithHeader {...props} />
        </>
    );
};

export default FinderLayout;
