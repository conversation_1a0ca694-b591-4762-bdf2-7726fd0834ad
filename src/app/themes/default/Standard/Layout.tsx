import styled, { css } from 'styled-components';
import { LayoutType } from '../../../api/types';
import BasicProPageWithHeader, { PageWithHeaderProps } from '../../../layouts/BasicProLayout/BasicProPageWithHeader';
import { FooterGlobalStyle, FooterGlobalStyleProps } from '../../GlobalStyles';

export const StyleLayoutWithHeader = styled(BasicProPageWithHeader)<{
    headerHeadingAlign?: 'flex-start' | 'center' | 'flex-end';
    headerBottomMargin?: 'small' | 'medium';
}>`
    ${props =>
        props?.theme?.layoutType === LayoutType.PorscheV3
            ? css`
                  background-color: null;
              `
            : css`
                  background-color: #fff;
              `}

    height: calc(100% + var(--v3-layout-footer-height, 0px));

    .ant-pro-page-container-warp > .ant-page-header {
        ${props =>
            props?.theme?.layoutType === LayoutType.PorscheV3 &&
            css`
                padding: 0 0 ${props.headerBottomMargin === 'small' ? '16px' : '36px'};
            `}

        ${props =>
            props?.theme?.layoutType === LayoutType.PorscheV3 &&
            css`
                .ant-page-header-content {
                    padding-top: 16px;
                }
            `}
    }

    .ant-page-header-heading {
        justify-content: center;
        position: relative;
        ${props =>
            props?.theme?.layoutType === LayoutType.PorscheV3 &&
            css`
                align-items: flex-end;
            `}

        & .ant-page-header-heading-left {
            display: flex;
            align-items: ${props => props.headerHeadingAlign ?? 'flex-start'};

            & > .ant-page-header-back {
                ${props =>
                    props?.theme?.layoutType !== LayoutType.PorscheV3 &&
                    css`
                        position: absolute;
                        left: 0;
                    `}

                > .ant-page-header-back-button {
                    color: var(--ant-primary-color);
                    font-size: var(--button-font-size, 1rem);
                }

                > .ant-icon {
                    color: currentColor;
                    font-size: inherit;
                }
            }
        }

        .ant-page-header-heading-title {
            margin-right: 0;
        }
    }
`;

export type DefaultLayoutProps = PageWithHeaderProps &
    FooterGlobalStyleProps & {
        headerHeadingAlign?: 'flex-start' | 'center' | 'flex-end';
        headerBottomMargin?: 'small' | 'medium';
    };

const StandardLayout = ({
    hasFooterBar = false,
    preferredMobileFooterHeight = 80,
    footerType = 'default',
    footerMaxWidth,
    headerBottomMargin = 'medium',
    ...props
}: DefaultLayoutProps) => {
    // Destructure FooterGlobalStyleProps properties
    const footerProps: FooterGlobalStyleProps = {
        hasFooterBar,
        footerType,
        footerMaxWidth,
        preferredMobileFooterHeight,
    };

    return (
        <>
            <FooterGlobalStyle {...footerProps} />
            <StyleLayoutWithHeader {...props} headerBottomMargin={headerBottomMargin} />
        </>
    );
};

export default StandardLayout;
