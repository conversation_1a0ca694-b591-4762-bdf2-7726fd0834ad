import type { DualFieldLayoutProps } from '../../../calculator/fieldComponents/shared';
import { onDecimalNumberWithPercentageKeyPress } from '../../../utilities/form';

const DualFieldLayout = (props: DualFieldLayoutProps) => {
    const {
        FormItem,
        Input,
        Select,
        disabled,
        onUnitChange,
        options,
        unit,
        hideLabel,
        error,
        label,
        required,
        onBlur,
        onChange,
        onFocus,
        onPressEnter,
        value,
        fieldType,
    } = props;

    const addOn = disabled ? null : (
        <Select disabled={disabled} onChange={onUnitChange} options={options} value={unit} />
    );

    return (
        <FormItem
            className={`${fieldType} ${disabled ? 'form-item-disabled' : undefined}`}
            help={error}
            label={
                hideLabel ? null : (
                    <>
                        {label}
                        {addOn}
                    </>
                )
            }
            required={required}
            validateStatus={error ? 'error' : 'success'}
        >
            <Input
                addonAfter={hideLabel ? addOn : undefined}
                disabled={disabled}
                onBlur={onBlur}
                onChange={onChange}
                onFocus={onFocus}
                onKeyPress={onDecimalNumberWithPercentageKeyPress}
                onPressEnter={onPressEnter}
                value={value}
            />
        </FormItem>
    );
};
export default DualFieldLayout;
