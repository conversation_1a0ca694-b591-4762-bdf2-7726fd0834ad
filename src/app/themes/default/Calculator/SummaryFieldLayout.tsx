import styled, { css } from 'styled-components';
import { SummaryFieldLayoutProps } from '../../../calculator/fieldComponents/shared';

const monthlyInstalmentCss = css`
    & .ant-typography {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;

        display: block;
    }

    & .first-monthly-instalment {
        margin-right: 12px;
    }

    & div {
        display: inline-block;
    }

    & .monthly-instalment-label {
        font-size: 13px;
    }

    & .gfv-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
`;

const StyledSummaryWrapper = styled.span<{ isMultiple: boolean }>`
    ${props => props.isMultiple && monthlyInstalmentCss}
`;

const SummaryFieldLayout = (props: SummaryFieldLayoutProps) => {
    const { FormItem, Text, additionalHeight, hideLabel, onClick, value, label, isMultiple = false, fieldType } = props;

    return (
        <FormItem
            $additionalHeight={additionalHeight}
            className={`form-item-disabled ${fieldType}`}
            label={hideLabel ? null : label}
            onClick={onClick}
        >
            <StyledSummaryWrapper isMultiple={isMultiple}>
                <Text disabled>{value}</Text>
            </StyledSummaryWrapper>
        </FormItem>
    );
};
export default SummaryFieldLayout;
