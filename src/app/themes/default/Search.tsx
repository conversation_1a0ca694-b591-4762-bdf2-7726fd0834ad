import { Input } from 'antd';
import { SearchProps } from 'antd/lib/input/Search';
import { memo } from 'react';
import styled from 'styled-components';

const StyledSearch = styled(Input.Search)`
    background-color: transparent;
    box-shadow: none;
    border-top: none;
    border-left: none;
    border-right: none;
    border-radius: 0;
    font-size: var(--input-font-size, 1rem);

    &.ant-input {
        border-right-color: transparent;
    }

    &.ant-input-group-addon {
        border-left-color: transparent;
    }

    &.ant-input-search-button {
        border-left-color: transparent;
    }
`;

const Search = (props: SearchProps) => <StyledSearch {...props} />;

export default memo(Search);
