import { CloseOutlined } from '@ant-design/icons';
import { Button, Col, Row } from 'antd';
import { useCallback, useState } from 'react';
import styled from 'styled-components';
import Modal from '../../components/Modal';
import Media from '../../pages/portal/ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/components/Media';
import { ImageContainer } from '../../pages/portal/ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/ui';
import breakpoints from '../../utilities/breakpoints';
import renderMarkdown from '../../utilities/renderMarkdown';
import useTranslatedString from '../../utilities/useTranslatedString';
import type { PackageDetailsModalProps } from './shared';
import LeftIcon from '../../assets/ci/configurator/leftIcon.svg';
import RightIcon from '../../assets/ci/configurator/rightIcon.svg';

const StyledModal = styled(Modal)`
    & .ant-modal-content {
        background-color: transparent;
        box-shadow: none;

        & .ant-modal-body {
            height: 500px;
            padding: 0;
        }
    }
`;

const Content = styled.div<{ isHidden: boolean }>`
    margin: 0 40px;
    background-color: #fff;
    height: 100%;

    ${({ isHidden }) => isHidden && 'display: none;'}

    @media screen and (min-width: ${breakpoints.md}) {
        & .ant-row {
            height: 100%;
        }
    }
`;

const StyledImageContainer = styled(ImageContainer)`
    height: 250px;

    img,
    video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    @media screen and (min-width: ${breakpoints.md}) {
        height: 100%;
    }
`;

const StyledButton = styled(Button)<{ position: 'left' | 'right' | 'topRight' }>`
    &.ant-btn {
        position: absolute;
        z-index: 10;
        border: none;
        background-color: transparent;
        margin-left: 0;
        padding: 0;

        &.ant-btn[disabled]:hover {
            background-color: transparent;
            border: none;
        }
    }

    &:hover {
        background-color: transparent;
    }

    ${({ position }) =>
        position === 'topRight' &&
        `
            top: 16px;
            right: 56px;

            @media screen and (min-width: ${breakpoints.md}) {
                top: 24px;
                right: 64px;
            }
        `}
    ${({ position }) => position !== 'topRight' && 'top: 50%;'}
    ${({ position }) => position === 'left' && 'left: 0;'}
    ${({ position }) => position === 'right' && 'right: 0;'}
`;

const StyledCloseOutlined = styled(CloseOutlined)`
    font-size: 1.5rem;
    color: #fff;

    @media screen and (min-width: ${breakpoints.md}) {
        color: #000;
    }
`;

const DetailsContainer = styled.div`
    padding: 24px;
    height: 250px;
    display: flex;
    flex-direction: column;

    @media screen and (min-width: ${breakpoints.md}) {
        padding: 72px 32px 32px 32px;
        height: 500px;
    }
`;

const Title = styled.div`
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: normal;
`;

const Description = styled.div`
    font-size: 16px;
    overflow: auto;
`;

const PackageDetailsModal = ({ details, packageName, modalVisible, setModalVisible }: PackageDetailsModalProps) => {
    const [detailsIndex, setDetailsIndes] = useState(0);

    const translatedString = useTranslatedString();

    const onClose = useCallback(() => {
        setModalVisible(false);
        setDetailsIndes(0);
    }, [setModalVisible]);

    if (!details) {
        return null;
    }

    return (
        <StyledModal footer={null} onCancel={onClose} open={modalVisible} width={950} centered>
            <>
                {details.map((detail, index) => (
                    <Content key={`packageDetails${index.toString()}`} isHidden={index !== detailsIndex}>
                        <Row>
                            <Col md={11} sm={24} xs={24}>
                                <StyledImageContainer>
                                    <Media fileName={detail.image.filename} source={detail.image.url} />
                                </StyledImageContainer>
                            </Col>
                            <Col md={13} sm={24} xs={24}>
                                <DetailsContainer>
                                    {detail.title && <Title>{translatedString(detail.title)}</Title>}
                                    <Description>
                                        {renderMarkdown(translatedString(detail.description) || '')}
                                    </Description>
                                </DetailsContainer>
                            </Col>
                        </Row>
                    </Content>
                ))}
                <StyledButton onClick={onClose} position="topRight">
                    <StyledCloseOutlined />
                </StyledButton>
                {details.length > 1 && (
                    <>
                        <StyledButton
                            disabled={details.length <= detailsIndex + 1}
                            onClick={() => setDetailsIndes(prevState => prevState + 1)}
                            position="right"
                        >
                            <RightIcon fill="#fff" opacity={details.length <= detailsIndex + 1 && '0.4'} />
                        </StyledButton>
                        <StyledButton
                            disabled={detailsIndex === 0}
                            onClick={() => setDetailsIndes(prevState => prevState - 1)}
                            position="left"
                        >
                            <LeftIcon fill="#fff" opacity={detailsIndex === 0 && '0.4'} />
                        </StyledButton>
                    </>
                )}
            </>
        </StyledModal>
    );
};

export default PackageDetailsModal;
