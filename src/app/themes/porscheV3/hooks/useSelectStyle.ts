import { componentsReady } from '@porsche-design-system/components-react';
import { ReactNode, useEffect } from 'react';
import { css } from 'styled-components';
import { SelectProps } from '../../types';

const SELECT_WRAPPER_TAG = 'p-select-wrapper';
const SELECT_WRAPPER_DROPDOWN_TAG = 'p-select-wrapper-dropdown';

const placeholderCss = css`
    :host .option:first-of-type,
    select option:first-of-type {
        display: none;
    }
`;

const dropdownCss = (maxHeight: number) => `
    :host ul {
        max-height: ${maxHeight}px;
        overflow-y: auto;
    }
`;

const popoverCss = css`
    [id='list'][popover] {
        all: unset;
        position: fixed;
        z-index: 99;
        padding: max(2px, 1 * 6px);
        display: flex;
        flex-direction: column;
        gap: max(2px, 1 * 8px);
        max-height: 250px;
        max-height: max(calc(224px), calc(50vh - anchor-size(height) / 2 - 6px * 2));
        box-sizing: border-box;
        overflow: hidden auto;
        scrollbar-width: thin;
        scrollbar-color: auto;
        animation: var(--p-animation-duration, 0.25s) fade-in cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
        filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.15));
        background: #fff;
        border: 1px solid #d8d8db !important;
        border-radius: 8px;
        position-anchor: --anchor-select-wrapper;
        position-visibility: always;
        position-try-order: normal;
        position-area: bottom span-all;
        position-try-fallbacks: flip-block;
        width: 100%; /* Fallback for browsers without anchor-size support */
        width: anchor-size(width); /* Will be used by browsers that support it */
        margin: 6px 0;
    }
`;

const inputCss = (isValueSelected: boolean) => `
    input[role='combobox'] {
        opacity: 1;

        &::placeholder {
            font-size: 16px;
            color: ${!isValueSelected ? '#535457' : '#010205'};
        }
    }
`;

const supportsNativeCSSAnchorPositioning = (): boolean => {
    // SSR or older browsers
    if (typeof CSS === 'undefined' || !CSS.supports) {
        return false;
    }

    return CSS.supports(
        // eslint-disable-next-line max-len
        '(anchor-name: --test) and (position-anchor: --test) and (position-area: bottom) and (position-try-fallbacks: flip-block) and (width: anchor-size(width))'
    );
};

const useSelectStyle = (
    wrapperRef: React.RefObject<HTMLDivElement>,
    placeholder?: ReactNode,
    listHeight?: SelectProps['listHeight'],
    displayOptionsAsPopover?: boolean,
    isValueSelected?: boolean
) => {
    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        // eslint-disable-next-line consistent-return
        componentsReady(wrapper).then(() => {
            const shadowRoot = (wrapper.querySelector(SELECT_WRAPPER_TAG) as HTMLDivElement)?.shadowRoot;
            const dropdownShadowRoot = (shadowRoot?.querySelector(SELECT_WRAPPER_DROPDOWN_TAG) as HTMLDivElement)
                ?.shadowRoot;

            // note: hide first option if placeholder is set
            if (placeholder) {
                const style = document.createElement('style');
                style.textContent = placeholderCss.toString();
                dropdownShadowRoot?.appendChild(style);
            }

            if (listHeight) {
                const style = document.createElement('style');
                style.textContent = dropdownCss(listHeight).toString();
                dropdownShadowRoot?.appendChild(style);
            }

            const inputStyle = document.createElement('style');
            inputStyle.textContent = inputCss(isValueSelected).toString();
            dropdownShadowRoot?.appendChild(inputStyle);

            if (displayOptionsAsPopover) {
                const style = document.createElement('style');
                style.textContent = popoverCss.toString();
                dropdownShadowRoot?.appendChild(style);

                const comboboxElement = dropdownShadowRoot?.querySelector<HTMLInputElement | HTMLButtonElement>(
                    '[role="combobox"]'
                );
                if (comboboxElement) {
                    comboboxElement.style.setProperty('anchor-name', '--anchor-select-wrapper');
                }

                const dropdownDiv = dropdownShadowRoot?.querySelector('div:empty');

                const observer = new MutationObserver(mutations => {
                    mutations.forEach(mutation => {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            mutation.addedNodes.forEach(node => {
                                if (node.nodeType === Node.ELEMENT_NODE) {
                                    const listElement = node as HTMLElement;
                                    listElement.setAttribute('popover', 'manual');
                                    listElement.showPopover();

                                    // For browsers without anchor-size support, set explicit width
                                    if (!supportsNativeCSSAnchorPositioning() && wrapper) {
                                        const wrapperRect = wrapper.getBoundingClientRect();
                                        const scrollTop = window.scrollY || document.documentElement.scrollTop;
                                        const top = wrapperRect.top + scrollTop + wrapperRect.height;

                                        listElement.style.width = `${wrapperRect.width.toString()}px`;
                                        listElement.style.top = `${top.toString()}px`;
                                        listElement.style.position = 'absolute';
                                    }
                                }
                            });
                        }
                    });
                });

                if (dropdownDiv) {
                    observer.observe(dropdownDiv, {
                        childList: true,
                        subtree: false,
                    });
                }

                return () => {
                    observer.disconnect();
                };
            }
        });
    }, [listHeight, placeholder, wrapperRef, displayOptionsAsPopover, isValueSelected]);
};

export default useSelectStyle;
