/* eslint-disable react/no-array-index-key */
import { PCarousel, PButtonPure } from '@porsche-design-system/components-react';
import { useRef, useCallback } from 'react';
import { useIntersection } from 'react-use';
import styled, { css } from 'styled-components';
import { useRouter } from '../../../components/contexts/shared';
import useCarouselStyle from './useCarouselStyle';

type Props = {
    images: string[];
    activeIndex: number;
    setActiveIndex: (index: number) => void;
    showNavButtons?: boolean;
};

type NavButtonProps = {
    onClick: () => void;
    type: 'prev' | 'next';
};

const Container = styled.div<{ v3LayoutType?: boolean }>`
    position: relative;
    padding-top: clamp(8px, 0.5vw + 6px, 16px);
    padding-bottom: ${({ v3LayoutType }) => (v3LayoutType ? '0px' : 'clamp(8px, 0.5vw + 6px, 16px)')};
    overflow: hidden;

    --thumbnail-width: 96px;

    @media (min-width: 1000px) {
        --thumbnail-width: 142px;
    }
`;

const ImageButton = styled.button<{ active: boolean; first?: boolean }>`
    padding: 2px;
    flex: none;
    cursor: pointer;
    border-radius: 6px;
    border-width: 2px;
    border-color: ${({ active }) => (active ? 'rgb(1, 2, 5, 1)' : 'transparent')};
    background-color: transparent;
    background-image: none;

    :hover {
        border-color: rgb(1, 2, 5, 1);
    }

    ${({ first }) => first && 'margin-inline-start: 2px;'}
`;

const Thumbnail = styled.img`
    aspect-ratio: 16 / 9;
    border-radius: 4px;
    width: var(--thumbnail-width);
    object-fit: cover;
`;

const NavButtonContainer = styled.div<{ type: NavButtonProps['type'] }>`
    display: flex;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    align-items: center;
    height: calc(9 / 16 * var(--thumbnail-width));
    background-color: transparent;

    ${({ type }) =>
        type === 'next'
            ? css`
                  --gradient-to: rgb(238 239 242 / 0.7);
                  --gradient-stops: transparent, var(--gradient-to);
                  background-image: linear-gradient(to right, var(--gradient-stops));
                  inset-inline-end: 0;
                  padding-inline-end: clamp(4px, 0.25vw + 3px, 8px);
              `
            : css`
                  --gradient-to: rgb(238 239 242 / 0.7);
                  --gradient-stops: transparent, var(--gradient-to);
                  background-image: linear-gradient(to left, var(--gradient-stops));
                  padding-inline-start: clamp(4px, 0.25vw + 3px, 8px);
              `};
`;

const ButtonCard = styled.div`
    padding: 2px;
    border-radius: 4px;
    box-shadow: 0px 0px 4px #0000001a;
    background-color: rgb(238 239 242 / 1);
`;

const NavButton = ({ type, onClick }: NavButtonProps) => (
    <NavButtonContainer type={type}>
        <ButtonCard>
            <PButtonPure
                icon={type === 'prev' ? 'arrow-head-left' : 'arrow-head-right'}
                onClick={onClick}
                type="button"
                hideLabel
            />
        </ButtonCard>
    </NavButtonContainer>
);

const useCarouselNavigation = (containerRef: React.RefObject<HTMLDivElement>) => {
    const getSplideElement = useCallback(() => {
        if (!containerRef.current) {
            return null;
        }

        const carousel = containerRef.current?.querySelector<HTMLDivElement>('p-carousel');
        const splide = carousel.shadowRoot?.querySelector<HTMLDivElement>('#splide-list');

        return splide;
    }, [containerRef]);

    const getCurrentTranslateX = useCallback(() => {
        const splideElem = getSplideElement();
        const currentTransform = splideElem?.style.transform || '';
        const match = currentTransform.match(/translateX\((-?\d+(?:\.\d*)?)px\)/);

        return match ? parseInt(match[1], 10) : 0;
    }, [getSplideElement]);

    const scrollLeft = useCallback(() => {
        const splideElem = getSplideElement();

        if (!splideElem) {
            return;
        }

        const translate = Math.min(getCurrentTranslateX() + 100, 0);
        splideElem.style.transform = `translateX(${translate}px)`;
    }, [getCurrentTranslateX, getSplideElement]);

    const scrollRight = useCallback(() => {
        const splideElem = getSplideElement();

        if (!splideElem) {
            return;
        }

        const translate = Math.max(getCurrentTranslateX() - 100, splideElem.offsetWidth - splideElem.scrollWidth);
        splideElem.style.transform = `translateX(${translate}px)`;
    }, [getCurrentTranslateX, getSplideElement]);

    return { scrollLeft, scrollRight };
};

const ThumbnailCarousel = ({ images, activeIndex, setActiveIndex, showNavButtons = true }: Props) => {
    const { layout } = useRouter();
    const containerRef = useRef<HTMLDivElement>(null);
    useCarouselStyle(containerRef, 'small');

    const firstSlideRef = useRef<HTMLButtonElement>(null);
    const lastSlideRef = useRef<HTMLButtonElement>(null);

    const { isIntersecting: isFirstVisible } = useIntersection(firstSlideRef, { threshold: 1 }) || {};
    const { isIntersecting: isLastVisible } = useIntersection(lastSlideRef, { threshold: 1 }) || {};
    const { scrollLeft, scrollRight } = useCarouselNavigation(containerRef);

    const handleThumbnailClick = useCallback(
        (index: number) => {
            setActiveIndex(index);
        },
        [setActiveIndex]
    );

    return (
        <Container ref={containerRef} v3LayoutType={layout?.__typename === 'PorscheV3Layout'}>
            <PCarousel
                activeSlideIndex={activeIndex}
                pagination={false}
                rewind={false}
                slidesPerPage="auto"
                width="extended"
            >
                {images.map((image, index) => {
                    let ref = null;
                    if (index === 0) {
                        ref = firstSlideRef;
                    } else if (index === images.length - 1) {
                        ref = lastSlideRef;
                    }

                    return (
                        <ImageButton
                            key={index}
                            ref={ref}
                            active={activeIndex === index}
                            first={index === 0}
                            onClick={() => handleThumbnailClick(index)}
                            type="button"
                        >
                            <Thumbnail alt={`thumbnail-${index}`} src={image} />
                        </ImageButton>
                    );
                })}
            </PCarousel>

            {showNavButtons && (
                <>
                    {!isFirstVisible && <NavButton onClick={scrollLeft} type="prev" />}
                    {!isLastVisible && <NavButton onClick={scrollRight} type="next" />}
                </>
            )}
        </Container>
    );
};

export default ThumbnailCarousel;
