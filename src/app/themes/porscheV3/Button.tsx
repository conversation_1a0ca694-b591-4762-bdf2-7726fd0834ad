import { <PERSON><PERSON><PERSON><PERSON>, P<PERSON>uttonProps, PButtonPure, componentsReady } from '@porsche-design-system/components-react';
import { useEffect, useMemo, useRef } from 'react';
import styled, { css } from 'styled-components';
import type { ButtonProps } from '../types';

const BUTTON_TAG = 'p-button';

const StyledPButton = styled(PButton).attrs<{ block?: boolean; buttonTheme?: PButtonProps['theme'] }>(props => ({
    theme: props.buttonTheme,
    buttonTheme: undefined,
}))<{ block?: boolean; buttonTheme?: PButtonProps['theme'] }>`
    width: ${({ block }) => (block ? '100%' : 'auto')};
`;

const StyledPButtonPure = styled(PButtonPure)<{ block?: boolean }>`
    width: ${({ block }) => (block ? '100%' : 'auto')};
`;

const ghostButtonCss = css`
    :host .root {
        background-color: rgba(215, 215, 218, 0.35);
        -webkit-backdrop-filter: blur(32px);
        backdrop-filter: blur(32px);
        border: none;
    }

    :host .root:hover {
        background-color: rgba(175, 175, 182, 0.35);
        border-color: rgba(175, 175, 182, 0.35);
        -webkit-backdrop-filter: blur(32px);
        backdrop-filter: blur(32px);
    }
`;

const compactButtonCss = css`
    :host .root {
        --p-internal-scaling-factor: calc(4 / 13);
        padding: calc(var(--p-internal-button-scaling, var(--p-internal-scaling-factor)) * 0.8125 * 16px + 2px);
        width: 100%;
        min-width: min-content;
        min-height: unset;
    }
`;

type AntdButtonTypes = ButtonProps['type'];

const getPorscheButtonVariant = (type: AntdButtonTypes): PButtonProps['variant'] => {
    switch (type) {
        case 'primary':
            return 'primary';

        case 'default':
            return 'secondary';

        case 'ghost':
            return 'ghost';

        default:
            return 'secondary';
    }
};

const getLoading = (loading: ButtonProps['loading']) => {
    if (typeof loading === 'boolean') {
        return loading;
    }

    return !!loading;
};

const appendClonedSubmitButton = (wrapper: HTMLDivElement, form: string) => {
    // note: select the button element inside the shadow dom
    const pButtonElement = wrapper.querySelector(BUTTON_TAG) as HTMLDivElement;
    const shadowRoot = pButtonElement?.shadowRoot;
    const button = shadowRoot?.querySelector('button');

    if (!button) {
        return;
    }

    // note: clone the button and append it to the wrapper, and hide it
    const clonedButton = button?.cloneNode(true) as HTMLButtonElement;
    clonedButton.setAttribute('hidden', 'true');
    clonedButton.setAttribute('form', form);
    clonedButton.setAttribute('type', 'submit');

    pButtonElement.appendChild(clonedButton);

    // note: trigger the click event on the cloned button when the original button is clicked
    button.addEventListener('click', e => {
        e.stopPropagation();
        clonedButton.click();
    });
};

const useButtonStyle = (
    wrapperRef: React.RefObject<HTMLDivElement>,
    type: AntdButtonTypes,
    isCompact: boolean,
    customStyle?: React.CSSProperties
) => {
    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const shadowRoot = (wrapper.querySelector(BUTTON_TAG) as HTMLDivElement)?.shadowRoot;

            if (shadowRoot && type === 'ghost') {
                const style = document.createElement('style');
                style.textContent = ghostButtonCss.toString();
                shadowRoot.appendChild(style);
            }

            if (shadowRoot && isCompact && type !== 'link') {
                const style = document.createElement('style');
                style.textContent = compactButtonCss.toString();
                shadowRoot.appendChild(style);
            }

            if (customStyle) {
                const btn = shadowRoot.querySelector('button.root') as HTMLButtonElement | null;
                if (btn) {
                    Object.entries(customStyle).forEach(([prop, value]) => {
                        if (value !== undefined && value !== null) {
                            (btn.style as any)[prop] = value;
                        }
                    });
                }
            }
        });
    }, [isCompact, type, wrapperRef]);
};

const Button = ({
    children,
    type,
    porscheFallbackIcon = 'none',
    porscheFallbackIconSource,
    porscheTheme,
    htmlType = 'button',
    value,
    disabled,
    onClick,
    className,
    style,
    loading,
    size,
    customV3Style,
    ...props
}: ButtonProps) => {
    const { form } = props;
    const wrapperRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!wrapperRef.current || !form || htmlType !== 'submit') {
            return;
        }

        const wrapper = wrapperRef.current;
        // they will fix this on v3.28.x
        componentsReady(wrapper).then(() => appendClonedSubmitButton(wrapper, form));
    }, [form, htmlType]);

    useButtonStyle(wrapperRef, type, size === 'small', customV3Style);

    const childNode = useMemo(() => {
        if (type === 'link') {
            return (
                <StyledPButtonPure
                    block={props.block}
                    disabled={disabled}
                    icon={porscheFallbackIcon}
                    iconSource={porscheFallbackIconSource}
                    loading={getLoading(loading)}
                    onClick={onClick}
                    style={style}
                    type={htmlType}
                    value={value ? value.toString() : undefined}
                >
                    <span style={{ wordBreak: 'keep-all' }}>{children}</span>
                </StyledPButtonPure>
            );
        }

        return (
            <StyledPButton
                {...props}
                buttonTheme={porscheTheme}
                compact={props.isCompact}
                disabled={disabled}
                hideLabel={!children}
                icon={porscheFallbackIcon}
                iconSource={porscheFallbackIconSource}
                loading={getLoading(loading)}
                name={form}
                onClick={onClick}
                style={style}
                type={htmlType}
                value={value ? value.toString() : undefined}
                variant={getPorscheButtonVariant(type)}
            >
                {children}
            </StyledPButton>
        );
    }, [
        children,
        disabled,
        form,
        htmlType,
        loading,
        onClick,
        porscheFallbackIcon,
        porscheFallbackIconSource,
        porscheTheme,
        props,
        style,
        type,
        value,
    ]);

    return (
        <div ref={wrapperRef} className={`${BUTTON_TAG} ${className || ''}`}>
            {childNode}
        </div>
    );
};

export default Button;
