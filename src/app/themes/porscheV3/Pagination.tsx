import {
    PPagination,
    PSelect,
    PSelectOption,
    PaginationUpdateEvent,
    SelectUpdateEventDetail,
} from '@porsche-design-system/components-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import breakpoints from '../../utilities/breakpoints';
import type { PaginationProps } from '../types';

const BREAKPOINT_SM = 576;
const PAGE_SIZE_DROPDOWN_WIDTH = 145;

const StyledPagination = styled.div<{ enoughSpaceToCenterPagination: boolean }>`
    display: flex;
    position: relative;
    width: auto;
    justify-content: flex-end;

    .pagination {
        padding-top: 7px;
        margin-right: 16px;
    }

    .page-size {
        width: ${PAGE_SIZE_DROPDOWN_WIDTH}px;
        position: static;

        // screen width <= 576 => hide page size dropdown and show the pagination only. (follow ant design)
        @media screen and (max-width: ${breakpoints.sm}) {
            display: none;
        }
    }

    ${props =>
        props.enoughSpaceToCenterPagination &&
        css`
            width: 100%;
            justify-content: center;

            .pagination {
                margin-right: 0px;
            }

            .page-size {
                position: absolute;
                right: 0;
            }
        `}
`;

const Pagination = ({
    current,
    total,
    pageSize,
    onChange,
    onShowSizeChange,
    pageSizeOptions,
    alignPaginationCenter,
    showSizeChanger,
}: PaginationProps) => {
    const { t } = useTranslation('antd');

    const [paginationElementWidth, setPaginationElementWidth] = useState(0);
    const [containerParentWidth, setContainerParentWidth] = useState(0);

    const [isAbleToCenterPagination, setIsAbleToCenterPagination] = useState(false);

    const containerRef = useRef(null);
    const paginationRef = useRef(null);

    const onPageChange = useCallback(
        (event: CustomEvent<PaginationUpdateEvent>) => {
            onChange?.(event.detail.page, pageSize);
        },
        [onChange, pageSize]
    );

    const onPageSizeChange = useCallback(
        (event: CustomEvent<SelectUpdateEventDetail>) => {
            const newPageSize = event.detail.value;
            onShowSizeChange?.(current, +newPageSize);
        },
        [current, onShowSizeChange]
    );

    const pageOptions = useMemo(() => {
        if (pageSizeOptions?.length > 0) {
            return pageSizeOptions.map(option => (
                <PSelectOption value={option.toString()}>
                    {option}
                    {t('antd:Pagination.items_per_page')}
                </PSelectOption>
            ));
        }
        const defaultOptions = [10, 20, 50, 100];

        return defaultOptions.map(option => (
            <PSelectOption value={option.toString()}>
                {option}
                {t('antd:Pagination.items_per_page')}
            </PSelectOption>
        ));
    }, [pageSizeOptions, t]);

    useEffect(() => {
        if (!alignPaginationCenter) {
            return () => {};
        }

        const paginationEl = paginationRef.current;
        const parentEl = containerRef.current?.parentElement;

        if (!paginationEl && !parentEl) {
            return () => {};
        }

        const observer = new ResizeObserver(entries => {
            for (const entry of entries) {
                if (entry.target === paginationEl) {
                    setPaginationElementWidth(entry.contentRect.width);
                } else if (entry.target === parentEl) {
                    setContainerParentWidth(entry.contentRect.width);
                }
            }
        });

        if (paginationEl) {
            observer.observe(paginationEl);
        }

        if (parentEl) {
            observer.observe(parentEl);
        }

        return () => observer.disconnect(); // Clean up
    }, [alignPaginationCenter]);

    useEffect(() => {
        // screen width <= 576 => hide page size dropdown and show the pagination only. (follow ant design)
        // page size dropdown width = 145px
        // can center the pagination = (parentWidth - paginationWidth) / 2 > 145
        if (alignPaginationCenter) {
            const isSmallScreen = window.innerWidth <= BREAKPOINT_SM; // || containerParentWidth <= BREAKPOINT_SM;
            const enoughSpaceToCenter = (containerParentWidth - paginationElementWidth) / 2 > PAGE_SIZE_DROPDOWN_WIDTH;

            setIsAbleToCenterPagination(isSmallScreen || enoughSpaceToCenter);
        } else {
            setIsAbleToCenterPagination(false);
        }
    }, [alignPaginationCenter, containerParentWidth, paginationElementWidth]);

    return (
        <StyledPagination ref={containerRef} enoughSpaceToCenterPagination={isAbleToCenterPagination}>
            <PPagination
                ref={paginationRef}
                activePage={current}
                className="pagination"
                itemsPerPage={pageSize}
                onUpdate={onPageChange}
                totalItemsCount={total}
            />
            {showSizeChanger && (
                <PSelect className="page-size" name="options" onUpdate={onPageSizeChange} value={pageSize.toString()}>
                    {pageOptions}
                </PSelect>
            )}
        </StyledPagination>
    );
};

export default Pagination;
