import styled from 'styled-components';
import { SummaryFieldLayoutProps } from '../../../calculator/fieldComponents/shared';

const StyledWrapper = styled.div<{ isMultiple: boolean }>`
    background: #eeeff2;
    min-height: 54px;
    padding: 11px 16px;
    border-radius: 4px;
    outline: 0px;
    ${props => (props.isMultiple ? '' : 'align-items: center;')}
    display: flex;
    justify-content: space-between;
    color: #010205;

    .monthly-instalment-label {
        ${props => (props.isMultiple ? 'font-size: 16px;font-weight: normal' : '')}
    }

    & .gfv-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .porsche-v3-calculator-display-field-wrapper {
        height: auto;
        font-size: inherit;
        padding: 0;
        border: none;
        border-radius: 0;
        outline: 0px;
    }
`;
const StyledLabel = styled.div`
    font-size: 20px;
    font-weight: bold;
`;

const SummaryFieldLayout = (props: SummaryFieldLayoutProps) => {
    const {
        FormItem,
        Text,
        additionalHeight,
        hideLabel,
        onClick,
        value,
        label,
        isMultiple = false,
        fieldType,
        disabled = true,
    } = props;

    return (
        <FormItem
            $additionalHeight={additionalHeight}
            className={`form-item-disabled ${fieldType}`}
            label={null}
            onClick={onClick}
        >
            <StyledWrapper className="porsche-v3-calculator-summary-field-wrapper" isMultiple={isMultiple}>
                {hideLabel ? '' : <StyledLabel>{label}</StyledLabel>}

                {/* align="end" => for financing calculator grid that using v3 p-text component */}
                {/* textAlign="left" to override align="end" in case not using v3 component (comparison grid) */}
                <Text
                    align="end"
                    disabled={disabled}
                    size="inherit"
                    style={{ fontSize: '20px', textAlign: 'left' }}
                    weight="bold"
                >
                    {value}
                </Text>
            </StyledWrapper>
        </FormItem>
    );
};
export default SummaryFieldLayout;
