import { componentsReady } from '@porsche-design-system/components-react';
import { useEffect, useRef } from 'react';
import styled from 'styled-components';
import type { DualFieldLayoutProps } from '../../../calculator/fieldComponents/shared';
import breakpoints from '../../../utilities/breakpoints';
import { onDecimalNumberWithPercentageKeyPress } from '../../../utilities/form';

const StyledWrapper = styled.div`
    display: flex;
    gap: 10px;
`;

const StyledUnit = styled.div`
    max-width: 95px;

    @media (max-width: ${breakpoints.xs}) {
        max-width: fit-content;
    }
`;

const StyledValue = styled.div`
    flex: 1;
`;

const optionsCss = `
    div[role="option"].option--selected {
        gap: 0px;
    }
`;

const DualFieldLayout = (props: DualFieldLayoutProps) => {
    const wrapperRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const selectWrapper = wrapper.querySelector('p-select-wrapper');
            const selectShadowRoot = selectWrapper?.shadowRoot;
            const dropdownWrapper = selectShadowRoot?.querySelector('p-select-wrapper-dropdown');
            const dropdownShadowRoot = dropdownWrapper?.shadowRoot;

            if (dropdownShadowRoot) {
                const style = document.createElement('style');
                style.textContent = optionsCss;
                dropdownShadowRoot.appendChild(style);
            }
        });
    }, []);

    const {
        FormItem,
        Input,
        Select,
        disabled,
        onUnitChange,
        options,
        unit,
        hideLabel,
        error,
        label,
        required,
        onBlur,
        onChange,
        onFocus,
        onPressEnter,
        value,
        fieldType,
    } = props;

    return (
        <FormItem
            className={`${fieldType} ${disabled ? 'form-item-disabled' : undefined}`}
            help={error}
            label={hideLabel ? null : label}
            required={required}
            validateStatus={error ? 'error' : 'success'}
        >
            <StyledWrapper ref={wrapperRef}>
                {!disabled && (
                    <StyledUnit>
                        <Select
                            disabled={disabled}
                            onChange={onUnitChange}
                            options={options}
                            status={error ? 'error' : ''}
                            value={unit}
                        />
                    </StyledUnit>
                )}
                <StyledValue>
                    <Input
                        disabled={disabled}
                        onBlur={onBlur}
                        onChange={onChange}
                        onFocus={onFocus}
                        onKeyPress={onDecimalNumberWithPercentageKeyPress}
                        onPressEnter={onPressEnter}
                        status={error ? 'error' : ''}
                        value={value}
                    />
                </StyledValue>
            </StyledWrapper>
        </FormItem>
    );
};
export default DualFieldLayout;
