import { <PERSON>r } from 'antd';
import { useField } from 'formik';
import { isNil } from 'lodash/fp';
import { CascaderProps as RcCascaderProps } from 'rc-cascader';
import { memo, useCallback, useMemo } from 'react';
import styled from 'styled-components';
import { SelectDropdownContainer } from '../../../components/fields/SelectField';
import type { CascaderFieldProps } from '../../../components/fields/shared';
import useCompanyColorLuminance from '../../../utilities/useCompanyColorLuminance';
import { StyledCommonFormItem } from './shared';

const StyledCascader = styled(Cascader)`
    &.ant-select:not(.ant-select-customize-input) .ant-select-selector {
        background-color: transparent;
        box-shadow: none;
        font-size: 16px;
        height: 34px;
    }
`;

const CascaderField = <T extends { children?: any; value: any }>({
    name,
    required,
    label,
    itemProps,
    options,
    dropdownRender: dropdownRenderProp,
    ...props
}: CascaderFieldProps<T>) => {
    const [field, meta, { setValue }] = useField(name);
    const flattenOptions = useMemo(() => {
        const flatten = (options: CascaderFieldProps<T>['options'], parent = null) => {
            const result: Array<any> = [];
            options.forEach(cur => {
                if (cur.children) {
                    const { children, ...option } = cur;
                    result.push({ ...option, parent });
                    result.push(...flatten(children, cur.value));
                } else {
                    result.push({ ...cur, parent });
                }
            });

            return result;
        };

        return flatten(options);
    }, [options]);

    const enhancedValue = useMemo(() => {
        if (!flattenOptions || (!field.value && !options.find(option => isNil(option.value)))) {
            return [];
        }

        const buildEnhancedValue = (value: string) => {
            const foundOption = flattenOptions.find(option => option.value === value);
            if (foundOption?.parent) {
                return [...buildEnhancedValue(foundOption.parent), value];
            }

            return [value];
        };

        return buildEnhancedValue(field.value);
    }, [field, flattenOptions, options]);

    const enhancedOnChange = useCallback<RcCascaderProps['onChange']>(
        value => {
            if (value) {
                setValue(value.slice(-1)[0]);
            } else {
                setValue(null);
            }
        },
        [setValue]
    );

    const luminance = useCompanyColorLuminance();
    const dropdownRender = useCallback(
        (menu: React.ReactElement) => (
            <SelectDropdownContainer luminance={luminance}>
                {dropdownRenderProp ? dropdownRenderProp(menu) : menu}
            </SelectDropdownContainer>
        ),
        [dropdownRenderProp, luminance]
    );

    return (
        <StyledCommonFormItem label={label} meta={meta} required={required} {...itemProps}>
            <StyledCascader
                {...props}
                allowClear={!isNil(enhancedValue)}
                dropdownRender={dropdownRender}
                multiple={false}
                onChange={enhancedOnChange}
                options={options}
                value={enhancedValue}
            />
        </StyledCommonFormItem>
    );
};

export default memo(CascaderField);
