import { PSwitch, SwitchUpdateEventDetail } from '@porsche-design-system/components-react';
import { useField } from 'formik';
import { useCallback } from 'react';
import type { SwitchProps } from '../../../components/fields/shared';
import { StyledCommonFormItem } from './shared';

const SwitchField = ({ name, required, label, tooltip, disabled, isLabelInSide }: SwitchProps) => {
    const [field, meta, helpers] = useField({ name });

    const onUpdate = useCallback((e: CustomEvent<SwitchUpdateEventDetail>) => {
        helpers.setValue(e.detail.checked);
    }, []);

    return (
        <StyledCommonFormItem disabled={disabled} label={isLabelInSide ? undefined : label} required={required}>
            <PSwitch
                {...field}
                alignLabel="end"
                checked={field.value}
                disabled={disabled}
                hideLabel={!isLabelInSide}
                onUpdate={onUpdate}
            >
                {label}
            </PSwitch>
        </StyledCommonFormItem>
    );
};

export default SwitchField;
