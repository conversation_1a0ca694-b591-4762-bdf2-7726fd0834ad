import { PIcon } from '@porsche-design-system/components-react';
import MultipleDragger from '../../../components/fields/MultipleDraggerField';
import type { DraggerFieldProps } from '../../../components/fields/shared';
import { UploaderContainer } from './shared';

const MultipleDraggerField = (props: DraggerFieldProps) => (
    <UploaderContainer>
        <MultipleDragger
            {...props}
            errorIcon={
                <PIcon aria={{ 'aria-label': 'Exclamation icon' }} color="notification-error" name="exclamation" />
            }
        />
    </UploaderContainer>
);

export default MultipleDraggerField;
