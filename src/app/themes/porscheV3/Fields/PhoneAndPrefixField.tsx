/* eslint-disable max-len */
import { PFieldset, componentsReady } from '@porsche-design-system/components-react';
import { Form, Row, Col } from 'antd';
import { useField, useFormikContext } from 'formik';
import { CountryCode, parsePhoneNumberFromString, getCountries, getCountryCallingCode } from 'libphonenumber-js';
import { isEqual } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { type PhoneFieldProps } from '../../../components/fields/ci/PhoneField';
import { onNumericKeyPress } from '../../../utilities/form';
import { StyledTextFieldWrapper } from './shared';
import { getTextFieldState } from './utils';

const FIELDSET_TAG = 'p-fieldset';
const TEXT_FIELD_TAG = 'p-text-field-wrapper';

const InvisibleLabel = styled.span`
    visibility: hidden;
`;

// note: to make the style of legend and label consistent
const fieldsetMessageCss = css`
    legend {
        margin-top: 0 !important;
        margin-bottom: 4px !important;
        font:
            400 1rem / calc(6px + 2.125ex) 'Porsche Next',
            'Arial Narrow',
            Arial,
            'Heiti SC',
            SimHei,
            sans-serif !important;
    }

    #message {
        margin-top: 4px;
    }
`;

const inputCss = css`
    input::placeholder {
        font-size: 16px;
        color: #535457;
    }
`;

const useOverrideStyle = (wrapperRef: React.RefObject<HTMLDivElement>) => {
    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const fieldsetElement = wrapper.querySelector(FIELDSET_TAG);
            const fieldSetShadowRoot = fieldsetElement?.shadowRoot;

            if (!fieldsetElement || !fieldSetShadowRoot) {
                return;
            }

            const style = document.createElement('style');
            style.textContent = fieldsetMessageCss.toString();
            fieldSetShadowRoot?.appendChild(style);

            const textFieldElements = fieldsetElement.querySelectorAll(TEXT_FIELD_TAG);

            // note: porsche component render the padding based on the offset width of the prefix element.
            // when the prefix element is in collapse(hidden), the padding is not rendered correctly.
            // this is a workaround to add in the padding manually.
            const prefixElement = textFieldElements[0];
            const prefixInput = prefixElement?.querySelector('input');
            prefixInput?.setAttribute(
                'style',
                `--p-internal-text-field-input-padding-start: calc(36px - 2px) !important;`
            );

            textFieldElements.forEach(el => {
                const inputStyle = document.createElement('style');
                inputStyle.textContent = inputCss.toString();
                el.appendChild(inputStyle);
            });

            const elementShadowRoot = prefixElement?.shadowRoot;
            const rootContainer = elementShadowRoot?.querySelector('.root');

            if (elementShadowRoot && rootContainer) {
                rootContainer.setAttribute('style', 'min-width:auto');
            }
        });
    }, [wrapperRef]);
};

const isKorea = (countryCode?: CountryCode): boolean => countryCode === 'KR';

const prefixToCountry = (prefix: number | undefined): CountryCode | undefined => {
    if (!prefix) {
        return undefined;
    }

    const numeric = prefix.toString();

    return getCountries().find(country => getCountryCallingCode(country) === numeric);
};

const formatPhoneNumber = (phone: string, countryCode?: CountryCode): string => {
    if (!isKorea(countryCode)) {
        return phone || '';
    }

    const raw = phone.replace(/[^\d+]/g, '');
    const parsed = parsePhoneNumberFromString(raw, countryCode);
    const nationalNumber = parsed?.formatNational();

    if (nationalNumber?.startsWith('0')) {
        return nationalNumber.substring(1);
    }

    return nationalNumber || raw;
};

const getPlaceholder = (countryCode?: CountryCode): string => {
    const { t } = useTranslation('common');

    if (!countryCode || !isKorea(countryCode)) {
        return t('common:mobileNoPlaceholder');
    }

    const sample = parsePhoneNumberFromString('1020002000', countryCode);
    const nationalNumber = sample?.formatNational();

    if (nationalNumber?.startsWith('0')) {
        return nationalNumber.substring(1);
    }

    return nationalNumber || t('common:mobileNoPlaceholder');
};

const PhoneAndPrefixField = ({
    name,
    itemProps,
    required,
    status,
    label,
    placeholder,
    disabled,
    extraValidationError,
    showExtraErrorOnSubmit = true,
}: PhoneFieldProps) => {
    const wrapperRef = useRef<HTMLDivElement>(null);
    const { submitCount } = useFormikContext();

    const [prefixField, prefixMeta, { setValue: setPrefixValue }] = useField<number | undefined>({
        name: `${name}.prefix`,
    });
    const [valueField, valueMeta, { setValue }] = useField<string>({ name: `${name}.value` });

    const meta = useMemo(
        () => ({
            error:
                prefixMeta.error ||
                valueMeta.error ||
                (showExtraErrorOnSubmit ? submitCount > 0 && extraValidationError : extraValidationError),
            touched: prefixMeta.touched || valueMeta.touched,
            initialValue: valueMeta.initialValue,
            value: valueMeta.value,
        }),
        [
            prefixMeta.error,
            prefixMeta.touched,
            valueMeta.error,
            valueMeta.initialValue,
            valueMeta.touched,
            valueMeta.value,
            extraValidationError,
            submitCount,
            showExtraErrorOnSubmit,
        ]
    );

    const onPrefixChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const cleaned = e.target.value.replace(/\D/g, '');
            setPrefixValue(cleaned ? Number(cleaned) : undefined);
        },
        [setPrefixValue]
    );

    const onPhoneChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const digitsOnly = e.target.value.replace(/\D/g, '');
            setValue(digitsOnly);
        },
        [setValue]
    );

    useOverrideStyle(wrapperRef);

    const hasError = !!meta?.error && (meta?.touched || !isEqual(meta?.initialValue, meta?.value));
    const errorMessage = Array.isArray(meta?.error) ? '' : meta?.error;
    const countryCode = prefixToCountry(prefixField.value);
    const displayPlaceholder = placeholder || getPlaceholder(countryCode);
    const formattedValue = formatPhoneNumber(valueField.value || '', countryCode);

    return (
        <div ref={wrapperRef}>
            <Form.Item {...itemProps} required={required}>
                <PFieldset labelSize="small" required={required} state={hasError ? 'error' : getTextFieldState(status)}>
                    <span slot="label">{label}</span>
                    <Row gutter={[8, 8]} wrap={false}>
                        <Col flex="90px">
                            <StyledTextFieldWrapper
                                state={hasError ? 'error' : getTextFieldState(status)}
                                unit="+"
                                unitPosition="prefix"
                                hideLabel
                            >
                                <span slot="label">{label}</span>
                                <input
                                    autoComplete="tel-country-code"
                                    disabled={disabled}
                                    name={`${name}.prefix`}
                                    onChange={onPrefixChange}
                                    onKeyPress={onNumericKeyPress}
                                    placeholder="82"
                                    required={required}
                                    type="text"
                                    value={prefixField.value ?? ''}
                                />
                            </StyledTextFieldWrapper>
                        </Col>

                        <Col flex="auto">
                            <StyledTextFieldWrapper state={hasError ? 'error' : getTextFieldState(status)} hideLabel>
                                <InvisibleLabel slot="label">{label}</InvisibleLabel>
                                <input
                                    autoComplete="tel-national"
                                    disabled={disabled}
                                    name={`${name}.value`}
                                    onBlur={valueField.onBlur}
                                    onChange={onPhoneChange}
                                    placeholder={displayPlaceholder}
                                    required={required}
                                    type="text"
                                    value={formattedValue}
                                />
                            </StyledTextFieldWrapper>
                        </Col>
                    </Row>
                    {hasError && <span slot="message">{errorMessage}</span>}
                </PFieldset>
            </Form.Item>
        </div>
    );
};

export default PhoneAndPrefixField;
