import { componentsReady, PIcon } from '@porsche-design-system/components-react';
import { Form } from 'antd';
import { useField } from 'formik';
import { isEqual } from 'lodash/fp';
import { useEffect, useRef } from 'react';
import styled from 'styled-components';
import { type TranslatedInputFieldProps } from '../../../components/fields/TranslatedInputField/TranslatedInputFieldProps';
import { useTranslationDrawer } from '../../../components/fields/TranslatedInputField/TranslationDrawer';
import { StyledTextFieldWrapper } from './shared';
import useRenderTooltip from './useRenderTooltip';
import { getTextFieldState } from './utils';

const StyledIcon = styled(PIcon)`
    position: absolute;
    right: 5%;
    top: 50%;
    cursor: pointer;
`;

const useOverrideStyle = (wrapperRef: React.RefObject<HTMLDivElement>) => {
    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const elm = wrapper.querySelector('p-text-field-wrapper');
            const elmShaRoot = elm?.shadowRoot;
            if (!elm || !elmShaRoot) {
                return;
            }

            const rootContainer = elmShaRoot.querySelector('.root');
            const description = rootContainer.querySelector('#description');
            if (description) {
                rootContainer.setAttribute('style', 'gap:2px');
            }
        });
    }, [wrapperRef]);
};

const TranslatedInputField = ({
    type = 'text',
    placeholder,
    status,
    label,
    required = false,
    name,
    itemProps,
    tooltip,
    disabled,
    title,
    languages,
    ...props
}: TranslatedInputFieldProps) => {
    const [field, meta] = useField({ name: `${name}.defaultValue` });
    const drawer = useTranslationDrawer();

    const renderTooltip = useRenderTooltip(tooltip);

    const button = disabled ? null : (
        <StyledIcon aria={{ 'aria-label': 'Translation icon' }} name="globe" onClick={drawer.open} />
    );

    const hasError = !!meta?.error && (meta?.touched || !isEqual(meta?.initialValue, meta?.value));
    const errorMessage = Array.isArray(meta?.error) ? '' : meta?.error;

    const wrapperRef = useRef<HTMLDivElement>(null);
    useOverrideStyle(wrapperRef);

    return (
        <div ref={wrapperRef}>
            <Form.Item {...itemProps} required={required}>
                <StyledTextFieldWrapper state={hasError ? 'error' : getTextFieldState(status)}>
                    {label && (
                        <span slot="label">
                            {label}
                            {renderTooltip()}
                        </span>
                    )}
                    {itemProps?.help && <span slot="description">{itemProps?.help}</span>}
                    <input {...field} disabled={disabled} placeholder={placeholder} required={required} type={type} />
                    {hasError && <span slot="message">{errorMessage}</span>}
                    {button}
                </StyledTextFieldWrapper>
            </Form.Item>
            {drawer.render({ name, title: title ?? label, languages, maxInputLength: props.maxLength })}
        </div>
    );
};

export default TranslatedInputField;
