import {
    componentsReady,
    MultiSelectUpdateEventDetail,
    PMultiSelect,
    PMultiSelectOption,
    POptgroup,
} from '@porsche-design-system/components-react';
import { SelectProps } from 'antd';
import { useField } from 'formik';
import { isEmpty, isEqual } from 'lodash/fp';
import { ReactNode, useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import FormItem from '../../../components/fields/FormItem';
import {
    SelectFieldProps as FormSelectFieldProps,
    getSingleOptionValue,
    isSelectOptionGroup,
} from '../../../components/fields/SelectField';
import Select from './Select';

export type SelectOption<T> = {
    label: JSX.Element | ReactNode;
    value: T;
};

const multiSelectListHeightCss = (maxHeight: number) => `
    :host [role="listbox"] {
        max-height: ${maxHeight}px;
        overflow-y: auto;
    }
`;

const useOverrideStyle = (wrapperRef: React.RefObject<HTMLDivElement>, listHeight?: number) => {
    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const elm = wrapper.querySelector('p-multi-select');
            const elmShaRoot = elm?.shadowRoot;
            if (!elm || !elmShaRoot) {
                return;
            }

            const rootContainer = elmShaRoot.querySelector('.root');
            const inputWrapper = rootContainer?.querySelector('.wrapper');
            const filterInput = inputWrapper?.querySelector('#filter');
            if (filterInput) {
                filterInput.setAttribute('style', 'background:transparent');
            }

            if (listHeight) {
                const style = document.createElement('style');
                style.textContent = multiSelectListHeightCss(listHeight);
                elmShaRoot.appendChild(style);
            }
        });
    }, [listHeight, wrapperRef]);
};

const MultiSelectOption = ({ option }: { option: FormSelectFieldProps['options'][number] }) => {
    const { label } = option;

    if (isSelectOptionGroup(option)) {
        const { options, label: groupLabel } = option;

        return (
            <POptgroup key={groupLabel.toString()} label={groupLabel.toString()}>
                {options.map(item => (
                    <PMultiSelectOption key={item.value.toString()} value={item.value.toString()}>
                        {item.label}
                    </PMultiSelectOption>
                ))}
            </POptgroup>
        );
    }

    const { value } = option;

    return (
        <PMultiSelectOption key={value.toString()} value={value.toString()}>
            {label}
        </PMultiSelectOption>
    );
};

const SelectField = ({
    name,
    required,
    label,
    itemProps,
    disabled,
    onChange = null,
    enhanceOptions,
    ...props
}: FormSelectFieldProps) => {
    const { t } = useTranslation('common');
    const [field, _meta, { setValue }] = useField({ name });
    const enhancedOnChange = useCallback(
        value => {
            setValue(value);

            if (onChange) {
                onChange(value);
            }
        },
        [setValue, onChange]
    );

    const enhancedOnMultiSelectChange = useCallback(
        (e: CustomEvent<MultiSelectUpdateEventDetail>) => {
            setValue(e.detail.value);

            if (onChange) {
                onChange(e.detail.value);
            }
        },
        [setValue, onChange]
    );

    const { options } = props;

    // track latest options
    const prevOptions = useRef(null);

    const wrapperRef = useRef<HTMLDivElement>(null);
    useOverrideStyle(wrapperRef, props?.listHeight);

    useEffect(() => {
        const onlyValue = getSingleOptionValue(options);

        if (
            onlyValue &&
            (!isEqual(options, prevOptions?.current) || isEmpty(field.value)) &&
            !props.allowClear &&
            props.autoSelected
        ) {
            const newValue = props.mode === 'multiple' ? [onlyValue] : onlyValue;

            // Should use the `setValue` function to update the field value
            // instead of enhanced one, because the enhanced one will trigger
            // the `onChange` callback, which is not desired here.
            // ie. onAssignedChange in Application details should not be triggered
            // on page load
            enhancedOnChange(newValue);
            prevOptions.current = options;
        }

        // reset previous options such that the previous condition can be fulfilled
        if (!isEqual(options, prevOptions?.current)) {
            prevOptions.current = null;
        }
    }, [props.mode, options, required, enhancedOnChange, props.allowClear, field.value, props.autoSelected]);

    // account for both empty array and null values
    const hasValue = !isEmpty(field.value) || (Array.isArray(field.value) && field.value.length !== 0);

    // To handle value that do not belong to html select options
    const placeholder = hasValue ? field.value : t('common:pleaseSelect');
    const inner = useMemo(() => {
        if (props.mode === 'multiple') {
            return (
                <div ref={wrapperRef}>
                    <PMultiSelect
                        // spread props
                        {...props}
                        // then spread the field properties itself
                        disabled={disabled || (hasValue && options?.length === 1 && required)}
                        label={label}
                        name={name}
                        // onSelect is required
                        onSelect={() => {}}
                        onUpdate={enhancedOnMultiSelectChange}
                        required={required}
                        value={field.value}
                    >
                        {options.map(item => (
                            <MultiSelectOption key={item.label.toString()} option={item} />
                        ))}
                    </PMultiSelect>
                </div>
            );
        }

        return (
            <Select
                // spread props
                {...props}
                // then spread the field properties itself
                disabled={disabled || (hasValue && options?.length === 1 && required)}
                isValueSelected={hasValue}
                label={label}
                name={name}
                onChange={enhancedOnChange}
                options={(enhanceOptions ? enhanceOptions(options) : options) as SelectProps['options']}
                placeholder={placeholder}
                required={required}
            />
        );
    }, [
        props,
        disabled,
        hasValue,
        options,
        required,
        label,
        name,
        enhancedOnChange,
        enhanceOptions,
        placeholder,
        enhancedOnMultiSelectChange,
        field.value,
    ]);

    return (
        <FormItem {...itemProps} required={required}>
            {inner}
        </FormItem>
    );
};

export default SelectField;
