import { ShopOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import type { CascaderFieldProps } from '../../../../components/fields/shared';
import CustomDealershipCascaderField from './CustomDealershipCascaderField';
import { type DealershipDrawerProps, useDealershipDrawer } from './DealershipDrawer';
import Down from '../../../../icons/down.svg';

export type DealershipCascaderFieldProps = CascaderFieldProps<{ children?: any; value: any }> &
    Pick<DealershipDrawerProps, 'dealers'>;

const DealershipCascaderField = ({ name, dealers, disabled, options, ...props }: DealershipCascaderFieldProps) => {
    const drawer = useDealershipDrawer();
    // hide button completely when disable
    const button = disabled ? null : <Button icon={<ShopOutlined />} onClick={drawer.open} size="small" type="link" />;

    return (
        <>
            <CustomDealershipCascaderField
                disabled={disabled}
                name={`${name}.defaultId`}
                {...props}
                options={options}
                suffixIcon={
                    <>
                        {button}
                        <Down fill="var(--ant-primary-color)" />
                    </>
                }
            />
            {drawer.render({ name, title: props.label, dealers, options })}
        </>
    );
};

export default DealershipCascaderField;
