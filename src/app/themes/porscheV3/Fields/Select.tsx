import { PSelectWrapper } from '@porsche-design-system/components-react';
import { useField } from 'formik';
import { isBoolean, isNumber, isObject, omit } from 'lodash/fp';
import { useCallback, useMemo, useRef } from 'react';
import type { SelectProps } from '../../types';
import useSelectStyle from '../hooks/useSelectStyle';
import { getTextFieldState } from './utils';

export type SelectPProps = SelectProps & {
    name: string;
    label?: string;
    required?: boolean;
    /** When true, the select options will be displayed as a popover */
    displayOptionsAsPopover?: boolean;
    /** When false, the placeholder color will be applied */
    isValueSelected?: boolean;
};

const Select = ({
    status,
    name,
    label,
    required = false,
    options,
    onChange,
    onSelect,
    placeholder,
    brightness,
    filterOption,
    disabled,
    listHeight,
    // make popover as default in order to avoid random issues on select not showing
    displayOptionsAsPopover = true,
    isValueSelected,
}: SelectPProps) => {
    const [field, meta] = useField({ name });
    const wrapperRef = useRef<HTMLDivElement>(null);

    useSelectStyle(wrapperRef, placeholder, listHeight, displayOptionsAsPopover, isValueSelected);

    // Keep the value in another properties
    // Porsche v3 map the value as string internally
    const modifiedOptions = useMemo(() => {
        if (!options) {
            return [];
        }

        return options.map(option => {
            if (isNumber(option?.value)) {
                return { ...option, value: option.value.toString(), originalValue: option.value };
            }

            if (isBoolean(option?.value)) {
                return { ...option, value: option.value ? 'true' : 'false', originalValue: option.value };
            }

            if (Array.isArray(option?.value) || isObject(option?.value)) {
                return { ...option, value: JSON.stringify(option.value), originalValue: option.value };
            }

            return { ...option, originalValue: option.value };
        });
    }, [options]);

    const parsedOptions = useMemo(
        () =>
            modifiedOptions?.map((option, index) => (
                <option key={option.value} selected={option.originalValue === field.value} value={option.value}>
                    {option.label}
                </option>
            )),
        [field.value, modifiedOptions]
    );

    // Placeholders are outside the domain of options
    const placeholderOption = useMemo(
        () =>
            placeholder && (
                <option value="" hidden selected>
                    {placeholder}
                </option>
            ),
        [placeholder]
    );

    const onChangeHandler = useCallback(
        (e: React.ChangeEvent<HTMLSelectElement>) => {
            const foundOption = modifiedOptions.find(option => option.value === e.target.value);

            if (onChange) {
                onChange(foundOption?.originalValue ?? e.target.value, { label: foundOption?.label });
            }

            if (onSelect) {
                onSelect(foundOption?.originalValue ?? e.target.value, { label: foundOption?.label });
            }
        },
        [modifiedOptions, onChange, onSelect]
    );

    const { hasError, errorMessage } = useMemo(
        () => ({
            hasError: !!meta?.error && meta?.touched,
            errorMessage: Array.isArray(meta?.error) ? '' : meta?.error,
        }),
        [meta?.error, meta?.touched]
    );

    return (
        <div ref={wrapperRef} className="porsche-v3-calculator-select-field-wrapper">
            <PSelectWrapper
                filter={!!filterOption}
                state={hasError ? 'error' : getTextFieldState(status)}
                theme={brightness === 'dark' ? 'dark' : undefined}
            >
                {label && <span slot="label">{label}</span>}
                <select {...omit('value', field)} disabled={disabled} onChange={onChangeHandler} required={required}>
                    {placeholderOption}
                    {parsedOptions}
                </select>
                {hasError && <span slot="message">{errorMessage}</span>}
            </PSelectWrapper>
        </div>
    );
};

export default Select;
