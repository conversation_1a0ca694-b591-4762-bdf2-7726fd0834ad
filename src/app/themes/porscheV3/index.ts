import { Card, Collapse, Drawer, Switch, Table, Transfer, Upload } from 'antd';
import Dragger from 'antd/lib/upload/Dragger';
import styled from 'styled-components';
import { CompanyTheme } from '../../api/types';
import ActivityLogButton from '../../components/button/ActivityLogButton';
import ResponsiveStyledTable from '../../pages/shared/ResponsiveStyledTable';
import type { ThemeComponents } from '../context';
import CardWithHeader from '../default/CardWithHeader';
import DraggerWrapper from '../default/DraggerWrapper';
import makeThemeComponents from '../makeThemeComponents';
import ConfiguratorDropdown from '../porsche/Configurator/Dropdown';
import ConfiguratorLayout from '../porsche/Configurator/Layout';
import ConfiguratorSubHeader from '../porsche/Configurator/SubHeader';
import Dropdown from '../porsche/Dropdown';
import FinderLayout from '../porsche/Finder/Layout';
import JourneySteps from '../porsche/JourneySteps';
import MobilityWebpageLayout from '../porsche/Mobility/WebPageLayout';
import StandardLayout from '../porsche/Standard/Layout';
import Anchor from './Anchor';
import ArrowDownIcon from './ArrowDownIcon';
import BackButton from './BackButton';
import Button from './Button';
import Calculator from './Calculator';
import Carousel from './Carousel';
import Checkbox from './Checkbox/Checkbox';
import CheckboxGroup from './Checkbox/CheckboxGroup';
import CloseButton from './CloseButton';
import ConfiguratorChatIcon from './Configurator/ChatIcon';
import ConfiguratorDepositDisplay from './Configurator/DepositDisplay';
import ConfiguratorMessageButton from './Configurator/MessageButton';
import ConsentModal from './ConsentModal';
import LegalText from './ConsentsAndDeclarations/LegalText';
import ErrorMessageBox from './ErrorMessageBox';
import ExclamationIcon from './ExclamationIcon';
import BirthdayField from './Fields/BirthdayField';
import CascaderField from './Fields/CascaderField';
import CheckboxInputField from './Fields/CheckboxField';
import CountryField from './Fields/CountryField';
import DatePickerField from './Fields/DatePickerField';
import DealershipCascaderField from './Fields/DealershipCascaderField';
import DisplayField from './Fields/DisplayField';
import DisplayFieldWithUTCOffset from './Fields/DisplayFieldWithUTCOffset';
import FixedRangePickerField from './Fields/FixedRangePickerField';
import GenderField from './Fields/GenderField';
import IdentityNumberField from './Fields/IdentityNumberField';
import InputField from './Fields/InputField';
import InputNumberField from './Fields/InputNumberField';
import MaritalStatusField from './Fields/MaritalStatusField';
import MultipleDraggerField from './Fields/MultipleDraggerField';
import NationalityField from './Fields/NationalityField';
import PasswordField from './Fields/PasswordField';
import PhoneAndPrefixField from './Fields/PhoneAndPrefixField';
import PhoneVerificationField from './Fields/PhoneVerificationField';
import RaceField from './Fields/RaceField';
import RadioGroupField from './Fields/RadioGroupField';
import RangePickerField from './Fields/RangePickerField';
import RegionField from './Fields/RegionField';
import ResidentialStatusField from './Fields/ResidentialStatusField';
import SelectField from './Fields/SelectField';
import SwitchField from './Fields/SwitchField';
import TextAreaField from './Fields/TextAreaField';
import TimeRangePickerField from './Fields/TimeRangePickerField';
import TranslatedInputField from './Fields/TranslatedInputField';
import VehicleMakeSelectField from './Fields/VehicleMakeSelectField';
import VehicleModelSelectField from './Fields/VehicleModelSelectField';
import InfoMessageBox from './InfoMessageBox';
import Input from './Input';
import Link from './Link';
import ListItem from './ListItem';
import LoadingSpinner from './LoadingSpinner';
import Modal from './Modal';
import OTPInput from './OTPInput';
import PackageDetailsModal from './PackageDetailsModal';
import PageWithHeader from './PageWithHeader';
import PaginatedTableWithContext from './PaginatedTableWithContext';
import Pagination from './Pagination';
import Popconfirm from './Popconfirm';
import PorscheGlobalStyles from './PorscheV3GlobalStyles';
import Radio from './Radio/Radio';
import RadioGroup from './Radio/RadioGroup';
import RangePicker from './RangePicker';
import Search from './Search';
import Select from './Select';
import Steps from './Steps';
import StyledBasicProPageWithHeader from './StyledBasicProPageWithHeader';
import Tabs from './Tabs';
import TimePicker from './TimePicker';
import Title from './Title';
import Tooltip from './Tooltip';
import Typography from './Typography';
import VehicleCarousel from './VehicleCarousel/VehicleCarousel';
import WarningMessageBox from './WarningMessageBox';
import notification from './notification';

const PorscheThemeV3: ThemeComponents = {
    theme: CompanyTheme.PorscheV3,
    gutter: {
        testDriveModal: [14, 0],
    },
    Input,
    Card,
    ConfiguratorDropdown,
    ConfiguratorSubHeader,
    ConfiguratorMessageButton,
    ConfiguratorChatIcon,
    ConfiguratorDepositDisplay,
    CollapsePanel: Collapse,
    CollapseWrapper: Collapse,
    PageWithHeader,
    BasicProPageWithHeader: StyledBasicProPageWithHeader,
    Dropdown,
    Select,
    StandardLayout,
    ConfiguratorLayout,
    Button,
    Title,
    MobilityWebpageLayout,
    BackButton,
    CloseButton,
    ComparisonButton: Button,
    ActivityLogButton,
    FinderLayout,
    Checkbox,
    CheckboxGroup,
    RangePicker,
    TimePicker,
    FormFields: {
        RangePickerField,
        FixedRangePickerField,
        SelectField,
        InputField,
        InputNumberField,
        DatePickerField,
        IdentityNumberField,
        NationalityField,
        PhoneAndPrefixField,
        PhoneVerificationField,
        RegionField,
        CountryField,
        MultipleDraggerField,
        TimePickerField: TimeRangePickerField,
        RaceField,
        GenderField,
        ResidentialStatusField,
        MaritalStatusField,
        TranslatedInputField,
        CheckboxField: CheckboxInputField,
        SwitchField,
        DisplayField,
        PasswordField,
        DisplayFieldWithUTCOffset,
        VehicleMakeSelectField,
        VehicleModelSelectField,
        CascaderField,
        DealershipCascaderField,
        TextAreaField,
        BirthdayField,
        RadioGroupField,
    },
    LoadingSpinner,
    Modal,
    PackageDetailsModal,
    ConsentModal,
    notification,
    Radio,
    RadioGroup,
    CardWithHeader,
    Steps,
    Search,
    ErrorMessageBox,
    InfoMessageBox,
    WarningMessageBox,
    JourneySteps,
    ResponsiveStyledTable,
    PaginatedTableWithContext,
    GenericTable: styled(Table)``,
    Table,
    Popconfirm,
    Carousel,
    Pagination,
    Tooltip,
    Calculator,
    ListItem,
    Tabs,
    Anchor,
    LegalText,
    ArrowDownIcon,
    VehicleCarousel,
    Transfer,
    Switch,
    Drawer,
    Dragger,
    DraggerWrapper,
    Upload,
    Typography,
    Link,
    ExclamationIcon,
    OTPInput,
};

export default makeThemeComponents('PorscheV3ThemeProvider', PorscheThemeV3, PorscheGlobalStyles, true);
