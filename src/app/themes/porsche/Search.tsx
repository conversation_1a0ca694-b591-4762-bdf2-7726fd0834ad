import { Input } from 'antd';
import { SearchProps } from 'antd/lib/input/Search';
import { memo } from 'react';
import styled from 'styled-components';

const StyledSearch = styled(Input.Search)`
    height: 48px;
    padding: 8px;
    font-size: var(--input-font-size, 1rem);
    border: 1px solid rgb(98, 102, 105);
    border-radius: 0;
    outline: transparent solid 1px;
    outline-offset: 2px;
    background-color: white;

    :hover {
        border-color: black;
    }

    :has(.ant-input:focus),
    :has(.ant-input-focused) {
        outline-color: rgb(98, 102, 105);
    }

    & .ant-input-group > .ant-input:first-child {
        border: none;
        box-shadow: none;
        font-size: var(--input-font-size, 1rem);
    }

    & > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary) {
        border: none;
        box-shadow: none;

        :hover,
        :focus {
            border-color: none;
            box-shadow: none;
        }
    }
`;

const Search = (props: SearchProps) => <StyledSearch {...props} />;

export default memo(Search);
