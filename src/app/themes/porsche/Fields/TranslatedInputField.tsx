import TranslatedInputField from '../../../components/fields/TranslatedInputField';
import { type TranslatedInputFieldProps } from '../../../components/fields/TranslatedInputField/TranslatedInputFieldProps';
import { InputContainer } from './shared';

const TranslatedInputFormField = (props: TranslatedInputFieldProps) => (
    <InputContainer>
        <TranslatedInputField {...props} />
    </InputContainer>
);

export default TranslatedInputFormField;
