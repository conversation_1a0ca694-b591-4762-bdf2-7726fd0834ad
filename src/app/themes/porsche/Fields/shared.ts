import styled, { css } from 'styled-components';
import { SelectFieldProps as FormSelectFieldProps } from '../../../components/fields/shared';
import type { Brightness } from '../../types';
import Arrow from './Arrow.svg';

export const porscheInputNumberAffixStyle = css`
    &.ant-input-number-affix-wrapper {
        height: 48px;
        padding: 8px;
        border: 1px solid rgb(98, 102, 105);
        border-radius: 0;

        outline: 1px solid transparent;
        outline-offset: 2px;

        & input {
            font-size: var(--input-font-size, 1rem);
        }
    }

    &.ant-input-number-affix-wrapper:not(.ant-input-number-affix-wrapper-disabled):hover {
        border-color: black;
    }

    &.ant-input-number-affix-wrapper:not(.ant-input-number-affix-wrapper-disabled):focus,
    &.ant-input-number-affix-wrapper-focused {
        box-shadow: none;
        outline-color: rgb(98, 102, 105);
    }

    &.ant-input-number-affix-wrapper-status-error:not(.ant-input-number-affix-wrapper-disabled):not(
            .ant-input-number-affix-wrapper-borderless
        ).ant-input-number-affix-wrapper {
        box-shadow: none;
        border: 2px solid rgb(224, 0, 0);

        :hover {
            border-color: rgb(163, 0, 0);
        }

        :focus {
            border-color: rgb(224, 0, 0);
            outline-color: rgb(224, 0, 0);
        }
    }

    &.ant-input-number-affix-wrapper-status-error:not(.ant-input-number-affix-wrapper-disabled):not(
            .ant-input-number-affix-wrapper-borderless
        ).ant-input-number-affix-wrapper:hover {
        border-color: rgb(163, 0, 0);
    }

    &.ant-input-number-affix-wrapper-status-error:not(.ant-input-number-affix-wrapper-disabled):not(
            .ant-input-number-affix-wrapper-borderless
        ).ant-input-number-affix-wrapper:focus,
    &.ant-input-number-affix-wrapper-status-error:not(.ant-input-number-affix-wrapper-disabled):not(
            .ant-input-number-affix-wrapper-borderless
        ).ant-input-number-affix-wrapper-focused {
        box-shadow: none;
        border-color: rgb(224, 0, 0);
        outline: 1px solid rgb(224, 0, 0);
    }

    &.ant-input-number-affix-wrapper-disabled {
        color: rgb(150, 152, 154);
        border-color: rgb(150, 152, 154);
    }
`;
const porscheInputAffixStyle = css`
    &.ant-input-affix-wrapper {
        height: 48px;
        padding: 8px;
        border: 1px solid rgb(98, 102, 105);
        border-radius: 0;
        outline: 1px solid transparent;
        outline-offset: 2px;

        & input {
            font-size: var(--input-font-size, 1rem);
        }
    }

    &.ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
        border-color: black;
    }

    &.ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):focus,
    &.ant-input-affix-wrapper-focused {
        box-shadow: none;
        outline-color: rgb(98, 102, 105);
    }

    &.ant-input-affix-wrapper-status-error:not(.ant-input-affix-wrapper-disabled):not(
            .ant-input-affix-wrapper-borderless
        ).ant-input-affix-wrapper {
        box-shadow: none;
        border: 2px solid rgb(224, 0, 0);

        :hover {
            border-color: rgb(163, 0, 0);
        }

        :focus {
            border-color: rgb(224, 0, 0);
            outline-color: rgb(224, 0, 0);
        }
    }

    &.ant-input-affix-wrapper-status-error:not(.ant-input-affix-wrapper-disabled):not(
            .ant-input-affix-wrapper-borderless
        ).ant-input-affix-wrapper-focused {
        border-color: rgb(224, 0, 0);
        outline: 1px solid rgb(224, 0, 0);
    }

    &.ant-input-affix-wrapper-disabled {
        color: rgb(150, 152, 154);
        border-color: rgb(150, 152, 154);
    }
`;

export const porscheInputStyle = css`
    ${porscheInputAffixStyle}

    &.ant-input {
        height: 48px;
        padding: 8px;
        border: 1px solid rgb(98, 102, 105);
        border-radius: 0;
        outline: 1px solid transparent;
        outline-offset: 2px;

        & input {
            font-size: var(--input-font-size, 1rem);
            line-height: normal;
        }
    }

    &.ant-input:not(.ant-input-disabled):hover {
        border-color: black;
    }

    &.ant-input:not(.ant-input-disabled):focus,
    &.ant-input-focused {
        box-shadow: none;
        border: 1px solid rgb(98, 102, 105);
        outline: 1px solid rgb(98, 102, 105);

        :hover {
            border-color: black;
        }
    }

    &.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless) {
        box-shadow: none;
        border: 2px solid rgb(224, 0, 0);
        outline: 1px solid transparent;

        :hover {
            border-color: rgb(163, 0, 0);
        }

        :focus {
            border-color: rgb(224, 0, 0);
            outline-color: rgb(224, 0, 0);
        }
    }

    &.ant-input.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input:focus {
        border-color: rgb(224, 0, 0);
        outline: 1px solid rgb(224, 0, 0);

        :hover {
            border-color: rgb(163, 0, 0);
        }
    }

    &.ant-input[disabled],
    &.ant-input-disabled {
        color: rgb(150, 152, 154);
        border-color: rgb(150, 152, 154);
    }
`;

export const porscheLabelStyle = css`
    & .ant-form-item-label {
        padding-bottom: 4px;
    }
    & .ant-form-item-label > label {
        font-weight: 4000;
    }

    & .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        order: 1;
        margin-right: 0px;
        margin-left: 4px;
    }
`;

export const CountryContainer = styled.div`
    &.ant-select-selector {
        background-color: transparent;
        min-height: 34px;
        padding-top: 1px;

        height: 48px;
        padding: 8px;
        border: 1px solid #d9d9d9;
    }
`;

export const PasswordContainer = styled.div`
    ${porscheLabelStyle}

    & .ant-form-item-control-input-content {
        & > {
            ${porscheInputStyle}
        }

        & > .ant-input-affix-wrapper {
            & .ant-input-password-icon.anticon:hover {
                color: rgb(213, 0, 28);
            }

            & .anticon-eye-invisible.ant-input-password-icon {
                color: black;
            }

            & .anticon-eye.ant-input-password-icon {
                & svg path:nth-last-child(-n + 2) {
                    fill: black;
                }

                :hover {
                    & svg path:nth-last-child(-n + 2) {
                        fill: rgb(213, 0, 28);
                    }
                }
            }
        }
    }
`;

export const NationalityContainer = styled.div`
    ${porscheLabelStyle}

    &.ant-select-selector {
        background-color: transparent;
        min-height: 34px;
        padding-top: 1px;
    }

    & .ant-form-item-control-input-content {
        & > {
            ${porscheInputStyle}
        }
    }
`;

export const StyledPorscheArrow = styled(Arrow)`
    height: 24px;
    width: 24px;
`;

export const SelectContainer = styled.div<Brightness>`
    ${porscheLabelStyle}

    & .ant-select-arrow > svg {
        fill: ${({ brightness = 'light' }) => (brightness === 'dark' ? 'white' : 'black')};
        transform: rotate3d(0, 0, 1, 0.0001deg);
        transition: transform var(--p-transition-duration, 0.24s) ease;
    }

    & .ant-select-open .ant-select-arrow > svg {
        transform: rotate3d(0, 0, 1, 180deg);
    }

    // add border outline for select container
    & .ant-form-item-control-input-content > .ant-select > .ant-select-selector {
        height: 48px;
        padding: 8px;
        border: 1px solid
            ${({ brightness = 'light' }) => (brightness === 'dark' ? 'rgb(176, 177, 178);' : 'rgb(98, 102, 105);')};
        border-radius: 0;
        outline: 1px solid transparent;
        outline-offset: 2px;

        & input {
            font-size: var(--input-font-size, 1rem);
            height: 100%;
            margin-left: -3px;
        }
    }

    // retian border styling when focused
    & .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
        border: 1px solid
            ${({ brightness = 'light' }) => (brightness === 'dark' ? 'rgb(176, 177, 178)' : 'rgb(98, 102, 105)')};
    }

    // change color when select is open to fixed color
    & .ant-select-single.ant-select-open .ant-select-selection-item {
        color: ${({ brightness = 'light' }) => (brightness === 'dark' ? 'rgb(255, 255, 255)' : 'rgba(0, 0, 0, 0.85)')};
    }

    // add outline when select is focused
    & .ant-form-item-control-input-content > .ant-select.ant-select-focused > .ant-select-selector {
        outline: 1px solid transparent;
        outline-color: ${({ brightness = 'light' }) =>
            brightness === 'dark' ? 'rgb(176, 177, 178)' : 'rgb(98, 102, 105)'};
    }

    // change border color to red when there's error
    & .ant-select-status-error.ant-select .ant-select-selector,
    & .ant-select-status-error.ant-select:not([disabled]):hover .ant-select-selector {
        border: 2px solid rgb(224, 0, 0);
    }

    // change outline color to red when there's error
    & .ant-select-status-error.ant-select-focused .ant-select-selector {
        outline: 1px solid rgb(224, 0, 0);
    }

    & .ant-form-item-control-input-content > .ant-select-disabled > .ant-select-selector {
        color: rgb(150, 152, 154);
        border-color: rgb(150, 152, 154);
    }

    & .ant-select-dropdown-placement-bottomLeft,
    & .ant-select-dropdown-placement-bottomRight,
    & .ant-select-dropdown-placement-topLeft,
    & .ant-select-dropdown-placement-topRight {
        border-radius: 0;
        color: ${({ brightness = 'light' }) => (brightness === 'dark' ? 'white' : 'black')};
        background: ${({ brightness = 'light' }) => (brightness === 'dark' ? 'rgb(14, 20, 24)' : 'rgb(255, 255, 255)')};
        border: 1px solid
            ${({ brightness = 'light' }) => (brightness === 'dark' ? 'rgb(176, 177, 178)' : 'rgb(98, 102, 105)')};

        :hover {
            border: 1px solid ${({ brightness = 'light' }) => (brightness === 'dark' ? 'white' : 'black')};
            border-top: 1px solid
                ${({ brightness = 'light' }) => (brightness === 'dark' ? 'rgb(14, 20, 24)' : 'rgb(227, 228, 229)')};
        }
    }

    & .ant-select-dropdown-placement-bottomLeft,
    & .ant-select-dropdown-placement-bottomRight {
        border-top: 1px solid
            ${({ brightness = 'light' }) => (brightness === 'dark' ? 'rgb(14, 20, 24)' : 'rgb(227, 228, 229)')};
        padding: 0;
    }

    & .ant-select-dropdown-placement-topLeft,
    & .ant-select-dropdown-placement-topRight {
        border-bottom: 1px solid
            ${({ brightness = 'light' }) => (brightness === 'dark' ? 'rgb(14, 20, 24)' : 'rgb(227, 228, 229)')};
        padding: 0;
    }

    & .ant-select-item-option:not(.ant-select-item-option-selected):not(.ant-select-item-option-active) {
        background: transparent;
        color: ${({ brightness = 'light' }) => (brightness === 'dark' ? 'white' : 'black')};
    }

    & .ant-select-item-option-selected:not(.ant-select-item-option-disabled),
    & .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
        background: ${({ brightness = 'light' }) => (brightness === 'dark' ? 'black' : 'rgb(242, 242, 242)')};
        color: var(--ant-primary-color);
        font-weight: normal;
    }
`;

export const PorscheSelectCss = css<Brightness>`
    &.ant-select {
        &:not(.ant-select-customize-input) {
            @media screen and (max-width: 767px) {
                width: 100%;
            }

            & > .ant-select-arrow {
                color: ${({ brightness = 'light' }) => (brightness === 'dark' ? 'white' : 'black')};
                top: 50%;
            }

            & > .ant-select-selector {
                background-color: transparent;
                color: ${({ brightness = 'light' }) => (brightness === 'dark' ? 'white' : 'black')};
                height: 48px;
                width: 100%;
                display: flex;
                align-items: center;
                border: 1px solid
                    ${({ brightness = 'light' }) =>
                        brightness === 'dark' ? 'rgb(176, 177, 178);' : 'rgb(98, 102, 105);'};
                border-radius: 0;
                outline: 1px solid transparent;
                outline-offset: 2px;
                cursor: pointer;

                :hover {
                    border: 1px solid ${({ brightness = 'light' }) => (brightness === 'dark' ? 'white' : 'black')};
                }

                & input {
                    font-size: var(--input-font-size, 1rem);
                    height: 100%;
                    margin-left: -3px;
                }
            }

            &.ant-select-focused:not(.ant-select-disabled) {
                & > .ant-select-selector {
                    box-shadow: none;
                    outline: 1px solid transparent;
                    outline-color: ${({ brightness = 'light' }) =>
                        brightness === 'dark' ? 'rgb(176, 177, 178)' : 'rgb(98, 102, 105)'};
                    :hover {
                        border: 1px solid ${({ brightness = 'light' }) => (brightness === 'dark' ? 'white' : 'black')};
                    }
                }
            }

            &.ant-select-status-error.ant-select-focused:not(.ant-select-disabled) > .ant-select-selector {
                box-shadow: none;
            }

            &.ant-select-status-error.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(
                    .ant-pagination-size-changer
                )
                .ant-select-selector {
                border: 2px solid rgb(224, 0, 0) !important;
            }

            &.ant-select-status-error:not(.ant-select-disabled):not(.ant-pagination-size-changer)
                > .ant-select-selector {
                border-color: rgb(224, 0, 0) !important;
            }

            & .ant-select-selection-placeholder {
                font-size: var(--input-font-size, 1rem);
            }

            &.ant-select-disabled > .ant-select-selector {
                color: rgb(150, 152, 154);
                border-color: rgb(150, 152, 154);
            }
        }
    }
`;

export const treeCheckboxCss = css`
    &.ant-tree-checkbox-wrapper,
    & .ant-tree-checkbox-wrapper {
        font-size: inherit;
        color: rgba(0, 0, 0, 0.85);

        align-items: flex-start;

        &:hover .ant-tree-checkbox::after {
            visibility: hidden;
        }
    }

    & .ant-tree-checkbox {
        & + span {
            padding-top: 0.175rem;
            line-height: 1.5rem;
        }

        & .ant-tree-checkbox-inner {
            width: var(--checkbox-width, 1.5rem);
            height: var(--checkbox-height, 1.5rem);
            background-color: rgb(255, 255, 255);
            border-color: rgb(98, 102, 105);
            border-radius: 0;
            margin-bottom: -2px;
        }

        &:hover {
            & .ant-tree-checkbox-inner {
                border-color: black;
            }
        }

        &:hover::after {
            visibility: hidden;
        }
    }

    & .ant-tree-checkbox-indeterminate > .ant-tree-checkbox-inner {
        &::after {
            background-color: #000;
            width: 16px;
            height: 16px;
        }
    }

    & .ant-tree-checkbox-checked > .ant-tree-checkbox-inner {
        border-color: rgb(50, 54, 57);
        background-color: rgb(50, 54, 57);
        border-radius: 0;

        &::after {
            border-width: 1px;
            border-color: #fff;
            transform: rotate(40deg) scale(1.5, 2.1) translate(-50%, -50%);
            top: 15px;
            left: 5px;
        }
    }
`;

export const DropdownContainer = styled.div`
    ${porscheLabelStyle}

    & .ant-tree {
        font-size: var(--button-font-size, 1rem);

        ${treeCheckboxCss}
    }

    & .ant-dropdown-open.ant-dropdown-trigger > .ant-space > .ant-space-item > svg {
        transform: rotate3d(0, 0, 1, 180deg);
    }

    & .ant-dropdown-trigger > .ant-space > .ant-space-item > svg {
        transform: rotate3d(0, 0, 1, 0.0001deg);
        transition: transform var(--p-transition-duration, 0.24s) ease;
    }

    & .ant-dropdown-menu-title-content > span {
        margin-left: 4px;
    }

    & .ant-dropdown-menu-item-selected,
    .ant-dropdown-menu-item:hover {
        background: rgb(242, 242, 242);

        & .ant-dropdown-menu-title-content {
            color: rgb(213, 0, 28);
        }
    }
    & .ant-space-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: var(--button-font-size, 1rem);
    }

    // add border outline for select container
    & .ant-dropdown-trigger {
        height: 48px;
        border: 1px solid rgb(98, 102, 105);
        border-radius: 0;
        outline: 1px solid transparent;
        outline-offset: 2px;
        color: black;

        &::after {
            display: none;
        }
    }

    & .ant-btn:hover,
    .ant-btn:focus {
        color: black;
        border-color: rgb(98, 102, 105);
    }

    & .ant-dropdown-menu {
        margin: -3px 0px 0px;
        border: 1px solid rgb(98, 102, 105);
        border-radius: 0px;
        padding: 0px;
    }

    // add outline on dropdown open
    & .ant-dropdown-trigger:focus {
        box-shadow: none;
        outline: 1px solid rgb(98, 102, 105);
    }

    & .ant-dropdown-menu-item-group-title {
        display: block;
        padding: 0.5rem 0.75rem;
        margin-top: 0.5rem;
        font-weight: 700;
        color: black;
        font-size: var(--button-font-size, 1rem);
    }

    & .ant-dropdown-menu-item-group-list {
        margin: 0px;

        & .ant-dropdown-menu-title-content {
            padding-left: 12px;
        }
    }
    & .ant-dropdown-menu-title-content {
        color: black;
        font-size: var(--button-font-size, 1rem);
        line-height: 1.5;
    }
`;

export const InputContainer = styled.div`
    & .ant-form-item {
        ${porscheLabelStyle}
    }

    & .ant-form-item-control-input-content {
        & > {
            ${porscheInputStyle}
        }
    }
`;

/* For input number it's different structure */
export const InputNumberContainer = styled.div`
    ${porscheLabelStyle}

    & .ant-input-number {
        border: 1px solid rgb(98, 102, 105);
        padding: 0;
        height: 48px;
        border-radius: 0;

        .ant-input-number-input {
            height: 46px;
            border-radius: 0;
            outline: 1px solid transparent;
            outline-offset: 3px;
            padding: 8px;

            &:focus {
                outline-color: rgb(98, 102, 105);
            }
        }

        &.ant-input-number-disabled {
            color: rgb(150, 152, 154);
            border-color: rgb(150, 152, 154);
        }
    }

    & > {
        ${porscheInputStyle}
    }

    & .ant-input-number-affix-wrapper {
        ${porscheInputNumberAffixStyle}

        & {
            height: 48px;

            & .ant-input-number {
                height: auto;
                display: flex;
                align-items: center;
                width: 100%;
                padding: 0;

                .ant-input-number-input {
                    height: 100%;
                    padding: 0;

                    &:focus {
                        outline-color: transparent;
                    }
                }
            }
        }
    }

    & .ant-input-number-group-wrapper {
        height: 48px;

        & .ant-input-number-wrapper {
            height: 46px;
            border: 1px solid rgb(98, 102, 105);
            border-radius: 0;

            outline: 1px solid transparent;
            outline-offset: 2px;

            transition: all 0.3s;

            &:focus-within {
                outline-color: rgb(98, 102, 105);
            }
        }

        &.ant-input-number-group-wrapper-status-error {
            & .ant-input-number-wrapper {
                border: 2px solid rgb(224, 0, 0);

                outline: 1px solid transparent;
                outline-offset: 2px;

                &:focus-within {
                    outline: 1px solid rgb(224, 0, 0);
                }
            }
        }

        & .ant-input-number {
            height: 46px;
            border: 0;
            box-shadow: none;
            padding-top: 0;
            outline: 0;
            outline-offset: 0px;

            &.ant-input-number-status-error {
                box-shadow: none;
                outline: 0;
                outline-offset: 0px;
            }

            & input {
                outline: 0;
                outline-offset: 0px;
                box-shadow: none;
            }
        }

        & .ant-input-number-group-addon {
            border: 0;
        }
    }
`;

export const DateContainer = styled.div`
    ${porscheLabelStyle}

    /* Added this manually, just to inform the browser, which one should prioritized */
    & .ant-picker.porsche {
        height: 48px;
        padding: 8px;
        border: 1px solid rgb(98, 102, 105);
        border-radius: 0;

        outline: 1px solid transparent;
        outline-offset: 2px;

        & input {
            font-size: var(--input-font-size, 1rem);
        }

        &:focus,
        &.ant-picker-focused {
            outline-color: rgb(98, 102, 105);
        }

        & .ant-picker-clear {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &.ant-picker-disabled {
            border-color: rgb(150, 152, 154);

            & input {
                color: rgb(150, 152, 154);
            }
        }
    }

    & .ant-picker-status-error.ant-picker,
    & .ant-picker-status-error.ant-picker:not([disabled]):hover {
        border: 2px solid rgb(224, 0, 0);
    }

    & .ant-picker-status-error.ant-picker-focused {
        outline: 1px solid rgb(224, 0, 0);
    }

    & .ant-input.ant-input:focus {
        border: 1px solid rgb(98, 102, 105);
    }

    & .ant-picker-disabled,
    &.ant-picker-disabled {
        color: rgb(150, 152, 154);
        border-color: rgb(150, 152, 154);
    }
`;

export const StyledDisplay = styled.div`
    & .ant-form-item .ant-form-item-label {
        padding-bottom: 4px;
    }

    & .ant-row > .ant-form-item-control {
        border: 1px solid rgb(235, 235, 235);
        outline: transparent solid 1px;
        outline-offset: 2px;
        height: 48px;
        padding: 8px;
        color: rgb(98, 102, 105);
        background-color: rgb(235, 235, 235);
    }
`;

export default SelectContainer;

export type SelectFieldProps = FormSelectFieldProps & Brightness & { displayOptionsAsPopover?: boolean };
