import { isNil } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import Table from '../../components/PaginatedTableWithContext';
import type { CompanyPaginatedTableProps } from '../../components/shared';

const StyledTable = styled(Table)`
    & a {
        color: rgb(1, 2, 5);
        text-decoration: underline;
        padding: 3px;
        border-radius: 4px;
    }

    & a:hover {
        background: rgba(148, 149, 152, 0.18);
    }

    &
        .ant-table-thead
        > tr
        > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not(
            [colspan]
        )::before {
        background-color: rgba(0, 0, 0, 0);
    }

    & .ant-table-tbody > tr {
        & > td {
            border-bottom-color: #d8d8db;
        }

        &:not(:hover):not(.ant-table-row-selected) > td:not(.ant-table-column-sort) {
            background-color: white;
        }

        &.ant-table-row:hover > td {
            background: #f5f5f5;
        }

        &.ant-table-row-selected,
        &.ant-table-row-selected:hover {
            > td {
                background-color: #01020514;
            }
        }
    }

    .ant-table-thead > tr > th {
        border-bottom-color: #d8d8db;

        &:not(.ant-table-column-sort) {
            background-color: white;
        }
    }

    & table {
        font-size: 16px;
    }
`;

const PaginatedTableWithContext = ({ ...props }: CompanyPaginatedTableProps) => {
    const { columns, onChange } = props;
    const { t } = useTranslation('customerList');

    const sortOrder = useMemo(
        () =>
            columns.reduce(
                (acc, { key, defaultSortOrder }) => ({
                    ...acc,
                    [key as string]: defaultSortOrder || null,
                }),
                {}
            ),
        [columns]
    );

    const emptySortOrder = useMemo(
        () =>
            columns.reduce(
                (acc, { key }) => ({
                    ...acc,
                    [key as string]: null,
                }),
                {}
            ),
        [columns]
    );

    const [sorter, setSorter] = useState(sortOrder);

    const getSorterLabel = useCallback(
        sortOrder => {
            if (isNil(sortOrder)) {
                return t('customerList:sorterLabel.sortAscending');
            }

            switch (sortOrder) {
                case 'descend':
                    return t('customerList:sorterLabel.cancelSort');

                case 'ascend':
                    return t('customerList:sorterLabel.sortDescending');

                default:
                    throw new Error(`Invalid sort order: ${sortOrder}`);
            }
        },
        [t]
    );

    const enhancedOnChange = useCallback(
        (pagination, filters, sorter, extra) => {
            onChange(pagination, filters, sorter, extra);
            setSorter({ ...emptySortOrder, [sorter.columnKey]: sorter.order || null });
        },
        [emptySortOrder, onChange]
    );

    const enhancedColumns = useMemo(
        () =>
            columns.map(({ key, ...props }) => ({
                key,
                ...props,
                showSorterTooltip: {
                    title: getSorterLabel(sorter[key as string]),
                    color: 'white',
                    overlayInnerStyle: {
                        color: 'black',
                        padding: '8px 16px',
                        'font-weight': '400',
                        'font-size': '16px',
                        'border-radius': '4px',
                        filter: 'drop-shadow(rgba(0, 0, 0, 0.3) 0px 0px 16px)',
                        'backdrop-filter': 'drop-shadow(transparent 0px 0px 0px)',
                    },
                },
            })),
        [columns, getSorterLabel, sorter]
    );

    return <StyledTable {...props} columns={enhancedColumns} onChange={enhancedOnChange} />;
};

export default PaginatedTableWithContext;
