import { PageContainerProps } from '@ant-design/pro-layout';
import type {
    DropdownProps as AntdDropdownProps,
    FormItemProps as AntdFormItemProps,
    PopconfirmProps as AntdPopconfirmProps,
    SwitchProps as AntdSwitchProps,
    CardProps,
    CollapseProps,
    DrawerProps,
    InputProps,
    InputRef,
    RadioGroupProps,
    RadioProps,
    TableProps,
    TooltipProps,
    TransferProps,
    UploadProps,
} from 'antd';
import type { PickerProps } from 'antd/es/date-picker/generatePicker';
import type { CheckboxGroupProps, CheckboxProps } from 'antd/lib/checkbox';
import { Gutter } from 'antd/lib/grid/row';
import { SearchProps } from 'antd/lib/input/Search';
import type { StepsProps as AntdStepsProps } from 'antd/lib/steps';
import type { TextProps } from 'antd/lib/typography/Text';
import type { DraggerProps } from 'antd/lib/upload';
import { type ComponentType, type PropsWithChildren, type ReactNode, createContext } from 'react';
import type { DefaultTheme, StyledComponent } from 'styled-components';
import { CompanyTheme } from '../api/types';
import type { SummaryFieldLayoutProps, DualFieldLayoutProps } from '../calculator/fieldComponents/shared';
import type { DatePickerValue } from '../calculator/fieldComponents/withDatePickerFieldUI';
import type { DayjsRangePickerProps } from '../components/DayjsRangePicker';
import type { TimePickerFieldProps as TimePickerProps } from '../components/TimePickerField';
import type { BirthdayFieldProps } from '../components/fields/BirthdayField';
import type { CheckboxFieldProps } from '../components/fields/CheckboxField';
import type { DatePickerFieldProps } from '../components/fields/DatePickerField';
// eslint-disable-next-line max-len
import type { DealershipCascaderFieldProps } from '../components/fields/DealershipFields/DealershipCascaderField/shared';
import type { DisplayFieldProps } from '../components/fields/DisplayField';
import type { DisplayFieldWithUTCOffsetProps } from '../components/fields/DisplayFieldWithUTCOffset';
import type { FixedRangePickerFieldProps } from '../components/fields/FixedRangePickerField';
import type { IdentityNumberFieldProps } from '../components/fields/IdentityNumberField';
import type { InputFieldProps } from '../components/fields/InputField';
import type { InputFieldProps as InputNumberFieldProps } from '../components/fields/InputNumberField';
import type { RadioGroupFieldProps } from '../components/fields/RadioGroupField';
import type { RangePickerFieldProps } from '../components/fields/RangePickerField';
import type { TextAreaFieldProps } from '../components/fields/TextAreaFieldProps';
import type { TimePickerFieldProps } from '../components/fields/TimePickerField';
import { type TranslatedInputFieldProps } from '../components/fields/TranslatedInputField/TranslatedInputFieldProps';
import type { PhoneFieldProps } from '../components/fields/ci/PhoneField';
import type {
    RegionFieldProps,
    SwitchProps,
    PhoneVerificationFieldProps,
    NationalityFieldProps,
    DraggerFieldProps,
    MaritalStatusFieldProps,
    CascaderFieldProps,
    CountryFieldProps,
    GenderFieldProps,
    RaceFieldProps,
    ResidentialStatusFieldProps,
    VehicleMakeSelectFieldProps,
    VehicleModelSelectFieldProps,
} from '../components/fields/shared';
import type { CompanyPaginatedTableProps } from '../components/shared';
import type { PageWithHeaderProps } from '../layouts/BasicProLayout/BasicProPageWithHeader';
import type { ResponsiveStyledTableProps } from '../pages/shared/ResponsiveStyledTable';
import type { CardWithHeaderProps } from './default/CardWithHeader';
import type { ErrorMessageBoxProps } from './default/ErrorMessageBox';
import type { InfoMessageBoxProps } from './default/InfoMessageBox';
import type { OTPInputProps } from './default/OTPInput';
import type { DefaultLayoutProps } from './default/Standard/Layout';
import type { TabsProps } from './default/Tabs';
import type { Typography } from './default/Typography';
import type { WarningMessageBoxProps } from './default/WarningMessageBox';
import type { PackageDetailsModalProps } from './default/shared';
import type { ThemeNotificationApi } from './makeNotificationApi';
import type { CarouselProps } from './porsche/Carousel';
import type { SelectFieldProps } from './porsche/Fields/shared';
import type {
    AddVehicleButtonProps,
    AnchorProps,
    ButtonProps,
    ConfiguratorDepositDisplayProps,
    ConsentModalProps,
    DiscountCodeValidationProps,
    DisplaySelectFieldProps,
    DropdownProps,
    ListItemProps,
    ModalProps,
    PaginationProps,
    PromoCodeFieldProps,
    PromoCodeValidationProps,
    SelectProps,
    StepsProps,
    VehicleImageProps,
    DisplayLinkProp,
} from './types';

export type FormFields = {
    RangePickerField: ComponentType<RangePickerFieldProps>;
    FixedRangePickerField: ComponentType<FixedRangePickerFieldProps>;
    SelectField: ComponentType<SelectFieldProps>;
    BirthdayField: ComponentType<BirthdayFieldProps>;
    DatePickerField: ComponentType<DatePickerFieldProps>;
    InputField: ComponentType<InputFieldProps>;
    InputNumberField: ComponentType<InputNumberFieldProps>;
    IdentityNumberField: ComponentType<IdentityNumberFieldProps>;
    PhoneAndPrefixField: ComponentType<PhoneFieldProps>;
    PhoneVerificationField?: ComponentType<PhoneVerificationFieldProps>;
    NationalityField: ComponentType<NationalityFieldProps>;
    RegionField: ComponentType<RegionFieldProps>;
    CountryField: ComponentType<CountryFieldProps>;
    RaceField: ComponentType<RaceFieldProps>;
    GenderField: ComponentType<GenderFieldProps>;
    ResidentialStatusField: ComponentType<ResidentialStatusFieldProps>;
    MaritalStatusField: ComponentType<MaritalStatusFieldProps>;
    MultipleDraggerField: ComponentType<DraggerFieldProps>;
    TimePickerField: ComponentType<TimePickerFieldProps>;
    TranslatedInputField: ComponentType<TranslatedInputFieldProps>;
    SwitchField: ComponentType<SwitchProps>;
    CheckboxField: ComponentType<CheckboxFieldProps>;
    DisplayField: ComponentType<DisplayFieldProps>;
    PasswordField: ComponentType<InputFieldProps>;
    DisplayFieldWithUTCOffset: ComponentType<DisplayFieldWithUTCOffsetProps>;
    VehicleMakeSelectField: ComponentType<VehicleMakeSelectFieldProps>;
    VehicleModelSelectField: ComponentType<VehicleModelSelectFieldProps>;
    CascaderField: ComponentType<CascaderFieldProps<{ children?: any; value: any }>>;
    DealershipCascaderField: ComponentType<DealershipCascaderFieldProps>;
    TextAreaField: ComponentType<TextAreaFieldProps>;
    RadioGroupField: ComponentType<RadioGroupFieldProps<any>>;
};

export type CalculatorComponents = {
    fieldSize: number;
    SelectField: ComponentType<SelectProps>;
    SingleField: ComponentType<InputProps>;
    DisplayField: ComponentType<TextProps | { children: ReactNode }>;
    ClickableDisplayField: ComponentType<TextProps | { children: ReactNode }>;
    DatePickerField: ComponentType<PickerProps<DatePickerValue>>;
    DualFieldLayout: ComponentType<DualFieldLayoutProps>;
    SummaryFieldLayout: ComponentType<SummaryFieldLayoutProps>;
    PromoCodeField: ComponentType<PromoCodeFieldProps>;
    PromoCodeValidation: ComponentType<PromoCodeValidationProps>;
    DiscountCodeValidation: ComponentType<DiscountCodeValidationProps>;
    DisplaySelectField: ComponentType<DisplaySelectFieldProps>;
    AddVehicleButton: ComponentType<AddVehicleButtonProps>;
    VehicleImage: ComponentType<VehicleImageProps>;
    FormItem: ComponentType<AntdFormItemProps>;
    FormItemWrapper: ComponentType<{ children: ReactNode }>;
};

export type ConfiguratorFooterContainerProps = {
    visible: boolean;
};

// Flexible gutter values for different themes
export type GutterTheme = {
    testDriveModal?: [Gutter, Gutter];
};

export type VehicleCarouselProps = {
    sources: string[];
    fallbackSource: string;
    isEnableEncodedURI?: boolean;
};

export type ThemeComponents = {
    theme: CompanyTheme;
    gutter: GutterTheme;
    Button: ComponentType<ButtonProps>;
    ComparisonButton: ComponentType<ButtonProps>;
    ActivityLogButton: StyledComponent<ComponentType<any>, DefaultTheme, {}, never>;
    Input: ComponentType<InputProps & React.RefAttributes<InputRef>>;
    PageWithHeader: ComponentType<
        Omit<PropsWithChildren<PageContainerProps>, 'breadcrumb' | 'breadcrumbRender' | 'backIcon'>
    >;
    BasicProPageWithHeader: ComponentType<PageWithHeaderProps>;
    ConfiguratorDropdown: ComponentType<AntdDropdownProps>;
    Card: StyledComponent<React.FC<CardProps>, {}, {}, never> | ComponentType<CardProps>;
    Dropdown: ComponentType<DropdownProps>;
    Select: ComponentType<SelectProps>;
    StandardLayout: ComponentType<DefaultLayoutProps>;
    ConfiguratorLayout: ComponentType<PropsWithChildren<DefaultLayoutProps>>;
    ConfiguratorSubHeader: StyledComponent<'div', DefaultTheme, {}, never>;
    ConfiguratorMessageButton: ComponentType<ButtonProps>;
    ConfiguratorChatIcon?: ComponentType;
    ConfiguratorDepositDisplay: ComponentType<ConfiguratorDepositDisplayProps>;
    CollapsePanel: StyledComponent<React.FC<CollapseProps>, {}, {}, never> | ComponentType;
    CollapseWrapper: StyledComponent<React.FC<CollapseProps>, {}, {}, never> | ComponentType;
    MobilityWebpageLayout: ComponentType<PropsWithChildren<DefaultLayoutProps>>;
    BackButton: ComponentType<ButtonProps>;
    CloseButton: ComponentType<ButtonProps>;
    FinderLayout: ComponentType<PropsWithChildren<DefaultLayoutProps>>;
    Title: StyledComponent<'div', DefaultTheme, {}, never> | ComponentType;
    Checkbox: ComponentType<CheckboxProps>;
    CheckboxGroup: ComponentType<CheckboxGroupProps>;
    RangePicker: ComponentType<Omit<DayjsRangePickerProps, 'locale'>>;
    TimePicker: ComponentType<TimePickerProps>;
    FormFields: FormFields;
    LoadingSpinner: ComponentType;
    Modal: ComponentType<ModalProps>;
    PackageDetailsModal: ComponentType<PackageDetailsModalProps>;
    ConsentModal: ComponentType<ConsentModalProps>;
    notification: ThemeNotificationApi;
    Radio: ComponentType<RadioProps>;
    RadioGroup: ComponentType<RadioGroupProps>;
    GlobalStyles?: ComponentType;
    CardWithHeader: ComponentType<CardWithHeaderProps>;
    Steps: ComponentType<StepsProps>;
    Search: ComponentType<SearchProps>;
    ErrorMessageBox: ComponentType<ErrorMessageBoxProps>;
    InfoMessageBox: ComponentType<InfoMessageBoxProps>;
    WarningMessageBox: ComponentType<WarningMessageBoxProps>;
    JourneySteps: ComponentType<AntdStepsProps>;
    ResponsiveStyledTable: ComponentType<ResponsiveStyledTableProps>;
    PaginatedTableWithContext: ComponentType<CompanyPaginatedTableProps>;
    // `GenericTable` and `Table` serve different purposes due to TypeScript's inability to infer generic types on StyledComponents.
    GenericTable: StyledComponent<React.FC<TableProps<any>>, {}, {}, never>; // Used when explicitly specifying generic types (e.g., `<GenericTable<RecordType>>`), ensuring type safety and IntelliSense for specific data structures
    Table: ComponentType<TableProps<any>>; // The default table component, more flexible but less type-safe, as it doesn't enforce specific data types for rows.
    StyledFieldArrayTable?: StyledComponent<React.FC<TableProps<any>>, {}, {}, never>;
    StyledEditableTable?: StyledComponent<React.FC<TableProps<any>>, {}, { unitIsPercentage: boolean }, never>;
    Popconfirm: ComponentType<AntdPopconfirmProps>;
    Carousel: ComponentType<CarouselProps>;
    Pagination?: ComponentType<PaginationProps>;
    Tooltip: ComponentType<TooltipProps>;
    Calculator: CalculatorComponents;
    ListItem: ComponentType<ListItemProps>;
    Tabs: ComponentType<TabsProps>;
    Anchor: ComponentType<AnchorProps>;
    LegalText:
        | StyledComponent<'span', DefaultTheme, { isBefore?: boolean; $disabled?: boolean }, never>
        | ComponentType;
    ArrowDownIcon: JSX.Element;
    VehicleCarousel: ComponentType<VehicleCarouselProps>;
    Transfer: StyledComponent<React.FC<TransferProps<any>>, {}, {}, never> | ComponentType<TransferProps<any>>;
    Switch: StyledComponent<React.FC<AntdSwitchProps>, {}, {}, never> | ComponentType<AntdSwitchProps>;
    Drawer: ComponentType<DrawerProps>;
    Upload: ComponentType<UploadProps>;
    Dragger: ComponentType<DraggerProps>;
    DraggerWrapper: ComponentType<any>;
    Typography: Typography;
    Link: ComponentType<DisplayLinkProp>;
    ExclamationIcon: ComponentType<any>;
    OTPInput?: ComponentType<OTPInputProps>;
};

const ThemeComponentsContext = createContext<ThemeComponents | null>(null);

export default ThemeComponentsContext;
