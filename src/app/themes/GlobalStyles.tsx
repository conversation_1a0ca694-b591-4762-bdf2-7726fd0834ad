/* eslint-disable max-len */
import { isNil } from 'lodash/fp';
import { createGlobalStyle, css, CSSProperties } from 'styled-components';
import PorscheFooterContainer from '../components/PorscheFooterContainer';
import { CSSValue } from '../components/WorldFlag';
import FooterContainer from '../utilities/FooterContainer';
import breakpoints from '../utilities/breakpoints';

type Variable = `var(--${string})`;

export type FooterGlobalStyleProps = {
    /**
     * Footer type to display. default is 'default'.
     * Set it as null or undefined if no footer is required.
     */
    footerType?: 'default' | 'porsche';

    /**
     * The maximum width for the footer. This is used when taking the full width
     * of the container is not desirable.
     */
    footerMaxWidth?: CSSValue | Variable;

    /**
     * Indicates if the page has a custom extra floating component if any(e.g. action bar with Next button) at the bottom.
     * This is used to prevent it from covering the page footer (i.e. a footer with type 'default' or 'porsche').
     */
    hasFooterBar?: boolean;

    /**
     * The preferred height for the custom extra floating component if any(e.g. action bar with Next button) at the bottom on mobile devices.
     * This is used to determine how much space at the bottom the footer needs to have in order to avoid being covered by the action bar.
     * This will be applied ONLY if `hasFooterBar` is true.
     * The default height is 70px for both mobile and desktop.
     */
    preferredMobileFooterHeight?: number;

    /**
     * paddings to override the default
     */
    paddingOverride?: CSSProperties['padding'];

    /**
     * background color to override the default
     */
    backgroundColor?: CSSProperties['backgroundColor'];
};

export const FooterGlobalStyle = createGlobalStyle<FooterGlobalStyleProps>`
    .ant-layout {
        ${FooterContainer},
        ${PorscheFooterContainer} {
            ${props => {
                if (props.hasFooterBar) {
                    if (props.preferredMobileFooterHeight) {
                        return `margin-bottom: ${props.preferredMobileFooterHeight}px;
                        @media screen and (min-width: ${breakpoints.md}){
                            margin-bottom: 70px;
                        }`;
                    }

                    return 'margin-bottom: 70px;';
                }

                return 'margin-bottom: 0px;';
            }}
            ${props =>
                !isNil(props.footerMaxWidth) &&
                css`
                    max-width: ${typeof props.footerMaxWidth === 'number'
                        ? `${props.footerMaxWidth}px`
                        : props.footerMaxWidth};
                    margin-left: auto;
                    margin-right: auto;
                `}
            ${props =>
                !isNil(props.paddingOverride) &&
                css`
                    padding: ${props.paddingOverride};
                `} 
            ${props =>
                !isNil(props.backgroundColor) &&
                css`
                    background-color: ${props.backgroundColor};
                `}
        }

        ${FooterContainer} {
            display: ${({ footerType }) => (footerType === 'default' ? 'block' : 'none')};
        }
        ${PorscheFooterContainer} {
            display: ${({ footerType }) => (footerType === 'porsche' ? 'block' : 'none')};
        }
    }
`;
